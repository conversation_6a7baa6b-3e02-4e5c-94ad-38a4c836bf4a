import { Formik, type <PERSON>ik<PERSON><PERSON><PERSON> } from "formik"
import { useLocation, useParams } from "react-router-dom"
import { useEffect, useState } from "react"
import { type BaseRecord } from "@refinedev/core"
import { date, number, object, string } from "yup"

import { Box } from "@mui/material"
import { FormEnforcementLiabilityRestrictionCreate } from "@/components/form/enforcement/liability-restriction/Create"
import { FormEnforcementLiabilityRestrictionWhitelisting, PenguatkuasaanSekatanLiabilitiWhitelistingRequestBodyCreate } from "@/components/form/enforcement/liability-restriction/Whitelisting"

import { type EnforcementLiabilityRestrictionRequestBodyCheck } from "./Create"
import { countWords, fromBase64, useMutation, useQuery } from "@/helpers"
import { type PenguatkuasaanSekatanLiabilitiResponseBodyGet } from "./Base"
import dayjs from "@/helpers/dayjs"
import { useTranslation } from "react-i18next"

export interface EnforcementLiabilityRestrictionRequestBodyCreate
  extends EnforcementLiabilityRestrictionRequestBodyCheck {
    societyId: string | number
    letterReferenceNumber: string
    offenseSection: ("9A" | "13")[]
    sectionDetails: string
    dateOfCommencementOfTheOffence: string | null
    remarks: string
}

export interface EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo {
  id: number
  societyName: string
  societyNo: string | null
  applicationNo: string | null
  blacklistId: string
}

export interface PenguatkuasaanInternalSekatanLiabilitiCreate002Props {
  /**
   * @default CREATE
   */
  mode?: "CREATE" | "UPDATE" | "VIEW"
}

export const PenguatkuasaanInternalSekatanLiabilitiCreate002 = <
  PropType extends PenguatkuasaanInternalSekatanLiabilitiCreate002Props = PenguatkuasaanInternalSekatanLiabilitiCreate002Props,
  RequestBody extends PenguatkuasaanSekatanLiabilitiResponseBodyGet = PenguatkuasaanSekatanLiabilitiResponseBodyGet,
  WhitelistingRequestBody extends PenguatkuasaanSekatanLiabilitiWhitelistingRequestBodyCreate = PenguatkuasaanSekatanLiabilitiWhitelistingRequestBodyCreate,
  SocietyLists extends EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo = EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo
>({
  mode = "CREATE"
}: PropType) => {
  const viewOrUpdate = (["VIEW", "UPDATE"] as PropType["mode"][]).includes(mode);

  const { t } = useTranslation();
  const { id } = useParams();
  const location = useLocation();
  const [societyRegisteredlists, setSocietyRegisteredLists] = useState<SocietyLists[]>([])
  const [blacklistDataByIdResponse, setBlacklistDataByIdResponse] = useState<RequestBody | null>(null)

  const {
    refetch: getSocietyRegisteredByUser
  } = useQuery<{ data: SocietyLists[] }>({
    url: `society/blacklist/getSocietyRegisteredByUser`,
    autoFetch: false,
    onSuccess: (data) => {
      setSocietyRegisteredLists(data.data.data ?? [])
    }
  })
  const { fetchAsync: createBlacklist } = useMutation<
    BaseRecord,
    RequestBody
  >({
    url: "society/blacklist/create"
  })
  const { fetchAsync: whitelistingUser } = useMutation<
    BaseRecord,
    WhitelistingRequestBody
  >({
    url: "society/blacklist/whitelist/create"
  })

  const getInitialValue = (): RequestBody => {
    if (viewOrUpdate) {
      const offenseSection = blacklistDataByIdResponse?.section?.split("_")?.[1] ?? null
      const effectiveDate = blacklistDataByIdResponse?.effectiveDate
        ? dayjs(blacklistDataByIdResponse.effectiveDate, "DD-MM-YYYY").toDate()
        : null
      return {
        ...(blacklistDataByIdResponse ?? {
          section: "",
          societyNo: ""
        }),
        offenseSection,
        effectiveDate
      } as unknown as RequestBody
    }
    return {
      ...(location.state),
      societyId: "",
      societyNo: "",
      noticeReferenceNo: "",
      offenseSection: null,
      section: "",
      effectiveDate: null,
      notes: ""
    }
  }
  const getInitialValueWhitelisting = (): WhitelistingRequestBody => {
    const name = blacklistDataByIdResponse?.name ?? null
    const referenceNo = blacklistDataByIdResponse?.identificationNo ?? null
    const societyNo = blacklistDataByIdResponse?.societyNo ?? null
    return {
      name,
      referenceNo,
      societyNo,
      noticeReferenceNo: "",
      societyId: blacklistDataByIdResponse?.societyId ?? null,
      blacklistId: blacklistDataByIdResponse?.id ?? null,
      whitelistDate: blacklistDataByIdResponse?.whitelistDate ? dayjs(
        blacklistDataByIdResponse?.whitelistDate,
        "DD-MM-YYYY"
      ).toDate() : null,
      notes: ""
    } as unknown as WhitelistingRequestBody
  }
  const handleSubmit = async (
    initialPayload: RequestBody,
    { setSubmitting }: FormikHelpers<RequestBody>
  ) => {
    setSubmitting(true);
    try {
      const payload = {
        ...initialPayload,
        effectiveDate: dayjs(initialPayload.effectiveDate as unknown as Date).format("DD-MM-YYYY")
      } as RequestBody
      await createBlacklist(payload);
    } finally {
      setSubmitting(false);
    }
  }
  const handleSubmitWhitelisting = async (
    initialPayload: WhitelistingRequestBody,
    { setSubmitting }: FormikHelpers<WhitelistingRequestBody>
  ) => {
    setSubmitting(true);
    try {
      const payload = {
        ...initialPayload,
        whitelistDate: dayjs(initialPayload.whitelistDate as unknown as Date).format("DD-MM-YYYY")
      } as WhitelistingRequestBody
      await whitelistingUser(payload);
    } finally {
      setSubmitting(false);
    }
  }

  const initialFormValue = getInitialValue();
  const whitelistingValidationSchema = object({
    noticeReferenceNo: string().required(),
    whitelistDate: date().required(),
    notes: string()
      .test({
        name: "maxWordCount",
        message: t("inputValidationErrorStringExceedsLimitWord", {
          label: t("ulasan"),
          value: 200
        }),
        test: (value) => {
          if (!value) return true;
          return countWords(value) <= 200;
        }
      })
      .notRequired()
  }).required()

  useQuery<{ data: RequestBody }>({
    url: `society/blacklist/${fromBase64(id!)}`,
    autoFetch: viewOrUpdate,
    onSuccess: async (data) => {
      setBlacklistDataByIdResponse(() => {
        const blacklistDataByIdResponse = data.data?.data ?? null
        getSocietyRegisteredByUser({
          filters: [
            {
              field: "identificationNo",
              operator: "eq",
              value: blacklistDataByIdResponse?.identificationNo
            }
          ]
        })
        return blacklistDataByIdResponse
      })
    }
  })

  useEffect(() => {
    (async () => {
      if (mode === "CREATE") {
        await getSocietyRegisteredByUser({
          filters: [
            {
              field: "identificationNo",
              operator: "eq",
              value: location.state?.identificationNo
            }
          ]
        })
      }
    })()
  }, [])

  return (
    <>
      <Box
        sx={{
          marginTop: "0.5rem",
          backgroundColor: "white",
          borderRadius: "1rem",
          padding: 2,
          boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)"
        }}
      >
        <Formik
          initialValues={initialFormValue}
          onSubmit={handleSubmit}
          validationSchema={object().shape({
            name: string().required(),
            identificationNo: string().required(),
            societyId: number().required(),
            noticeReferenceNo: string().required(),
            offenseSection: string().oneOf(["9A", "13"]).required(),
            section: string().required(),
            effectiveDate: date().required(),
            notes: string().notRequired()
          }).required().defined()}
          enableReinitialize
          isInitialValid={false}
        >
          <FormEnforcementLiabilityRestrictionCreate {...{ mode, societyRegisteredlists }} viewOnly={viewOrUpdate} />
        </Formik>
      </Box>
      {
        mode === "UPDATE" && (
          <Box
            sx={{
              marginTop: "0.5rem",
              backgroundColor: "white",
              borderRadius: "1rem",
              padding: 2,
              boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)"
            }}
          >
            <Formik
              initialValues={getInitialValueWhitelisting()}
              onSubmit={handleSubmitWhitelisting}
              validationSchema={whitelistingValidationSchema}
              enableReinitialize
              isInitialValid={false}
            >
              <FormEnforcementLiabilityRestrictionWhitelisting />
            </Formik>
          </Box>
        )
      }
    </>
  )
}
