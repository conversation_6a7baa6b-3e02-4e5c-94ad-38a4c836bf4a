import React, { useEffect, useState } from "react";
import { Box, Typography, Button, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useCustom, useCustomMutation } from "@refinedev/core";
import FileUploader from "@/components/input/fileUpload";
import {
  CitizenshipStatus,
  DocumentUploadType,
  DurationOptions,
  getLocalStorage,
  IdTypes,
  OrganisationPositions,
  useQuery,
} from "@/helpers";
import { API_URL } from "@/api";
import Input from "@/components/input/Input";
import dayjs from "dayjs";
import { ButtonOutline } from "@/components";

interface FormValues {
  name: any;
  citizenshipStatus: any;
  identificationType: any;
  identificationNo: any;
  applicantCountryCode: any;
  gender: any;
  visaNo: number | any;
  visaExpirationDate: any;
  permitNo: any;
  permitExpirationDate: any;
  tujuanDMalaysia: any;
  tempohDMalaysia: any;
  stayDurationDigit: number | any;
  stayDurationUnit: any;
  designationCode: any;
  summary: any;
  societyNo: string | null;
  societyName: null;
}

export const BranchPaparanAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { id, mid } = useParams();
  const branchId = Number(id);
  const memberId = Number(mid);
  const addressList = getLocalStorage("address_list", null);

  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  const [branchData, setBranchData] = useState<any>({});
  const [durationOptionsTranslated, setDurationOptionsTranslated] = useState<
    { value: string; label: string }[]
  >([]);
  useEffect(() => {
    const newDurationList = DurationOptions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setDurationOptionsTranslated(newDurationList);
  }, [t]);

  const {
    data: nonCommitteeData,
    isLoading: nonCommitteeDataLoading,
    refetch: fetchNonCommitteeData,
  } = useQuery({
    url: `society/nonCitizenCommittee/${memberId}`,
    autoFetch: !!memberId,
    onSuccess: (data) => {
      setBranchData(data?.data?.data);
    },
  });

  useEffect(() => {
    if (branchData) {
      setFormValues(branchData);
    }
  }, [branchData]);

  // useEffect(() => {
  //   if (branchData) {
  //       setFormValues((prevValues) => (branchData));
  //     }
  //   }
  // }, [branchData]);

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${branchData?.societyId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!branchData?.societyId,
    },
  });

  const [formValues, setFormValues] = useState<FormValues>({
    name: "",
    societyNo: null,
    societyName: null,
    citizenshipStatus: 2,
    identificationType: "",
    identificationNo: null,
    visaNo: "",
    visaExpirationDate: "",
    permitNo: "",
    permitExpirationDate: "",
    tujuanDMalaysia: "",
    tempohDMalaysia: "",
    stayDurationDigit: null,
    stayDurationUnit: "",
    designationCode: "",
    summary: "",
    applicantCountryCode: null,
    gender: null,
  });

  useEffect(() => {
    if (societyData) {
      setFormValues((prevValues) => ({
        ...prevValues,
        societyNo: societyData?.data?.data?.societyNo,
        societyName: societyData?.data?.data?.societyName,
      }));
    }
  }, [societyData]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "32px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };

  const handleSenaraiAjk = () => {
    navigate(-1);
  };

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newOList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setIdTypeTranslatedList(newOList);
  }, [t]);

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            px: 2,
            py: 4,
          }}
        >
          {branchData?.name}
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={sectionStyle}>
            {t("nonCitizenAJK")}
          </Typography>

          <Input
            value={
              societyData?.data?.data?.societyNo
                ? societyData?.data?.data?.societyNo
                : societyData?.data?.data?.applicationNo
            }
            name="societyNo"
            disabled
            required
            label={t("organizationNumber2")}
          />
          <Input
            value={societyData?.data?.data?.societyName}
            name="societyName"
            disabled
            required
            label={t("organization_name")}
          />
          <Input
            disabled
            value={formValues.name ? formValues.name : ""}
            name="name"
            label={t("fullNameCapitalizedOnlyFirstLetter")}
          />
          <Input
            disabled
            value={
              formValues.citizenshipStatus
                ? Number(formValues.citizenshipStatus)
                : 2
            }
            name="citizenshipStatus"
            label={t("citizenship")}
            type="select"
            options={CitizenshipStatus.map((item) => ({
              ...item,
              label: t(item.label),
            }))}
            required
          />
          <Input
            disabled
            value={
              formValues.identificationType ? formValues.identificationType : ""
            }
            name="identificationType"
            required
            label={t("idTypeCapitalizedOnlyFirstLetter")}
            options={idTypeTranslatedList}
            type="select"
          />
          <Input
            disabled
            value={
              formValues.identificationNo ? formValues.identificationNo : ""
            }
            name="identificationNo"
            required
            label={t("idNumberCapitalizedOnlyFirstLetter")}
          />
          <Input
            disabled
            value={Number(formValues.applicantCountryCode) ?? ""}
            name="applicantCountryCode"
            required
            label={t("originCountry")}
            options={CountryData}
            type="select"
          />
          <Input
            disabled
            value={formValues.visaNo ? formValues.visaNo : ""}
            name="visaNo"
            label={t("nomborVisa")}
          />
          <Input
            disabled
            value={
              formValues.visaExpirationDate
                ? dayjs(formValues.visaExpirationDate).format("DD-MM-YYYY")
                : ""
            }
            name="visaExpirationDate"
            type="date"
            label={t("visaExpiryDate")}
          />
          <Input
            disabled
            value={formValues.permitNo ? formValues.permitNo : ""}
            name="permitNo"
            label={t("nomborPermit")}
          />
          <Input
            disabled
            value={
              formValues.permitExpirationDate
                ? dayjs(formValues.permitExpirationDate).format("DD-MM-YYYY")
                : ""
            }
            name="permitExpirationDate"
            type="date"
            label={t("permitExpiryDate")}
          />
          <Input
            value={formValues?.tujuanDMalaysia ?? ""}
            name="tujuanDMalaysia"
            required
            label={t("tujuanDiMalaysia")}
            disabled
          />
          {Number(formValues.identificationType) !== 4 && (
            <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
              <Grid item xs={12} sm={4}>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("tempohDiMalaysia")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Input
                      value={formValues?.stayDurationDigit ?? ""}
                      name="stayDurationDigit"
                      required
                      type="text"
                      inputMode="numeric"
                      onChange={(e) => {
                        const value = e.target.value;
                        if (/^\d*$/.test(value)) {
                          setFormValues({
                            ...formValues,
                            stayDurationDigit: parseInt(value) || null,
                          });
                        }
                      }}
                      disabled
                      isLabelNoSpace={false}
                      isLabel={false}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Input
                      value={formValues?.stayDurationUnit ?? ""}
                      name="stayDurationUnit"
                      required
                      type="select"
                      options={durationOptionsTranslated}
                      disabled
                      isLabelNoSpace={false}
                      isLabel={false}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
          <Input
            disabled
            value={Number(formValues?.designationCode) ?? ""}
            name="designationCode"
            required
            options={positionsTranslatedList}
            label={t("position")}
            type="select"
          />
          <Input
            disabled
            value={formValues.summary ? formValues.summary : ""}
            name="summary"
            multiline
            rows={4}
            required
            label={t("importanceOfPosition2")}
          />
        </Box>
        <Box sx={{ mt: 2 }}>
          <FileUploader
            title="ajkEligibilityCheck"
            type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
            societyId={parseInt(branchData?.societyId)}
            branchId={branchId ? Number(branchId) : null}
            icNo={formValues?.identificationNo}
            validTypes={[
              "text/plain",
              "application/rtf",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              "application/msword",
              "application/vnd.oasis.opendocument.text",
              "application/pdf",
            ]}
            disabled={true}
          />
        </Box>

        <Box
          sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}
        >
          <ButtonOutline onClick={handleSenaraiAjk}>{t("back")}</ButtonOutline>
        </Box>
      </Box>
    </Box>
  );
};

export default BranchPaparanAjkBukanWn;
