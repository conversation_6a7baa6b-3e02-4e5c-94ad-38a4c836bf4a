import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, CircularProgress, useTheme, Button, useMediaQuery, Grid, Divider, Tooltip, IconButton } from '@mui/material';
import { useDispatch } from 'react-redux';
import { setChatbotOpenedRedux } from '@/redux/chatbotReducer';
import { SpeechProvider } from '@/contexts/chatbot/SpeechContext';
import { FullPageChatArea } from '@/components/chatbot/FullPageChatArea';
import { useSpeech } from '@/contexts/chatbot/SpeechContext';
import { useGetIdentity } from '@refinedev/core';
import { IUser } from '@/types';
import { useTranslation } from 'react-i18next';
import { Refresh as RefreshIcon } from '@mui/icons-material';
import { setupSpeechRecognition } from '@/components/chatbot/SpeechUtils';
import { normalizeRosieName } from '@/components/chatbot/SpeechUtils';


export const BetaBadgeTopRight = () => (
  <Box
    sx={{
      position: "absolute",
      top: 0,
      right: 0,
      width: 100,
      height: 100,
      zIndex: 20,
      pointerEvents: "none",
    }}
  >
    <Box
      sx={{
        position: "absolute",
        width: 160,
        transform: "rotate(45deg)",
        top: 10,
        // right: -40,
        backgroundColor: "red",
        textAlign: "center",
        py: 0.4,
        boxShadow: 2,
      }}
    >
      <Typography
        variant="caption"
        sx={{
          color: "white",
          fontWeight: "bold",
          fontSize: 16,
          // letterSpacing: 1,
        }}
      >
        BETA
      </Typography>
    </Box>
  </Box>
);

/**
 * Combined Chatbot Full Page
 * This page combines both the audience chat view and the VIP speech input functionality
 * in a single page layout with direct communication (no cross-page communication).
 */
const ChatbotFullPage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { i18n } = useTranslation();
  const { data: user } = useGetIdentity<IUser>();
  const fullPageChatAreaRef = useRef<any>(null);
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [refreshKey, setRefreshKey] = useState<number>(0);

  // Function to force refresh the chat area
  const handleRefresh = () => {
    console.log('ChatbotFullPage: Forcing refresh of chat area');
    setRefreshKey(prev => prev + 1);
  };

  // Get locale
  const locale = ["bm", "my"].includes(i18n.language) ? "ms" : i18n.language;

  // Ensure the chatbot is opened when this page is visited
  // and hide the floating chatbot widget
  useEffect(() => {
    // Set chatbot state to closed to prevent the floating widget from showing
    dispatch(setChatbotOpenedRedux(false));
    console.log('ChatbotFullPage: Page mounted with direct communication architecture');

    // Return cleanup function
    return () => {
      console.log('ChatbotFullPage: Component unmounted');
    };
  }, [dispatch]);

  return (
    <SpeechProvider>
      <Box
        sx={{
          height: '100vh',
          width: '100vw',
          overflow: 'hidden',
          bgcolor: theme.palette.background.default,
          display: 'flex',
        }}
      >
        {isMobile ? (
          // Mobile layout - stacked vertically
          <MobileLayout
            fullPageChatAreaRef={fullPageChatAreaRef}
            refreshKey={refreshKey}
            onRefresh={handleRefresh}
          />
        ) : (
          // Desktop layout - side by side
          <DesktopLayout
            fullPageChatAreaRef={fullPageChatAreaRef}
            refreshKey={refreshKey}
            onRefresh={handleRefresh}
          />
        )}
      </Box>
    </SpeechProvider>
  );
};

/**
 * Desktop layout with side-by-side panels
 */
const DesktopLayout: React.FC<{
  fullPageChatAreaRef: React.RefObject<any>,
  refreshKey?: number,
  onRefresh?: () => void
}> = ({ fullPageChatAreaRef, refreshKey = 0, onRefresh }) => {
  return (
    <Grid container sx={{ height: '100%', width: '100%', flexWrap: 'nowrap', position: 'relative' }}>
      {/* Refresh button */}
      {/* {onRefresh && (
        <Tooltip title="Refresh chat history">
          <IconButton
            onClick={onRefresh}
            sx={{
              position: 'absolute',
              top: 16,
              left: 16,
              zIndex: 1000,
              bgcolor: 'rgba(255, 255, 255, 0.8)',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.9)',
              },
            }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      )} */}

      {/* Left side - Chat Area */}
      <Grid item xs={8} sx={{ height: '100%', maxWidth: '75%', flexGrow: 1 }}>
        <BetaBadgeTopRight/>
        {/* <Box sx={{ height: '100%', p: 2 }}> */}
          <FullPageChatArea
            ref={fullPageChatAreaRef}
            key={`chat-area-${refreshKey}`}
            enableCrossPageCommunication={false}
          />
        {/* </Box> */}
      </Grid>

      {/* Divider */}
      <Divider orientation="vertical" flexItem sx={{ bgcolor: 'grey.700' }} />

      {/* Right side - Speech Input */}
      <Grid item xs={4} sx={{ height: '100%', bgcolor: 'black', maxWidth: '25%', flexGrow: 0 }}>
        <SpeechInputPanel fullPageChatAreaRef={fullPageChatAreaRef} />
      </Grid>
    </Grid>
  );
};

/**
 * Mobile layout with stacked panels
 */
const MobileLayout: React.FC<{
  fullPageChatAreaRef: React.RefObject<any>,
  refreshKey?: number,
  onRefresh?: () => void
}> = ({ fullPageChatAreaRef, refreshKey = 0, onRefresh }) => {
  const [activeTab, setActiveTab] = useState<'chat' | 'speech'>('chat');
  const isLandscape = useMediaQuery('(orientation: landscape)');

  // For landscape mode on mobile, use a side-by-side layout similar to desktop
  if (isLandscape) {
    return (
      <Grid container sx={{ height: '100%', width: '100%', flexWrap: 'nowrap', position: 'relative' }}>
        {/* Refresh button */}
        {onRefresh && (
          <Tooltip title="Refresh chat history">
            <IconButton
              onClick={onRefresh}
              sx={{
                position: 'absolute',
                top: 8,
                left: 8,
                zIndex: 1000,
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.9)',
                },
                width: 32,
                height: 32,
              }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}

        {/* Left side - Chat Area */}
        <Grid item xs={7} sx={{ height: '100%', maxWidth: '70%', flexGrow: 1 }}>
          <Box sx={{ height: '100%', p: 1 }}>
            <FullPageChatArea
              ref={fullPageChatAreaRef}
              key={`chat-area-${refreshKey}`}
              enableCrossPageCommunication={false}
            />
          </Box>
        </Grid>

        {/* Divider */}
        <Divider orientation="vertical" flexItem sx={{ bgcolor: 'grey.700' }} />

        {/* Right side - Speech Input */}
        <Grid item xs={5} sx={{ height: '100%', bgcolor: 'black', maxWidth: '30%', flexGrow: 0 }}>
          <SpeechInputPanel fullPageChatAreaRef={fullPageChatAreaRef} />
        </Grid>
      </Grid>
    );
  }

  // Portrait mode - use tabs
  return (
    
    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', height: '100%', position: 'relative' }}>
      <BetaBadgeTopRight/>
      {/* Tab buttons */}
      <Box sx={{ display: 'flex', borderBottom: 1, borderColor: 'divider' }}>
        <Button
          variant={activeTab === 'chat' ? 'contained' : 'text'}
          onClick={() => setActiveTab('chat')}
          sx={{ flex: 1, borderRadius: 0, py: 1.5 }}
        >
          Chat
        </Button>
        <Button
          variant={activeTab === 'speech' ? 'contained' : 'text'}
          onClick={() => setActiveTab('speech')}
          sx={{ flex: 1, borderRadius: 0, py: 1.5 }}
        >
          Speech Input
        </Button>
      </Box>

      {/* Refresh button - only show in chat tab */}
      {onRefresh && activeTab === 'chat' && (
        <Tooltip title="Refresh chat history">
          <IconButton
            onClick={onRefresh}
            sx={{
              position: 'absolute',
              top: 60, // Below the tabs
              right: 8,
              zIndex: 1000,
              bgcolor: 'rgba(255, 255, 255, 0.8)',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.9)',
              },
              width: 32,
              height: 32,
            }}
          >
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}

      {/* Content area */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        {activeTab === 'chat' ? (
          <Box sx={{ height: '100%', p: 1 }}>
            <FullPageChatArea
              ref={fullPageChatAreaRef}
              key={`chat-area-${refreshKey}`}
              enableCrossPageCommunication={false}
            />
          </Box>
        ) : (
          <Box sx={{ height: '100%', bgcolor: 'black' }}>
            <SpeechInputPanel fullPageChatAreaRef={fullPageChatAreaRef} />
          </Box>
        )}
      </Box>
    </Box>
  );
};

/**
 * Speech Input Panel
 * Uses direct communication with the FullPageChatArea component
 */
const SpeechInputPanel: React.FC<{ fullPageChatAreaRef: React.RefObject<any> }> = ({ fullPageChatAreaRef }) => {
  const theme = useTheme();
  const { i18n } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const locale = ["bm", "my"].includes(i18n.language) ? "ms" : i18n.language;

  const [isListening, setIsListening] = useState(false);
  const [recognizedText, setRecognizedText] = useState('');
  const [messageSent, setMessageSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [liveTranscript, setLiveTranscript] = useState('');

  // Store all final speech results
  const allResultsRef = useRef<string[]>([]);
  const recognitionRef = useRef<any>(null);

  // Start recording
  const startRecording = () => {
    setIsListening(true);
    setRecognizedText('');
    setMessageSent(false);
    setError(null);
    setLiveTranscript('');
    allResultsRef.current = [];
    recognitionRef.current = setupSpeechRecognition({
      locale: locale === 'ms' ? 'ms-MY' : 'en-US',
      onInterim: (interim) => setLiveTranscript(interim),
      onFinal: (final) => {
        allResultsRef.current.push(normalizeRosieName(final));
        setLiveTranscript('');
      },
      onError: (e) => {
        setError('Speech recognition error');
        setIsListening(false);
      },
      onEnd: () => setIsListening(false),
    });
    recognitionRef.current.start();
  };

  // Stop recording
  const stopRecording = () => {
    setIsListening(false);
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    // Wait 500ms to ensure all results are received
    setTimeout(() => {
      const fullText = allResultsRef.current.join(' ');
      setLiveTranscript('');
      if (fullText) {
        setRecognizedText(fullText);
        setError(null);
        if (fullPageChatAreaRef.current?.submitSpeechInput) {
          const messageId = `direct_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`;
          fullPageChatAreaRef.current.submitSpeechInput(fullText, messageId);
        }
        setMessageSent(true);
        setTimeout(() => {
          setMessageSent(false);
        }, 3000);
      }
      allResultsRef.current = [];
    }, 500);
  };

  const handleMicrophoneClick = () => {
    if (isListening) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const handleAskAgain = () => {
    setRecognizedText('');
    setMessageSent(false);
    setError(null);
    setLiveTranscript('');
    allResultsRef.current = [];
  };

  useEffect(() => {
    return () => {
      allResultsRef.current = [];
      if (recognitionRef.current) recognitionRef.current.stop();
    };
  }, []);

  return (
    <Box
      sx={{
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        bgcolor: 'black',
        color: 'white',
        position: 'relative',
        p: 2,
      }}
    >
      <Typography
        variant={isMobile ? 'h3' : 'h2'}
        component="h1"
        sx={{
          mb: 4,
          fontWeight: 'bold',
          textAlign: 'center',
          px: 2,
          fontSize: isMobile ? '1.75rem' : undefined,
        }}
      >
        {locale === 'ms'
          ? isListening
            ? 'Klik untuk berhenti'
            : 'Klik untuk berbual'
          : isListening
          ? 'Click to Stop'
          : 'Click to Speak'}
      </Typography>

      {/* Microphone button */}
      <Box
        sx={{
          width: isMobile ? 100 : 120,
          height: isMobile ? 100 : 120,
          borderRadius: '50%',
          bgcolor: isListening ? 'error.main' : 'primary.main',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'scale(1.05)',
            boxShadow: '0 0 20px rgba(255, 255, 255, 0.3)',
          },
          '&:active': {
            transform: 'scale(0.95)',
          },
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            width: '30%',
            height: '30%',
            borderRadius: '50%',
            bgcolor: 'white',
            opacity: isListening ? 0.8 : 0.6,
            transition: 'all 0.3s ease',
          },
        }}
        onClick={handleMicrophoneClick}
      >
        {/* Microphone icon */}
        <Box
          component="svg"
          viewBox="0 0 24 24"
          sx={{
            width: isMobile ? 40 : 48,
            height: isMobile ? 40 : 48,
            fill: 'black',
            zIndex: 1,
          }}
        >
          {/* <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" /> */}
          {/* <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" /> */}
        </Box>
      </Box>

      {/* Status text */}
      <Typography
        variant="h6"
        sx={{
          mt: 4,
          textAlign: 'center',
          minHeight: 48,
          px: 2,
        }}
      >
        {isListening && liveTranscript
          ? `${locale === 'ms' ? 'Sedang berkata:' : 'Speaking:'} "${liveTranscript}"`
          : messageSent
          ? (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <CircularProgress size={24} sx={{ mr: 2, color: 'white' }} />
              {locale === 'ms' ? `Memproses: "${recognizedText}"` : `Processing: "${recognizedText}"`}
            </Box>
          )
          : isListening
          ? locale === 'ms' ? 'Mendengar...' : 'Listening...'
          : recognizedText
          ? `${locale === 'ms' ? 'Anda' : 'You'}: "${recognizedText}"`
          : locale === 'ms'
          ? 
          ""
          // 'Tekan dan tahan butang untuk bercakap'
          : 
          ""
          // 'Press and hold the button to speak'
          }
      </Typography>

      {/* Action button */}
      {(recognizedText || messageSent) && (
        <Box
          sx={{
            mt: 4,
            display: 'flex',
            flexDirection: isMobile ? 'column' : 'row',
            gap: 2,
            width: isMobile ? '100%' : 'auto',
            px: isMobile ? 2 : 0,
          }}
        >
          <Button
            variant="contained"
            color="primary"
            onClick={handleAskAgain}
            fullWidth={isMobile}
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              fontWeight: 'bold',
              textTransform: 'none',
              fontSize: isMobile ? '0.9rem' : '1rem',
            }}
          >
            {locale === 'ms' ? 'Tanya Soalan Lain' : 'Ask Another Question'}
          </Button>
        </Box>
      )}

      {/* Error message if speech recognition fails */}
      {(error) && (
        <Typography
          color="error"
          sx={{
            mt: 2,
            textAlign: 'center',
            px: 2,
          }}
        >
          {error}
        </Typography>
      )}
    </Box>
  );
};

export default ChatbotFullPage;
