import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const TrainingRequiredIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"
         style={{ color, ...sx }}
         {...props}
    >
      <path d="M4.22715 3.00428C4.58763 1.94936 6.04523 1.91741 6.47262 2.90843L6.50879 3.00488L6.99526 4.42752C7.10675 4.75378 7.28691 5.05234 7.52358 5.30306C7.76026 5.55377 8.04796 5.75082 8.36726 5.8809L8.49807 5.92973L9.92071 6.41559C10.9756 6.77607 11.0076 8.23367 10.0172 8.66107L9.92071 8.69724L8.49807 9.18371C8.1717 9.29512 7.87302 9.47524 7.6222 9.71193C7.37137 9.94861 7.17423 10.2363 7.04409 10.5557L6.99526 10.6859L6.5094 12.1092C6.14891 13.1641 4.69131 13.196 4.26452 12.2056L4.22715 12.1092L3.74128 10.6865C3.62987 10.3601 3.44974 10.0615 3.21306 9.81064C2.97638 9.55982 2.68865 9.36268 2.36928 9.23253L2.23907 9.18371L0.816436 8.69784C-0.239089 8.33736 -0.271038 6.87976 0.719986 6.45297L0.816436 6.41559L2.23907 5.92973C2.56533 5.81824 2.86389 5.63808 3.11461 5.4014C3.36533 5.16472 3.56237 4.87703 3.69245 4.55772L3.74128 4.42752L4.22715 3.00428ZM5.36827 3.39369L4.8824 4.81633C4.71265 5.31383 4.43649 5.76836 4.07315 6.14824C3.70982 6.52813 3.26804 6.82425 2.77859 7.01599L2.62789 7.07085L1.20525 7.55672L2.62789 8.04258C3.12539 8.21234 3.57991 8.4885 3.9598 8.85184C4.33969 9.21517 4.63581 9.65695 4.82755 10.1464L4.8824 10.2971L5.36827 11.7197L5.85414 10.2971C6.0239 9.7996 6.30006 9.34508 6.66339 8.96519C7.02673 8.5853 7.4685 8.28918 7.95795 8.09744L8.10866 8.04319L9.53129 7.55672L8.10866 7.07085C7.61115 6.90109 7.15663 6.62493 6.77674 6.2616C6.39686 5.89826 6.10073 5.45649 5.90899 4.96704L5.85474 4.81633L5.36827 3.39369ZM10.1908 0.925781C10.3035 0.925781 10.4141 0.957416 10.5097 1.01709C10.6054 1.07676 10.6825 1.16209 10.7321 1.26336L10.761 1.33389L10.972 1.95237L11.5911 2.16335C11.7041 2.20175 11.8032 2.27284 11.8758 2.3676C11.9483 2.46236 11.9912 2.57653 11.9988 2.69565C12.0064 2.81477 11.9785 2.93347 11.9186 3.0367C11.8586 3.13994 11.7694 3.22306 11.6622 3.27554L11.5911 3.30448L10.9726 3.51546L10.7616 4.13455C10.7232 4.24754 10.652 4.34656 10.5572 4.41907C10.4624 4.49159 10.3482 4.53433 10.2291 4.54188C10.11 4.54944 9.99135 4.52146 9.88815 4.4615C9.78495 4.40154 9.70187 4.3123 9.64944 4.20508L9.62051 4.13455L9.40952 3.51607L8.79044 3.30508C8.67742 3.26669 8.57835 3.1956 8.50577 3.10084C8.4332 3.00608 8.39039 2.8919 8.38277 2.77279C8.37515 2.65367 8.40307 2.53497 8.46298 2.43173C8.52289 2.3285 8.6121 2.24537 8.71931 2.19289L8.79044 2.16396L9.40892 1.95297L9.61991 1.33389C9.66055 1.21478 9.73746 1.11139 9.83983 1.03821C9.94221 0.965018 10.0649 0.925706 10.1908 0.925781Z" fill="#666666"/>
    </svg>

  );
});
