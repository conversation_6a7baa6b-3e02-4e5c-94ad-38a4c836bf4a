import { SearchProvider } from "@/contexts/searchProvider";
import { Box } from "@mui/material";
import SearchPengguna from "./searchInternalUsers";
import ListPengguna from "./listInternalUsers";

export const MainPenggunaJPMList = () => {
  return (
    <SearchProvider>
      <Box sx={{ display: "grid", gap: 2, height: "auto" }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <SearchPengguna />
          </Box>
        </Box>

        <ListPengguna />
      </Box>
    </SearchProvider>
  );
};
