import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IEvent, IEventResponse } from '../types/event';

interface TakwimState {
  data: IEvent[];
  loading: boolean;
  error: string | null;
}

const initialState: TakwimState = {
  data: [],
  loading: false,
  error: null,
};

export const takwimSlice = createSlice({
  name: 'takwim',
  initialState,
  reducers: {
    setTakwimDataRedux: (state, action: PayloadAction<IEvent[]>) => {
      state.data = action.payload;
    },
    setTakwimLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setTakwimError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setTakwimDataRedux, setTakwimLoading, setTakwimError } = takwimSlice.actions;

// Export the reducer
export default takwimSlice.reducer;





