import React, {useEffect, useState} from "react";
import {Typography, Box, Checkbox, Radio} from "@mui/material";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import TrainingFragment from "@/pages/training/trainingFragment";
import {TrainingEnums} from "@/helpers";
import {useTranslation} from "react-i18next";
import {ButtonPrimary} from "@/components";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import {DurationIcon} from "@/components/icons/duration";
import Countdown from "@/pages/training/trainingDetails/countdown";
import {useNavigate} from "react-router-dom";

interface TrainingFeedbackFragmentProps {
  courseId: number,
  enrollId: number
  //handleNext: () => void,
}

const TrainingFeedback: React.FC<TrainingFeedbackFragmentProps> = ({courseId, enrollId}) => {

  const {t, i18n} = useTranslation();
  const [step, setStep] = useState(1);
  const [answered, setAnswered] = useState(false);
  const [questions, setQuestions] = useState<any[]>([]);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [quizAttemptId, setQuizAttemptId] = useState<number>(0);
  const [timeLeft, setTimeLeft] = useState(-1);

  const navigate = useNavigate();

  const demoData = [
    {
      questionText: "Bagaimanakah Latihan ini dapat membantu anda ?",
      options: [
        {
          optionText: "Buruk",
        },
        {
          optionText: "Biasa",
        },
        {
          optionText: "Baik",
        },
        {
          optionText: "Cemerlang",
        },
        {
          optionText: "Mantap",
        }
      ]
    },
    {
      questionText: "Bagaimanakah Latihan ini dapat membantu anda ?",
      options: [
        {
          optionText: "Buruk",
        },
        {
          optionText: "Biasa",
        },
        {
          optionText: "Baik",
        },
        {
          optionText: "Cemerlang",
        },
        {
          optionText: "Mantap",
        }
      ]
    },
    {
      questionText: "Bagaimanakah Latihan ini dapat membantu anda ?",
      options: [
        {
          optionText: "Buruk",
        },
        {
          optionText: "Biasa",
        },
        {
          optionText: "Baik",
        },
        {
          optionText: "Cemerlang",
        },
        {
          optionText: "Mantap",
        }
      ]
    }
  ]

  const next = () => {
    navigate("/latihan/sijil", {state:{courseId:courseId,enrollId:enrollId}});
  }

  useEffect(() => {
    setQuestions(demoData)
  },[]);


  return (
    <>
      <Box
        sx={{
          //flex: 5,
          width: "100%",
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            //height: "100%",
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            px: 5,
            py: 2,
            mb: 1,
          }}
        >
          <Box sx={{display: "flex", justifyContent: "start", gap: 1}}>
            <Typography
              sx={{
                color: "#0CA6A6",
                //pt: 3,
                fontWeight: "500",
                fontSize: 20,
              }}
            >
              {'Maklumbalas'}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            //height: "100%",
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            px: 5,
            py: 2,
            mb: 1,
          }}
        >
          {questions.map((item: any, index: number) => {
              return (
                <Box key={index} sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
                  <Box>
                    <Typography
                      sx={{
                        color: "#666666",
                        pt: 3,
                        fontWeight: "400",
                        fontSize: 14,
                      }}
                    >
                      {`Question ${index + 1}`}
                    </Typography>
                    <Typography
                      sx={{
                        color: "#666666",
                        pt: 3,
                        fontWeight: "400",
                        fontSize: 14,
                      }}
                    >
                      {item.questionText}
                    </Typography>
                    <Box sx={{pl: 1}}>
                      {item.options.map((item2: any, index2: number) => {
                        return (<Typography key={index2}
                                            sx={{fontWeight: "400", fontSize: 14}}
                        >
                          <Radio
                            disabled={disabled}
                            onChange={(e) => {}}
                            sx={{p: 0, ml: 1, mr: 1}}
                          />
                          {`Answer ${item2.optionText}`}
                        </Typography>)
                      })}
                    </Box>
                  </Box>
                </Box>)
          })}
        </Box>
        <Box sx={{display: "flex", mt: 1, justifyContent: "flex-end", gap: 1}}>
          <ButtonPrimary
            variant="outlined"
            sx={{
              borderColor: "#0CA6A6",
              bgcolor: "#0CA6A6",
              "&:hover": {bgcolor: "#0CA6A6", borderColor: "#0CA6A6",},
              color: "#fff",
              fontWeight: "400",
            }}
            onClick={() => next()}
          >
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
    </>
  );
}

export default TrainingFeedback;
