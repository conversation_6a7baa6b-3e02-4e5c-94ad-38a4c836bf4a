import React from "react";
import { useTranslation } from "react-i18next";
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Typography,
  CircularProgress,
  Box,
  TablePagination,
} from "@mui/material";
import CustomPagination from "../pagination/CustomPagination";

type RenderCellParams<T> = {
  row: T;
  rowIndex: number;
};

export interface IColumn<T = Record<string, any>> {
  field: keyof T | "";
  headerName?: string;
  flex?: number;
  renderCell?: ({ row, rowIndex }: RenderCellParams<T>) => React.ReactNode;
  cellClassName?: string;
  idField?: string;
  align?: "left" | "center" | "right";
  headerAlign?: "left" | "center" | "right";
  width?: number;
  background?: string;
}

interface DataTableProps<T extends Record<string, any>> {
  columns: IColumn<T>[];
  rows: T[];
  page: number;
  rowsPerPage: number;
  totalCount: number;
  onPageChange?: (newPage: number) => void;
  onPageSizeChange?: (newPageSize: number) => void;
  pagination?: boolean;
  paginationPosition?: "left" | "center" | "right";
  align?: "left" | "center" | "right";
  sx?: React.CSSProperties;
  idField?: string;
  customNoDataText?: string;
  isLoading?: boolean;
  paginationType?: "standard" | "custom";
  noPagination?: boolean;
  rowHeight?: number;
  labelRowsPerPage?: string;
  clientPaginationMode?: boolean;
}

const tableCellStyle = {
  color: "#666666",
  fontSize: "13px",
};

export const DataTable = <T extends Record<string, any>>({
  columns,
  rows,
  page,
  rowsPerPage,
  totalCount,
  pagination = true,
  onPageChange,
  onPageSizeChange = undefined,
  paginationPosition = "right",
  idField = "id",
  align = "left",
  customNoDataText,
  isLoading = false,
  paginationType = "standard",
  noPagination = false,
  rowHeight,
  labelRowsPerPage: initialLabelRowsPerPage,
  clientPaginationMode = false,
}: DataTableProps<T>) => {
  const { t } = useTranslation();

  const labelRowsPerPage = initialLabelRowsPerPage ?? t("rowsPerPage");
  const justifyContent = {
    left: "flex-start",
    center: "center",
    right: "flex-end",
  }[paginationPosition];

  const paginatedRows = React.useMemo(() => {
    if (clientPaginationMode) {
      const startIndex = (page - 1) * rowsPerPage;
      return rows.slice(startIndex, startIndex + rowsPerPage);
    }
    return rows;
  }, [rows, page, rowsPerPage, clientPaginationMode]);
  const actualTotalCount = clientPaginationMode ? rows.length : totalCount;

  return (
    <>
      <TableContainer sx={{ overflow: "auto" }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((column, index) => (
                <TableCell
                  key={index}
                  sx={{
                    width: column.width || "auto",
                    flex: column.flex || 1,
                    fontWeight: 500,
                    textAlign: column.headerAlign || "center",
                    background: column.background || "#FFF",
                    ...tableCellStyle,
                  }}
                >
                  {column.headerName}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  sx={{
                    textAlign: "center",
                    ...tableCellStyle,
                  }}
                >
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <CircularProgress size={24} />
                    <Typography sx={{ ml: 2 }}>{t("loading")}</Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : rows?.length > 0 ? (
              paginatedRows?.map((row, rowIndex) => (
                <TableRow
                  key={row[idField] || rowIndex}
                  sx={{ height: rowHeight }}
                >
                  {/* Set max row height */}
                  {columns.map((column, colIndex) => (
                    <TableCell
                      key={colIndex}
                      className={column.cellClassName}
                      sx={{
                        width: column.width ? `${column.width}px` : "auto",
                        flex: column.flex || 1,
                        ...tableCellStyle,
                        textAlign: column.align || align || "left",
                        height: rowHeight, // Set max height
                        padding: "8px",
                      }}
                    >
                      <Box
                        sx={{
                          maxHeight: rowHeight,
                          overflowY: "auto", // Enable scrolling inside the cell
                          display: "block",
                        }}
                      >
                        {column.renderCell
                          ? column.renderCell({ row, rowIndex })
                          : (row[column.field] as React.ReactNode)}
                      </Box>
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  sx={{
                    textAlign: "center",
                    ...tableCellStyle,
                  }}
                >
                  <Typography>
                    {customNoDataText ? customNoDataText : t("noData")}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination && rows?.length > 0 && !noPagination && (
        <>
          {paginationType === "standard" && (
            <Box
              sx={{ display: "flex", justifyContent: justifyContent, mt: 2 }}
            >
              <TablePagination
                component="div"
                count={actualTotalCount}
                page={page - 1}
                rowsPerPage={rowsPerPage}
                onPageChange={(_, newPage) => onPageChange?.(newPage + 1)}
                onRowsPerPageChange={(e) =>
                  onPageSizeChange?.(parseInt(e.target.value, 10))
                }
                rowsPerPageOptions={[5, 10, 25, 50]}
                labelRowsPerPage={labelRowsPerPage}
              />
            </Box>
          )}

          {paginationType === "custom" && (
            <CustomPagination
              count={totalCount}
              page={page}
              onPageChange={(newPage) => onPageChange?.(newPage)}
              rowsPerPage={rowsPerPage}
              position={paginationPosition}
            />
          )}
        </>
      )}
    </>
  );
};

DataTable.displayName = "DataTable";

export default DataTable;
