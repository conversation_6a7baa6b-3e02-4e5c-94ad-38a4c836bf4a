import Stack from "@mui/material/Stack";
import { useTranslation } from "react-i18next";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import {
  Box,
  Grid,
  TextField,
  Typography,
  Fade,
  useTheme,
  useMediaQuery,
  Dialog,
  Theme,
  CircularProgress,
} from "@mui/material";
import CustomPopover from "@/components/popover";
import { useSelector } from "react-redux";
import { ConstitutionType, MeetingTypeOption } from "@/helpers/enums";
import { Controller, useFormContext } from "react-hook-form";
import ViewMeetingDetailsModal from "./ViewMeetingDetailsModal";
import { DisabledTextField, SelectFieldController } from "@/components";
import { useQuery } from "@/helpers";
import Input from "@/components/input/Input";
import MessageDialog from "@/components/dialog/message";
import { Meeting } from "@/pages/pertubuhan/pernyata-tahunan/interface";
import dayjs from "dayjs";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export interface PindaanPerlembagaan {
  id: number;
  fasal: number;
  namaFasal: string;
  applicationStatusCode: 1 | 2;
}

export const MeetingPindaanPerlembagaan = ({
  handleSaveMeeting,
  isViewMode,
}: {
  amendmentId: number | string;
  handleSaveMeeting: () => void;
  meetingList: any;
  isViewMode: boolean;
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [filteredMeetings, setFilteredMeeting] = useState<Meeting[]>();
  // ========
  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const [savedMeetingDate, setSavedMeetingDate] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [viewMeetingDetailData, setViewMeetingDetailData] = useState<any>("");
  //GET ID PARAMS
  const { id } = useParams();
  const encodedId = id;
  const { control, setValue, getValues, watch, resetField, reset } =
    useFormContext();

  const meetingIdExist = getValues("meetingId");
  const meetingDateExist = getValues("meetingDate");
  const handlerOncloseMeetingDetail = () => {
    setDialogSaveOpen(false);
  };

  // ===========================

  const { isLoading: isAllMeetingDataLoading } = useQuery({
    url: `society/meeting/findBySocietyId/${encodedId}`,
    onSuccess: (data) => {
      const meetings = data?.data?.data || [];
      setMeetings(meetings);
      const availableList = meetings.map((item: any) => {
        return item.meetingDate;
      });
      setAvailableDateList(availableList);
    },
  });

  function setMeetingList(meetingList: Meeting[]) {
    if (isAllMeetingDataLoading) {
      return;
    }
    setFilteredMeeting(meetingList);
    if (meetingList && meetingList.length > 0) {
      setIsDialogOpen(false);
    } else {
      setIsDialogOpen(true);
    }
  }

  function goMeetingPage() {
    navigate(`../../../mesyuarat`);
  }

  function resetChanges() {
    setFilteredMeeting([]);
    reset();
    setValue("meetingId", "");
    setViewMeetingDetailData("");
  }

  useEffect(() => {
    if (meetingDateExist) {
      const filteredMeeting = meetings.filter(
        (meeting) =>
          meeting.meetingDate.toString() ===
          dayjs(meetingDateExist).format("YYYY-MM-DD")
      );
      setMeetingList(filteredMeeting);
    }

    if (meetingIdExist) {
      const selectedMeeting = meetings.find(
        (meeting) => meeting.id === meetingIdExist
      );
      setViewMeetingDetailData(selectedMeeting);
    }
  }, [meetingDateExist]);

  return (
    <>
      <Fade in={true} timeout={500}>
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatMesyuaratPindaanPerlembagaan")}
          </Typography>

          {isAllMeetingDataLoading ? (
            <Grid container spacing={2} sx={{ mt: 0 }}>
              <Grid item xs={12} sm={12}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    minHeight: "100px",
                  }}
                >
                  <CircularProgress size={24} />
                </Box>
              </Grid>
            </Grid>
          ) : (
            <>
              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} sm={12}>
                  <Input
                    isStandardSize
                    required
                    label={t("meetingDate")}
                    type="date"
                    availableDate={availableDateList}
                    value={
                      meetingDateExist ? meetingDateExist : savedMeetingDate
                    }
                    onChange={(e) => {
                      setSavedMeetingDate(e.target.value);
                      setValue("meetingDate", e.target.value);
                      resetChanges();
                      const filteredMeeting = meetings.filter(
                        (meeting) =>
                          meeting.meetingDate.toString() ===
                          dayjs(e.target.value).format("YYYY-MM-DD")
                      );
                      setMeetingList(filteredMeeting);
                    }}
                  />
                </Grid>

                {filteredMeetings && filteredMeetings.length > 0 ? (
                  <>
                    <Grid item xs={12} sm={3}>
                      <Typography sx={labelStyle}>
                        {t("meetingList")}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={9}>
                      <SelectFieldController
                        control={control}
                        name="meetingId"
                        onChange={(e) => {
                          const selectedMeeting = meetings.find(
                            (meeting) => meeting.id === e.target.value
                          );
                          setValue("meetingType", selectedMeeting?.meetingType);
                          setViewMeetingDetailData(selectedMeeting);
                        }}
                        options={filteredMeetings.map((meeting) => ({
                          value: meeting.id,
                          label: `${
                            MeetingTypeOption.find(
                              (option) =>
                                Number(option.value) ===
                                Number(meeting.meetingType)
                            )?.label ?? "-"
                          } (${dayjs(meeting.meetingDate).format(
                            "DD-MM-YYYY"
                          )})`,
                        }))}
                        required
                        disabled={isViewMode}
                      />
                    </Grid>
                  </>
                ) : null}

                {meetingIdExist ? (
                  <>
                    <Grid item xs={12} sm={3}>
                      <Typography sx={labelStyle}>
                        {t("maklumatMesyuarat")}{" "}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={9}>
                      <ButtonOutline onClick={() => setDialogSaveOpen(true)}>
                        {t("ViewMeetings")}
                      </ButtonOutline>
                    </Grid>
                  </>
                ) : null}

                {viewMeetingDetailData?.meetingMemberAttendances &&
                viewMeetingDetailData?.meetingMemberAttendances.length > 0 ? (
                  <>
                    <Grid item xs={12} sm={3}>
                      <Typography sx={labelStyle}>
                        {t("bilanganPemegangJawatan")}{" "}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={9} container spacing={2}>
                      {viewMeetingDetailData?.meetingMemberAttendances?.map(
                        (member: any, index: any) => (
                          <Grid container item xs={12} spacing={2} key={index}>
                            <Grid item xs={6}>
                              <DisabledTextField value={member.name} />
                            </Grid>
                            <Grid item xs={6}>
                              <DisabledTextField value={member.position} />
                            </Grid>
                          </Grid>
                        )
                      )}
                    </Grid>
                  </>
                ) : null}
              </Grid>

              <Stack
                direction="row"
                spacing={2}
                mt={2}
                sx={{ pl: 1 }}
                justifyContent="flex-end"
              >
                <ButtonPrimary
                  sx={{ display: isViewMode ? "none" : "block" }}
                  disabled={meetingIdExist ? false : true}
                  onClick={handleSaveMeeting}
                >
                  {t("save")}
                </ButtonPrimary>
              </Stack>
            </>
          )}
        </Box>
      </Fade>

      <Dialog
        open={dialogSaveOpen}
        onClose={() => setDialogSaveOpen(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#000",
            minWidth: fullScreen ? "100%" : "1000px",
            maxWidth: "1000px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <ViewMeetingDetailsModal
          data={viewMeetingDetailData}
          onClose={handlerOncloseMeetingDetail}
        />
      </Dialog>

      <MessageDialog
        open={isDialogOpen}
        onClickFunction={() => goMeetingPage()}
        buttonText={t("Viewlist")}
        onClose={() => setIsDialogOpen(false)}
        message={t("createMeetingReminder")}
      />
    </>
  );
};

export default MeetingPindaanPerlembagaan;
