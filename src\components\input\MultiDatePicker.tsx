import React, { useState, useEffect } from "react";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { TextField, Chip, Stack } from "@mui/material";
import dayjs, { Dayjs } from "dayjs";
import { LocalizationProvider } from "../provider/Localization";
import { PickersDay, PickersDayProps } from "@mui/x-date-pickers";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { useTakwim } from "@/contexts/takwimProvider";

interface MultiDatePickerProps {
  onMonthYearChange?: (date: Dayjs) => void;
}

export default function MultiDatePicker({
  onMonthYearChange,
}: MultiDatePickerProps) {
  // const [selectedDates, setSelectedDates] = useState<Dayjs[]>([]);
  const [tempDate, setTempDate] = useState<Dayjs | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const { selectedMonthYear, setSelectedFilterDates, selectedFilterDates } =
    useTakwim();
  // console.log(selectedMonthYear?.format("YYYY-MM"), "selectedMonthYear");

  // Reset selected dates when month/year changes
  useEffect(() => {
    // Clear selected dates when month/year changes
    setSelectedFilterDates([]);
    setTempDate(null);
  }, [selectedMonthYear, setSelectedFilterDates]);

  const handleDateChange = (newDate: Dayjs | null) => {
    if (!newDate) return;

    // Format for comparison
    const newDateStr = newDate.format("YYYY-MM-DD");

    // Check if date already exists
    const dateExists = selectedFilterDates.some(
      (date) => date.format("YYYY-MM-DD") === newDateStr
    );

    if (dateExists) {
      // Remove the date if it exists
      setSelectedFilterDates((prev) =>
        prev.filter((date) => date.format("YYYY-MM-DD") !== newDateStr)
      );
    } else {
      // Add the date if it doesn't exist
      setSelectedFilterDates((prev) => [...prev, newDate]);
    }

    // Always update tempDate to the clicked date
    setTempDate(newDate);
  };

  const handleMonthChange = (date: Dayjs) => {
    if (onMonthYearChange) {
      onMonthYearChange(date);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setTempDate(null);
  };

  const handleDelete = (dateToDelete: Dayjs) => {
    setSelectedFilterDates((prev) =>
      prev.filter(
        (date) =>
          date.format("YYYY-MM-DD") !== dateToDelete.format("YYYY-MM-DD")
      )
    );
  };

  // Custom day renderer to highlight selected dates
  const renderDay = (
    day: Dayjs,
    _selectedDays: Array<Dayjs | null>,
    pickersDayProps: PickersDayProps<Dayjs>
  ) => {
    // Check if this day is in our selectedDates array
    const isSelected = selectedFilterDates.some(
      (selectedDate) =>
        selectedDate.format("YYYY-MM-DD") === day.format("YYYY-MM-DD")
    );

    return (
      <PickersDay
        {...pickersDayProps}
        day={day}
        selected={isSelected}
        onClick={() => {
          // Handle day click manually
          handleDateChange(day);
        }}
        sx={{
          ...(isSelected && {
            backgroundColor: "primary.main",
            color: "primary.contrastText",
            "&:hover": {
              backgroundColor: "primary.dark",
            },
          }),
        }}
      />
    );
  };

  return (
    <LocalizationProvider>
      <Stack
        spacing={2}
        sx={{
          borderRight: "1px solid #E5E7EB",
          paddingRight: 2,
          height: "40px",
          // display: "flex",
          // justifyContent: "center"
        }}
      >
        <DatePicker
          label="Tarikh"
          value={tempDate}
          onChange={() => {}} // We handle changes in renderDay
          open={isOpen}
          onOpen={() => setIsOpen(true)}
          onMonthChange={handleMonthChange}
          referenceDate={selectedMonthYear}
          views={["day"]}
          onClose={handleClose}
          slotProps={{
            textField: {
              size: "small",
              fullWidth: true,
              onClick: () => setIsOpen(true),
              inputProps: {
                placeholder: "Tarikh",
                readOnly: true,
              },
            },
            previousIconButton: {
              hidden: true,
            },
            nextIconButton: {
              hidden: true,
            },
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
              backgroundColor: "white",
              width: "120px",
              "& fieldset": {
                borderColor: "transparent",
              },
              "&:hover fieldset": {
                borderColor: "transparent",
              },
              "&.Mui-focused fieldset": {
                borderColor: "transparent",
              },
            },
            "& .MuiInputBase-input": {
              fontSize: "14px",
              padding: "8px 14px",
            },
            "& .MuiFormLabel-root": {
              fontSize: "14px",
              color: "#666666",
              "&.Mui-focused": {
                color: "#666666",
              },
            },
          }}
          closeOnSelect={false}
          slots={{
            day: (props) =>
              renderDay(
                props.day as Dayjs,
                [
                  props.selected
                    ? dayjs(props.selected as unknown as Date)
                    : null,
                ],
                props as PickersDayProps<Dayjs>
              ),
            openPickerIcon: () => <KeyboardArrowDownIcon sx={{ mt: 0.6 }} />,
          }}
        />
        {/* <Stack direction="row" spacing={1} flexWrap="wrap">
          {selectedFilterDates.map((date, index) => (
            <Chip
              key={index}
              label={date.format("DD-MM")}
              onDelete={() => handleDelete(date)}
            />
          ))}
        </Stack> */}
      </Stack>
    </LocalizationProvider>
  );
}
