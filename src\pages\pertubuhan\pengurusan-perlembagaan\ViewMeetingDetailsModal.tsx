import Stack from "@mui/material/Stack";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import MenuItem from "@mui/material/MenuItem";
import { useTranslation } from "react-i18next";
import { ChangeEvent, useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import {
  Box,
  CircularProgress,
  FormControl,
  FormHelperText,
  Grid,
  Select,
  TextField,
  Typography,
  Fade,
  useTheme,
  useMediaQuery,
  Dialog,
  DialogContent,
  DialogTitle,
  Input,
  Theme,
  Button,
  IconButton,
} from "@mui/material";
import CustomPopover from "../../../components/popover";
import { API_URL } from "../../../api";
import { useCustom } from "@refinedev/core";
import useMutation from "../../../helpers/hooks/useMutation";
import { useSelector } from "react-redux";
import {
  setClauseid,
  setIsDisplayConstituition,
  setCurrentAmendmentConstitutionType,
} from "../../../redux/fasalReducer";
import {
  ConstitutionType,
  HideOrDisplayInherit,
  MeetingContent,
  MeetingMethods,
  MeetingTypeOption,
} from "../../../helpers/enums";
import { removeFromStorage } from "../pengurusan-pertubuhan/perlembagaan/removeFasal";
import { useDispatch } from "react-redux";
import {
  capitalizeWords,
  filterEmptyValuesOnObject,
  formatArrayDate,
  getLocalStorage,
  removeLocalStorage,
  setLocalStorage,
} from "../../../helpers/utils";
import ConfirmationDialog from "../../../components/dialog/confirm";
import useQuery from "@/helpers/hooks/useQuery";
import {
  DisabledTextField,
  FormFieldRow,
  Label,
  SelectFieldController,
} from "@/components";
import { MapContainer, Marker, TileLayer } from "react-leaflet";
import { FormMeetingDateTime } from "@/components/form/meeting/DateTime";
import dayjs from "dayjs";
import { FormMeetingAttendees } from "@/components/form/meeting/Attendees";
import { IMeetingOptions } from "@/types";
import { useFormContext } from "react-hook-form";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export interface PindaanPerlembagaan {
  id: number;
  fasal: number;
  namaFasal: string;
  applicationStatusCode: 1 | 2;
}

export const ViewMeetingDetailsModal = ({
  onClose,
}: {
  onClose: () => void;
}) => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [isViewMode, setIsViewMode] = useState(
    fasalItems.IsViewPindaan ? true : false
  );

  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const { control, setValue, getValues, watch } = useFormContext();

  //GET ID PARAMS
  const { id } = useParams();
  const params = new URLSearchParams(window.location.search);
  const isEdit = params.get("isEdit");
  const isView = fasalItems.IsViewPindaan || null;
  const encodedId = id;
  const meetingOptions: IMeetingOptions[] = getLocalStorage("meeting_list", []);

  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };

  const getMeetingName = (meetingId: number): string => {
    const meeting = meetingOptions.find((m) => m.id === meetingId);
    if (meeting) {
      return i18n.language === "my" ? meeting.nameBm : meeting.nameEn;
    }
    return "-";
  };

  const addressList = getLocalStorage("address_list", null);

  const getStateName = (stateCode: any) => {
    const stateName = addressList.filter(
      (i: any) => Number(i.id) === Number(stateCode)
    );
    return stateName[0]?.name;
  };

  const getDistrict = (val = null) => {
    const address = addressList
      .filter((items: any) => Number(items.id) === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));
    return address?.[0]?.label || "-";
  };

  const platformType =
    getValues("platformType")?.length === 1
      ? null
      : MeetingTypeOption?.find((item) =>
          getValues("platformType")
            ? parseInt(getValues("platformType")) === item.value
            : false
        ) ?? null;

  const meetingPlatformOptions = useMemo(
    () =>
      MeetingTypeOption?.filter((item: any) => item.pid === 3).map(
        (item: any) => ({
          value: String(item.id),
          label: item.nameBm,
        })
      ),
    [MeetingTypeOption]
  );

  // ===========================
  return (
    <>
      <Box sx={{ display: "flex", gap: 1 }}>
        <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatMesyuarat")}
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("meetingType")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={
                    getValues("meetingType")
                      ? getMeetingLabel(getValues("meetingType"))
                      : "-"
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("meetingMethod")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={
                    getValues("meetingMethod")
                      ? getMeetingName(parseInt(getValues("meetingMethod")))
                      : "-"
                  }
                />
              </Grid>

              {getValues("meetingMethod") &&
                [MeetingMethods.ATAS_TALIAN, MeetingMethods.HYBRID].includes(
                  getValues("meetingMethod")
                ) &&
                platformType && (
                  <Grid item sm={12}>
                    <SelectFieldController
                      name="platformType"
                      control={control}
                      options={meetingPlatformOptions}
                      placeholder={t("selectPlaceholder")}
                    />
                  </Grid>
                )}

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("tujuanMesyuarat")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={getValues("meetingPurpose")}
                />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("masaDanTarikhMesyuarat")}
            </Typography>
            <FormMeetingDateTime
              viewOnly
              meetingTimeFromAttribute="meetingTime"
              defaultValues={{
                meetingDate: getValues("meetingDate")
                  ? dayjs(getValues("meetingDate"))
                  : null,
                meetingTime: getValues("meetingDate")
                  ? dayjs(
                      `${getValues("meetingDate")} ${
                        getValues("meetingTime") ?? "00:00:00"
                      }`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
                meetingTimeTo: getValues("meetingDate")
                  ? dayjs(
                      `${getValues("meetingDate")} ${
                        getValues("meetingTimeTo") ?? "00:00:00"
                      }`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
              }}
            />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("namaTempatMesyuarat")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={getValues("meetingPlace") ?? "-"}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("locationMap")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Box>
                  <MapContainer
                    /**
                     * @todo change meetingLocation with data from backend
                     */
                    center={[2.745564, 101.707021]}
                    zoom={13}
                    style={{
                      height: "10rem",
                      width: "100%",
                      borderRadius: "0.5rem",
                    }}
                  >
                    <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                    {/**
                     * @todo change meetingLocation with data from backend
                     */}
                    <Marker position={[2.745564, 101.707021]} />
                  </MapContainer>
                </Box>
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("alamatTempatMesyuarat")}
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("meetingPlaceAddress")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={getValues("meetingPlace") ?? "-"}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("negeri")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={
                    getValues("state") ? getStateName(getValues("state")) : "-"
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("bandar")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={getValues("city") ?? "-"}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("daerah")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={
                    getValues("district")
                      ? getDistrict(getValues("district"))
                      : "-"
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("poskod")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={getValues("postcode") ?? "-"}
                />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatMesyuarat")}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("maklumatMesyuarat")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  size="small"
                  value={getValues("meetingContent") ?? "-"}
                />
              </Grid>
            </Grid>
          </Box>

          <Grid container spacing={2}>
            <Grid item sm={12}>
              <FormMeetingAttendees
                defaultValues={{
                  totalAttendees: getValues("totalAttendees") ?? 7,
                }}
                viewOnly
              />
            </Grid>
          </Grid>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("minitMesyuarat")}
            </Typography>
            <Grid container spacing={2}>
              {uploadedFiles && (
                  <Box>
                    {uploadedFiles?.map((file, index) => (
                      <Box
                        key={file.id}
                        sx={{
                          border: "1px solid #E0E0E0",
                          borderRadius: "8px",
                          backgroundColor: "#fff",
                          p: 2,
                          mb: 1,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <Typography
                            sx={{
                              cursor: "pointer",
                              "&:hover": {
                                color: "var(--primary-color)",
                                textDecoration: "underline",
                              },
                            }}
                            // onClick={() => handlePreviewDocument(file.url)}
                          >
                            {file.name}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                                fill="var(--primary-color)"
                              />
                            </svg>
                          </Box>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                )}
            </Grid>
          </Box>

          <Grid container spacing={2}>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <Button
                size="medium"
                variant="contained"
                onClick={onClose}
                sx={{ minWidth: "12rem", textTransform: "capitalize" }}
              >
                {t("back")}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default ViewMeetingDetailsModal;
