import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import styled from "@emotion/styled";

import Grid from "@mui/material/Grid/Grid";
import { JumbotronPublic } from "../../../components/jumbotron/Public";
import { ModelCalendarActivityResponseBodyGet } from "../../../models";
import { CardCalendarSidebar } from "../../../components/card/calendar/Sidebar";
import { CardCalendarActivityDetails } from "../../../components/card/calendar/ActivityDetails";
import Button from "@mui/material/Button/Button";

import './activity-details.css';
import { CalendarEvent } from "calendar-link";

const Card = styled.div`
  box-shadow: 0 0.75rem 0.75rem 0 #EAE8E866;
  border-radius: 1.25rem;
  padding: 0.5rem 1rem 2.25rem 1rem;
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;
`

const CalendarPublicActivityDetailsPage = <
  DataType extends ModelCalendarActivityResponseBodyGet = ModelCalendarActivityResponseBodyGet
>() => {
  const { t } = useTranslation()
  const { activityId } = useParams<Record<string, string>>()

  const [data, setData] = useState<null | DataType>(null)
  const [loading, setLoading] = useState(true)

  const handleButtonAddToGoogleCalendar = async () => {
    const { google } = await import('calendar-link')
    const initialData = data as DataType
    const event: CalendarEvent = {
      title: initialData.title,
      description: `
      ${initialData.description}
      `,
      start: initialData.date_start,
      end: initialData.date_end,
      location: initialData.venue
    }
    const googleCalendarURL = google(event)
    window.open(googleCalendarURL, '_blank', 'noreferer,noopener')
  }

  useEffect(() => {
    setTimeout(() => {
      setData({
        title: 'Hari Terbuka Jabatan Pendaftaran Pertubuhan Malaysia',
        datetime_start: '2025-01-16T20:41:00.000Z',
        datetime_end: '2025-01-16T22:41:00.000Z',
        venue: 'Putrajaya International Convention Center (PICC)',
        venue_description: 'Precinct 5, 62000 Putrajaya. Wilayah Persekutuan Putrajaya. MALAYSIA',
        partners: [
          { name: 'Kementerian Komunikasi dan Propaganda' }
        ],
        description: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Odio consectetur iste hic beatae fuga distinctio facilis, eveniet deserunt amet consequatur, esse vel voluptatibus ut eligendi voluptates atque eum reprehenderit dicta.",
        objectives: [
          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus sed ipsa iste ratione dicta doloremque incidunt, adipisci praesentium libero recusandae, dolor reprehenderit consequuntur officiis voluptatem deleniti debitis totam voluptatum veniam.',
        ]
      } as unknown as DataType)
      setLoading(false)
    }, 3000)
  }, [])

  return (
    <>
      <JumbotronPublic
        breadcrumbs={[
          {
            url: "/takwim",
            label: t('calendar'),
            navigationHighlighted: () => true
          },
          {
            url: "/takwim/aktiviti",
            label: t('calendarActivityLists'),
            navigationHighlighted: () => true
          },
          {
            url: `/takwim/aktiviti/${activityId}`,
            ...(data?.title ? { label: data.title } : {})
          }
        ]}
        title={t('calendarActivityDetails')}
      />
      <div className="content">
        <Grid container spacing={2}>
          <Grid item md={3} sm={12}>
            <CardCalendarSidebar />
          </Grid>
          <Grid item sm={12} md={9} sx={{ width: "100%", rowGap: "0.5rem" }}>
            <Card>
              <CardCalendarActivityDetails data={data} loading={loading} />
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button onClick={handleButtonAddToGoogleCalendar} disabled={loading} variant="contained" size="large" style={{ textTransform: "capitalize"}}>
                  {t('calendarActionAddToGoogleCalendar')}
                </Button>
              </div>
            </Card>
          </Grid>
        </Grid>
      </div>
    </>
  );
}

export default CalendarPublicActivityDetailsPage
