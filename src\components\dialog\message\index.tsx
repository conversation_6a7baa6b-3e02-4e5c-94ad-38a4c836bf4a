import {
  Dialog,
  DialogTitle,
  DialogContent,
  Typography,
  Box,
  IconButton,
  DialogActions,
  Button,
} from "@mui/material";
import { t } from "i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";

type ConfirmationDialogProps = {
  open: boolean;
  onClose: () => void;
  onClickFunction?: () => void;
  title?: string;
  fullScreen?: boolean;
  maxWidth?: "sm" | "md" | "lg" | "xl";
  message?: string;
  buttonText?: string;
  size?: "SMALL" | "LARGE";
};

const MessageDialog: React.FC<ConfirmationDialogProps> = ({
  open,
  onClose,
  onClickFunction,
  title,
  fullScreen = false,
  maxWidth = "md",
  message,
  buttonText,
  size,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      sx={{
        "& .MuiPaper-root": {
          width: size === "SMALL" ? "337px" : "400px",
          height: onClickFunction ? "180px" : "150px",
        },
      }}
    >
      <DialogContent
        sx={{
          padding: "30px 20px 10px",
          display: "flex",
          justifyContent: "center",
          justifyItems: "center",
        }}
      >
        <Typography className="label" sx={{ textAlign: "center" }}>
          {message}
        </Typography>
      </DialogContent>
      <DialogActions
        sx={{
          p: 3,
          display: "flex",
          flexDirection: "column",
          alignContent: "center",
        }}
      >
        <Box sx={{ display: "flex", alignContent: "center", gap: 1 }}>
          {onClickFunction ? (
            <>
              <ButtonPrimary
                onClick={onClickFunction}
                sx={{
                  fontWeight: "normal",
                  borderRadius: "5px",
                  height: "50px",
                  lineHeight: "18px",
                }}
              >
                {buttonText ? buttonText : t("ok")}
              </ButtonPrimary>
              <ButtonOutline
                sx={{
                  fontWeight: "normal",
                  borderRadius: "5px",
                  height: "50px",
                  lineHeight: "18px",
                }}
                onClick={onClose}
              >
                {t("back")}
              </ButtonOutline>
            </>
          ) : (
            <Button>
              <Typography
                onClick={onClose}
                sx={{
                  fontWeight: "normal",
                  color: "#666666",
                  textDecoration: "underline",
                  textUnderlineOffset: "2px",
                  textTransform: "none",
                  fontSize: "14px",
                }}
              >
                {t("back")}
              </Typography>
            </Button>
          )}
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default MessageDialog;
