import {
  Box,
  Divider,
  Grid,
  IconButton,
  InputBase,
  Menu,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import TuneIcon from "@mui/icons-material/Tune";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { ApplicationStatusList, HideOrDisplayInherit } from "@/helpers/enums";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";
import ConfirmationDialog from "@/components/dialog/confirm";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";

const MaklumatPemegangAmanah: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleDaftarPemegangAmanah = () => {
    navigate("create");
  };

  const { trusteeList, fetchTrusteeList } = usejawatankuasaContext();

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  useEffect(() => {
    fetchTrusteeList();
    setShouldFetch(false);
  }, [shouldFetch]);

  const handleEditTrustee = (trusteeId: number) => {
    navigate("create", {
      state: {
        trusteeId: trusteeId,
      },
    });
  };

  const handleViewTrustee = (trusteeId: number) => {
    navigate("create", {
      state: {
        trusteeId: trusteeId,
        view: true,
      },
    });
  };

  const { mutate: deleteTrustee, isLoading: isUpdateAJK } = useCustomMutation();
  const [trusteeId, setTrusteeId] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);

  const handleConfirmDeleteTrustee = (id: number) => {
    setTrusteeId(id);
    setOpenConfirm(true);
  };

  const handleDeleteTrustee = () => {
    deleteTrustee(
      {
        url: `${API_URL}/society/trustee/${trusteeId}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          setShouldFetch(true);
          setOpenConfirm(false);
        },
      }
    );
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const isManager = useSelector(getUserPermission);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          textAlign: "center",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "var(--primary-color)",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {t("bilanganPemegangAmanahTerkini")}
        </Typography>
        <Typography
          sx={{
            color: "#666666",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {trusteeList.length} Orang
        </Typography>
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("trusteeList")}
        </Typography>
        <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
          {isManager || isAliranTugasAccess ? (
            <ButtonOutline onClick={handleDaftarPemegangAmanah}>
              {t("registerTrustee")}
            </ButtonOutline>
          ) : (
            <></>
          )}
        </Box>
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: "none",
            border: "1px solid #e0e0e0",
            backgroundColor: "white",
            borderRadius: 2.5 * 1.5,
            p: 1,
            mb: 3,
          }}
        >
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("name")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("idNumber")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("email")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("tarikhLantik")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("status")}
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                ></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {trusteeList.map((row, index) => (
                <TableRow key={row.id}>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {row.name}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {row.identificationNo}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {row.email}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {row.appointmentDate
                      ? dayjs(
                          row.appointmentDate
                            .map((part, index) =>
                              index > 0
                                ? part.toString().padStart(2, "0")
                                : part
                            ) // Pad month and day to 2 digits
                            .join("-")
                        ).format("DD-MM-YYYY")
                      : "-"}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {t(
                      ApplicationStatusList.find(
                        (item) => item.id === row.status
                      )?.value || "-"
                    )}
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                  >
                    {row.status === 11 ? (
                      isManager || isAliranTugasAccess ? (
                        <>
                          <IconButton onClick={() => handleEditTrustee(row.id)}>
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            onClick={() => handleConfirmDeleteTrustee(row.id)}
                          >
                            <TrashIcon />
                          </IconButton>
                        </>
                      ) : (
                        <></>
                      )
                    ) : (
                      <IconButton onClick={() => handleViewTrustee(row.id)}>
                        <EyeIcon />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDelete")}
        message={`${t("deleteConfirmationMessage")}`}
        onConfirm={handleDeleteTrustee}
        onCancel={() => setOpenConfirm(false)}
      />
    </Box>
  );
};

export default MaklumatPemegangAmanah;
