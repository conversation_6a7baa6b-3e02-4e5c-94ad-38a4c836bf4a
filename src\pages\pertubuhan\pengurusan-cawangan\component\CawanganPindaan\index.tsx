import { useMemo, useC<PERSON>back, useState, useEffect } from "react";
import { useForm, FieldValues } from "react-hook-form";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ApplicationStatusList, useMutation, useQuery } from "@/helpers";

import {
  Box,
  Typography,
  Select,
  MenuItem,
  SelectChangeEvent,
  TextField,
  CircularProgress,
  debounce,
  IconButton,
} from "@mui/material";
import StatusChip from "@/components/status-chip";
import DataTable, { IColumn } from "@/components/datatable";
import { ButtonPrimary } from "@/components/button";

import { IApiResponse, ISocietyBranchList } from "@/types";

import { EditIcon, FilterIcon, EyeIcon } from "@/components/icons";
import { Search } from "@mui/icons-material";
import SelectFieldController from "@/components/input/select/SelectFieldController";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { useDispatch } from "react-redux";
import {
  resetBranchAmendInfo,
  setBranchAmendInfo,
  setIsView,
} from "@/redux/branchAmendReducer";
import { DialogConfirmation } from "@/components";
import FilterBar from "@/components/filter";

export const CawanganPindaanPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const [list, setList] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showConfirmDeleteDialog, setShowConfirmDeleteDialog] = useState(false);
  const [branchDetail, setBranchDetail] = useState<any>(null);
  const [deleteBranchId, setDeleteBranchId] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const dispatch = useDispatch();

  const filterOptions = {
    applicationStatusCode: [
      { value: 2, label: "Menunggu Keputusan" },
      { value: 1, label: "Belum dihantar" },
      { value: 3, label: "Lulus" },
      { value: 4, label: "Tolak" },
      { value: 5, label: "MENUNGGU_BAYARAN_KAUNTER" },
      { value: 6, label: "MENUNGGU_BAYARAN_ONLINE" },
    ],
  };

  const { fetch: updateDecision, isLoading: isLoadingDecisionUpdate } =
    useMutation({
      url: `society/external/branchAmendment/${deleteBranchId}`,
      method: "delete",
      onSuccess: (data) => {
        setShowConfirmDeleteDialog(false);
        fetchDataResultBranchAmendment();
      },
    });

  const deleteAmendmentHandle = () => {
    updateDecision();
  };

  const handleSearchChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { value } = e.target;
    setSearchTerm(value);
  };

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      setValue("page", 1);
      setSearchQuery(value);
    }
  };
  const onSeachHandler = () => {
    setValue("page", 1);
    setSearchQuery(searchTerm);
  };
  const { setValue, watch, control } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      filter: "",
      selectedBranch: "",
      applicationStatusCode: undefined,
    },
  });

  const columns: IColumn[] = [
    {
      field: "branchName",
      align: "center",
      headerName: t("branchNameDetails"),
      flex: 3,
      renderCell: (params: any) => {
        const row = params?.row;
        return row.currentBranchName ? row.currentBranchName : "-";
      },
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      flex: 2,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const status = ApplicationStatusList.find(
          (item) => item.id === Number(row.applicationStatusCode)
        );
        return status ? t(status.value) : t("-");
      },
      cellClassName: "custom-cell",
    },
    {
      field: "action",
      flex: 2,
      align: "center",
      renderCell: ({ row }: any) => {
        const status = Number(row?.applicationStatusCode);
        const waitingPayment = status === 5 || status === 6;
        const ViewEye = status === 2 || status === 3 || status === 4;
        return (
          <Box>
            {ViewEye || waitingPayment ? (
              <>

                {waitingPayment ? (
                  <IconButton
                    onClick={() => {
                      dispatch(setIsView(false));
                      dispatch(setBranchAmendInfo(row));
                      navigate(
                        `/pertubuhan/society/${id}/senarai/cawangan/branch-Info/form/${row?.id}`
                      );
                    }}
                    sx={{ minHeight: "3rem", minWidth: "3rem" }}
                  >
                    <EditIcon color="var(--primary-color)" />
                  </IconButton>
                ) : null}

                {waitingPayment ? null : (
                  <IconButton
                    onClick={() => {
                      dispatch(setIsView(true));
                      dispatch(setBranchAmendInfo(row));
                      navigate(
                        `/pertubuhan/society/${id}/senarai/cawangan/branch-Info/form/${row?.id}`
                      );
                    }}
                    sx={{ minHeight: "3rem", minWidth: "3rem" }}
                  >
                    <EyeIcon color="#147C7C" />
                  </IconButton>
                )}

                {waitingPayment ? (
                  <IconButton
                    onClick={() => {
                      setDeleteBranchId(row?.id);
                      setShowConfirmDeleteDialog(true);
                    }}
                    sx={{ minHeight: "3rem", minWidth: "3rem" }}
                  >
                    <img src="/tableDeleteIcon.svg" alt="" />
                  </IconButton>
                ) : null}
              </>
            ) : (
              <>
                <IconButton
                  onClick={() => {
                    dispatch(setIsView(false));
                    dispatch(setBranchAmendInfo(row));
                    navigate(
                      `/pertubuhan/society/${id}/senarai/cawangan/branch-Info/form/${row?.id}`
                    );
                  }}
                  sx={{ minHeight: "3rem", minWidth: "3rem" }}
                >
                  <EditIcon color="var(--primary-color)" />
                </IconButton>
                <IconButton
                  onClick={() => {
                    setDeleteBranchId(row?.id);
                    setShowConfirmDeleteDialog(true);
                  }}
                  sx={{ minHeight: "3rem", minWidth: "3rem" }}
                >
                  <img src="/tableDeleteIcon.svg" alt="" />
                </IconButton>
              </>
            )}
          </Box>
        );
      },
    },
  ];

  const { page, pageSize, filter, selectedBranch, applicationStatusCode } =
    watch();

  const {
    data: dataResultBranchAmendment,
    isLoading: isDataResultBranchAmendmentLoading,
    refetch: fetchDataResultBranchAmendment,
  } = useQuery({
    url: `society/external/branchAmendment/branchAmendments`,
    filters: [
      { field: "societyId", operator: "eq", value: id },
      {
        field: "branchNameQuery",
        value: searchQuery,
        operator: "eq",
      },
      {
        field: "applicationStatusCode",
        value: applicationStatusCode,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      setList(responseData);
    },
  });

  const {
    data: dataBranchResult,
    isLoading: isDataBranchResultLoading,
    refetch: fetchDataResult,
  } = useQuery({
    url: `society/branch/getBranchByParam`,
    filters: [
      { field: "societyId", operator: "eq", value: id },
      { field: "applicationStatusCode", operator: "eq", value: 3 },
    ],
  });

  const branchPindaanList = dataBranchResult?.data?.data?.data ?? [];

  const branchListPidaanOptions = useMemo(
    () =>
      branchPindaanList?.map((data: any) => ({
        value: String(data?.id),
        label: data?.name,
      })) || [],
    [branchPindaanList]
  );

  const handleChangePage = (newPage: number) => setValue("page", newPage);

  const handleResetBranchAmendInfo = () => {
    dispatch(resetBranchAmendInfo());
  };

  useEffect(() => {
    handleResetBranchAmendInfo();
  }, []);

  useEffect(() => {
    const branchInfo =
      branchPindaanList?.find(
        (data: any) => data?.id === Number(selectedBranch)
      ) ?? "";
    setBranchDetail(branchInfo);
  }, [selectedBranch]);

  const { mutate: createBranchAmendment, isLoading: isLoadingCreate } =
    useCustomMutation();

  const goto = () => {
    if (!branchDetail) {
      return;
    }
    handleResetBranchAmendInfo();
    createBranchAmendment(
      {
        url: `${API_URL}/society/external/branchAmendment/create`,
        method: "post",
        values: {
          societyId: branchDetail?.societyId,
          societyNo: branchDetail?.societyNo,
          branchId: branchDetail?.id,
          branchNo: branchDetail?.branchNo,
          applicationStatusCode: 1,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          const amendmentId = data?.data?.data;
          dispatch(setIsView(false));
          dispatch(
            setBranchAmendInfo({
              branchId: branchDetail?.id,
              branchNo: branchDetail?.branchNo,
              id: amendmentId,
              currentBranchName: branchDetail?.name,
              currentBranchAddress: branchDetail?.address,
            })
          );
          navigate(
            `/pertubuhan/society/${id}/senarai/cawangan/branch-Info/form/${amendmentId}`
          );
        },
      }
    );
  };

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    applicationStatusCode: t("statusPermohonan"),
  });

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const onFilterChange = (field: string, value: number | string) => {
    setValue("page", 1);
    setValue(field, value);
  };

  let branchNo = branchDetail?.branchNo || "";

  return (
    <>
      <Box>
        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "16px",
            p: { xs: 3, sm: 4 },
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
            marginTop: 2,
          }}
        >
          <Box
            sx={{
              display: "flex",
              gap: 2,
              mb: 3,
              mx: { xs: 2, md: "auto" },
              maxWidth: "600px",
              width: "100%",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                display: "flex",
                flex: 1,
                backgroundColor: "#F8F8F8",
                borderRadius: "8px",
                alignItems: "center",
                px: 1,
                border: "1px solid #EAEAEA",
                width: "100%",
              }}
            >
              <Search
                sx={{ color: "#666666", mr: 1 }}
                onClick={() => onSeachHandler()}
              />
              <TextField
                placeholder="Nama cawangan"
                variant="standard"
                value={searchTerm}
                onKeyDown={onSearchKeyDown}
                onChange={handleSearchChange}
                fullWidth
                InputProps={{
                  disableUnderline: true,
                }}
                sx={{
                  "& input": {
                    p: "8px 0",
                  },
                }}
              />
            </Box>
            <FilterBar
              filterOptions={filterOptions}
              onFilterChange={onFilterChange}
              selectedFilters={selectedFilters}
              onSelectedFiltersChange={handleSelectedFiltersChange}
            />
          </Box>

          <Box
            sx={{
              maxWidth: 650,
              mx: "auto",
            }}
          >
            <DataTable
              columns={columns}
              rows={list || []}
              page={page}
              rowsPerPage={pageSize}
              totalCount={list?.length || 0}
              onPageChange={handleChangePage}
              pagination={false}
            />
          </Box>
        </Box>

        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "16px",
            p: { xs: 3, sm: 4 },
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
            marginTop: 2,
            display: "flex",
            justifyContent: "center",
          }}
        >
          <Box
            sx={{
              p: { xs: 2, sm: 3 },
              width: "100%",
              maxWidth: "900px",
              backgroundColor: "none",
              border: "1px solid #EAEAEA",
              borderRadius: "12px",
            }}
          >
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
                mb: 2,
              }}
            >
              {t("choosePindaan")}
            </Typography>

            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                paddingLeft: 10,
                paddingRight: 10,
                paddingTop: 3,
                gap: 2,
              }}
            >
              {isDataBranchResultLoading ? (
                <CircularProgress sx={{ marginInline: "auto" }} />
              ) : (
                <>
                  <SelectFieldController
                    control={control}
                    name="selectedBranch"
                    options={branchListPidaanOptions}
                    placeholder={t("choosePindaan")}
                    sx={{
                      height: "56px",
                      backgroundColor: "white",
                      borderRadius: "8px",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                      "& .MuiInputBase-input": {
                        fontSize: "1rem",
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    placeholder={t("branchNumber")}
                    value={branchNo}
                    disabled
                    sx={{
                      backgroundColor: "#F5F5F5",
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "8px",
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  />
                </>
              )}

              <Box sx={{ display: "flex", justifyContent: "center", mt: 6 }}>
                <ButtonPrimary
                  disabled={!branchDetail}
                  sx={{
                    backgroundColor: "#1BD5D2",
                    color: "white",
                    "&:hover": { backgroundColor: "#1BD5D2" },
                    textTransform: "none",
                    minWidth: "200px",
                    borderRadius: "8px",
                  }}
                  onClick={() => goto()}
                >
                  {t("seterusnya")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      <DialogConfirmation
        open={showConfirmDeleteDialog}
        onClose={() => {
          setShowConfirmDeleteDialog(false);
          setDeleteBranchId(null);
        }}
        onAction={deleteAmendmentHandle}
        isMutating={isLoadingDecisionUpdate}
        onConfirmationText={t("deleteConfirmationMessage")}
      />
    </>
  );
};

export default CawanganPindaanPage;
