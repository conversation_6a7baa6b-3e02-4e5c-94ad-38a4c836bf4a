import { Route } from "react-router-dom";
import UpdateAttendance from "@/pages/takwim/UpdateAttendance";
import TakwimProvider from "@/contexts/takwimProvider";
import TakwimLandingPage from "@/pages/takwim/TakwimLandingPage";
import TakwimActivityListsPage from "@/pages/takwim/ActivityLists";
import TakwimActivityDetailsPage from "@/pages/takwim/ActivityDetails";
import CreateEventPage from "@/pages/takwim/CreateEvent";
import TermaPenggunaan from "@/pages/landing-page/mainLanding/termaPenggunaan";

export const takwimAuth = {
  routes: (
    <>
      <Route
        path="/takwim"
        element={
          <TakwimProvider>
            <TakwimLandingPage />
          </TakwimProvider>
        }
      >
        {/* <Route index element={<TakwimActivityListsPage />} />
        <Route path="activity" element={<TakwimActivityListsPage />} />
        <Route
          path="activity/:eventNo"
          element={<TakwimActivityDetailsPage />}
        />
        <Route path="create-event" element={<CreateEventPage />} />
        <Route path="edit-event/:eventNo" element={<CreateEventPage />} />
        <Route path="terma-penggunaan" element={<TermaPenggunaan />} /> */}
        <Route path="update-attendance" element={<UpdateAttendance />} />
      </Route>
    </>
  ),
};
