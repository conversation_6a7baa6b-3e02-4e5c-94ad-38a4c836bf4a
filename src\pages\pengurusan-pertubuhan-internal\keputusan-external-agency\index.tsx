import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import { Box, CircularProgress, Grid, Typography } from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import { FieldValues, useForm } from "react-hook-form";
import TextFieldController from "@/components/input/TextFieldController";
import useQuery from "@/helpers/hooks/useQuery";
import { DisabledTextField } from "@/components";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

function KeputusanIndukExternalAgency() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { id } = useParams();
  const societyId = atob(id || "");
  const [societyInfo, setSocietyInfo] = useState<any>("");
  const [date, setDate] = useState("");
  const [currentAgencyExternalId, setCurrentAgencyExternalId] = useState(null);

  const {
    data: agencyReviewData,
    isLoading: agencyReviewDataLoading,
    refetch: fetchAgencyReviewDataData,
  } = useQuery({
    url: `society/internal/externalAgencyReview/getLatest`,
    autoFetch: true,
    filters: [{ field: "societyId", operator: "eq", value: societyId }],
    onSuccess: (data) => {
      const id = data?.data?.data?.id || null;
      const date = data?.data?.data?.createdDate || "";
      setCurrentAgencyExternalId(id);
      setDate(date);
    },
  });

  const { data: societyData, isLoading: societyDataLoading } = useQuery({
    url: `society/${societyId}`,
    autoFetch: true,
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      setSocietyInfo(responseData);
    },
  });

  const { mutate: submitRecord, isLoading: isLoadingSubmitRecord } =
    useCustomMutation();

  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString(undefined, { hour12: false });
  };
  const getCurrentDate = () => {
    const now = new Date();
    return now.toLocaleDateString(undefined).replace(/\//g, ".");
  };

  const getLocalISOTime = () => {
    const now = new Date();
    const tzOffset = now.getTimezoneOffset() * 60000;  
    const localISOTime = new Date(Number(now) - tzOffset).toISOString().slice(0, 19);
    return localISOTime;
  };

  const onSubmit = (data: FieldValues) => { 
    const getCurrentTime = getLocalISOTime(); 
    submitRecord(
      {
        url: `${API_URL}/society/internal/externalAgencyReview/submit`,
        method: "patch",
        values: {
          id: currentAgencyExternalId,
          submissionDate: getCurrentTime,
          note: data.note,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification(data, values, resource) {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("error"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          navigate(-1);
        },
      }
    );
  };

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        note: "",
      },
    });

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
              display: "grid",
              gap: 1,
            }}
          >
            <Typography sx={{ fontWeight: "400!important" }}>
              {societyInfo?.societyName}
            </Typography>
            <Typography sx={{ fontWeight: "400!important" }}>
              {societyInfo?.societyNo}
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography color={"primary"}>
                  {t("externalAgencyReviews")}
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    name="note"
                    multiline
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("masaUlusan")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <DisabledTextField value={getCurrentTime()} />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("reviewDate")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <DisabledTextField value={getCurrentDate()} />
                </Grid>
              </Grid>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "34px",
                  gap: "10px",
                }}
              >
                <ButtonOutline
                  onClick={() =>
                    navigate(
                      "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk?tab=pendaftaran-induk-menuggu-ulasan-luar"
                    )
                  }
                >
                  {t("back")}
                </ButtonOutline>
                <ButtonPrimary type="submit" disabled={isLoadingSubmitRecord}>
                  {isLoadingSubmitRecord ? (
                    <CircularProgress
                      size="0.5rem"
                      sx={{
                        display: "block",
                      }}
                    />
                  ) : (
                    t("save")
                  )}
                </ButtonPrimary>
              </Box>
            </Box>
          </form>
        </Box>
      </Box>
    </>
  );
}

export default KeputusanIndukExternalAgency;
