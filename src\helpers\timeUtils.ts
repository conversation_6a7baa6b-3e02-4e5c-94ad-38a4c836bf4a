import dayjs from "./dayjs";
import i18next from "i18next";

export const formatTime = (time: string): string => {
  if (!time) return "";

  try {
    return dayjs(time, "HH:mm:ss").format("h:mm");
  } catch (error) {
    console.error('Error formatting time:', error);
    return time;
  }
};

export const formatTimeWithPeriod = (time: string): string => {
  if (!time) return "";

  try {
    const formattedTime = dayjs(time, "HH:mm").format("h:mm");
    const [hours] = time.split(':').map(Number);

    const t = i18next.t;

    if (hours >= 0 && hours < 12) {
      return `${formattedTime} ${t('pagi')}`;
    } else if (hours === 12) {
      return `${formattedTime} ${t('tgh hari')}`;
    } else {
      return `${formattedTime} ${t('ptg')}`;
    }
  } catch (error) {
    console.error('Error formatting time:', error);
    return time; // Return original time if formatting fails
  }
};

// You can add more time-related utility functions here
export const isValidTime = (time: string): boolean => {
  return dayjs(time, "HH:mm", true).isValid();
};

export const convertTo24Hour = (time: string): string => {
  return dayjs(time, "h:mm A").format("HH:mm");
};

export const formatArrayDateField = (dateField: any) => {
  if (Array.isArray(dateField) && dateField.length === 3) {
    const [year, month, day] = dateField;
    // Ensure month and day are zero-padded to 2 digits
    const paddedMonth = String(month).padStart(2, '0');
    const paddedDay = String(day).padStart(2, '0');
    return `${year}-${paddedMonth}-${paddedDay}`;
  }
  return dateField;
};
