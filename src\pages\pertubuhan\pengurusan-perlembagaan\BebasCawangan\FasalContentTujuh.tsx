import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  TextareaAutosize,
  MenuItem,
  Select,
  FormControl,
  FormHelperText,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

interface FasalContentSatuProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: string;
  name: string;
}
export const FasalContentTujuhBebasCawangan: React.FC<
  FasalContentSatuProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const requiredText = ["jenis mesyuarat agung"];
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);

  const [clauseContentId, setClauseContentId] = useState("");
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [lainlain, setLainLain] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [clauseContentEditable, setClauseContentEditable] = useState(
    clause.clauseContent
  );
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }

    return errors;
  };
  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const handleChange = (e: any) => {
    setClauseContentEditable(e.target.value);
  };

  const checkBebasEditFields = (
    requiredFieldText: string[],
    clauseContentEditable: string
  ) => {
    const missingFields = requiredFieldText.filter(
      (field) => !clauseContentEditable.includes(`<<${field}>>`)
    );
    if (missingFields.length > 0) {
      alert(
        `Kandungan tidak lengkap! Sila masukkan:\n\n${missingFields
          .map((field) => `<<${field}>>`)
          .join("\n")}\n\ndi dalam kandungan anda`
      );
      return true;
    }
    return false;
  };

  useEffect(() => {
    if (clause) {
      //const clause = JSON.parse(clause15);
      setDataId(clause.id);
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      setClauseContentEditable(
        clause.clauseModifyContent
          ? clause.clauseModifyContent
          : clause.clauseContent
      ); 
      if((clause.constitutionValues[0]?.definitionName && clause.constitutionValues[0]?.definitionName !== t("annual") && clause.constitutionValues[0]?.definitionName !== t("biennial"))){
        setPemilihanAjk(t("lainLain"));
        setLainLain(clause.constitutionValues[0]?.definitionName) 
      }else{
        setPemilihanAjk(clause.constitutionValues[0]?.definitionName);
      }
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clauseContentEditable;
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${lainlain ? lainlain : pemilihanAjk  || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  const handlerChangeClauseContentEditable = (val: string) => {
    setClauseContentEditable(val);
  };
  return (
    <>
      <Grid container>
        {/* name section */}
        <FasalNameCom clauseId={clauseId} name={name} />
        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Typography sx={{ mb: 1, ...sectionStyle }}>{name}</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("jenisMesyuaratAgung")}{" "}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.pemilihanAjk}
                  >
                    <Select
                      size="small"
                      value={pemilihanAjk}
                      displayEmpty
                      onChange={(e) => {
                        setPemilihanAjk(e.target.value as string);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          pemilihanAjk: "",
                        }));

                        if (e.target.value === t("annual")) {
                          setLainLain("") 
                        } else if (e.target.value === t("biennial")) {
                          setLainLain("") 
                        }
                      }}
                    >
                      <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                      <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                      <MenuItem value={t("lainLain")}>{t("lainLain")}</MenuItem>
                    </Select>
                    {formErrors.pemilihanAjk && (
                      <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
                    )}
                  </FormControl> 
                </Grid>
                {pemilihanAjk === t("lainLain") ? 
                    <>
                      <Grid item xs={12} md={4}></Grid>
                      <Grid item xs={12} md={8}>
                        <TextField
                          type="text"
                          size="small"
                          placeholder="Yearly, Biannually, Tri-tahunan"
                          fullWidth
                          required
                          value={lainlain}
                          onChange={(e) => {
                            setLainLain(e.target.value as string); 
                          }} 
                        />
                      </Grid>
                    </> : null
                  }
              </Grid>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <ReminderEditable />
            <EditableFasalTextArea
              requiredFieldText={requiredText}
              clauseContentEditable={clauseContentEditable}
              setClauseContentEditable={handlerChangeClauseContentEditable}
            />
          </Box>

          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <ContentBox clauseContent={clauseContent} />
            <CheckContent checked={checked} onChange={handleChangeCheckbox} />
            {/* submit */}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  display: isViewMode ? "none" : "block",
                }}
                onClick={() => {
                  const errors = validateForm();
                  if (Object.keys(errors).length > 0) {
                    setFormErrors(errors);
                    return;
                  }
                  const isMissingRequired = checkBebasEditFields(
                    requiredText,
                    clauseContentEditable
                  );
                  if (isMissingRequired) {
                    return;
                  }
                  handleSaveContent({
                    i18n,
                    societyId: id,
                    societyName: namaPertubuhan,
                    amendmentId: amendmentId,
                    clauseContentId,
                    clauseNo: clauseNo,
                    clauseName: clauseName,
                    dataId,
                    isEdit,
                    createClauseContent,
                    editClauseContent,
                    description: clauseContent,
                    modifiedTemplate: clauseContentEditable,
                    constitutionValues: [
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: lainlain ? lainlain : pemilihanAjk,
                        titleName: "Jenis Mesyuarat Agung",
                      },
                    ],
                    clause: "clause6",
                    clauseCount: 6,
                  });
                }}
                disabled={isCreatingContent || isEditingContent || !checked}
              >
                {isCreatingContent || isEditingContent
                  ? t("saving")
                  : t("save")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default FasalContentTujuhBebasCawangan;
