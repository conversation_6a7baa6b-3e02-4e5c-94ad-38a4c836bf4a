import { useField, useFormikContext } from "formik"
import Input, { type Props } from "./Input"
import { ChangeEvent } from "react"
import { DatePickerFormik, DatePickerFormikProps } from "./DatePickerFormik"
import { FormControl } from "@mui/material"

export type InputControllerFormikProps = Omit<Props, "name"> & {
  name: string
  onValueChange?: (<V = any>(value: V) => void | Promise<void>) | null

  /**
   * @description this props applied when type value is "date"
   */
  dateInputProps?: null | Omit<DatePickerFormikProps, "name">

  /**
   * @default ""
   */
  helperTextFallback?: string
}

export const InputControllerFormik = <
  PayloadVal = any,
  PropType extends InputControllerFormikProps = InputControllerFormikProps
>({ name, disabled: initialDisabled, onValueChange, helperTextFallback = "", ...otherProps }: PropType) => {
  const [field, meta] = useField<PayloadVal>(name);
  const { isSubmitting } = useFormikContext<PayloadVal>();

  const hasError = !!meta.error || typeof otherProps?.helperText === "string"
  const disabled = initialDisabled || isSubmitting

  const dateInputComponent = () => {
    return (
      // @ts-expect-error
      <DatePickerFormik
        {...{ name, disabled }}
        {...field}
        {...(otherProps?.type === "date" && { label: undefined, ...(otherProps?.dateInputProps ?? {}) })}
        sx={{
          ...(disabled && { backgroundColor: "#E8E9E8" }),
          borderRadius: "5px",
          fontSize: "14px",
          "& .MuiSelect-select": {
            color: "black",
            fontSize: "16px",
          },
          "&.Mui-disabled .MuiSelect-select": {
            WebkitTextFillColor: "#666666",
          },
          ...otherProps?.customClass,
        }}
        helperTextFallback=""
        withFormControl={true}
        formControlWrapper={(dateInput) => (
          <FormControl
            fullWidth
            variant="outlined"
            size="small"
            disabled={disabled || otherProps?.isLoadingData}
            error={hasError}
            required={otherProps?.required}
          >
            {dateInput}
          </FormControl>
        )}
      />
    )
  }
  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    field.onChange(e);
    otherProps.onChange?.(e);
    onValueChange?.(field.value);
  }

  return (
    // @ts-expect-error
    <Input
      {...field}
      {...otherProps}
      {...{ disabled }}
      error={hasError}
      helperText={disabled ? "" : otherProps?.helperText ?? (hasError ? meta.error : helperTextFallback)}
      onChange={handleChange}
      dateInputComponent={dateInputComponent}
      {...(otherProps?.type === "select" && { trimHelperText: hasError })}
    />
  )
}
