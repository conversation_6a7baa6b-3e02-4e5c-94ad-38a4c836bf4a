import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setSocietyBySearchDataRedux, setSocietyBySearchError, setSocietyBySearchLoading } from '../societyBySearchDataReducer';

export const fetchSocietyBySearchData = createAsyncThunk(
  'roles/fetchData',
  async (
    { searchParams, size, page }: { searchParams: { [key: string]: any }; size: number; page: number },
    { dispatch }
  ) => {
    dispatch(setSocietyBySearchLoading(true));
    try {
      const query = new URLSearchParams({
        ...searchParams,
        size: size.toString(),
        page: page.toString(),
      }).toString();
      const response = await fetch(`${API_URL}/society/search?${query}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();
      dispatch(setSocietyBySearchDataRedux(data));
    } catch (error: any) {
      dispatch(setSocietyBySearchError(error.message));
    } finally {
      dispatch(setSocietyBySearchLoading(false));
    }
  }
);
