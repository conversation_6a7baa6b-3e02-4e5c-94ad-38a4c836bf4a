import {
  Box,
  CircularProgress,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { OrganizationStepper } from "../organization-stepper";
import { useNavigate, useParams } from "react-router-dom";
import FasalContentSatu from "./induk/FasalContentSatu";
import FasalContentDua from "./induk/FasalContentDua";
import FasalContentTiga from "./induk/FasalContentTiga";
import FasalContentEmpat from "./induk/FasalContentEmpat";
import FasalContentLima from "./induk/FasalContentLima";
import FasalContentEnam from "./induk/FasalContentEnam";
import FasalContentTujuh from "./induk/FasalContentTujuh";
import FasalContentDelapan from "./induk/FasalContentDelapan";
import FasalContentSembilan from "./induk/FasalContentSembilan";
import FasalContentSepuluh from "./induk/FasalContentSepuluh";
import FasalContentSebelas from "./induk/FasalContentSebelas";
import FasalContentDuaBelas from "./induk/FasalContentDuaBelas";
import FasalContentTigaBelas from "./induk/FasalContentTigaBelas";
import FasalContentEmpatBelas from "./induk/FasalContentEmpatBelas";
import FasalContentLimaBelas from "./induk/FasalContentLimaBelas";
import FasalContentEnamBelas from "./induk/FasalContentEnamBelas";
import FasalContentTujuhBelas from "./induk/FasalContentTujuhBelas";
import FasalContentDelapanBelas from "./induk/FasalDelapanBelas";
import FasalContentSatuCawangan from "./cawangan/FasalContentSatu";
import FasalContentDuaCawangan from "./cawangan/FasalContentDua";
import FasalContentTigaCawangan from "./cawangan/FasalContentTiga";
import FasalContentEmpatCawangan from "./cawangan/FasalContentEmpat";
import FasalContentLimaCawangan from "./cawangan/FasalContentLima";
import FasalContentEnamCawangan from "./cawangan/FasalContentEnam";
import FasalContentTujuhCawangan from "./cawangan/FasalContentTujuh";
import FasalContentDelapanCawangan from "./cawangan/FasalContentDelapan";
import FasalContentSembilanCawangan from "./cawangan/FasalContentSembilan";
import FasalContentSepuluhCawangan from "./cawangan/FasalContentSepuluh";
import FasalContentSebelasCawangan from "./cawangan/FasalContentSebelas";
import FasalContentDuaBelasCawangan from "./cawangan/FasalContentDuaBelas";
import FasalContentTigaBelasCawangan from "./cawangan/FasalContentTigaBelas";
import FasalContentEmpatBelasCawangan from "./cawangan/FasalContentEmpatBelas";
import FasalContentLimaBelasCawangan from "./cawangan/FasalContentLimaBelas";
import FasalContentEnamBelasCawangan from "./cawangan/FasalContentEnamBelas";
import FasalContentTujuhBelasCawangan from "./cawangan/FasalContentTujuhBelas";
import FasalContentDelapanBelasCawangan from "./cawangan/FasalDelapanBelas";
import FasalContentSembilanBelasCawangan from "./cawangan/FasalContentSembilanBelas";
import FasalContentDuaPuluhCawangan from "./cawangan/FasalContentDuaPuluh";
import FasalContentDuaPuluhSatuCawangan from "./cawangan/FasalContentDuaPuluhSatu";
import FasalContentDuaPuluhDuaCawangan from "./cawangan/FasalContentDuaPuluhDua";
import FasalContentDuaPuluhTigaCawangan from "./cawangan/FasalContentDuaPuluhTiga";
import FasalContentSatuFaedah from "./faedah/FasalContentSatu";
import FasalContentDuaFaedah from "./faedah/FasalContentDua";
import FasalContentTigaFaedah from "./faedah/FasalContentTiga";
import FasalContentEmpatFaedah from "./faedah/FasalContentEmpat";
import FasalContentLimaFaedah from "./faedah/FasalContentLima";
import FasalContentEnamFaedah from "./faedah/FasalContentEnam";
import FasalContentTujuhFaedah from "./faedah/FasalContentTujuh";
import FasalContentDelapanFaedah from "./faedah/FasalContentDelapan";
import FasalContentSembilanFaedah from "./faedah/FasalContentSembilan";
import FasalContentSepuluhFaedah from "./faedah/FasalContentSepuluh";
import FasalContentSebelasFaedah from "./faedah/FasalContentSebelas";
import FasalContentDuaBelasFaedah from "./faedah/FasalContentDuaBelas";
import FasalContentTigaBelasFaedah from "./faedah/FasalContentTigaBelas";
import FasalContentEmpatBelasFaedah from "./faedah/FasalContentEmpatBelas";
import FasalContentLimaBelasFaedah from "./faedah/FasalContentLimaBelas";
import FasalContentEnamBelasFaedah from "./faedah/FasalContentEnamBelas";
import FasalContentTujuhBelasFaedah from "./faedah/FasalContentTujuhBelas";
import FasalContentDelapanBelasFaedah from "./faedah/FasalDelapanBelas";
import FasalContentSembilanBelasFaedah from "./faedah/FasalContentSembilanBelas";
import FasalContentDuaPuluhFaedah from "./faedah/FasalContentDuaPuluh";
import InfoQACard from "../InfoQACard";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ApplicationStatus, ConstitutionType } from "../../../../helpers/enums";
import { useSelector } from "react-redux";
import FasalContentSatuBebas from "./bebas/FasalContentSatu";
import FasalContentDuaBebas from "./bebas/FasalContentDua";
import FasalContentTigaBebas from "./bebas/FasalContentTiga";
import FasalContentEmpatBebas from "./bebas/FasalContentEmpat";
import FasalContentLimaBebas from "./bebas/FasalContentLima";
import FasalContentEnamBebas from "./bebas/FasalContentEnam";
import FasalContentOther from "./bebas/FasalContentOther";
import FasalContentSatuBebasCawangan from "./bebasCawangan/FasalContentSatu";
import FasalContentDuaBebasCawangan from "./bebasCawangan/FasalContentDua";
import FasalContentTigaBebasCawangan from "./bebasCawangan/FasalContentTiga";
import FasalContentEmpatBebasCawangan from "./bebasCawangan/FasalContentEmpat";
import FasalContentLimaBebasCawangan from "./bebasCawangan/FasalContentLima";
import FasalContentEnamBebasCawangan from "./bebasCawangan/FasalContentEnam";
import FasalContentTujuhBebasCawangan from "./bebasCawangan/FasalContentTujuh";
import FasalContentLapanBebasCawangan from "./bebasCawangan/FasalContentLapan";
import FasalContentSembilanBebasCawangan from "./bebasCawangan/FasalContentSembilan";
import FasalContentOtherCawangan from "./bebasCawangan/FasalContentOther";

export interface ClauseValueProps {
  definitionName: string;
}

export interface ClauseProps {
  id: number;
  constitutionValues: ClauseValueProps[];
  clauseContent: string;
  clauseContentId: string;
  edit: boolean;
  clauseModifyContent?: string;
  clauseNo?: string;
  clauseName?: string;
  constitutionTypeId?: string | number;
  clauseDescription?: string;
}
export interface FasalContentProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
}
export interface FasalBebasProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: string;
  name: string;
}

export const Fasal: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(2);
  const [fasalName, setFasalName] = useState<string | null>(null);
  const [constitutionType, setConstitutionType] = useState<string | null>(null);
  const [constitutionTypeInt, setConstitutionTypeInt] = useState<
    string | number
  >(0);
  const [societyId, setSocietyId] = useState<any>(null);
  const [refresh, setRefresh] = useState(0);

  const [fasalID, setFasalID] = useState("");

  const [clause, setClause] = useState<ClauseProps>({
    id: 0,
    constitutionValues: [],
    clauseContent: "",
    clauseContentId: "",
    edit: false,
  });
  const ref = useRef<HTMLDivElement>(null);
  const clauseNumber = Number(id);
  const isBebasType =
    constitutionType === ConstitutionType.Bebas[1] ||
    constitutionType === ConstitutionType.CawanganBebas[1];
  const isAddedBebasFasal =
    (constitutionType === ConstitutionType.Bebas[1] && clauseNumber > 6) ||
    (constitutionType === ConstitutionType.CawanganBebas[1] &&
      clauseNumber > 9);
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const { data: fasalData, isLoading: fasaldataIsLoading } = useCustom({
    url: `${API_URL}/society/constitution/clausecontenttemplate/getByConsTypeAndClauseNo`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        constitutionType: constitutionTypeInt,
        clauseNo: id,
        societyId: societyId,
      },
    },
    queryOptions: {
      enabled: !!constitutionTypeInt && !!id,
      cacheTime: 0,
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        setFasalID(responseData.id);
      },
    },
  });

  const fasal = fasalData?.data?.data;
  const clauseContentName = fasal?.name;
  const clauseContentId = fasal?.id;
  // console.log( !!societyId && !!fasalID && (!!clauseContentId || !!id)) 
  const { data: clauseContentData, isLoading: clauseContentDataIsLoading } =
    useCustom({
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          ...(isAddedBebasFasal
            ? {
                clauseNo: id,
              }
            : { clauseContentId: fasalID }),
        },
      },
      queryOptions: {
        // enabled: !!societyId && !!clauseContentId,
        enabled:
          !!societyId &&
          (isAddedBebasFasal ? !!id : !!fasalID && !!clauseContentId),
        cacheTime: 0,
        onSuccess: (data) => {
          const responseData = data?.data?.data?.data[0];
          // For added bebas description
          if (isAddedBebasFasal) {
            setClause((prevState) => ({
              ...prevState,
              clauseName: responseData?.clauseName,
              id: responseData?.id ?? 0,
              constitutionValues: [
                { definitionName: responseData?.description },
              ],
            }));
          }

          // For fixed bebas fasal
          if (isBebasType) {
            setClause((prevState) => ({
              ...prevState,
              id: responseData?.id,
              clauseModifyContent: responseData?.modifiedTemplate ?? null,
            }));
          }
        },
      },
    });
  const BebasName = clauseContentData?.data?.data?.data[0]?.clauseName;
  const clauseContent = clauseContentData?.data?.data?.data[0];  
  const { data: clauseValueData, isLoading: clauseValueDataIsLoading } =
    useCustom({
      url: `${API_URL}/society/constitutionvalue/getAll`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          clauseContentId: clauseContentId,
        },
      },
      queryOptions: {
        enabled: !!societyId && !!clauseContentId,
        cacheTime: 0,
      },
    });

  const clauseValue = clauseValueData?.data?.data?.data;

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  useEffect(() => {
    setClause((prevState) => ({
      ...prevState,
      id: clauseContent?.id ?? 0,
      ...(isAddedBebasFasal ? {} : { constitutionValues: clauseValue ?? [] }),
      clauseContent: fasal?.content ?? "Failed to fetch Fasal",
      clauseDescription: clauseContent?.description,
      clauseContentId: fasal?.id ?? 0,
      clauseNo: fasal?.clauseNo,
      clauseName: fasal?.name,
      constitutionTypeId: fasal?.constitutionTypeId,
      edit: !!clauseContent,
    }));
    setRefresh(refresh + 1);
  }, [fasal, clauseContent, clauseValue]);

  useEffect(() => {
    if (encodedId) {
      const decodedId = atob(encodedId);
      setSocietyId(decodedId);
    }
    if (societyDataRedux) {
      setConstitutionType(societyDataRedux.constitutionType);
      for (const [key, value] of Object.entries(ConstitutionType)) {
        if (value[1] === societyDataRedux.constitutionType) {
          setConstitutionTypeInt(value[0]);
        }
      }
    }
  }, []);

  const apiIsLoading = isAddedBebasFasal
    ? fasaldataIsLoading || clauseContentDataIsLoading
    : clauseValueDataIsLoading ||
      fasaldataIsLoading ||
      clauseContentDataIsLoading;

  const renderContentInduk = () => {
    switch (id) {
      case "1":
        return (
          <FasalContentSatu
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "2":
        return (
          <FasalContentDua
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "3":
        return (
          <FasalContentTiga
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "4":
        return (
          <FasalContentEmpat
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "5":
        return (
          <FasalContentLima
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "6":
        return (
          <FasalContentEnam
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "7":
        return (
          <FasalContentTujuh
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "8":
        return (
          <FasalContentDelapan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "9":
        return (
          <FasalContentSembilan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "10":
        return (
          <FasalContentSepuluh
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "11":
        return (
          <FasalContentSebelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "12":
        return (
          <FasalContentDuaBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "13":
        return (
          <FasalContentTigaBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "14":
        return (
          <FasalContentEmpatBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "15":
        return (
          <FasalContentLimaBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "16":
        return (
          <FasalContentEnamBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "17":
        return (
          <FasalContentTujuhBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "18":
        return (
          <FasalContentDelapanBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      default:
        return <></>;
    }
  };

  const renderContentCawangan = () => {
    switch (id) {
      case "1":
        return (
          <FasalContentSatuCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "2":
        return (
          <FasalContentDuaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "3":
        return (
          <FasalContentTigaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "4":
        return (
          <FasalContentEmpatCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "5":
        return (
          <FasalContentLimaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "6":
        return (
          <FasalContentEnamCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "7":
        return (
          <FasalContentTujuhCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "8":
        return (
          <FasalContentDelapanCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "9":
        return (
          <FasalContentSembilanCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "10":
        return (
          <FasalContentSepuluhCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "11":
        return (
          <FasalContentSebelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "12":
        return (
          <FasalContentDuaBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "13":
        return (
          <FasalContentTigaBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "14":
        return (
          <FasalContentEmpatBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "15":
        return (
          <FasalContentLimaBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "16":
        return (
          <FasalContentEnamBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "17":
        return (
          <FasalContentTujuhBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "18":
        return (
          <FasalContentDelapanBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "19":
        return (
          <FasalContentSembilanBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "20":
        return (
          <FasalContentDuaPuluhCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "21":
        return (
          <FasalContentDuaPuluhSatuCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "22":
        return (
          <FasalContentDuaPuluhDuaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "23":
        return (
          <FasalContentDuaPuluhTigaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      default:
        return <></>;
    }
  };

  const renderContentFaedah = () => {
    switch (id) {
      case "1":
        return (
          <FasalContentSatuFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "2":
        return (
          <FasalContentDuaFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "3":
        return (
          <FasalContentTigaFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "4":
        return (
          <FasalContentEmpatFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "5":
        return (
          <FasalContentLimaFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "6":
        return (
          <FasalContentEnamFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "7":
        return (
          <FasalContentTujuhFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "8":
        return (
          <FasalContentDelapanFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "9":
        return (
          <FasalContentSembilanFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "10":
        return (
          <FasalContentSepuluhFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "11":
        return (
          <FasalContentSebelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "12":
        return (
          <FasalContentDuaBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "13":
        return (
          <FasalContentTigaBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "14":
        return (
          <FasalContentEmpatBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "15":
        return (
          <FasalContentLimaBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "16":
        return (
          <FasalContentEnamBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "17":
        return (
          <FasalContentTujuhBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "18":
        return (
          <FasalContentDelapanBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "19":
        return (
          <FasalContentSembilanBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "20":
        return (
          <FasalContentDuaPuluhFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      default:
        return <></>;
    }
  };

  const renderContentBebas = () => {
    switch (id) {
      case "1":
        return (
          <FasalContentSatuBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "2":
        return (
          <FasalContentDuaBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "3":
        return (
          <FasalContentTigaBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "4":
        return (
          <FasalContentEmpatBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "5":
        return (
          <FasalContentLimaBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "6":
        return (
          <FasalContentEnamBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      default:
        return (
          <FasalContentOther
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={BebasName}
            asalData={clauseContent?.description}
          />
        );
    }
  };

  const renderContentBebasCawangan = () => {
    switch (id) {
      case "1":
        return (
          <FasalContentSatuBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "2":
        return (
          <FasalContentDuaBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "3":
        return (
          <FasalContentTigaBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "4":
        return (
          <FasalContentEmpatBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "5":
        return (
          <FasalContentLimaBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "6":
        return (
          <FasalContentEnamBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "7":
        return (
          <FasalContentTujuhBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "8":
        return (
          <FasalContentLapanBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      case "9":
        return (
          <FasalContentSembilanBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent?.description}
          />
        );
      default:
        return (
          <FasalContentOtherCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={BebasName}
            asalData={clauseContent?.description}
          />
        );
    }
  };

  const sectionStyle = {
    backgroundColor: "#CDE4E4",
    padding: "8px 16px",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
  };

  useEffect(() => {
    if (ref.current) {
      ref.current.scrollIntoView({ block: "start" });
    }
  }, []);

  return (
    <Box
      ref={ref}
      sx={{ display: "flex", height: "100%", gap: 2, alignItems: "flex-start" }}
    >
      {apiIsLoading ? (
        <Box
          sx={{
            height: "100%",
            width: "55vw",
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
            }}
          >
            <CircularProgress />
          </Box>
        </Box>
      ) : (
        <Box
          sx={{
            height: "100%",
            width: "55vw",
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
          }}
        >
          {/* <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {id}: {fasalName}
        </Typography> */}

          {constitutionType === ConstitutionType.IndukNGO[1] ||
          constitutionType === ConstitutionType.IndukAgama[1]
            ? renderContentInduk()
            : constitutionType === ConstitutionType.FaedahBersama[1]
            ? renderContentFaedah()
            : constitutionType === ConstitutionType.Bebas[1]
            ? renderContentBebas()
            : constitutionType === ConstitutionType.CawanganBebas[1]
            ? renderContentBebasCawangan()
            : renderContentCawangan()}
        </Box>
      )}

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />

        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}

        <InfoQACard />
      </Box>
    </Box>
  );
};

export default Fasal;
