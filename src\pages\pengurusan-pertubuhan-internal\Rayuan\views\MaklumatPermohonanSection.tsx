import React, { useEffect, useState } from "react";
import { Typo<PERSON>, Box } from "@mui/material";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import "leaflet/dist/leaflet.css";
import dayjs from "dayjs";
import { AppDispatch } from "@/redux/store";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { fetchAppealByIdData } from "@/redux/APIcalls/appealByIdThunks";
import { fetchOrganisationCategoriesData } from "@/redux/APIcalls/organisationCategoriesThunks";
import { fetchUserCommitteeBySocietyIdData } from "@/redux/APIcalls/userCommitteeBySocietyIdThunks";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";
import {
  getLocalStorage,
  IdTypes,
  ListGelaran,
  <PERSON><PERSON><PERSON>,
  OrganisationPositions,
  SebabR<PERSON>List,
} from "@/helpers";
import Input from "@/components/input/Input";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

interface FormValues {
  designationCode?: any;
  identificationType?: any;
  identificationNo?: any;
  name?: any;
  titleCode?: any;
  gender?: any;
  jobCode?: any;
  residentialAddress?: any;
  reason?: any;
  email?: any;
  phoneNumber?: any;
  homePhoneNumber?: any;
  officePhoneNumber?: any;
  organisationCategory?: any;
  organisationApplicationDate?: any;
  typeOfAppeal?: any;
  rejectedDate?: any;
}

const MaklumatPermohonanSection = () => {
  const { t } = useTranslation();
  const { id, type } = useParams();
  const decodedId = atob(id ?? "");
  const decodedType = atob(type ?? "");
  const [formValues, setFormValues] = useState<FormValues>({
    designationCode: null,
    identificationType: null,
    identificationNo: null,
    name: null,
    //
    titleCode: null,
    jobCode: null,
    residentialAddress: null,
    reason: null,
    email: null,
    gender: null,
    phoneNumber: null,
    homePhoneNumber: null,
    officePhoneNumber: null,
    organisationCategory: null,
    rejectedDate: null,
  });

  // const isLoading = isAddressLoading || isSocietyLoading || isRoListLoading;
  const dispatch: AppDispatch = useDispatch();
  const {
    data: appealDataById,
    loading: loadingAppeal,
    error: errorAppeal,
  } = useSelector((state: any) => state.appealByIdData);

  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  const { data: userCommitteeBySocietyId } = useSelector(
    (state: any) => state.userCommitteeBySocietyIdData
  );

  useEffect(() => {
    if (decodedId) {
      dispatch(fetchAppealByIdData({ id: decodedId }));
      dispatch(fetchOrganisationCategoriesData());
    }
  }, [decodedId]);

  useEffect(() => {
    if (appealDataById?.societyId) {
      dispatch(fetchSocietyByIdData({ id: appealDataById.societyId }));
      if (decodedId) {
        dispatch(fetchUserCommitteeBySocietyIdData({ id: decodedId }));
      }
    }
  }, [appealDataById?.societyId]);

  useEffect(() => {
    if (userCommitteeBySocietyId) {
      setFormValues({
        designationCode: Number(userCommitteeBySocietyId?.designationCode),
        identificationType: userCommitteeBySocietyId?.identificationType,
        identificationNo: userCommitteeBySocietyId?.identificationNo,
        name: userCommitteeBySocietyId?.name,
        titleCode: userCommitteeBySocietyId?.titleCode,
        gender: userCommitteeBySocietyId?.gender,
        jobCode: userCommitteeBySocietyId?.jobCode,
        residentialAddress: userCommitteeBySocietyId?.residentialAddress,

        email: userCommitteeBySocietyId?.email,
        phoneNumber: userCommitteeBySocietyId?.phoneNumber,
        homePhoneNumber: userCommitteeBySocietyId?.telephoneNumber,
        officePhoneNumber: userCommitteeBySocietyId?.noTelP,

        typeOfAppeal: appealDataById?.idSebab,
        reason: appealDataById?.sebabLain,

        organisationCategory: societyDataById?.categoryCodeJppm,
        organisationApplicationDate: appealDataById?.registeredDate,
        rejectedDate: appealDataById?.rejectedDate,
      });
    }
  }, [societyDataById, appealDataById, userCommitteeBySocietyId]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};
    const requiredFields: (keyof FormValues)[] = [
      "designationCode",
      "identificationType",
      "identificationNo",
      "name",
      "titleCode",
      "gender",
      "jobCode",
      "residentialAddress",
      "reason",
      "email",
      "phoneNumber",
      "homePhoneNumber",
      "officePhoneNumber",
      "organisationCategory",
      "organisationApplicationDate",
      "rejectedDate",
      "typeOfAppeal",
    ];

    requiredFields.forEach((field) => {
      if (!formValues[field]) {
        newErrors[field] = t("requiredField");
      }
    });
  };

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1) ?? [];

  const mainCategoriesOptions = mainCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  return (
    <Box
      sx={{
        borderRadius: "10px",
      }}
    >
      {/* <LoadingOverlay isLoading={isLoading} /> */}

      <Box component="form" onSubmit={handleSubmit}>
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("organizationInfo")}
          </Typography>
          <Input
            disabled
            name="organisationCategory"
            label={t("organization_category")}
            value={t(
              `${
                mainCategoriesOptions.find(
                  (item: any) =>
                    item?.value === formValues?.organisationCategory
                )?.label || ""
              }`
            )}
          />
          <Input
            disabled
            name="organisationApplicationDate"
            label={t("organizationApplicationDate")}
            value={
              formValues?.organisationApplicationDate
                ? dayjs(formValues?.organisationApplicationDate).format(
                    "DD-MM-YYYY"
                  )
                : ""
            }
            type="date"
          />
          <Input
            disabled
            name="rejectedDate"
            label={t("tarikhKeputusanDitolak")}
            value={
              formValues?.rejectedDate
                ? dayjs(formValues?.rejectedDate).format("DD-MM-YYYY")
                : ""
            }
            type="date"
          />
          <Input
            disabled
            name="typeOfAppeal"
            label={t("jenisRayuan")}
            // type="select"
            // options={SebabRyuanList}
            value={t(
              `${
                SebabRyuanList.find(
                  (item) => item?.value === Number(formValues?.typeOfAppeal)
                )?.label || ""
              }`
            )}
          />
        </Box>

        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("orgAppealApplicationInfo")}
          </Typography>
          <Input
            onChange={handleChange}
            required
            disabled
            name="designationCode"
            label={t("jawatanDalamPertubuhan")}
            value={t(
              `${
                OrganisationPositions.find(
                  (item) => item?.value === formValues.designationCode
                )?.label || ""
              }`
            )}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="identificationType"
            label={t("idType")}
            value={t(
              `${
                IdTypes.find(
                  (item) => item.value === formValues.identificationType
                )?.label || ""
              }`
            )}
            isLoadingData={!IdTypes}
          />
          <Input
            onChange={handleChange}
            disabled
            name="identificationNo"
            label={t("idNumber")}
            value={
              formValues.identificationNo ? formValues.identificationNo : ""
            }
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="name"
            label={t("namaPenuh")}
            value={formValues.name ? formValues.name : ""}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="titleCode"
            label={t("title")}
            isLoadingData={!ListGelaran}
            value={t(
              `${
                ListGelaran.find((item) => item.value === formValues.titleCode)
                  ?.label || ""
              }`
            )}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="gender"
            label={t("gender")}
            // type="select"
            // options={ListGender}
            value={t(
              `${
                ListGender.find((item) => item.value === formValues.gender)
                  ?.label || ""
              }`
            )}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="jobCode"
            label={t("occupation")}
            value={formValues.jobCode}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="organisationAddress"
            label={t("alamatPertubuhan")}
            value={formValues.residentialAddress}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="reason"
            label={t("detailsForAppeal")}
            value={formValues.reason}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="email"
            label={t("emel")}
            value={formValues.email ? formValues.email : ""}
          />
          <Input
            required
            onChange={handleChange}
            disabled
            name="phoneNumber"
            label={t("phoneNumber")}
            value={formValues.phoneNumber ? formValues.phoneNumber : ""}
          />
          <Input
            onChange={handleChange}
            disabled
            name="homePhoneNumber"
            label={t("nomborTelefonRumah")}
            value={formValues.homePhoneNumber ? formValues.homePhoneNumber : ""}
          />
          <Input
            onChange={handleChange}
            disabled
            name="officePhoneNumber"
            label={t("nomborTelefonPejabat")}
            value={
              formValues.officePhoneNumber ? formValues.officePhoneNumber : ""
            }
          />
        </Box>
      </Box>
    </Box>
  );
};

export default MaklumatPermohonanSection;
