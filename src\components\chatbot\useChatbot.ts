import { useCallback, useEffect, useState, useRef } from "react";
import { GroupedChatMessage } from "./types";
import { createMessage, getGreetingMessage, getPredefinedResponses, getSessionId } from "./utils";
import { fetchMessageHistory, fetchSocietyList, sendChatbotMessage } from "./api";
import { IUser } from "@/types";
import { USER_DETAILS_KEY } from "@/helpers";
import { useSpeech } from "@/contexts/chatbot/SpeechContext";

interface UseChatbotProps {
  user: IUser | undefined;
  locale: string;
  isOpen: boolean | unknown;
  refetchSocietyList: () => Promise<any>;
  societyListDataResponse: any;
  isWidget?: boolean;
}

/**
 * Custom hook for managing chatbot state and logic
 */
export const useChatbot = ({
  user,
  locale,
  isOpen,
  refetchSocietyList,
  societyListDataResponse,
  isWidget = false,
}: UseChatbotProps) => {
  // Get speech functionality
  const { speak } = useSpeech();

  // State for messages and UI
  const [messages, setMessages] = useState<GroupedChatMessage[]>([]);
  const [currentBotMessage, setCurrentBotMessage] = useState<string | null>(null);
  const [displayedBotText, setDisplayedBotText] = useState("");
  const [showOptionPanel, setShowOptionPanel] = useState(true);
  const [selectedParentKey, setSelectedParentKey] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [initialMessagesLoaded, setInitialMessagesLoaded] = useState(false);
  const [autoPlayEnabled, setAutoPlayEnabled] = useState(true);
  const [hasSpokenAutoGreeting, setHasSpokenAutoGreeting] = useState(false);

  // Get predefined responses based on locale
  const predefinedResponses = getPredefinedResponses(locale);
  const greetingMessage = getGreetingMessage(locale);

  const resetChatbot = () => {
    setMessages([]);
    setCurrentBotMessage(null);
    setDisplayedBotText("");
    setShowOptionPanel(true);
    setSelectedParentKey(null);
    setInitialMessagesLoaded(false);
  };

  // Reset state when user logs out
  useEffect(() => {
    if (!user?.identificationNo) {
      setMessages([]);
      setCurrentBotMessage(null);
      setDisplayedBotText("");
      setShowOptionPanel(true);
      setSelectedParentKey(null);
      setInitialMessagesLoaded(false);
    }
  }, [user?.identificationNo]);

  /**
   * Handles sending a message to the chatbot API
   */
  const handleSendMessage = async (text: string) => {
    setLoading(true);
    setError("");
    setShowOptionPanel(false);

    try {
      const response = await sendChatbotMessage(text, user, locale);

      if (response) {
        setCurrentBotMessage(response);
      } else {
        const errorMessage = locale === "ms"
          ? "Terdapat masalah semasa memproses permintaan anda."
          : "There was an error processing your request.";

        setMessages(prev => [...prev, createMessage("bot", errorMessage)]);
        setError(errorMessage);
        setShowOptionPanel(true);
      }
    } catch (err) {
      // console.error("Error sending message:", err);
      const errorMessage = locale === "ms"
        ? "Terdapat masalah semasa memproses permintaan anda."
        : "There was an error processing your request.";

      setMessages(prev => [...prev, createMessage("bot", errorMessage)]);
      setError(errorMessage);
      setShowOptionPanel(true);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handles option selection from the option panel
   */
  const handleOptionSelect = async (option: string) => {
    // If a parent category is selected
    if (!selectedParentKey) {
      setSelectedParentKey(option);
      setShowOptionPanel(true);
      return;
    }

    // Add user message
    const userMessage = createMessage("user", option);
    setMessages(prev => [...prev, userMessage]);
    setShowOptionPanel(false);

    // Check if it's a society-related query
    const isSocietyIntent =
      selectedParentKey === "pertubuhan" &&
      ["bilangan pertubuhan yang disertai", "numbers of societies joined"].includes(option);

    if (isSocietyIntent) {
      const societyResponse = await fetchSocietyList(
        user,
        locale,
        refetchSocietyList,
        societyListDataResponse
      );

      if (societyResponse) {
        setCurrentBotMessage(societyResponse);
      }
    } else {
      // Check for predefined responses
      const normalizedOption = option.toLowerCase();
      const matchingKey = Object.keys(predefinedResponses).find(
        key => key.toLowerCase() === normalizedOption
      );

      if (matchingKey) {
        setCurrentBotMessage(predefinedResponses[matchingKey]);
      } else {
        await handleSendMessage(option);
      }
    }
  };

  /**
   * Handles going back to parent options
   */
  const handleBackToOptions = useCallback(() => {
    setSelectedParentKey(null);
    setShowOptionPanel(true);
  }, []);

  // Track processed message IDs to prevent duplicates
  const processedMessageIdsRef = useRef<Set<string>>(new Set());

  /**
   * Handles submitting a user message
   */
  const handleSubmitMessage = useCallback(async (text: string, messageId?: string) => {
    if (!text.trim()) return;

    // Generate a message ID if one wasn't provided
    const msgId = messageId || `user_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`;

    // Check if we've already processed this message
    if (processedMessageIdsRef.current.has(msgId)) {
      // console.log('useChatbot: Skipping already processed message ID:', msgId);
      return;
    }

    // Add this message ID to our processed set
    processedMessageIdsRef.current.add(msgId);
    // console.log('useChatbot: Added message ID to processed set:', msgId);

    // console.log('useChatbot: Submitting message:', text);
    const userMessage = createMessage("user", text);
    setMessages(prev => [...prev, userMessage]);
    setShowOptionPanel(false);

    await handleSendMessage(text);
  }, []);

  // Load initial messages or show greeting
  useEffect(() => {
    const loadInitialMessages = async () => {
      // We're using API directly for message history, no need for sessionStorage

      // Only fetch message history for logged-in users if no conversation history
      const isLoggedIn = !!user?.identificationNo ||
                         !!localStorage.getItem(USER_DETAILS_KEY);

      // Convert isOpen to boolean to handle both boolean and unknown types
      const chatIsOpen = Boolean(isOpen);
      if (chatIsOpen && messages.length === 0 && currentBotMessage === null && !initialMessagesLoaded) {
        if (isLoggedIn) {
          // Fetch all message history directly instead of just the latest conversation
          const sessionId = getSessionId(user);
          // console.log('useChatbot: Fetching all message history for session:', sessionId);

          const history = await fetchMessageHistory(sessionId);
          if (history.length > 0) {
            // console.log('useChatbot: Found message history from API:', history.length, 'messages');

            // Sort messages by timestamp to ensure they're in chronological order
            const sortedHistory = [...history].sort((a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
            );

            // Set all messages to the chat
            setMessages(sortedHistory);
            setInitialMessagesLoaded(true);

            // If auto-play is enabled and not in widget mode, speak the last bot message
            if (autoPlayEnabled && !isWidget) {
              // Find the last bot message
              const lastBotMessage = [...sortedHistory]
                .reverse()
                .find(msg => msg.sender === 'bot');

              if (lastBotMessage) {
                // Check if we're on the speech input page
                const isOnSpeechInputPage = window.location.pathname === "/speech-input";
                // console.log('useChatbot: Auto-playing last bot message:', lastBotMessage.text.substring(0, 50) + '...');
                
                // Simple speech attempt without complex retry logic
                setTimeout(() => {
                  speak(
                    lastBotMessage.text, 
                    lastBotMessage.messageId || lastBotMessage.timestamp, 
                    isOnSpeechInputPage
                  );
                }, 1000);
              }
            }

            return;
          } else {
            // console.log('useChatbot: No message history found');
          }
        }

        // Show greeting for new users or guests
        setCurrentBotMessage(greetingMessage);
        setInitialMessagesLoaded(true);
      }
    };

    loadInitialMessages();
  }, [isOpen, messages.length, currentBotMessage, greetingMessage, user, initialMessagesLoaded, locale, autoPlayEnabled, speak]);

  // Animate bot message typing
  useEffect(() => {
    if (currentBotMessage !== null) {
      const chunkSize = 5;
      let index = 0;
      const interval = setInterval(() => {
        index += chunkSize;
        setDisplayedBotText(currentBotMessage.slice(0, index));

        if (index >= currentBotMessage.length) {
          clearInterval(interval);

          // Create the bot message
          const botMessage = createMessage("bot", currentBotMessage);
          setMessages(prev => [...prev, botMessage]);

          // Auto-play the response if enabled and not in widget mode
          if (autoPlayEnabled && !isWidget) {
            // Generate a unique key for this message
            const messageKey = botMessage.messageId || botMessage.timestamp || currentBotMessage;

            // Dispatch a custom event to notify that this message has been spoken
            // This will help coordinate with the FullPageChatArea component
            const event = new CustomEvent('bot-message-spoken', {
              detail: { messageKey }
            });
            window.dispatchEvent(event);

            // Simple speech attempt without complex retry logic
            setTimeout(() => {
              // console.log('useChatbot: Auto-playing bot response after typing animation');
              // Check if we're on the speech input page
              const isOnSpeechInputPage = window.location.pathname === "/speech-input";
              speak(currentBotMessage, botMessage.timestamp, isOnSpeechInputPage);
            }, 500);
          }

          setCurrentBotMessage(null);
          setDisplayedBotText("");
          setShowOptionPanel(true);
        }
      }, 30);

      return () => clearInterval(interval);
    }
  }, [currentBotMessage, autoPlayEnabled, speak]);

  // Auto-greeting effect - triggers after initial messages are loaded
  useEffect(() => {
    // Only trigger auto-greeting if:
    // 1. Initial messages have been loaded
    // 2. We haven't spoken the auto-greeting yet
    // 3. Auto-play is enabled
    // 4. We're on the chatbot page (not speech input page)
    // 5. Chat is open
    const chatIsOpen = Boolean(isOpen);
    const isOnChatbotPage = window.location.pathname === "/chatbot";
    
    if (
      initialMessagesLoaded && 
      !hasSpokenAutoGreeting && 
      autoPlayEnabled && 
      !isWidget &&
      isOnChatbotPage && 
      chatIsOpen
    ) {
      // console.log('useChatbot: Triggering auto-greeting after message load');
      
      // Add a delay to ensure any existing bot messages finish speaking first
      const timer = setTimeout(() => {
        // console.log('useChatbot: Speaking auto-greeting:', greetingMessage);
        speak(greetingMessage, 'auto-greeting-after-load', false);
        setHasSpokenAutoGreeting(true);
      }, 2000); // 2 second delay to ensure everything is settled

      return () => clearTimeout(timer);
    }
  }, [initialMessagesLoaded, hasSpokenAutoGreeting, autoPlayEnabled, isOpen, greetingMessage, speak]);

  // Reset auto-greeting flag when locale changes
  useEffect(() => {
    setHasSpokenAutoGreeting(false);
  }, [locale]);

  // Function to reload messages from API
  const reloadMessagesFromAPI = async () => {
    if (user) {
      const sessionId = getSessionId(user);
      // console.log('useChatbot: Reloading messages from API for session:', sessionId);

      const history = await fetchMessageHistory(sessionId);
      if (history.length > 0) {
        // Sort messages by timestamp to ensure they're in chronological order
        const sortedHistory = [...history].sort((a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );

        // Set all messages to the chat
        setMessages(sortedHistory);
        return true;
      }
    }
    return false;
  };

  return {
    messages,
    currentBotMessage,
    displayedBotText,
    showOptionPanel,
    selectedParentKey,
    loading,
    error,
    autoPlayEnabled,
    setAutoPlayEnabled,
    handleOptionSelect,
    handleBackToOptions,
    handleSubmitMessage,
    resetChatbot,
    loadConversationHistory: reloadMessagesFromAPI, // Provide API-based reload function
  };
};
