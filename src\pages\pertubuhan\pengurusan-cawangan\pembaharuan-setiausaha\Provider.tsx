/* eslint-disable react-refresh/only-export-components */
import {
  useState,
  useEffect,
  useContext,
  createContext,
  PropsWithChildren,
} from "react";
import { useLocation, useParams } from "react-router-dom";
import { useGetIdentity } from "@refinedev/core";
import { FieldValues, useForm, FormProvider } from "react-hook-form";
import { omitKeysFromObject, useQuery } from "@/helpers";
import { useSenaraiContext } from "../../SenaraiContext";

import { IMeetingList, IUser } from "@/types";

interface SecretaryBranchReformContextProps {
  handleIsAgreed: (value: boolean) => void;
  handleSetMeetingList: (value: IMeetingList[]) => void;
  meetingList: IMeetingList[];
  isLoadingSecretary: boolean;
  isEditable: boolean;
  isViewOnly: boolean;
  isAgreed: boolean;
}

const SecretaryBranchReformContext = createContext<
  SecretaryBranchReformContextProps | undefined
>(undefined);

export const useSecretaryBranchReformContext =
  (): SecretaryBranchReformContextProps => {
    const context = useContext(SecretaryBranchReformContext);

    if (!context) {
      throw new Error(
        "useSecretaryBranchReformContext must be used within a SecretaryBranchReformContextProvider"
      );
    }
    return context;
  };

const SecretaryBranchReformProvider: React.FC<PropsWithChildren> = ({
  children,
}) => {
  const location = useLocation();
  const { secretaryId } = useParams();
  const { data: user } = useGetIdentity<IUser>();
  const { societyDetail } = useSenaraiContext();

  const paths = location.pathname.split("/").filter(Boolean);
  const lastPath = paths[paths.length - 1];

  const [isAgreed, setIsAgreed] = useState(false);
  const [meetingList, setMeetingList] = useState<IMeetingList[]>([]);

  const methods = useForm<FieldValues>({
    defaultValues: {
      societyId: "",
      societyNo: "",
      branchId: "",
      branchNo: "",
      titleCode: "",
      committeeName: "",
      gender: "",
      committeeCode: 3,
      jobCode: "",
      citizenshipStatus: 1,
      dateOfBirth: "",
      placeOfBirth: "",
      identityType: 1,
      committeeIcNo: "",
      email: "",
      homeTelNoCode: "",
      homeTelNo: "",
      hpNoCode: "",
      hpNo: "",
      workTelNoCode: "",
      workTelNo: "",
      committeeResidenceAddress: "",
      committeeResidencePostcode: "",
      committeeResidenceAddressStatus: "",
      committeeResidenceCountryCode: "",
      committeeResidenceStateCode: "",
      committeeResidenceDistrictCode: "",
      committeeResidenceCityCode: "",
      committeeResidenceCity: "",
      committeeEmployerName: "",
      committeeEmployerAddress: "",
      committeeEmployerAddressStatus: "",
      committeeEmployerPostcode: "",
      committeeEmployerCountryCode: "",
      committeeEmployerStateCode: "",
      committeeEmployerDistrictCode: "",
      committeeEmployerCity: "",
      oldSecretaryId: "",
      oldSecretaryName: "",
      oldSecretaryIdentificationNumber: "",
      meetingId: "",
      meetingDate: "",
      meetingType: "",
      reasonOfChange: "",
      otherReason: "",
      batalFlat: "",
      pegHarta: "",
      otherPosition: "",
      memberCount: "",
      ajkCount: "",
    },
  });
  const { setValue } = methods;

  const {
    data: secretaryResponse,
    refetch: fetchSecretary,
    isLoading: isLoadingSecretary,
  } = useQuery({
    url: `society/secretary/principal/${secretaryId}`,
    autoFetch: false,
    onSuccess: (res) => {
      const data = res?.data?.data;
      if (!data) return;

      const keysToSkip = ["workTel", "hpNo", "homeTel"];
      const filteredData = omitKeysFromObject(data, keysToSkip);

      Object.entries(filteredData).forEach(([key, value]) => {
        setValue(key as keyof FieldValues, value);
      });

      ["workTel", "hpNo", "homeTel"].forEach((key) => {
        const [code = "", number = ""] = data[key]?.split(" ") ?? [];
        setValue(`${key}Code`, code);
        setValue(key, number);
      });
    },
  });

  const secretaryDetailData = secretaryResponse?.data?.data ?? null;

  const VIEW_ONLY_STATUSES = [2, 3, 4];
  const EDITABLE_STATUSES = [1, 36];

  const isViewOnly = VIEW_ONLY_STATUSES.includes(
    secretaryDetailData?.applicationStatusCode
  );
  const isEditable = EDITABLE_STATUSES.includes(
    secretaryDetailData?.applicationStatusCode
  );

  const handleIsAgreed = (value: boolean) => setIsAgreed(value);
  const handleSetMeetingList = (meetingList: IMeetingList[]) =>
    setMeetingList(meetingList);

  useEffect(() => {
    if (secretaryId) fetchSecretary();
  }, [secretaryId]);

  useEffect(() => {
    if (!user) return;

    setValue("identityType", user?.identificationType ?? "");
    setValue("committeeIcNo", user?.identificationNo ?? "");
  }, [user]);

  useEffect(() => {
    if (!societyDetail) return;

    setValue("societyId", societyDetail.id);
    setValue("societyNo", societyDetail.societyNo);
  }, [societyDetail]);

  return (
    <SecretaryBranchReformContext.Provider
      value={{
        isEditable,
        isViewOnly,
        isAgreed,
        meetingList,
        handleIsAgreed,
        handleSetMeetingList,
        isLoadingSecretary,
      }}
    >
      <FormProvider {...methods}>{children}</FormProvider>
    </SecretaryBranchReformContext.Provider>
  );
};

export default SecretaryBranchReformProvider;
