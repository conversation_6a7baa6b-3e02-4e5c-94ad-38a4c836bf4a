import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ExtensionItems {
  societyId: string | number;  
  societyNo: string | number; 
  branchId: string | number;
  branchNo: string | number;
}

// Provide a proper initial state
const initialState: ExtensionItems = {
  societyId: "",
  societyNo: "",
  branchId: "",
  branchNo: "",
};

export const extensionTimeSlice = createSlice({
  name: "extensionTime",
  initialState,
  reducers: { 
    setExtensionItems: (state, action: PayloadAction<Partial<ExtensionItems>>) => {
      Object.assign(state, action.payload); 
    },
  },
});

export const { setExtensionItems } = extensionTimeSlice.actions;
export default extensionTimeSlice.reducer;