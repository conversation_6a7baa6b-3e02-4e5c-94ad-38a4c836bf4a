import React, { useState } from "react";
import {
  Box,
  Paper,
  Grid,
  Typography,
  TextField,
  Button,
  Alert,
  IconButton,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import CampaignIcon from "@mui/icons-material/Campaign";
import CloseIcon from "@mui/icons-material/Close";
import CreateAset from "../../../../pernyata-tahunan/aset-dan-liabiliti/CreateAset";
import CreateLiabiliti from "../../../../pernyata-tahunan/aset-dan-liabiliti/CreateLiabiliti";
import SectionHeader from "@/components/header/section/SectionHeader";
import StepperComponent from "@/components/stepper/StepperComponent";
import { steps } from "../../../../pembubaran/add-pembubaran/constant";

export const CreatePembubaran: React.FC = () => {
  const { t } = useTranslation();

  const [tanggalPembentangan, setTanggalPembentangan] = useState("");
  const [mesyuaratAgung, setMesyuaratAgung] = useState("");
  const [tanggalMesyuarat, setTanggalMesyuarat] = useState("");
  const [jumlahKehadiran, setJumlahKehadiran] = useState("");
  const [jumlahBolehMengundi, setJumlahBolehMengundi] = useState("");

  const labelStyle = {
    fontWeight: "bold",
    marginBottom: "8px",
  };

  return (
    <>
      <StepperComponent steps={steps} activeStep={0} />

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          mt: 4,
          border: "1px solid #D9D9D9",
          backgroundColor: "#FCFCFC",
          borderRadius: "14px",
        }}
      >
        <Box>
          <Alert
            severity="info"
            sx={{ mb: 4 }}
            icon={<CampaignIcon />}
            action={
              <IconButton aria-label="close" color="inherit" size="small">
                <CloseIcon fontSize="inherit" />
              </IconButton>
            }
          >
            {t("pembubaranNote")}
          </Alert>
          <SectionHeader
            title={t("liquidationInformation")}
            sx={{ mb: 2 }}
          ></SectionHeader>

          <Grid mb={4} container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("selectAnnualStatementPresentationDate")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                select
                fullWidth
                value={tanggalPembentangan}
                onChange={(e) => setTanggalPembentangan(e.target.value)}
                required
              >
                {/* Tambahkan opsi tanggal di sini */}
              </TextField>
            </Grid>

            <Grid item xs={12}>
              <Paper elevation={1} sx={{ p: 2, mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("generalMeeting")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      value={mesyuaratAgung}
                      onChange={(e) => setMesyuaratAgung(e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>{t("meetingDate")}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      type="date"
                      value={tanggalMesyuarat}
                      onChange={(e) => setTanggalMesyuarat(e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("registeredMemberAttendanceCount")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      type="number"
                      value={jumlahKehadiran}
                      onChange={(e) => setJumlahKehadiran(e.target.value)}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("eligibleVoterCount")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      type="number"
                      value={jumlahBolehMengundi}
                      onChange={(e) => setJumlahBolehMengundi(e.target.value)}
                    />
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          </Grid>

          <CreateAset t={t} />

          <CreateLiabiliti t={t} />

          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <Button
              variant="contained"
              sx={{
                backgroundColor: "#8DC6C6",
                color: "white",
                "&:hover": { backgroundColor: "#7AB5B5" },
              }}
            >
              {t("next")}
            </Button>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default CreatePembubaran;
