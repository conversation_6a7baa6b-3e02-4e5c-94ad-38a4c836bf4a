import React, { useRef, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import ButtonText from "../button/ButtonText";
import OutlinedInput from "./OutlinedInput";
import type { ButtonProps, InputProps } from "@mui/material";
import { styled } from "@mui/material/styles";
import { TFunction } from "i18next";

interface FileInputProps extends InputProps {
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  rawFileProps?: React.DetailedHTMLProps<
    React.InputHTMLAttributes<HTMLInputElement>,
    HTMLInputElement
  >;
  buttonText?: string;
  buttonProps?: ButtonProps;
  t?: TFunction;
  acceptedFormats?: string[];
  maxSize?: string;
  uploadedFilesRef?: React.RefObject<(File | null)[]>;
  currentIndex?:number
}

const FileFormatBadge = styled(Box)(({ theme }) => ({
  backgroundColor: "#EAF5FF",
  color: "#00ACC1",
  fontSize: "12px",
  fontWeight: 500,
  padding: "4px 8px",
  borderRadius: "12px",
  marginRight: theme.spacing(1),
  display: "inline-block",
}));

export const FileInput: React.FC<FileInputProps> = ({
  rawFileProps: { onChange: onRawFileChange, ...rawFileProps } = {},
  buttonText = "uploadButton",
  buttonProps: { sx: buttonSx, ...buttonProps } = {},
  onChange,
  name,
  t,
  acceptedFormats = ["PDF", "DOCX", "TXT"], // Default accepted formats
  maxSize = "< 25 MB",
  uploadedFilesRef,
  currentIndex,
  ...props
}) => {
  const [documentName, setDocumentName] = useState("");
  const [error, setError] = useState<string | null>(null); // To store error messages
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonUploadOnClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]; 
    if (file) {
      // Get the file extension and convert it to lowercase
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;

      // Check if the lowercase file extension is in accepted formats (also convert accepted formats to lowercase)
      if (fileExtension && acceptedFormats.map(format => format.toLowerCase()).includes(fileExtension)) {

        // check same file
        if(uploadedFilesRef){
          const isDuplicate = uploadedFilesRef?.current?.some((existingFile, i) => {
            return (
              existingFile &&
              existingFile.name === file.name &&
              existingFile.size === file.size &&
              i !== currentIndex  
            );
          });
          if (isDuplicate) {
            alert("Anda tidak boleh memuat naik fail yang sama");
            event.target.value = "";  
            return;
          } 
        }
         
        setDocumentName(file.name);
        setError(null); // Clear any previous errors
      } else {
        setError(`Format fail tidak dibenarkan. Sila muat naik fail dalam format JPEG, JPG atau PNG sahaja.`);
        setDocumentName(""); // Clear document name
        return; // Stop processing the file if it's not accepted
      }
    }


    // If there's a raw file change handler, call it
    if (onRawFileChange) {
      onRawFileChange(event);
    }

    // Call the passed onChange prop if available
    if (onChange) {
      onChange(event);
    }
  };

  return (
    <Box
      sx={{
        border: "1px solid #e0e0e0",
        borderRadius: "8px",
        padding: "24px",
        textAlign: "center",
        cursor: "pointer",
      }}
      onClick={handleButtonUploadOnClick}
    >
      {/* Input File Hidden */}
      <input
        name={name}
        type="file"
        ref={fileInputRef}
        style={{ display: "none" }}
        onChange={handleFileChange}
        {...rawFileProps}
      />

      {/* Icon Upload */}
      <img src="/file-add.svg" alt="" />

      {/* Text Upload */}
      <Typography
        variant="body1"
        sx={{ fontWeight: 500, color: "#666666", mt: 1 }}
      >
        {documentName || (t ? t(buttonText) : "Click to upload")}
      </Typography>

      {/* Error Message */}
      {error && (
        <Typography sx={{ fontSize: "12px", color: "red", mt: 1 }}>
          {error}
        </Typography>
      )}

      {/* Accepted Formats and Max Size */}
      <Box sx={{ mt: 2 }}>
        {acceptedFormats.map((format, index) => (
          <FileFormatBadge key={index}>{format}</FileFormatBadge>
        ))}
        <FileFormatBadge>{maxSize}</FileFormatBadge>
      </Box>
    </Box>
  );
};

export default FileInput;
