import React from "react";
import {Box, LinearProgress, Typography} from "@mui/material";
import {TrainingEnums} from "@/helpers";
import {ClauseProps} from "@/pages/pertubuhan/pengurusan-perlembagaan/UpdatePindaanPerlembagaan";
import {ButtonPrimary} from "@/components";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import {DurationIcon} from "@/components/icons/duration";
import {PaticipantsIcon} from "@/components/icons/participants";
import {TrainingRequiredIcon} from "@/components/icons/trainingRequired";

interface TrainingFragmentProps {
  item: any;
  type: number;
  width?: string;
}

const TrainingFragment: React.FC<TrainingFragmentProps> = ({item, type, width="20%"}) => {
  const {t, i18n} = useTranslation();
  const navigate = useNavigate();

  const hour = Math.floor(item.duration/60);
  const minute = item.duration % 60;

  console.log("item",item);

  const goToDetails = () => {
    navigate("maklumat", {state: {item: item}})
  }

  const handleContinue = () => {
    navigate("/latihan/info", {state:{item:item}});
  }

  return (
    <>
      <Box
        sx={{
          p: 2,
          //pl: {xs: 2, md: 6},
          border: "1px solid #D9D9D9",
          borderRadius: "14px",
          //flex: 1,
          m: 1,
          width: width,
        }}
      >
        <Box
          sx={{
            //height: "100%",
            height: "150px",
            zIndex: 0,
            backgroundImage: `url(${item.poster ?? '/latihanSample/images5.jpg'})`,
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center center",
            justifyContent: "left",
            display: "flex",
            px: 1,
            py: 1,
            borderRadius: 2.5,
          }}>
          <Box
            sx={{
              height: "32px",
              borderRadius: 1.5,
              backgroundColor: "#fff",
              justifyContent: "space-evenly",
              display: "flex",
              p: 1
              //px: 2,
              //py: 2,
              //mb: 1,
            }}
          >
            <TrainingRequiredIcon/>
            <Typography
              sx={{
                color: "#666666",
                //pt: 3,
                fontWeight: "400",
                fontSize: 10,
              }}
            >
              {item.required ? "Latihan Wajib" : ""}
            </Typography>
          </Box>
        </Box>
        <Typography
          sx={{
            color: "#666666",
            //pt: 3,
            m: 1,
            fontWeight: "400",
            fontSize: 12,
          }}
        >
          {item.type}
        </Typography>
        <Typography
          sx={{
            color: "#666666",
            //pt: 3,
            m: 1,
            fontWeight: "500",
            fontSize: 14,
          }}
        >
          {item.title}
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            width: "45%"
          }}
        >
          <Box
            sx={{
              //display:"block",
              backgroundColor: `${item.levelColor}`,
              borderRadius: 5,
              margin: "auto",
              //ml:1,
              flex:1,
              width:"60%",
              py: 1,
              //px: 2
            }}
          >
            <Typography
              sx={{
                color: "#fff",
                lineHeight: "100%",
                fontWeight: "500",
                fontSize: 5,
                textAlign: "center"
              }}
            >
              {item.difficultyLevel}
            </Typography>
          </Box>
          <Box
            sx={{flex:1,py: 1, px: 1}}
          >
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontWeight: "500",
                lineHeight: "18px",
                fontSize: 9,
              }}
            >
              {`${item.points ?? 0} Points`}
            </Typography>
          </Box>
        </Box>
        <Box sx={{
          p: 0,
          m: 0,
          height: 20,
          display: "flex",
          flexDirection: "row",
        }}>
          <Box
            sx={{py: 1, px: 1}}
          >
            <PaticipantsIcon/>
          </Box>
          <Box
            sx={{py: 1, px: 0}}
          >
            <Typography
              sx={{
                color: "#666666",
                lineHeight: "18px",
                fontWeight: "400",
                fontSize: 10,
              }}
            >
              {`${item.participants ?? 0} Mendaftar`}
            </Typography>
          </Box>
        </Box>
        <Box sx={{
          p: 0,
          m: 0,
          height: 30,
          display: "flex",
          flexDirection: "row",
        }}>
          <Box
            sx={{py: 1, px: 1}}
          >
            <DurationIcon/>
          </Box>
          <Box
            sx={{py: 1, px: 0}}
          >
            <Typography
              sx={{
                color: "#666666",
                lineHeight: "18px",
                fontWeight: "400",
                fontSize: 10,
              }}
            >
              {`${hour} Jam ${minute} Minit`}
            </Typography>
          </Box>
        </Box>
        {type === TrainingEnums.History || type === TrainingEnums.All ?
          <Typography
            sx={{
              ml: 4,
              color: "#3483F9",
              lineHeight: "18px",
              fontWeight: "400",
              fontSize: 10,
              textDecoration: "underline",
              cursor: "pointer",
            }}
            onClick={goToDetails}
          >
            Info lanjut
          </Typography>
          : <></>}
        {type === TrainingEnums.Assigned ? (
          <>
            <Box sx={{
              mt: 5,
              display: "flex",
              flexDirection: "row",
            }}>
              <LinearProgress
                variant="determinate"
                value={(item.currentStep/item.totalStep)*100}
                sx={{
                  flex: 1,
                  height: 15,
                  borderRadius: 3,
                  backgroundColor: "#E0E0E0",
                  "& .MuiLinearProgress-bar": {
                    backgroundColor: "#00BCD4",
                    borderRadius: 3,
                  },
                }}
              />
              <Typography
                sx={{
                  ml: 5,
                  color: "#666666",
                  lineHeight: "18px",
                  fontWeight: "400",
                  fontSize: 16,
                }}
              >
                {`${(item.currentStep/item.totalStep)*100} %`}
              </Typography>
            </Box>
            { item.completionStatus !== "COMPLETED" ?
            <Box sx={{mt: 2, display: "flex", justifyContent: "center"}}>
              <ButtonPrimary
                variant="outlined"
                sx={{
                  bgcolor: "white",
                  "&:hover": {bgcolor: "white"},
                  color: "#0CA6A6",
                  fontWeight: "400",
                }}
                onClick={handleContinue}
              >
                {t("continue")}
              </ButtonPrimary>
            </Box> : <></> }
          </>
        ) : <></>}
      </Box>
    </>
  );
}

export default TrainingFragment;
