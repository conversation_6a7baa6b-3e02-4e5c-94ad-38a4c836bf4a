import {
  Box,
  Grid,
  <PERSON><PERSON>ield,
  Ty<PERSON>graphy,
  IconButton,
  LinearProgress,
} from "@mui/material";
import { t } from "i18next";
import React, { useEffect, useRef, useState } from "react";
import { API_URL } from "../../../../api";
import useUploadDocument from "../../../../helpers/hooks/useUploadDocument";
import { ButtonPrimary } from "../../../../components/button";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { AJ<PERSON>paparan } from "@/redux/ajkReducer";
import {
  DocumentUploadType,
  formatArrayDate,
  getLocalStorage,
  ListGelaran,
  MALAYSIA,
  getIdTypeLabel,
  getGenderLabel,
  getStateNameById,
  getCitizenshipLabel,
  ExternalStateCodes,
  OrganisationPositions
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";

const labelStyle = {
  fontWeight: "500!important",
  marginBottom: "8px",
  fontSize: "14px",
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

function PaparanAjk() {
  const ajkPaparan = useSelector(AJKpaparan);

  const [district, setDistrict] = useState("");
  const [state, setState] = useState("");
  const { id } = useParams();

  const addressList = getLocalStorage("address_list", null);
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const navigate = useNavigate();
  const getTitleName = (val = null) => {
    if (!val) return "Unknown Title"; // Handle null or undefined titleId
    const gelaran = ListGelaran.find((item) => item.value === val);
    return gelaran ? gelaran.label : "Unknown Title"; // Fallback for unknown IDs
  };

  const getDistrict = (val = null) => {
    const address = addressList
      .filter((items: any) => items.id === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));

    return address?.[0]?.label || "-";
  };

  const [formData, setFormData] = useState({
    position: "",
    idType: "",
    icNo: "",
    title: "",
    name: "",
    gender: "",
    citizen: "",
    dateOfBirth: "",
    placeOfBirth: "",
    pekerjaan: "",
    residentialAddress: "",
    negeri: "",
    daerah: "",
    bandar: "",
    poskod: "",
    emel: "",
    phoneNumber: "",
    nomborTelefonRumah: "",
    nomborTelefonPejabat: "",
    employerName: "",
    employerAddress: "",
    employerCountryCode: "",
    employerDistrict: "",
    employerCity: "",
    employerPostcode: "",
  });

  useEffect(() => {
    if (ajkPaparan) {
      console.log(ajkPaparan)
      const digits = Number(ajkPaparan?.identificationNo.substring(6, 8));
      const matchingState = ExternalStateCodes.find(
        (state) => state.value === digits
      );

      setFormData({
        ...formData,
        position: t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === parseInt(ajkPaparan?.designationCode)
            )?.label || "-"
          }`
        ),
        idType: t(getIdTypeLabel(ajkPaparan?.identificationType)),
        icNo: ajkPaparan?.identificationNo,
        title: ajkPaparan?.designationCode
          ? getTitleName(ajkPaparan?.designationCode)
          : "-",
        name: ajkPaparan?.name,
        gender: t(getGenderLabel(ajkPaparan?.gender)),
        citizen: ajkPaparan?.nationalityStatus
          ? t(getCitizenshipLabel(Number(ajkPaparan.nationalityStatus)))
          : "-",
        dateOfBirth: ajkPaparan?.dateOfBirth
          ? formatArrayDate(ajkPaparan?.dateOfBirth, "DD-MM-YYYY")
          : "",
        placeOfBirth: matchingState?.label ?? "-",
        pekerjaan: ajkPaparan?.jobCode,
        residentialAddress: ajkPaparan?.residentialAddress,
        negeri: ajkPaparan?.residentialStateCode
          ? getStateNameById(ajkPaparan.residentialStateCode)
          : "-",
        daerah: ajkPaparan?.residentialDistrictCode
          ? getDistrict(ajkPaparan?.residentialDistrictCode)
          : "-",
        bandar: ajkPaparan?.residentialCity,
        poskod: ajkPaparan?.residentialPostcode,
        emel: ajkPaparan?.email,
        phoneNumber: ajkPaparan?.phoneNumber,
        nomborTelefonRumah: ajkPaparan?.telephoneNumber,
        nomborTelefonPejabat: ajkPaparan?.telephoneNumber,
        employerName: ajkPaparan?.employerName,
        employerAddress: ajkPaparan?.employerAddress,
        employerCountryCode: ajkPaparan?.employerStateCode
          ? getStateNameById(ajkPaparan.employerStateCode)
          : "-",
        employerDistrict: ajkPaparan?.employerDistrict
          ? getDistrict(ajkPaparan?.employerDistrict)
          : "-",
        employerCity: ajkPaparan?.employerCity,
        employerPostcode: ajkPaparan?.employerPostcode,
      });

      setDistrict(ajkPaparan?.employerDistrict);
      setState(ajkPaparan?.employerStateCode);
    }
  }, [ajkPaparan]);

  const goback = () => {
    navigate(-1);
  };

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            px: 2,
            py: 4,
          }}
        >
          {societyDataRedux?.societyName}
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        {/*  */}
        <Box
          sx={{
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairman")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("jawatan")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  value={formData.position}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      position: e.target.value,
                    })
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairmanPersonalInfo")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("idType")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      idType: e.target.value,
                    })
                  }
                  value={formData.idType}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("idNumber")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      icNo: e.target.value,
                    })
                  }
                  value={formData.icNo}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("title")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      title: e.target.value,
                    })
                  }
                  value={formData.title}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("fullName")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      name: e.target.value,
                    })
                  }
                  value={formData.name}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("gender")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      gender: e.target.value,
                    })
                  }
                  value={formData.gender}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("citizenship")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      citizen: e.target.value,
                    })
                  }
                  value={formData.citizen}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("dateOfBirth")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      dateOfBirth: e.target.value,
                    })
                  }
                  value={formData.dateOfBirth}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("placeOfBirth")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      placeOfBirth: e.target.value,
                    })
                  }
                  value={formData.placeOfBirth}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("occupation")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      pekerjaan: e.target.value,
                    })
                  }
                  value={formData.pekerjaan}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("residentialAddress")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  rows={4}
                  multiline
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      residentialAddress: e.target.value,
                    })
                  }
                  value={formData.residentialAddress}
                />
              </Grid>

              {/*=========== negeri section ========*/}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("negeri")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      negeri: e.target.value,
                    })
                  }
                  value={formData.negeri}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("daerah")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      daerah: e.target.value,
                    })
                  }
                  value={formData.daerah}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("bandar")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      bandar: e.target.value,
                    })
                  }
                  value={formData.bandar}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("poskod")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      poskod: e.target.value,
                    })
                  }
                  value={formData.poskod}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("emel")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      emel: e.target.value,
                    })
                  }
                  value={formData.emel}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("phoneNumber")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      phoneNumber: e.target.value,
                    })
                  }
                  value={formData.phoneNumber}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("nomborTelefonRumah")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      nomborTelefonRumah: e.target.value,
                    })
                  }
                  value={formData.nomborTelefonRumah}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("nomborTelefonPejabat")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      nomborTelefonPejabat: e.target.value,
                    })
                  }
                  value={formData.nomborTelefonPejabat}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("employerInfo")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("employerName")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerName: e.target.value,
                    })
                  }
                  value={formData.employerName}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("employerAddress")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerAddress: e.target.value,
                    })
                  }
                  value={formData.employerAddress}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("negeri")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerCountryCode: e.target.value,
                    })
                  }
                  value={formData.employerCountryCode}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("daerah")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerDistrict: e.target.value,
                    })
                  }
                  value={formData.employerDistrict}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("bandar")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerCity: e.target.value,
                    })
                  }
                  value={formData.employerCity}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("poskod")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerPostcode: e.target.value,
                    })
                  }
                  value={formData.employerPostcode}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}

        <Box>
          <Box sx={{ textAlign: "left", mt: 2 }}>
            {ajkPaparan?.id && (
              <FileUploader
                title={t("ajkEligibilityCheck")}
                type={DocumentUploadType.CITIZEN_COMMITTEE}
                societyId={ajkPaparan?.societyId}
                icNo={ajkPaparan?.identificationNo}
                societyCommitteeId={ajkPaparan?.id}
                disabled={true}
                validTypes={[
                  "text/plain",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "application/pdf",
                ]}
              />
            )}
          </Box>
        </Box>

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrimary onClick={goback}>{t("back")}</ButtonPrimary>
        </Box>
      </Box>
    </Box>
  );
}

export default PaparanAjk;
