import React from "react";
import { useTranslation } from "react-i18next";
import { useCallback, useEffect, useMemo } from "react";
import {
  useWatch,
  useFormContext,
  useFieldArray,
  FieldValues,
} from "react-hook-form";
import { useDebounce } from "@/helpers";

import { Box, Typography, useTheme } from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import FormFieldRow from "@/components/form-field-row";
import Label from "@/components/label/Label";
import MemberRow from "./MemberRow";
import AttendeesInput from "./AttendeesInput";

interface MemberAttendancesProps {
  disabledState: boolean;
  isEditable: boolean;
  isViewOnly: boolean;
  meetingDetail?: boolean;
}

export interface IMember {
  id?: number;
  tempId?: string;
  name: string;
  position: string;
  status?: number;
}

interface IFormContext extends FieldValues {
  totalAttendees: number;
}

const MemberAttendances: React.FC<MemberAttendancesProps> = React.memo(
  ({ disabledState, isEditable, isViewOnly, meetingDetail }) => {
    const { t, i18n } = useTranslation();
    const theme = useTheme();
    const isMyLanguage = i18n.language === "my";

    const primary = theme.palette.primary.main;

    return (
      <Box
        sx={{
          borderRadius: "10px",
          padding: "41px 25px 25px",
          border: "0.5px solid #DADADA",
          marginBottom: "15px",
        }}
      >
        <Typography
          fontSize="14px"
          color={primary}
          fontWeight={500}
          marginBottom="20px"
        >
          {isMyLanguage
            ? "Kehadiran Ahli Mesyuarat"
            : "Attendance of Meeting Members"}
        </Typography>

        <FormFieldRow
          label={<Label text={t("jumlahKehadiranAhliMesyuarat")} />}
          value={<AttendeesInput disabledState={disabledState} />}
        />
      </Box>
    );
  }
);

export default MemberAttendances;
