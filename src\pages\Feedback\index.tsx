import { Box, Icon<PERSON>utton, Stack, Typography } from "@mui/material";
import { t } from "i18next";
import React, { useState } from "react";
import i18n from "../../i18n";
import MenuIcon from "@mui/icons-material/Menu";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import deco from "/feedbackBoxIcon2.svg";
import deco1 from "/feedbackBoxIcon1.svg";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { useNavigate } from "react-router-dom";

const FeedbackBox: React.FC<{
  imageIcon: string;
  title: string;
  onClick: () => void;
}> = ({ imageIcon, title, onClick }) => (
  <Box
    sx={{
      width: { xs: "100%", sm: "375px" },
      height: "175px",
      bgcolor: "#FFFFFF33",
      border: "1px solid #fff",
      borderRadius: "15px",
      color: "#fff",
      p: "20px",
      cursor: "pointer",
      position: "relative",
    }}
    onClick={onClick}
  >
    <Box
      sx={{
        position: "absolute",
        right: "30px",
        top: "50px",
      }}
    >
      <img
        src={imageIcon}
        alt={deco}
        style={{
          width: "100px",
          height: "100px",
        }}
      />
    </Box>
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        justifyContent: "space-between",
        height: "100%",
        gap: 4,
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontFamily: "Poppins, sans-serif",
          fontSize: "16px",
          fontWeight: 400,
          lineHeight: "24px",
          color: "#fff",
          display: "flex",
          width: "80%",
        }}
      >
        {title}
      </Typography>

      <Typography
        sx={{
          fontFamily: "Poppins, sans-serif",
          fontSize: "14px",
          fontWeight: 400,
          lineHeight: "24px",
          color: "#fff",
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        {t("Continue")} <ChevronRightIcon sx={{ fontSize: "16px" }} />
      </Typography>
    </Box>
  </Box>
);

const FeedbackSelection = () => {
  const navigate = useNavigate();
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: { xs: "100%", sm: "398px" },
        gap: 2,
      }}
    >
      <FeedbackBox
        imageIcon={deco1}
        title={t("cadanganMaklumbalasbaru")}
        onClick={() => navigate("/feedback/cadanganBaru")}
      />

      <FeedbackBox
        imageIcon={deco}
        title={t("SemakCadangan")}
        onClick={() => navigate("/feedback/cadanganSemak")}
      />
    </Box>
  );
};

function Feedback() {
  const navigate = useNavigate();

  const [languageAnchorEl, setLanguageAnchorEl] = useState<null | HTMLElement>(
    null
  );

  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] =
    useState<null | HTMLElement>(null);

  // Add this function to get the correct language display
  const getLanguageDisplay = (lang: string) => {
    switch (lang.toLowerCase()) {
      case "bm":
      case "ms":
      case "my":
        return "BM";
      case "en":
        return "EN";
      default:
        return lang.toUpperCase();
    }
  };

  const handleLanguageMenuClose = () => {
    setLanguageAnchorEl(null);
  };

  const handleLanguageMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMobileMenuAnchorEl(event.currentTarget);
  };

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    handleLanguageMenuClose();
  };

  const goBack = () => {
    console.log("hit");
    if (localStorage.getItem("refine-auth")) {
      if (localStorage.getItem("portal") === "1") {
        navigate("/");
      } else if (localStorage.getItem("portal") === "2") {
        navigate("/meja-bantuan/aduan-makluman");
      } else {
        navigate(-1);
      }
    } else {
      navigate("/login");
    }
  };

  return (
    <>
      <Box
        sx={{
          minHeight: "100vh",
          background: "#F1F4FA",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          position: "relative",
          backgroundImage: 'url("/bg-feedback.jpg")',
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "center center",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            background: "rgba(0, 0, 0, 0.3)",
            zIndex: 1,
          },
          "& > *": {
            position: "relative",
            zIndex: 2,
          },
        }}
      >
        <Box
          sx={{
            flexGrow: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: { xs: "20px 5%", sm: "55px 10%" },
            width: "100%",
          }}
        >
          <Box
            sx={{
              position: "relative",
              display: "flex",
              flexDirection: { xs: "column", md: "row" },
              justifyContent: "space-between",
              alignItems: { xs: "center", md: "flex-start" },
              width: "100%",
              maxWidth: "1200px",
            }}
          >
            <Box
              sx={{
                color: "white",
                maxWidth: { xs: "100%", md: "50%" },
                mb: { xs: 4, md: 0 },
                textAlign: { xs: "center", md: "left" },
                overflow: "hidden",
                width: "100%",
              }}
            >
              <Box
                sx={{
                  mt: { xs: "20px", md: "150px" },
                  width: "100%",
                  maxWidth: "427px",
                  opacity: 1,
                  ml: { xs: 0, md: 2 },
                }}
              >
                <Typography
                  variant="h4"
                  component="h1"
                  gutterBottom
                  sx={{
                    mb: 0,
                    color: "#fff",
                    fontFamily: "Poppins, sans-serif",
                    fontSize: { xs: "25px" },
                    fontWeight: 500,
                    lineHeight: "30px",
                  }}
                >
                  {t("feedbackHerotext")}
                </Typography>
              </Box>
            </Box>

            <FeedbackSelection />

            {/* language select */}
            <Stack
              direction="row"
              sx={{
                position: "absolute",
                left: "0",
                top: { xs: "-10%", sm: "-20%" },
                mb: { xs: 3, sm: "36px" },
                width: "100%",
                opacity: 1,
                justifyContent: "space-between",
                alignItems: "center",
                px: { xs: 0, sm: 2 },
              }}
            >
              <Stack
                direction="row"
                spacing={2}
                display="flex"
                alignItems="center"
                alignContent={"center"}
              >
                <Box
                  sx={{
                    color: "#fff",
                    cursor: "pointer",
                    display: "flex",
                    alignItems: "center",
                  }}
                  onClick={goBack}
                >
                  <ChevronLeftIcon />
                </Box>
                <Typography
                  onClick={handleLanguageMenuClick}
                  sx={{
                    color: "#fff",
                    cursor: "pointer",
                    fontWeight: 700,
                    fontSize: "16px",
                    fontFamily: "Inter, sans-serif",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {getLanguageDisplay(i18n.language)}
                </Typography>
              </Stack>
            </Stack>
          </Box>
        </Box>
      </Box>

      {/* Language Menu */}
      <Menu
        anchorEl={languageAnchorEl}
        open={Boolean(languageAnchorEl)}
        onClose={handleLanguageMenuClose}
      >
        <MenuItem onClick={() => changeLanguage("en")}>English</MenuItem>
        <MenuItem onClick={() => changeLanguage("bm")}>Bahasa Melayu</MenuItem>
      </Menu>
    </>
  );
}

export default Feedback;
