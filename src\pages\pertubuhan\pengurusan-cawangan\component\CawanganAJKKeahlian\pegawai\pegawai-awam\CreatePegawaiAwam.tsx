import {
  Box,
  Typography,
  Grid,
  FormControlLabel,
  Checkbox,
  TextField,
  Autocomplete,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
} from "@/helpers/enums";
import { useEffect, useState } from "react";
import { API_URL } from "@/api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { PublicOfficer } from "@/pages/pertubuhan/pernyata-tahunan/interface";
import { Controller, useForm } from "react-hook-form";
import Input from "@/components/input/Input";
import { usejawatankuasaContext } from "../../jawatankuasa/jawatankuasaProvider";
import dayjs from "dayjs";
import { useSelector } from "react-redux";

export const CreatePegawaiAwam: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { id: societyId } = useParams();
  const location = useLocation();
  const officerId = location.state?.officerId;
  const view = location.state?.view;
console.log(officerId)
  //@ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const renderFormField = (
    label: string,
    component: React.ReactNode,
    required = false
  ) => (
    <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{ color: "#666666", fontWeight: "400 !important", fontSize: 14 }}
        >
          {label}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        {component}
      </Grid>
    </Grid>
  );

  const [businessState, setBusinessState] = useState("");
  const [businessDistrict, setBusinessDistrict] = useState("");

  const handleBack = () => {
    navigate(-1);
  };

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = data?.data?.data || [];

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const {
    publicOfficer,
    fetchPublicOfficer,
    activeMembers,
    isFetchActiveMembersLoading,
    fetchActiveMembers,
  } = usejawatankuasaContext();

  useEffect(() => {
    if (officerId && isFetchActiveMembersLoading) { 
      fetchPublicOfficer();
      fetchActiveMembers();
      if (officerId) {  
        if (publicOfficer) 
          Object.entries(publicOfficer).forEach(([key, value]) => { 
            if (key == "citizenshipStatus") {
              setValue("citizenshipStatus", Number(value));
            } else {
              setValue(key, value);
            }
          }); 
        if (publicOfficer?.name) { 
          setValue("committeeName", publicOfficer?.name);
        } 
      }
    } else {
      fetchActiveMembers();
    }
  }, [fetchPublicOfficer, fetchActiveMembers]);

  const form = useForm<PublicOfficer | any>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    reset,
  } = form;

  const { mutate: saveOfficer, isLoading: isSaveTrustee } = useCustomMutation();

  const onSubmit = (data: any) => {
    saveOfficer(
      {
        url: officerId
          ? `${API_URL}/society/public_officer/${officerId}`
          : `${API_URL}/society/public_officer/branch/create`,
        method: officerId ? "put" : "post",
        values: {
          ...data,
          societyId: societyId,
          branchId: branchDataRedux.id,
          branchNo: branchDataRedux.branchNo,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: (response) => {
          const id = response?.data?.data.id;
          const createdDate = response?.data?.data.createdDate;
          navigate("bayaran", {
            state: {
              createdDate,
              societyId: societyId,
              publicOfficerId: id,
              publicOfficerName: getValues("name"),
            },
          });
        },
      }
    );
  };

  useEffect(() => {
    const type = getValues("identificationType");
    if (!view && type === "1") {
      const idNoString =
        typeof watch("identificationNo") === "number"
          ? String(watch("identificationNo"))
          : watch("identificationNo") ?? ""; // Ensure string

      if (idNoString.length >= 6) {
        const parsedDate = dayjs(idNoString.substring(0, 6), "YYMMDD");

        if (parsedDate.isValid()) {
          setValue("dateOfBirth", parsedDate.toDate());
        }
      }
    }
  }, [watch("identificationNo")]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("pilihanPegawaiAwam")}
          </Typography>

          <Controller
            name="jenisPegawai"
            control={control}
            defaultValue={getValues("identificationType")}
            render={({ field }) => (
              <Input
                disabled
                {...field}
                required
                label={t("jenisPegawai")}
                value={t("pegawaiAwam")}
              />
            )}
          />
          {!view ? (
            <Controller
              name="committeeName"
              control={control}
              // defaultValue={getValues("committeeName")}
              render={({ field }) => {
                const options =
                  activeMembers?.map((item: any) => ({
                    label: item.name || "-",
                    value: item.name,
                    data: item,
                  })) || [];
                return (
                  <Grid
                    container
                    spacing={2}
                    alignItems={"flex-start"}
                    sx={{
                      ...{ mb: 1 },
                      ...{ mb: 1 },
                      ...{ alignItems: "flex-start" },
                    }}
                  >
                    <Grid item xs={12} sm={4}>
                      <Typography
                        variant="body1"
                        sx={{
                          color: "#666666",
                          fontWeight: "400 !important",
                          fontSize: "14px",
                        }}
                      >
                        {t("namaAhliPertubuhan")}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={8}> 
                      <Autocomplete
                        fullWidth
                        options={options}
                        disabled={!!officerId}
                        value={
                          options.find(
                            (opt: any) => opt.value === field.value
                          ) || null
                        }
                        getOptionLabel={(option) => option.label}
                        isOptionEqualToValue={(option, value) =>
                          option.name === value.name
                        }
                        onChange={(_, selectedOption) => {
                          if (!selectedOption) return;

                          const selected = selectedOption.data;
                          setValue(
                            "identificationType",
                            selected?.identificationType
                          );

                          Object.entries(selected).forEach(([key, value]) => {
                            setValue(key, value);
                          });

                          let nationality = selected?.nationalityStatus;
                          if (selected?.nationalityStatus === "Warganegara") {
                            nationality = 1;
                          } else if (
                            selected?.nationalityStatus === "Bukan Warganegara"
                          ) {
                            nationality = 2;
                          }
                          if (nationality) {
                            setValue("citizenshipStatus", Number(nationality));
                          }

                          setValue("occupationCode", selected?.jobCode || "-");
                          setValue(
                            "address",
                            selected?.residentialAddress ?? "-"
                          );
                          setValue(
                            "stateCode",
                            selected?.residentialStateCode ?? "-"
                          );
                          setValue(
                            "districtCode",
                      selected?.residentialDistrictCode ?? "-"
                    );
                    setValue("city", selected?.residentialCity ?? "-");
                    setValue(
                      "postalCode",
                      selected?.residentialPostcode ?? "-"
                    );
                    setValue(
                      "mobilePhoneNumber",
                      selected?.phoneNumber ?? "-"
                    );
                    setValue(
                      "homePhoneNumber",
                      selected?.telephoneNumber ?? "-"
                    );
                    setValue("titleCode", selected?.titleCode);
                    setValue("placeOfBirth", selected?.placeOfBirth);
                    setValue("committeeName", selected?.name);
                  }}
                  renderInput={(params) => ( 
                    <TextField
                      {...params}
                      placeholder="Sila Pilih"
                      size="small"
                      fullWidth
                    />
                  )}
                />
              </Grid>
            </Grid>
          );
        }}
            />
          ) : null}
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("publicOfficialsInformation")}
          </Typography>

          <Controller
            name="titleCode"
            control={control}
            defaultValue={getValues("titleCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("title")}
                type="select"
                options={ListGelaran}
                value={getValues("titleCode")}
              />
            )}
          />
          <Controller
            name="name"
            control={control}
            defaultValue={getValues("name")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("fullName")}
                options={ListGelaran}
              />
            )}
          />

          <Controller
            name="gender"
            control={control}
            defaultValue={getValues("gender")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("gender")}
                type="select"
                options={ListGender.map((gender) => ({
                  ...gender,
                  label: t(gender.label),
                }))}
              />
            )}
          />

          <Controller
            name="citizenshipStatus"
            control={control}
            defaultValue={Number(getValues("citizenshipStatus"))}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                options={CitizenshipStatus.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
                type="select"
                label={t("citizenship")}
              />
            )}
          />

          <Controller
            name="identificationType"
            control={control}
            rules={{
              required: t("fieldRequired"),
            }}
            defaultValue={getValues("identificationType")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                options={IdTypes.map((item) => ({
                  label: t(item.label),
                  value: item.value,
                }))}
                type="select"
                label={t("idType")}
                value={getValues("identificationType")}
                onChange={(e) => {
                  const inputType = getValues("identificationType");
                  let value = e.target.value;
                  if (
                    (inputType !== "4" && value === "4") ||
                    (inputType !== "1" && value === "1")
                  ) {
                    setValue("identificationNo", "");
                  }

                  setValue(field.name, value);
                }}
              />
            )}
          />
          <Controller
            name="identificationNo"
            control={control}
            defaultValue={getValues("identificationNo")}
            rules={{
              required: t("fieldRequired"),
              validate: (value) => {
                const type = getValues("identificationType");
                if ((type === "4" || type === "1") && value.length !== 12) {
                  return t("fieldRequired");
                }
                return true;
              },
            }}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("idNumber")}
                value={getValues("identificationNo")}
                inputProps={
                  getValues("identificationType") === "4" ||
                  getValues("identificationType") === "1"
                    ? {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        maxLength: 12,
                        minLength: 12,
                      }
                    : undefined
                }
                onChange={(e) => {
                  const inputType = getValues("identificationType");
                  let value = e.target.value;

                  if (inputType === "4" || inputType === "1") {
                    value = value.replace(/\D/g, "").slice(0, 12);
                  }

                  setValue(field.name, value);
                }}
              />
            )}
          />

          <Controller
            name="dateOfBirth"
            control={control}
            defaultValue={getValues("dateOfBirth")}
            render={({ field }) => {
              return (
                <Input
                  disabled={view}
                  required
                  {...field}
                  label={t("dateOfBirth")}
                  type="date"
                  onChange={(newValue) =>
                    setValue("dateOfBirth", newValue.target.value)
                  }
                  value={
                    getValues("dateOfBirth")
                      ? dayjs(getValues("dateOfBirth")).format("DD-MM-YYYY")
                      : ""
                  }
                />
              );
            }}
          />

          <Controller
            name="placeOfBirth"
            control={control}
            defaultValue={getValues("placeOfBirth")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("placeOfBirth")}
                type="text"
              />
            )}
          />

          <Controller
            name="occupationCode"
            control={control}
            defaultValue={getValues("occupationCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("occupation")}
              />
            )}
          />

          <Controller
            name="address"
            control={control}
            defaultValue={getValues("address")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("residentialAddress")}
              />
            )}
          />

          <Controller
            name="stateCode"
            control={control}
            defaultValue={getValues("stateCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("state")}
                type="select"
                onChange={(e) => setValue("stateCode", e.target.value)}
                value={parseInt(getValues("stateCode"))}
                options={addressData
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => ({
                    label: item.name,
                    value: item.id,
                  }))}
              />
            )}
          />

          <Controller
            name="districtCode"
            control={control}
            defaultValue={getValues("districtCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("district")}
                type="select"
                value={parseInt(getValues("districtCode"))}
                onChange={(e) => setValue("districtCode", e.target.value)}
                options={addressData
                  .filter(
                    (item: any) => item.pid === parseInt(watch("stateCode"))
                  )
                  .map((item: any) => ({ label: item.name, value: item.id }))}
              />
            )}
          />

          <Controller
            name="city"
            control={control}
            defaultValue={getValues("city")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("city")}
                type="text"
              />
            )}
          />

          <Controller
            name="postalCode"
            control={control}
            defaultValue={getValues("postalCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("poskod")}
                type="text"
              />
            )}
          />

          <Controller
            name="email"
            control={control}
            defaultValue={getValues("email")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("email")}
                type="text"
              />
            )}
          />

          <Controller
            name="mobilePhoneNumber"
            control={control}
            defaultValue={getValues("mobilePhoneNumber")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("phoneNumber")}
                type="text"
              />
            )}
          />

          <Controller
            name="homePhoneNumber"
            control={control}
            defaultValue={getValues("homePhoneNumber")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("homeNumber")}
                type="text"
              />
            )}
          />
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Box
            sx={{
              display: "",
              alignItems: "center",
              gap: 4,
            }}
          >
            <Controller
              name="acknowledgeAjk"
              control={control}
              rules={{
                // The field must be true (checked) otherwise return an error message.
                validate: (value) => value === true || t("pleaseSelect"),
              }}
              render={({ field, fieldState: { error } }) => (
                <>
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={field.value}
                        sx={{ fontWeight: 100 }}
                      />
                    }
                    label={
                      <Typography
                        style={{
                          fontWeight: "normal",
                          fontSize: "14px",
                          color: "#333",
                          lineHeight: "1.5",
                        }}
                      >
                        {t("pegawaiAwamDeclaration")}{" "}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    }
                  />
                  {error && (
                    <Typography variant="caption" color="error">
                      {error.message}
                    </Typography>
                  )}
                </>
              )}
            />

            {/* <Checkbox
              sx={{
                color: "#00A7A7",
                "&.Mui-checked": {
                  color: "#00A7A7",
                },
                padding: "0",
              }}
            />
            <Typography
              sx={{
                fontWeight: "500 !important",
                color: "#666666",
                fontSize: 12,
              }}
            >
              Saya mengaku bahawa Pegawai Awam yang dilantik adalah daripada ahli
              persatuan. Sekiranya maklumat yang didapati palsu, adalah tertakluk
              kepada akta pertubuhan 1996 dan Peraturan-peraturan 1984.{" "}
              <span style={{ color: "red" }}>*</span>
            </Typography> */}
          </Box>
        </Box>

        <Box
          sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}
        >
          <ButtonOutline onClick={handleBack}>{t("semula")}</ButtonOutline>
          {!view && (
            <ButtonPrimary disabled={!watch("acknowledgeAjk")} type="submit">
              {t("update")}
            </ButtonPrimary>
          )}
        </Box>
      </Box>
    </form>
  );
};

export default CreatePegawaiAwam;
