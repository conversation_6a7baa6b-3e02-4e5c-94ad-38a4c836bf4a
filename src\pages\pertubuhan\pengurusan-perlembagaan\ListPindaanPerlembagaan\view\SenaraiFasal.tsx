import Stack from "@mui/material/Stack";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import { useTranslation } from "react-i18next";
import { ChangeEvent, useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Delete as DeleteIcon } from "@mui/icons-material";
import { DokumenIcon, EditIcon, EyeIcon } from "@/components/icons";
import RemoveIcon from "@mui/icons-material/Remove";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import {
  Box,
  CircularProgress,
  Typography,
  Fade,
  useTheme,
  IconButton,
  Button,
} from "@mui/material";
import { API_URL } from "@/api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import useMutation from "@/helpers/hooks/useMutation";
import { useSelector } from "react-redux";
import { getLocalStorage, setLocalStorage } from "@/helpers/utils";
import { useFormContext } from "react-hook-form";
import { ConstitutionType } from "@/helpers/enums";
import { Switch } from "@/components";
import { useDownloadAndExportConstitutions } from "@/helpers/hooks/useDownloadConstitutions";
import { useQuery } from "@/helpers";
import { useDispatch } from "react-redux";
import { setCreatedDate } from "@/redux/fasalReducer";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const TablebodyRow = ({
  row,
  index,
  handleDeleteFasal,
}: {
  row: any;
  index: number;
  handleDeleteFasal: (row: any) => void;
}) => {
  useEffect(() => {
    setIsPinda(row.isToggle);
  }, [row.isToggle]); 

  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isPinda, setIsPinda] = useState(row.isToggle);

  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [isViewMode, setIsViewMode] = useState(
    fasalItems.IsViewPindaan ? true : false
  );
  const amendmentId = getLocalStorage("amendmentId", null);
  const handleFasalKemaskini = (clauseNo: number | string, isView: boolean) => {
    setLocalStorage("isViewMode", isView);
    navigate(`update/${clauseNo}`, { state: isView });
  };
  const [tempStatus, setTempStatus] = useState(row.isToggle);
 
  const statusText = (status: number) => {
    let isReviewed;

    if (status === 2) {
      isReviewed = true;
    } else {
      isReviewed = false;
    }

    return (
      <Box
        sx={{
          p: 1,
          color: "#000",
          border: isReviewed
            ? "1px solid #12C2E9"
            : isPinda
            ? "1px solid #FFD100"
            : "1px solid #9747FF",
          textAlign: "center",
          borderRadius: "15px",
        }}
      >
        {isReviewed ? "Pindaan" : isPinda ? "Belum disemak" : "Tiada pindaan"}
      </Box>
    );
  };

  const { mutate: updateCustomJawatan, isLoading: customJawatanIsLoading } =
    useCustomMutation();

  const handleSwitchOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (isViewMode) return;
    const { checked } = event.target;
    if (row.status === 2) {
      updateCustomJawatan(
        {
          url: `${API_URL}/society/amendment/updateToggleButton?amendmentToggle=${checked}&constitutionContentId=${row.constitutionContentId}&amendmentId=${amendmentId}`,
          method: "put",
          values: {},
          config: {
            headers: {
              "Content-Type": "application/json",
              portal: localStorage.getItem("portal") || "",
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            const responseData = data?.data?.data;
            setIsPinda(responseData.amendmentToggle);
            setTempStatus(responseData.amendmentToggle);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error) {
            console.log(error);
          },
        }
      );
    } else {
      setIsPinda(checked);
    }
  };

  return (
    <TableRow key={index}>
      <TableCell
        sx={{
          color: "#666666",
          borderBottom: "1px solid #e0e0e0",
          p: 1,
          textAlign: "center",
        }}
      >
        {t("clause")} {row.added ? row.clauseNo : index + 1}
      </TableCell>
      <TableCell
        sx={{
          color: "#666666",
          p: 1,
          minWidth: "30px",
          maxWidth: "200px",
          whiteSpace: "wrap",
          textAlign: "center",
        }}
      >
        {row.name}
      </TableCell>
      <TableCell
        sx={{
          color: "white",
          borderBottom: "1px solid #e0e0e0",
          p: 1,
          maxWidth: "50px",
        }}
      >
        {statusText(row.status)}
      </TableCell>
      <TableCell
        sx={{
          color: "#666666",
          borderBottom: "1px solid #e0e0e0",
          p: 1,
          textAlign: "center",
        }}
      >
        <Switch checked={isPinda} onChange={handleSwitchOnChange} />
        {/* {row.status === 2 ? (
          <Switch
            checked={!isPinda && toggleStatus ? toggleStatus : !isPinda ? false : isPinda}
            onChange={handleSwitchOnChange}
          />
        ) : (
          <Switch checked={isPinda} onChange={handleSwitchOnChange} />
        )} */}
      </TableCell>
      <TableCell
        sx={{
          borderBottom: "1px solid #e0e0e0",
          p: 1,
          maxWidth: "80px",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {(isPinda || row.status === 2) && !isViewMode ? (
            <IconButton
              sx={{
                width: "3rem",
                height: "3rem",
                color: "var(--primary-color)",
              }}
              onClick={() => handleFasalKemaskini(row.clauseNo, false)}
            >
              <EditIcon
                style={{
                  color: "#147C7C !important",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          ) : null}
          <IconButton
            sx={{
              width: "3rem",
              height: "3rem",
              color: "var(--primary-color)",
            }}
            onClick={() => handleFasalKemaskini(row.clauseNo, true)}
          >
            <EyeIcon
              color="#147C7C"
              style={{ width: "1rem", height: "1rem" }}
            />
          </IconButton>
        </Box>
      </TableCell>

      <TableCell
        sx={{
          width: {
            borderTop: "none",
            borderBottom: "none",
            xs: "50px",
          },
        }}
      >
        {row.added && (
          <IconButton size="small" onClick={() => handleDeleteFasal(row)}>
            <Box
              sx={{
                background: "#FF0000",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "100%",
                height: "16px",
                width: "16px",
              }}
            >
              <RemoveIcon sx={{ color: "#fff", width: "16px" }} />
            </Box>
          </IconButton>
        )}
      </TableCell>
    </TableRow>
  );
};

export interface PindaanPerlembagaan {
  id: number;
  fasal: number;
  namaFasal: string;
  applicationStatusCode: 1 | 2;
}

export const SenaraiFasal = ({
  senaraiFasal,
  constitutionType,
  handleAddFasal,
  handleDeleteFasal,
  createdDate,
}: {
  senaraiFasal: any;
  constitutionType: string | null;
  handleAddFasal: () => void;
  handleDeleteFasal: (data: any) => void;
  createdDate: string;
}) => {
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [isViewMode, setIsViewMode] = useState(
    fasalItems.IsViewPindaan ? true : false
  );
  const { control, setValue, getValues, watch } = useFormContext();
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [meetingType, setMeetingType] = useState("");
  const [amendmentId, setAmendmentId] = useState<string | number>(
    getLocalStorage("amendmentId", null)
  );
  const [meetingId, setMeetingId] = useState<string | number | null>(null);
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  //GET ID PARAMS
  const { id } = useParams();
  const params = new URLSearchParams(window.location.search);
  const isEdit = params.get("isEdit");
  const isView = fasalItems.IsViewPindaan || null;
  const encodedId = id;
  useEffect(() => {
    if (encodedId) {
      const decodedId = atob(encodedId);
      setSocietyId(decodedId);
    }
  }, []);

  const isBebasCatergory =
    constitutionType === ConstitutionType.Bebas[1] ||
    constitutionType === ConstitutionType.CawanganBebas[1];
 
  // FUNCTION HANDLE/MUTATE
  // ====================================================
  // ====================================================

  const handleFasalKemaskini = (clauseNo: number | string) => {
    navigate(`update/${clauseNo}`);
  };

  const handleReviewConstitution = () => {
    navigate("semakan");
  };

  const goBack = () => {
    navigate(-1);
  };

  const goNext = () => {
    navigate(`bayaran`);
  };

  // const statusText = (status: number) => {
  //   if (status === 2) {
  //     return t("reviewed");
  //   } else {
  //     return t("notReviewed");
  //   }
  // };

  const { getConstitutionsFile, isLoadingDownloadConstitutions } =
    useDownloadAndExportConstitutions();
  // ====================================================

  return (
    <>
      <Fade in={true} timeout={500}>
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("clauseList")}
          </Typography>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              background: "#fff",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("clauseList")}
              </Typography>
              {isBebasCatergory ? (
                <Box>
                  <ButtonOutline
                    onClick={handleAddFasal}
                    sx={{
                      textTransform: "none",
                      fontWeight: 400,
                      fontSize: "13px",
                    }}
                  >
                    {t("AddClause")}
                  </ButtonOutline>
                </Box>
              ) : null}
            </Box>
            <TableContainer
              component={Paper}
              sx={{
                boxShadow: "none",
                backgroundColor: "white",
                borderRadius: 2.5 * 1.5,
                p: 1,
                mb: 3,
              }}
            >
              <Table>
                <TableHead>
                  <TableRow>
                    {["clause", "matter", "status", "pinda", "action"].map(
                      (header, index) => (
                        <TableCell
                          key={index}
                          sx={{
                            fontWeight: "bold",
                            color: "#666666",
                            p: 1,
                            textAlign: "center",
                            whiteSpace: "nowrap",
                          }}
                        >
                          {t(header)}
                        </TableCell>
                      )
                    )}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {senaraiFasal ? (
                    senaraiFasal?.map((row: any, index: any) => {
                      return (
                        <TablebodyRow
                          key={row.clauseNo}
                          row={row}
                          index={index}
                          handleDeleteFasal={handleDeleteFasal}
                        />
                      );
                    })
                  ) : (
                    <TableCell
                      colSpan={3}
                      align="center"
                      sx={{ borderBottom: "none" }}
                    >
                      <CircularProgress />
                    </TableCell>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>

          <Box
            sx={{ display: "flex", justifyContent: "flex-end", py: 3 }}
            gap={1}
          >
            <Button
              startIcon={<DokumenIcon />}
              variant="outlined"
              sx={{ p: "15px 30px" }}
              onClick={() => id && getConstitutionsFile(id, 1, amendmentId)}
            >
              {t("download")}
            </Button>
            <ButtonOutline
              onClick={handleReviewConstitution}
              sx={{ p: "15px 30px" }}
            >
              {t("reviewConstitution")}
            </ButtonOutline>
          </Box>
          <Stack
            direction="row"
            spacing={2}
            mt={2}
            sx={{ pl: 1 }}
            justifyContent="flex-end"
          >
            {isViewMode ? (
              <ButtonPrimary onClick={() => goBack()}>
                {t("back")}
              </ButtonPrimary>
            ) : (
              <ButtonPrimary
                disabled={!amendmentId || !getValues("meetingId")}
                onClick={() => goNext()}
              >
                {t("next")}
              </ButtonPrimary>
            )}
          </Stack>
        </Box>
      </Fade>
    </>
  );
};

export default SenaraiFasal;
