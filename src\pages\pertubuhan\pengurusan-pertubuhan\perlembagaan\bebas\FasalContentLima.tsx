import {
  Box,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  FormControl,
  Select,
  MenuItem,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "@/helpers/utils";
import { FasalBebasProps } from "../Fasal";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";
import { months, RegExNumbers } from "@/helpers";
import CustomPopover from "@/components/popover";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export const FasalContentLimaBebas: React.FC<FasalBebasProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const requiredText = ["tahun kewangan bermula"];

  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [jumlahWangTangan, setJumlahWangTangan] = useState("");
  const [jumlahWangTanganKata, setJumlahWangTanganKata] = useState("");
  const [kuasaPerbelanjaan, setKuasaPerbelanjaan] = useState("");
  const [kuasaPerbelanjaanKata, setKuasaPerbelanjaanKata] = useState("");
  const [kuasaPerbelanjaanJawatankuasa, setKuasaPerbelanjaanJawatankuasa] =
    useState("");
  const [
    kuasaPerbelanjaanJawatankuasaKata,
    setKuasaPerbelanjaanJawatankuasaKata,
  ] = useState("");
  const [perbelanjaanDibenarkan, setPerbelanjaanDibenarkan] = useState("");
  const [perbelanjaanDibenarkanKata, setPerbelanjaanDibenarkanKata] =
    useState("");
  const [tempohDibenarkan, setTempohDibenarkan] = useState("");
  const [tahunKewanganBermula, setTahunKewanganBermula] = useState("");

  const [clauseContentId, setClauseContentId] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [clauseContentEditable, setClauseContentEditable] = useState(
    clause.clauseContent
  );
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    // if (!jumlahWangTangan) {
    //   errors.jumlahWangTangan = t("fieldRequired");
    // }

    // if (!jumlahWangTanganKata) {
    //   errors.jumlahWangTanganKata = t("fieldRequired");
    // }

    // if (!kuasaPerbelanjaan) {
    //   errors.kuasaPerbelanjaan = t("fieldRequired");
    // }

    // if (!kuasaPerbelanjaanKata) {
    //   errors.kuasaPerbelanjaanKata = t("fieldRequired");
    // }

    // if (!kuasaPerbelanjaanJawatankuasa) {
    //   errors.kuasaPerbelanjaanJawatankuasa = t("fieldRequired");
    // }

    // if (!kuasaPerbelanjaanJawatankuasaKata) {
    //   errors.kuasaPerbelanjaanJawatankuasaKata = t("fieldRequired");
    // }

    // if (!perbelanjaanDibenarkan) {
    //   errors.perbelanjaanDibenarkan = t("fieldRequired");
    // }

    // if (!perbelanjaanDibenarkanKata) {
    //   errors.perbelanjaanDibenarkanKata = t("fieldRequired");
    // }

    // if (!tempohDibenarkan) {
    //   errors.tempohDibenarkan = t("fieldRequired");
    // }

    if (!tahunKewanganBermula) {
      errors.tahunKewanganBermula = t("fieldRequired");
    }

    return errors;
  };

  const checkBebasEditFields = (
    requiredFieldText: string[],
    clauseContentEditable: string
  ) => {
    const missingFields = requiredFieldText.filter(
      (field) => !clauseContentEditable.includes(`<<${field}>>`)
    );
    if (missingFields.length > 0) {
      alert(
        `Kandungan tidak lengkap! Sila masukkan:\n\n${missingFields
          .map((field) => `<<${field}>>`)
          .join("\n")}\n\ndi dalam kandungan anda`
      );
      return true;
    }
    return false;
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const handleChange = (e: any) => {
    setClauseContentEditable(e.target.value);
  };

  useEffect(() => {
    if (clause) {
      //const clause = JSON.parse(clause9);
      setDataId(clause.id);
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setJumlahWangTangan(clause.constitutionValues[0]?.definitionName);
      setJumlahWangTanganKata(clause.constitutionValues[1]?.definitionName);
      setKuasaPerbelanjaan(clause.constitutionValues[2]?.definitionName);
      setKuasaPerbelanjaanKata(clause.constitutionValues[3]?.definitionName);
      setKuasaPerbelanjaanJawatankuasa(
        clause.constitutionValues[4]?.definitionName
      );
      setKuasaPerbelanjaanJawatankuasaKata(
        clause.constitutionValues[5]?.definitionName
      );
      setPerbelanjaanDibenarkan(clause.constitutionValues[6]?.definitionName);
      setPerbelanjaanDibenarkanKata(
        clause.constitutionValues[7]?.definitionName
      );
      setTempohDibenarkan(clause.constitutionValues[8]?.definitionName);
      setTahunKewanganBermula(clause.constitutionValues[9]?.definitionName);
      setClauseContentEditable(
        clause.clauseModifyContent
          ? clause.clauseModifyContent
          : clause.clauseContent
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clauseContentEditable;
  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi,
    `<b>${
      jumlahWangTangan || "<<jumlah wang tangan yang dibenarkan dalam tangan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang dibenarkan-tulis dalam perkataan>>/gi,
    `<b>${
      jumlahWangTanganKata || "<<jumlah wang dibenarkan-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yg dibenarkan>>/gi,
    `<b>${perbelanjaanDibenarkan || "<<perbelanjaan yg dibenarkan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yg dibenarkan-tulis dalam perkataan>>/gi,
    `<b>${
      perbelanjaanDibenarkanKata ||
      "<<perbelanjaan yg dibenarkan-tulis dalam perkataan>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung>>/gi,
    `<b>${kuasaPerbelanjaan || "<<kuasa perbelanjaan mesyuarat agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung-tulis dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanKata ||
      "<<kuasa perbelanjaan mesyuarat agung-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasa || "<<kuasa perbelanjaan jawatankuasa>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa-tulis dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasaKata ||
      "<<kuasa perbelanjaan jawatankuasa-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>/gi,
    `<b>${
      tempohDibenarkan ||
      "<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tahun kewangan bermula>>/gi,
    `<b>${tahunKewanganBermula || "<<tahun kewangan bermula>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  const handlerChangeClauseContentEditable = (val: string) => {
    setClauseContentEditable(val);
  };

  return (
    <>
      <Grid container>
        {/* name section */}
        <FasalNameCom clauseId={clauseId} name={name} />
        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Typography sx={{ mb: 1, ...sectionStyle }}>{name}</Typography>
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("permulaanTahunKewangan")}
              </Typography>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <Typography sx={labelStyle}>
                      {t("tahunKewanganBermula")}{" "}
                      <Typography sx={{ display: "inline", color: "red" }}>
                        *
                      </Typography>
                    </Typography>
                    <CustomPopover
                      customStyles={{ maxWidth: "250px" }}
                      content={
                        <Typography sx={{ color: "#666666" }}>
                          {t("contohJan")}
                        </Typography>
                      }
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={8}>
                  <FormControl fullWidth required>
                    <Select
                      required
                      size="small"
                      disabled={isViewMode}
                      value={tahunKewanganBermula}
                      displayEmpty
                      onChange={(e) => {
                        setTahunKewanganBermula(e.target.value);
                      }}
                    >
                      {months.map((i) => {
                        return (
                          <MenuItem value={i.value} selected>
                            {i.label}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <ReminderEditable />
            <EditableFasalTextArea
              requiredFieldText={requiredText}
              clauseContentEditable={clauseContentEditable}
              setClauseContentEditable={handlerChangeClauseContentEditable}
            />
          </Box>

          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <ContentBox clauseContent={clauseContent} />
            <CheckContent checked={checked} onChange={handleChangeCheckbox} />
            {/* submit */}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  display: isViewMode ? "none" : "block",
                }}
                onClick={() => {
                  const errors = validateForm();
                  if (Object.keys(errors).length > 0) {
                    setFormErrors(errors);
                    return;
                  }
                  const isMissingRequired = checkBebasEditFields(
                    requiredText,
                    clauseContentEditable
                  );
                  if (isMissingRequired) {
                    return;
                  }
                  handleSaveContent({
                    i18n,
                    societyId: societyDataRedux.id,
                    societyName: societyDataRedux.societyName,
                    clauseContentId,
                    dataId,
                    isEdit,
                    clauseNo: clauseNo,
                    clauseName: clauseName,
                    createClauseContent,
                    editClauseContent,
                    description: clauseContent,
                    modifiedTemplate: clauseContentEditable,
                    constitutionValues: [
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahWangTangan,
                        titleName:
                          "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahWangTanganKata,
                        titleName:
                          "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan (Tulis Dalam Perkataan)",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: kuasaPerbelanjaan,
                        titleName: "Kuasa Perbelanjaan Mesyuarat Agung",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: kuasaPerbelanjaanKata,
                        titleName:
                          "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: kuasaPerbelanjaanJawatankuasa,
                        titleName: "Kuasa Perbelanjaan Jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: kuasaPerbelanjaanJawatankuasaKata,
                        titleName:
                          "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: perbelanjaanDibenarkan,
                        titleName: "Perbelanjaan yang Dibenarkan",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: perbelanjaanDibenarkanKata,
                        titleName:
                          "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohDibenarkan,
                        titleName:
                          "Tempoh yang Dibenarkan Untuk Memasukkan Wang Lebihan ke Dalam Bank",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tahunKewanganBermula,
                        titleName:
                          "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)",
                      },
                    ],
                    clause: "clause5",
                    clauseCount: 5,
                  });
                }}
                disabled={isCreatingContent || isEditingContent || !checked}
              >
                {isCreatingContent || isEditingContent
                  ? t("saving")
                  : t("save")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default FasalContentLimaBebas;
