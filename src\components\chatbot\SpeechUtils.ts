/**
 * Utilities for speech recognition and text-to-speech functionality
 */

// Speech Recognition setup
const SpeechRecognition: any = window.SpeechRecognition || window.webkitSpeechRecognition;
const SpeechGrammarList: any = window.SpeechGrammarList || window.webkitSpeechGrammarList;

// TypeScript type definitions for Web Speech API
interface SpeechRecognitionEvent {
  results: {
    [index: number]: {
      [index: number]: {
        transcript: string;
        confidence: number;
      };
    };
  };
}

interface SpeechRecognitionErrorEvent {
  error: string;
}

export interface SpeechRecognitionInstance {
  grammars: any;
  continuous: boolean;
  lang: string;
  interimResults: boolean;
  maxAlternatives: number;
  onresult: (event: SpeechRecognitionEvent) => void;
  onerror: (event: SpeechRecognitionErrorEvent) => void;
  onend: () => void;
  start: () => void;
  stop: () => void;
}

// Speech Synthesis setup
const speechSynthesis = window.speechSynthesis;

// Track active utterance for state management
let activeUtterance: SpeechSynthesisUtterance | null = null;
let isSpeakingRef = false;

/**
 * Check if speech synthesis is currently speaking
 * @returns True if speech synthesis is actively speaking
 */
export const isSpeaking = (): boolean => {
  return isSpeakingRef && speechSynthesis?.speaking;
};

/**
 * Initialize speech synthesis voices
 * This helps with browsers that don't load voices immediately
 * @returns A promise that resolves when voices are loaded
 */
export const initVoices = (): Promise<SpeechSynthesisVoice[]> => {
  return new Promise((resolve) => {
    let voices = speechSynthesis.getVoices();

    if (voices.length > 0) {
      resolve(voices);
      return;
    }

    // If no voices are available yet, wait for them to load
    speechSynthesis.onvoiceschanged = () => {
      voices = speechSynthesis.getVoices();
      resolve(voices);
    };

    // Force a refresh of voices
    speechSynthesis.getVoices();
  });
};

/**
 * Removes emojis from text
 * @param text The text to remove emojis from
 * @returns Text without emojis
 */
const removeEmojis = (text: string): string => {
  return text.replace(/[\p{Emoji}]/gu, '');
};

/**
 * Normalize text to handle variations of "Rosie"
 * @param text The text to normalize
 * @returns Normalized text
 */
export const normalizeRosieName = (text: string): string => {
  // Matches variations like rosi, rusi, russi, rozie, etc.
  const rosiePattern = /\b(?:k[aeiou]?)?r[ou](?:s{1,2}|z)(?:i|ie|ey|y)?\b/gi;

  // Matches greetings like hairozi, khairozi, hai rosi, etc.
  const haiRosiePattern = /\b(?:k|h)?h?a[iy][\s\-]?r[ou](?:s{1,2}|z)(?:i|ie|ey|y)?\b/gi;

  let normalized = text.replace(haiRosiePattern, 'Hai Rosie');
  normalized = normalized.replace(rosiePattern, 'Rosie');
  return normalized;
};

/**
 * Normalizes text by removing extra spaces, converting to lowercase, and handling special cases
 * @param text The text to normalize
 * @returns Normalized text
 */
const normalizeText = (text: string): string => {
  // First normalize Rosie name variations
  let normalized = normalizeRosieName(text);
  
  // Convert to lowercase
  normalized = normalized.toLowerCase();
  
  // Remove extra spaces
  normalized = normalized.replace(/\s+/g, ' ').trim();
  
  // Remove punctuation except for essential ones
  normalized = normalized.replace(/[^\w\s.,!?]/g, '');
  
  return normalized;
};

/**
 * Creates and configures a speech recognition instance
 * @param locale The locale to use for speech recognition
 * @returns A configured SpeechRecognition instance
 */
export const createSpeechRecognition = (locale: string): SpeechRecognitionInstance => {
  const recognition = new SpeechRecognition();
  const speechRecognitionList = new SpeechGrammarList();

  recognition.grammars = speechRecognitionList;
  // recognition.continuous = true;
  recognition.lang = locale;
  // recognition.interimResults = true;
  recognition.maxAlternatives = 1;

  // Add event listeners
  recognition.onresult = (event: any) => {
    const result = event.results[0][0].transcript;
    // Normalize the text to handle Rosie name variations
    const normalizedResult = normalizeRosieName(result);
    event.results[0][0].transcript = normalizedResult;
  };

  return recognition;
};

/**
 * Starts speech recognition
 * @param recognition The SpeechRecognition instance
 * @param onResult Callback for speech recognition results
 * @param onError Callback for speech recognition errors
 * @param onEnd Callback for when speech recognition ends
 */
export const startSpeechRecognition = (
  recognition: SpeechRecognitionInstance | null,
  onResult: (text: string) => void,
  onError: (error: string) => void,
  onEnd: () => void
): void => {
  if (!recognition) {
    onError('Speech recognition not supported');
    return;
  }

  recognition.onresult = (event: SpeechRecognitionEvent) => {
    const result = event.results[0][0].transcript;
    // First normalize Rosie name variations
    const normalizedResult = normalizeRosieName(result);
    onResult(normalizedResult);
  };

  recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
    onError(`Speech recognition error: ${event.error}`);
  };

  recognition.onend = () => {
    onEnd();
  };

  try {
    recognition.start();
  } catch (error) {
    onError(`Could not start speech recognition: ${error}`);
    onEnd();
  }
};

/**
 * Stops speech recognition
 * @param recognition The SpeechRecognition instance
 */
export const stopSpeechRecognition = (recognition: SpeechRecognitionInstance | null): void => {
  if (recognition) {
    try {
      recognition.stop();
    } catch (error) {
      // console.error('Error stopping speech recognition:', error);
    }
  }
};

/**
 * Speaks text using speech synthesis
 * @param text The text to speak
 * @param locale The locale to use for speech synthesis
 * @param onStart Callback for when speech starts
 * @param onEnd Callback for when speech ends
 * @param onError Callback for speech synthesis errors
 */
export const speakText = (
  text: string,
  locale: string,
  onStart?: () => void,
  onEnd?: () => void,
  onError?: (error: string) => void
): void => {
  if (!speechSynthesis) {
    if (onError) onError('Speech synthesis not supported');
    return;
  }

  // Cancel any ongoing speech and wait for cleanup
  if (activeUtterance) {
    speechSynthesis.cancel();
    // Small delay to ensure cleanup
    setTimeout(() => {
      startNewSpeech(text, locale, onStart, onEnd, onError);
    }, 100);
  } else {
    startNewSpeech(text, locale, onStart, onEnd, onError);
  }
};

/**
 * Internal function to start new speech after cleanup
 */
const startNewSpeech = (
  text: string,
  locale: string,
  onStart?: () => void,
  onEnd?: () => void,
  onError?: (error: string) => void
): void => {
  // Remove emojis from the text before speaking
  const cleanText = removeEmojis(text);
  
  const utterance = new SpeechSynthesisUtterance(cleanText);
  utterance.lang = 'ms-MY'; // Always use Malaysian language

  let voices = speechSynthesis.getVoices();

  // If voices array is empty, try to get them again
  if (voices.length === 0) {
    speechSynthesis.getVoices();
    voices = speechSynthesis.getVoices();
  }

  // Filter for Malaysian voices only
  const malayVoices = voices.filter(voice => 
    voice.lang.includes('ms-MY')
  );

  // Try to find Yasmin voice first
  const yasminVoice = malayVoices.find(voice => 
    voice.name.toLowerCase().includes('yasmin')
  );

  // If Yasmin not found, try to find Amira
  const amiraVoice = !yasminVoice ? malayVoices.find(voice => 
    voice.name.toLowerCase().includes('amira')
  ) : null;

  if (yasminVoice) {
    utterance.voice = yasminVoice;
    // console.log('Using Yasmin voice:', yasminVoice.name);
  } else if (amiraVoice) {
    utterance.voice = amiraVoice;
    // console.log('Using Amira voice:', amiraVoice.name);
  } else if (malayVoices.length > 0) {
    // Fallback to any Malaysian voice if neither Yasmin nor Amira is found
    utterance.voice = malayVoices[0];
    // console.log('Using available Malay voice:', malayVoices[0].name);
  } else {
    // console.log('No Malay voices found, using default voice');
  }

  // Set speech parameters for a natural female voice
  utterance.pitch = 1.2;
  utterance.rate = 1.0;
  utterance.volume = 1.0;

  // Set up event handlers
  utterance.onstart = () => {
    isSpeakingRef = true;
    activeUtterance = utterance;
    if (onStart) onStart();
  };

  utterance.onend = () => {
    isSpeakingRef = false;
    activeUtterance = null;
    if (onEnd) onEnd();
  };

  utterance.onerror = (event) => {
    isSpeakingRef = false;
    activeUtterance = null;
    if (onError) onError(`Speech synthesis error: ${event.error}`);
  };

  utterance.onpause = () => {
    isSpeakingRef = false;
  };

  utterance.onresume = () => {
    isSpeakingRef = true;
  };

  utterance.onmark = () => {
    // Handle speech marks if needed
  };

  utterance.onboundary = () => {
    // Handle word boundaries if needed
  };

  // Start speaking
  speechSynthesis.speak(utterance);
};

/**
 * Stops any ongoing speech synthesis
 */
export const stopSpeaking = (): void => {
  if (speechSynthesis) {
    speechSynthesis.cancel();
    isSpeakingRef = false;
    activeUtterance = null;
  }
};

/**
 * Checks if speech recognition is supported in the current browser
 * @returns True if speech recognition is supported
 */
export const isSpeechRecognitionSupported = (): boolean => {
  return !!SpeechRecognition;
};

/**
 * Checks if speech synthesis is supported in the current browser
 * @returns True if speech synthesis is supported
 */
export const isSpeechSynthesisSupported = (): boolean => {
  return !!speechSynthesis;
};

// Add TypeScript declarations for the Web Speech API
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
    SpeechGrammarList: any;
    webkitSpeechGrammarList: any;
  }
}

/**
 * Sets up a robust speech recognition instance with live and final transcript callbacks.
 * @param options Configuration options
 * @returns The recognition instance
 */
export function setupSpeechRecognition({
  locale = "en-US",
  onInterim,
  onFinal,
  onError,
  onEnd,
}: {
  locale?: string;
  onInterim: (text: string) => void;
  onFinal: (text: string) => void;
  onError?: (e: any) => void;
  onEnd?: () => void;
}) {
  const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
  recognition.lang = locale;
  recognition.continuous = true;
  recognition.interimResults = true;

  recognition.onresult = (event: any) => {
    let interim = "";
    let final = "";
    for (let i = event.resultIndex; i < event.results.length; ++i) {
      if (event.results[i].isFinal) {
        final += event.results[i][0].transcript;
      } else {
        interim += event.results[i][0].transcript;
      }
    }
    if (interim && onInterim) onInterim(interim);
    if (final && onFinal) onFinal(final);
  };

  recognition.onerror = onError || (() => {});
  recognition.onend = onEnd || (() => {});

  return recognition;
}
