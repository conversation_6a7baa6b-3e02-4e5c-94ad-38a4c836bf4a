import React from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, Typography } from "@mui/material";
import Input from "@/components/input/Input";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";

export const CreateLiabiliti: React.FC<{
  t: TFunction;
  control?: Control<FieldValues>; // Made optional by adding `?`
  setValue?: UseFormSetValue<FieldValues>; // Made optional by adding `?`
  getValues?: UseFormGetValues<FieldValues>; // Made optional by adding `?`
  watch?: UseFormWatch<FieldValues>; // Made optional by adding `?`
  checked?: boolean;
}> = ({ t, control, setValue, getValues, watch, checked }) => {
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  // const isAliranTugasAccess = true;

  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t(title)}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            {items.map((item, index) =>
              control ? ( // Check if `control` is provided
                <Controller
                  name={item.variable}
                  control={control}
                  key={index}
                  defaultValue={
                    getValues ? getValues(item.variable) : undefined
                  } // Default value or empty string if `getValues` is undefined
                  render={({ field }) => (
                    <Input
                      {...field}
                      label={t(item.label)}
                      type="currrency"
                      placeholder="0"
                      textColor="var(--primary-color)"
                      fontWeight="500!important"
                      disabled={
                        checked || (!isManager && !isAliranTugasAccess)
                        // item.variable === "totalLiability" ||
                        // !isAliranTugasAccess
                      }
                      onChange={(e) => {
                        let checkNum = e.target.value.replace(/[^\d]/g, "");

                        if (checkNum.length > 12) {
                          checkNum = checkNum.slice(0, 12);
                        }

                        const currentValue = getValues
                          ? (getValues(item.variable) as number)
                          : 0; // Get the current field value
                        const newValue = checkNum === "" ? 0 : Number(checkNum); // Convert the new input value to a number
                        const totalLiability = getValues
                          ? (getValues("totalLiability") as number)
                          : 0; // Get the current totalIncome value

                        // Subtract the previous value, then add the new value
                        const updatedTotalLiability =
                          totalLiability - currentValue + newValue;

                        // Update both the current field and totalIncome
                        field.onChange(newValue); // Update this field's value
                        if (setValue) {
                          setValue("totalLiability", updatedTotalLiability); // Update totalIncome if `setValue` is available
                        }
                      }}
                    />
                  )}
                />
              ) : (
                <Input
                  type="number"
                  label={t(item.label)}
                  required
                  key={index}
                  placeholder="0"
                  disabled={checked || (!isManager && !isAliranTugasAccess)}
                />
              )
            )}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {/* <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          py: 2,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "var(--primary-color)",
            borderRadius: "16px",
            fontSize: "14px",
            fontWeight: "500 !important",
          }}
        >
          {t("liabilities")}
        </Typography>
      </Box>

      {renderInputGroup("currentLiabilities", [
        { label: "creditors", variable: "creditorLiability" },
        { label: "unpaidTaxes", variable: "taxLiability" },
        { label: "loans", variable: "loanLiability" },
      ])}

      {renderInputGroup("longTermLiabilities", [
        { label: "longTermDebt", variable: "debtLiability" },
        { label: "unpaidDeferredTaxes", variable: "deferredTaxLiability" },
        { label: "longTermLoans", variable: "borrowLiability" },
      ])} */}

      {renderInputGroup("", [
        { label: "totalLiabilities", variable: "totalLiability" },
      ])}
    </>
  );
};

export default CreateLiabiliti;
