import { createSlice } from '@reduxjs/toolkit';

interface BranchByIdStore {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: BranchByIdStore = {
  data: null,
  loading: false,
  error: null,
};

export const branchByIdDataSlice = createSlice({
  name: 'branchByIdData',
  initialState,
  reducers: {
    setBranchByIdDataRedux(state, action) {
      state.data = action.payload;
    },
    setBranchByIdLoading(state, action) {
      state.loading = action.payload;
    },
    setBranchByIdError(state, action) {
      state.error = action.payload;
    },
  },
});

export const { setBranchByIdDataRedux, setBranchByIdLoading, setBranchByIdError } = branchByIdDataSlice.actions;
export default branchByIdDataSlice.reducer;
