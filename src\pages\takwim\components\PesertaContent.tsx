import TakwimContainer from "@/components/container/TakwimContainer";
import { TakwimPaper } from "@/components/paper";
import {
  Grid,
  Box,
  Typography,
  Avatar,
  Container,
  CircularProgress,
} from "@mui/material";
import { attendParticipants } from "../dummy/attendParticipants";
import { t } from "i18next";
import { eventService } from "@/services/eventService";
import { useEffect, useState } from "react";
import { AttendeesName, IEventAttendee } from "@/types/eventAttendee";
import { ApiResponse, IEvent } from "@/types/event";
import DetailedTooltip from "@/components/tooltip/DetailedTooltip";

interface PesertaContentProps {
  mainUser?: {
    name: string;
    avatar: string;
  };
  attendees?: AttendeesName[];
  totalSlots?: number | null;
  isLoadingAttendees?: boolean;
  renderNoParticipant?: () => JSX.Element;
}

const PesertaContent = ({
  mainUser,
  attendees = [],
  totalSlots,
  isLoadingAttendees,
  renderNoParticipant,
}: PesertaContentProps) => {
  return (
    <>
      <Grid container spacing={3}>
        {/* Penganjur Section */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" color="primary" sx={{ mb: 2 }}>
            {t("Penganjur Acara")}
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Avatar
              src={"https://www.ros.gov.my/assets/img/jppm.jpg"}
              alt={mainUser?.name}
              sx={{
                width: 48,
                height: 48,
                border: "2px solid white",
                boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                bgcolor: "#0CA6A6",
              }}
            />
            <Typography>JPPM</Typography>
          </Box>
        </Grid>

        {/* Participants Count Section */}
        <Grid item xs={12}>
          <Typography color="primary" sx={{ mb: 2 }}>
            Peserta telah mengesahkan penyertaan dalam acara:{" "}
            <Box component="span">
              {attendees?.length}/{totalSlots}
            </Box>
          </Typography>
        </Grid>

        {/* Participants Grid Section */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            {isLoadingAttendees ? (
              <Box
                sx={{
                  ml: "auto",
                  mr: "auto",
                }}
              >
                <CircularProgress />
              </Box>
            ) : (
              <>
                { attendees.length > 0 ? (
                  attendees.map((member, index) => (
                  <Grid item xs={6} sm={4} md={2} key={index}>
                    <DetailedTooltip
                      fields={[
                        { label: "Nama", value: member.fullName },
                        {
                          label: "Nama Pertubuhan",
                          values:
                            member?.societyNameList &&
                            member?.societyNameList.length > 0
                              ? member?.societyNameList
                              : ["Tiada"],
                        },
                        {
                          label: "Jawatan",
                          values:
                            member?.positionList &&
                            member?.positionList.length > 0
                              ? member?.positionList
                              : ["Tiada"],
                        },
                        {
                          label: "Tarikh Penyertaan",
                          value: member.dateRegistered,
                          isDate: true,
                        },
                        {
                          label: "Masa Penyertaan",
                          value: member.timeRegistered,
                          isTime: true,
                        },
                      ]}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          textAlign: "center",
                          gap: 1,
                        }}
                      >
                        <Avatar
                          src={member.fullName}
                          alt={member.fullName}
                          sx={{
                            width: 45,
                            height: 45,
                            mb: 1,
                            border: "2px solid white",
                            boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                            bgcolor: "#0CA6A6",
                          }}
                        />
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: "0.875rem",
                            maxWidth: "100%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                          }}
                        >
                          {member.fullName}
                        </Typography>
                      </Box>
                    </DetailedTooltip>
                  </Grid>
                ))
              ) : (
                renderNoParticipant? renderNoParticipant() : null
              )}
              </>
            )}
          </Grid>
        </Grid>
      </Grid>
    </>
  );
};

export default PesertaContent;
