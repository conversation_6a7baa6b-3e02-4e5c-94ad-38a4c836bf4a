import { IFeedback, IR<PERSON>ie<PERSON>, IQ<PERSON>y } from "./approval";

export interface ISecretaryList {
  id: number;
  societyName: string;
  applicationStatusCode: string;
}

export interface ISecretaryBranch {
  id: number;
  branchId: number;
  branchNo: string;
  committeeName: string;
  committeeIcNo: string;
}

export interface ISecretaryDetail {
  id: number;
  societyId: number;
  societyNo: string;
  titleCode: string;
  committeeName: string;
  sex: string;
  committeePosition: string;
  jobCode: string;
  citizenshipStatus: string;
  dateOfBirth: string;
  placeOfBirth: string | null;
  identificationType: string;
  identificationNo: string;
  email: string;
  residenceAddress: string | null;
  residencePostcode: string;
  residenceAddressStatus: string | null;
  residenceCountryCode: number | null;
  residenceStateCode: string;
  residenceDistrictCode: string;
  residenceCityCode: number | null;
  residenceCity: string | null;
  homeTelNo: string;
  hpNo: string;
  workTelNo: string;
  employerName: string | null;
  employerAddress: string | null;
  employerPostcode: string | null;
  employerCountryCode: number | null;
  employerStateCode: number | null;
  employerDistrictCode: number | null;
  employerCityCode: number | null;
  employerCity: string | null;
  oldSecretaryName: string;
  oldSecretaryIdentificationNumber: string;
  meetingId: number | null;
  meetingDate: string;
  meetingType: string;
  applicationStatusCode: number;
  reasonOfChange: string;
  isUserApplicant: boolean;
}

export interface IApprovalSecretaryList {
  stateCode: string;
  principleSecretaryId: number;
  applicant: string;
  societyName: string;
  societyNo: string;
  createdDate: string;
  transferDate: string;
  roName: string;
  state: string;
  applicationStatusCode: string;
}

export interface IApprovalSecretaryDetail extends ISecretaryDetail {
  feedbacks: IFeedback[];
  reviews: IReview[];
  queries: IQuery[];
  ro: string | number;
  roName: string;
  noteRo: string;
  notePpp: string;
  newSecretaryName: string;
  oldSecretaryResidentialAddress: string;
  oldSecretaryResidentialCity: string;
  oldSecretaryResidentialPostcode: string;
  oldSecretaryTelephoneNumber: string;
  oldSecretaryNameKeyIn: string;
  oldSecretaryIdentificationNumberKeyIn: string;
  userRo: boolean;
}

export interface ISecretaryBranchList {
  id: number;
  branchName: string;
  secretaryName: string;
  replacementDate: string;
}

export interface ISecretaryBranchDetail {
  societyNo: string;
  societyName: string;
  branchNo: string;
  branchName: string;
  secretaryName: string;
  secretaryIdNo: string;
  applicantName: string;
  applicantIdNo: string;
  applicationDateTime: string;
}
