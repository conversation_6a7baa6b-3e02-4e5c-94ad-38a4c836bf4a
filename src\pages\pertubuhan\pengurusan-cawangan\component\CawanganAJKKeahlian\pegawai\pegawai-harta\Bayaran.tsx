import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import InputLabel from "@mui/material/InputLabel";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { Select, Option } from "@/components/input";
import { useTheme, useMediaQuery, Fade } from "@mui/material";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { useNavigate } from "react-router-dom";
import { API_URL } from "@/api";
import { ApplicationStatus } from "@/helpers/enums";

export const BayaranHarta = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const navigate = useNavigate();
  const [isChecked, setIsChecked] = useState(false);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const decodedId = atob(encodedId || "");

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const safeParse = (str: string | null) => {
    try {
      return str ? JSON.parse(str) : {};
    } catch (e) {
      return {};
    }
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <>
      <Fade in={true} timeout={500}>
        <Box
          sx={{
            backgroundColor: "white",
            border: 1,
            borderColor: "grey.300",
            borderRadius: 4,
            p: 3,
          }}
        >
          <Box
            sx={{
              border: 1,
              borderColor: "grey.300",
              borderRadius: 1,
              p: 2,
              mb: 2,
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  color: "#00A7A7",
                  fontSize: 16,
                  fontWeight: "500 !important",
                }}
              >
                {t("payment")}
              </Typography>
            </Box>
            <Typography
              variant="body2"
              sx={{
                color: "#666666",
                fontFamily: "Poppins",
                fontSize: 14,
                fontWeight: "400 !important",
                lineHeight: "21px",
                textAlign: "left",
                textUnderlinePosition: "from-font",
                textDecorationSkipInk: "none",
                mb: 2,
              }}
            >
              Adalah dengan ini saya mengesahkan bahawa segala maklumat yang
              diberikan adalah benar. Jika pihak JABATAN PENDAFTARAN PERTUBUHAN
              mendapati berlaku penipuan dan kepalsuan dalam keterangan dokumen
              yang telah saya berikan di atas, maka pihak JABATAN PENDAFTARAN
              PERTUBUHAN adalah dengan ini berhak untuk menolak permohonan saya
              dan jika disabitkan bersalah, saya boleh dikenakan denda tidak
              melebihi denda RM 2000 mengikut seksyen 54 A, AKTA PERTUBUHAN 1966
              serta mana-mana undang-undang yang berkuatkuasa.
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Checkbox
                id="akuan-setuju-terima"
                checked={isChecked}
                onChange={handleCheckboxChange}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="akuan-setuju-terima"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: 1.4,
                }}
              >
                {t("agreementAcceptance")}
              </InputLabel>
            </Box>
          </Box>

          <Box
            sx={{
              border: 1,
              borderColor: "grey.300",
              borderRadius: 1,
              p: 2,
              mb: 3,
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Box sx={{ mb: 1 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: "500 !important",
                  }}
                >
                  {t("paymentMethod")}
                </Typography>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 15 }}>
                <InputLabel
                  htmlFor="cara-pembayaran"
                  required
                  sx={{
                    color: "#333333",
                    fontSize: 14,
                    fontWeight: 400,
                    minWidth: "150px",
                    "& .MuiFormLabel-asterisk": {
                      color: "red",
                    },
                  }}
                >
                  {t("paymentMethod")}
                </InputLabel>
                <Select
                  value={paymentMethod}
                  onChange={handleChange}
                  id="cara-pembayaran"
                  t={t}
                  sx={{
                    flex: 1,
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#E5E5E5",
                      borderRadius: 1,
                    },
                  }}
                >
                  <Option value="kaunter">{t("counter")}</Option>
                  <Option value="online">Online</Option>
                </Select>
              </Box>
            </Box>
            <Typography
              variant="body2"
              sx={{
                color: "#666666",
                fontSize: 12,
                marginTop: 10,
                textAlign: "center",
              }}
            >
              {t("paymentNote")}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <ButtonPrimary
              sx={{
                backgroundColor: "#00A7A7",
                "&:hover": {
                  backgroundColor: "#008F8F",
                },
                borderRadius: 1,
                textTransform: "none",
              }}
              disabled={!isChecked || !paymentMethod}
              onClick={handleSubmit}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </Box>
      </Fade>

      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        PaperProps={{
          style: {
            borderRadius: "8px",
            width: "400px",
            padding: "24px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        }}
      >
        <DialogContent sx={{ textAlign: "center", p: 0, mb: 3 }}>
          <Typography
            sx={{
              fontSize: 16,
              color: "#333333",
              fontWeight: 500,
            }}
          >
            {t("confirmSubmitApplication")}
          </Typography>
        </DialogContent>

        <DialogActions
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 1,
            p: 0,
          }}
        >
          <ButtonPrimary
            onClick={() => handleCloseDialog()}
            sx={{
              width: "79px",
              height: "24px",
              padding: "11px 16px",
              borderRadius: "10px",
              backgroundColor: "#147C7C",
              textTransform: "none",
              fontSize: "14px",
              "&:hover": {
                backgroundColor: "#116666",
              },
            }}
          >
            {t("hantar")}
          </ButtonPrimary>

          <Typography
            onClick={handleCloseDialog}
            sx={{
              fontFamily: "Poppins",
              fontSize: "12px",
              fontWeight: 400,
              lineHeight: "12px",
              textAlign: "center",
              textDecoration: "underline",
              textDecorationStyle: "solid",
              textUnderlinePosition: "from-font",
              textDecorationSkipInk: "none",
              color: "#DADADA",
              cursor: "pointer",
            }}
          >
            {t("kembali")}
          </Typography>
        </DialogActions>
      </Dialog>

      <Dialog
        open={dialogAlertSuccessSaveOpen}
        onClose={() => setDialogAlertSuccessSaveOpen(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ p: 4 }}>
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <img src="/success.gif" alt="success" width={200} height={200} />
          </Box>
          <Typography
            variant="h6"
            component="h2"
            sx={{ fontSize: 28, textAlign: "center" }}
          >
            Permohonan berjaya dihantar.
          </Typography>

          <Box
            sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
          >
            <ButtonPrimary
              sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
              onClick={() => setDialogAlertSuccessSaveOpen(false)}
            >
              {t("Continue")}
            </ButtonPrimary>
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BayaranHarta;
