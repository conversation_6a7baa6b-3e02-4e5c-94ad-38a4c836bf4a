import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  formatDateToDDMMYYYY,
  getLocalStorage,
  MeetingTypeOption,
} from "@/helpers";
// import { useSecretaryBranchReformContext } from "../Provider";

import {
  Box,
  Typography,
  Grid,
  FormControl,
  Select,
  TextField,
  useMediaQuery,
  Theme,
  MenuItem,
  IconButton,
} from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useEffect, useState } from "react";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ile<PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import { useSelector } from "react-redux";
import { EyeIcon } from "@/components/icons";
import { IMeetingOptions } from "@/types";
import { DisabledTextField } from "@/components";
interface MeetingMemberAttendance {
  id: number;
  name: string;
  position: string;
}

interface MeetingForm {
  meetingId: string;
  branchId: number | null;
  branchNo: string | null;
  city: string;
  closing: string;
  confirmBy: string;
  district: string;
  id: number;
  mattersDiscussed: string;
  meetingAddress: string;
  meetingContent: string;
  meetingDate: string;
  meetingMemberAttendances: MeetingMemberAttendance[];
  meetingMethod: string;
  meetingMinute: string;
  meetingPlace: string;
  meetingPurpose: string;
  meetingTime: string;
  meetingTimeDurationMinutes: string;
  meetingTimeTo: string;
  meetingType: string;
  openingRemarks: string;
  otherMatters: string;
  platformType: string;
  postcode: string;
  providedBy: string;
  societyId: number;
  societyNo: string | null;
  state: string;
  totalAttendees: number;
}

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const MeetingForm: React.FC = () => {
  const { id, branchId } = useParams();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const branchAmendRedux = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.data
  );
  const isView = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.isView
  );
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [fileName, setFileName] = useState<string>("");
  const [fileUrl, setFileUrl] = useState<string>("");
  const [disabledNext, setDisableNext] = useState(true);
  const [meetinglist, setMeetinglist] = useState([]);
  const [meetingForm, setMeetingForm] = useState<MeetingForm>({
    meetingId: "",
    branchId: null,
    branchNo: null,
    city: "",
    closing: "",
    confirmBy: "",
    district: "",
    id: 0,
    mattersDiscussed: "",
    meetingAddress: "",
    meetingContent: "",
    meetingDate: "",
    meetingMemberAttendances: [],
    meetingMethod: "",
    meetingMinute: "",
    meetingPlace: "",
    meetingPurpose: "",
    meetingTime: "",
    meetingTimeDurationMinutes: "",
    meetingTimeTo: "",
    meetingType: "",
    openingRemarks: "",
    otherMatters: "",
    platformType: "",
    postcode: "",
    providedBy: "",
    societyId: 0,
    societyNo: null,
    state: "",
    totalAttendees: 0,
  });

  function reset() {
    setMeetingForm({
      meetingId: "",
      branchId: null,
      branchNo: null,
      city: "",
      closing: "",
      confirmBy: "",
      district: "",
      id: 0,
      mattersDiscussed: "",
      meetingAddress: "",
      meetingContent: "",
      meetingDate: "",
      meetingMemberAttendances: [],
      meetingMethod: "",
      meetingMinute: "",
      meetingPlace: "",
      meetingPurpose: "",
      meetingTime: "",
      meetingTimeDurationMinutes: "",
      meetingTimeTo: "",
      meetingType: "",
      openingRemarks: "",
      otherMatters: "",
      platformType: "",
      postcode: "",
      providedBy: "",
      societyId: 0,
      societyNo: null,
      state: "",
      totalAttendees: 0,
    });
  }

  const meetingOptions: IMeetingOptions[] = getLocalStorage("meeting_list", []);

  const getMeetingName = (meetingId: number): string => {
    const meeting = meetingOptions.find((m) => m.id === meetingId);
    if (meeting) {
      return meeting.nameEn;
    }

    return "-";
  };

  const getMeetingMethod = (meetingId: number): string => {
    const meeting = meetingOptions.find((m) => m.id === meetingId);
    if (meeting) {
      return i18n.language === "my" ? meeting.nameBm : meeting.nameEn;
    }
    return "-";
  };
  const { data: addressList } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressListData = addressList?.data?.data || [];

  const getStateName = (stateCode: any) => {
    const stateName = addressListData.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };

  const getDistrict = (val: any) => {
    const address = addressListData
      .filter((items: any) => items.id === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));

    return address?.[0]?.label || "-";
  };

  useCustom({
    url: `${API_URL}/society/meeting/search`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        meetingDate: meetingForm.meetingDate,
      },
    },
    queryOptions: {
      enabled: !!meetingForm.meetingDate,
      onSuccess: (data) => {
        const meetingSearchlist = data?.data?.data?.data || [];
        if (meetingSearchlist.length > 0) {
          setMeetinglist(meetingSearchlist);
        }
      },
    },
  });

  useCustom({
    url: `${API_URL}/society/meeting/${meetingForm.meetingId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!meetingForm.meetingId,
      onSuccess: (data) => {
        const meetingListById = data?.data?.data;
        if (meetingListById) {
          setMeetingForm((prevState) => ({
            ...prevState,
            ...meetingListById,
          }));
        }
      },
    },
  });

  const { mutate: updateMeetingData, isLoading: isLoadingUpdateMeetingData } =
    useCustomMutation();

  const updateMeeting = () => {
    updateMeetingData(
      {
        url: `${API_URL}/society/external/branchAmendment/update`,
        method: "patch",
        values: {
          id: branchId,
          meetingId: meetingForm.id,
          meetingType: meetingForm.meetingType,
          meetingDate: meetingForm.meetingDate,
          meetingPlace: meetingForm.meetingPlace,
          meetingTime: meetingForm.meetingTime,
          totalAttendees: meetingForm.totalAttendees,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanError"),
            type: "error",
          };
        },
      },
      {
        onSuccess() {
          setDisableNext(false);
        },
      }
    );
  };

  useEffect(() => {
    if (isView) {
      setDisableNext(false);
    }
  }, [isView]);

  const RecenterAutomatically = ({
    lat,
    lng,
  }: {
    lat: number;
    lng: number;
  }) => {
    const map = useMap();
    useEffect(() => {
      map.setView([lat, lng]);
    }, [lat, lng]);
    return null;
  };

  const [meetingCoords, setMeetingCoords] = useState<[number, number]>([
    2.745564, 101.707021,
  ]);

  const goNext = () => {
    navigate(
      `/pertubuhan/society/${id}/senarai/cawangan/branch-Info/amend/${branchId}`
    );
  };

  useCustom({
    url: "society/document/documentByParam",
    method: "get",
    queryOptions: {
      enabled: !!meetingForm.meetingId,
      onSuccess(data) {
        const fileInfo = data?.data?.data?.[0];
        if (fileInfo) {
          setFileName(fileInfo?.name);
          setFileUrl(fileInfo?.url);
        }
      },
    },
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        { field: "meetingId", operator: "eq", value: meetingForm.meetingId },
      ],
    },
  });

  useEffect(() => {
    if (branchAmendRedux) {
      setMeetingForm((prevState) => ({
        ...prevState,
        meetingId: branchAmendRedux?.meetingId,
        branchId: branchAmendRedux?.id,
        branchNo: branchAmendRedux?.branchNo,
        district: branchAmendRedux?.districtCode,
        id: branchAmendRedux?.id,
        meetingAddress: branchAmendRedux?.address,
        meetingDate: branchAmendRedux?.meetingDate,
        meetingPlace: branchAmendRedux?.meetingPlace,
      }));
    }
  }, [branchAmendRedux]);

  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };

  const goToMeeting = () => {
    navigate(`/pertubuhan/society/${id}/senarai/mesyuarat`);
  };

  const viewFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <Typography
              sx={{
                color: "#FF0000",
                fontSize: "12px",
                fontWeight: "500 !important",
              }}
            >
              {t("peringatan")} :
            </Typography>
            <Box
              sx={{
                color: "#666666",
                fontSize: "12px",
                fontWeight: "400 !important",
                display: "inline-block",
              }}
            >
              {t("reminderPindaanTitle1")}
              <span
                style={{ cursor: "pointer", color: "var(--primary-color)" }}
                onClick={goToMeeting}
              >
                {" "}
                {t("clickhere")}{" "}
              </span>
              {t("reminderPindaanTitle2")}
            </Box>
          </Box>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("meetingPindaanTitle")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  {t("meetingDate")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextField
                size="small"
                disabled={isView}
                fullWidth
                required
                name="meetingDate"
                value={meetingForm.meetingDate}
                onChange={(e) => {
                  setMeetingForm((prevState: any) => ({
                    ...prevState,
                    meetingDate: e.target.value,
                  }));
                  setMeetinglist([]);
                }}
                inputProps={{
                  max: new Date().toISOString().split("T")[0],
                  onKeyDown: (e) => e.preventDefault(),
                }}
                type="date"
              />
            </Grid>

            {/* Senarai mesyuarat* */}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingList")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingForm.meetingId}
                  disabled={isView}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setMeetingForm((prevState: any) => ({
                      ...prevState,
                      meetingId: e.target.value,
                    }));
                  }}
                >
                  {meetinglist?.map((items: any, index) => {
                    return (
                      <MenuItem key={index} value={items.id}>
                        {getMeetingLabel(items.meetingType)} (
                        {formatDateToDDMMYYYY(items.meetingDate)})
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </Grid>
            {/* Kaedah Mesyuarat*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("meetingMethod")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField
                value={
                  meetingForm.meetingMethod
                    ? getMeetingMethod(parseInt(meetingForm.meetingMethod))
                    : "-"
                }
              />
            </Grid>
            {/* Jenis Platform*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("platformType")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField
                value={getMeetingName(parseInt(meetingForm.platformType))}
              />
            </Grid>
            {/* Tujuan Mesyuarat*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("meetingPurpose")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.meetingPurpose} />
            </Grid>
            {/* Masa */}

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("Masa")}</Typography>
            </Grid>

            <Grid item xs={12} sm={9} gap={1} sx={{ display: "flex" }}>
              <Grid item xs={6}>
                <DisabledTextField value={meetingForm.meetingTime} />
              </Grid>
              <Grid item xs={6}>
                <DisabledTextField value={meetingForm.meetingTimeTo} />
              </Grid>
            </Grid>

            {/* Nama tempat mesyuarat */}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("namaTempatMesyuarat")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField
                value={
                  meetingForm.meetingPlace ? meetingForm.meetingPlace : "-"
                }
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("meetingLocation")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <Box>
                <MapContainer
                  center={meetingCoords}
                  zoom={13}
                  style={{
                    height: "150px",
                    width: "100%",
                    borderRadius: "8px",
                  }}
                >
                  <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                  <Marker position={meetingCoords} />
                  <RecenterAutomatically
                    lat={meetingCoords[0]}
                    lng={meetingCoords[1]}
                  />
                </MapContainer>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("alamatTempatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            {/* Alamat tempat mesyuarat*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingPlaceAddress")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.meetingAddress ?? "-"} />
            </Grid>
            {/* Negeri*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("negeri")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField
                value={
                  meetingForm.state ? getStateName(meetingForm.state) : "-"
                }
              />
            </Grid>
            {/* Daerah*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("daerah")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField
                value={
                  meetingForm.district ? getDistrict(meetingForm.district) : "-"
                }
              />
            </Grid>
            {/* Bandar*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("bandar")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.city ?? "-"} />
            </Grid>

            {/* Poskod */}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("postcode")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.postcode ?? "-"} />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("maklumatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("totalAttendMember")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.totalAttendees} />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("meetingInformation")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("minitMesyuarat")}</Typography>
            </Grid>

            <Grid item xs={12} sm={9}>
              <Box
                sx={{
                  border: "1px solid #DADADA",
                  borderRadius: "8px",
                  p: 3,
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: 1,
                  backgroundColor: "#FFFFFF",
                  cursor: "normal",
                  "&:hover": {
                    backgroundColor: "#F9FAFB",
                  },
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <Box
                    sx={{
                      width: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Box>
                      <Typography
                        sx={{
                          color: "#222222",
                          fontSize: "14px",
                          textAlign: "center",
                        }}
                      >
                        {fileName || "-"}
                      </Typography>
                    </Box>

                    <IconButton
                      onClick={() => viewFile(fileUrl)}
                      sx={{
                        padding: 0,
                      }}
                    >
                      <EyeIcon color="#666666" />
                    </IconButton>
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
        <Grid container spacing={2} sx={{ display: isView ? "none" : "block" }}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => reset()}
            >
              {t("semula")}
            </ButtonOutline>

            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              onClick={updateMeeting}
              disabled={!meetingForm.meetingId || isLoadingUpdateMeetingData}
            >
              {t("update")}
            </ButtonPrimary>
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                width: isMobile ? "100%" : "auto",
                display: isView ? "block" : "none",
              }}
              onClick={() => navigate(-1)}
            >
              {t("back")}
            </ButtonOutline>

            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              disabled={disabledNext}
              onClick={() => goNext()}
            >
              {t("seterusnya")}
            </ButtonPrimary>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default MeetingForm;
