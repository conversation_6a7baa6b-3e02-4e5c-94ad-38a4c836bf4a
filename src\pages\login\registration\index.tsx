import React, { useEffect, useState } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { CreateWn } from "../createWn";
import { CreateBukanWn } from "../createBukanWn";
import { ChevronLeftRounded, DoneRounded } from "@mui/icons-material";
import { Step1 } from "@/pages/register/step1";
import { Step2 } from "@/pages/register/step2";
import { Step3 } from "@/pages/register/step3";
import { ButtonText } from "@/components/button";
import { CitizenshipStatus } from "@/helpers";

export const RegisterSelection = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [citizenStepCounter, setCitizenStepCounter] = useState(0);

  const [emailOTPSuccess, setEmailOTPSuccess] = useState(false);
  const [phoneOTPSuccess, setphoneOTPSuccess] = useState(false);
  const [isMyDigitalIdNewUser, setIsMyDigitalIdNewUser] = useState<
    boolean | null
  >(false);

  const [IC, setIC] = useState("");
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [idType, setIdType] = useState(1);
  const [idNumber, setIdNumber] = useState("");
  const [fullName, setFullName] = useState("");
  const [isUsingMyDigitalId, setIsUsingMyDigitalId] = useState();

  useEffect(() => {
    localStorage.removeItem("isLogout");
    const isNewValue = localStorage.getItem("myDigitalIdNewUser");
    setIsMyDigitalIdNewUser(isNewValue ? JSON.parse(isNewValue) : false);

    const nameValue = localStorage.getItem("myDigitalIdNewUserName");
    const idValue = localStorage.getItem("myDigitalIdNewUserID");
    const isUsingMyDigitalIdValue = localStorage.getItem("isUsingMyDigitalId");

    if (idValue) {
      setIdNumber(JSON.parse(idValue));
    }
    if (nameValue) {
      setFullName(JSON.parse(nameValue));
    }
    if (isUsingMyDigitalIdValue) {
      setIsUsingMyDigitalId(JSON.parse(isUsingMyDigitalIdValue));
    }

    if (isNewValue) {
      if (JSON.parse(isNewValue) === true) {
        setCurrentComponent("createWn");
      }
    }
  }, []);

  const [currentComponent, setCurrentComponent] = useState<string | null>(null);

  const handleBack = () => {
    setCurrentComponent(null);
    navigate("/login");
  };

  const handleNextStep = () => {
    setCitizenStepCounter(citizenStepCounter + 1);
  };

  const handleEmailOTPSuccess = () => {
    setEmailOTPSuccess(true);
  };

  const handlePhoneOTPSuccess = () => {
    setphoneOTPSuccess(true);
  };

  const handlePreviousStep = () => {
    if (citizenStepCounter === 0) {
      setCurrentComponent(null);
      setEmailOTPSuccess(false);
      setphoneOTPSuccess(false);
    } else {
      setEmailOTPSuccess(false);
      setphoneOTPSuccess(false);
      setCitizenStepCounter(0);
    }
  };

  const SelectionBox: React.FC<{ title: string; onClick: () => void }> = ({
    title,
    onClick,
  }) => (
    <Box
      sx={{
        width: "285px",
        height: "144px",
        bgcolor: "white",
        borderRadius: "15px",
        p: "10px 20px",
        boxShadow: 3,
        textAlign: "center",
        cursor: "pointer",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
      onClick={onClick}
    >
      <Typography
        variant="h6"
        sx={{
          fontFamily: "Poppins, sans-serif",
          fontSize: "16px",
          fontWeight: 500,
          lineHeight: "24px",
          textAlign: "center",
          color: "#55556DA1",
        }}
      >
        {title}
      </Typography>
    </Box>
  );

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "#F1F4FA",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "column",
        overflow: "hidden",
        position: "relative",
        backgroundImage: 'url("/bg-feedback.jpg")',
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center center",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: "rgba(153, 153, 153, 0.7)",
          zIndex: 1,
        },
        "& > *": {
          position: "relative",
          zIndex: 2,
        },
      }}
    >
      {currentComponent === null ? (
        <>
          <Typography
            sx={{
              fontFamily: "Poppins, sans-serif",
              fontSize: { xs: "18px", sm: "20px" },
              fontWeight: 500,
              textAlign: "center",
              color: "#FFFFFF",
              mb: 4,
            }}
          >
            {t("pleaseSelectNationality")}
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              width: { xs: "100%", sm: "600px" },
              gap: 4,
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", sm: "row" },
                justifyContent: "center",
                alignItems: "center",
                gap: 2,
                width: "100%",
                zIndex: 2, // Ensure content stays above the background
                position: "relative",
                mb: 2,
              }}
            >
              <SelectionBox
                title={t("citizen")}
                onClick={() => setCurrentComponent("createWn")}
              />
              <SelectionBox
                title={t("non-citizen")}
                onClick={() => setCurrentComponent("createBukanWn")}
              />
            </Box>

            <ButtonText
              onClick={handleBack}
              className="step-header-login"
              sx={{
                mr: 1.5,
                fontSize: "17px !important",
                fontWeight: 700,
                color: "#fff",
              }}
            >
              <ChevronLeftRounded />
              {t("back")}
            </ButtonText>
          </Box>
        </>
      ) : null}

      {currentComponent ? (
        <Box
          sx={{
            display: "flex",
            width: { sm: "600px", md: "840px", lg: "940px" },
            background: "white",
            borderRadius: { xs: "5px", sm: "35px", md: "35px" },
            p: { sm: 1, md: 3 },
            m: 1,
          }}
        >
          {currentComponent === "createWn" && (
            <Grid container spacing={0}>
              <Grid
                item
                sm={4}
                md={4}
                sx={{
                  margin: 0,
                  p: 0,
                  mt: 8,
                  mb: 8,
                  display: { xs: "none", sm: "block" },
                  borderRight: "2px solid #66666640", // Adds a right border
                }}
              >
                <Grid
                  sx={{
                    pr: { xs: 1, md: 1, lg: 3 },
                    pl: { xs: 1, md: 1, lg: 3 },
                    display: "grid",
                    gap: { xs: 2, md: 3, lg: 4 },
                  }}
                >
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {citizenStepCounter > 0 && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    <Grid item md={9}>
                      <Typography className="step-header-login">
                        {t("Langkah")} 1
                      </Typography>
                      <Box>
                        <Typography
                          sx={{ color: "var(--primary-color)" }}
                          className="sub-step-login"
                        >
                          {t("maklumatPengguna")}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {emailOTPSuccess && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    <Grid item md={9}>
                      <Typography
                        className={
                          citizenStepCounter > 0
                            ? "step-header-login"
                            : "step-header-login-disabled"
                        }
                      >
                        {t("Langkah")} 2
                      </Typography>
                      <Box>
                        <Typography
                          className={
                            citizenStepCounter > 0
                              ? "sub-step-login"
                              : "sub-step-login-disabled"
                          }
                        >
                          {t("EmailVerification")}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {phoneOTPSuccess && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    <Grid item md={9}>
                      <Typography
                        className={
                          citizenStepCounter > 1
                            ? "step-header-login"
                            : "step-header-login-disabled"
                        }
                      >
                        {t("Langkah")} 3
                      </Typography>
                      <Box>
                        <Typography
                          className={
                            citizenStepCounter > 1
                              ? "sub-step-login"
                              : "sub-step-login-disabled"
                          }
                        >
                          {t("phoneNoVerification")}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {citizenStepCounter > 3 && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    {isMyDigitalIdNewUser ? null : (
                      <Grid item md={9}>
                        <Typography
                          className={
                            citizenStepCounter > 2
                              ? "step-header-login"
                              : "step-header-login-disabled"
                          }
                        >
                          {t("Langkah")} 4
                        </Typography>
                        <Box>
                          <Typography
                            className={
                              citizenStepCounter > 2
                                ? "sub-step-login"
                                : "sub-step-login-disabled"
                            }
                          >
                            {t("resetPasswordButton")}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} sm={8} md={8}>
                <Box
                  sx={{
                    pr: { sm: 2, md: 9, lg: 9, xl: 9 },
                    pl: { sm: 2, md: 9, lg: 9, xl: 9 },
                    p: 2,
                    display: "grid",
                    justifyContent: "center",
                    alignItems: "center",
                    textAlign: { sm: "center", md: "left" },
                    height: "100%", // Adjust this if needed
                    maxHeight: "100vh", // Adjust as necessary
                    overflowY: "auto",
                    gap: { xs: 2, md: 3 },
                  }}
                >
                  {citizenStepCounter === 0 && (
                    <CreateWn
                      onBack={handlePreviousStep}
                      onNext={() => {
                        handleNextStep();
                      }}
                      setRegIC={setIC}
                      setRegName={setName}
                      setRegEmail={setEmail}
                      setRegPhone={setPhone}
                      name={fullName}
                      IC={idNumber}
                      isMyDigitalIdNewUser={isMyDigitalIdNewUser}
                    />
                  )}
                  {citizenStepCounter === 1 && (
                    <Step1
                      email={email}
                      phoneNumber={phone}
                      onBack={handlePreviousStep}
                      onNext={handleNextStep}
                      onEmailOTPSuccess={handleEmailOTPSuccess}
                    />
                  )}
                  {citizenStepCounter === 2 && (
                    <Step2
                      phoneNumber={phone}
                      onBack={handlePreviousStep}
                      onNext={handleNextStep}
                      onPhoneOTPSuccess={handlePhoneOTPSuccess}
                      IC={IC}
                      name={name}
                      email={email}
                      citizenshipTitle={
                        CitizenshipStatus.find(
                          (item) => item.label === "citizen"
                        )?.value
                      }
                      idType={1}
                      isMyDigitalIdNewUser={isMyDigitalIdNewUser}
                      isUsingMyDigitalId={isUsingMyDigitalId}
                    />
                  )}
                  {citizenStepCounter > 2 && (
                    <Step3
                      IC={IC}
                      name={name}
                      email={email}
                      phoneNumber={phone}
                      citizenshipTitle={
                        CitizenshipStatus.find(
                          (item) => item.label === "citizen"
                        )?.value
                      }
                      idType={1}
                      onBack={handlePreviousStep}
                      onNext={handleNextStep}
                      onSubmit={() => {
                        // Handle completion of registration process
                      }}
                    />
                  )}
                </Box>
              </Grid>
            </Grid>
          )}

          {currentComponent === "createBukanWn" && (
            <Grid container spacing={0}>
              <Grid
                item
                sm={4}
                md={4}
                sx={{
                  margin: 0,
                  p: 0,
                  mt: 8,
                  display: { xs: "none", sm: "block" },
                  borderRight: "2px solid #66666640", // Adds a right border
                }}
              >
                <Grid
                  sx={{
                    pr: { xs: 1, md: 1, lg: 3 },
                    pl: { xs: 1, md: 1, lg: 3 },
                    display: "grid",
                    gap: { xs: 2, md: 3, lg: 4 },
                  }}
                >
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {citizenStepCounter > 0 && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    <Grid item md={9}>
                      <Typography className="step-header-login">
                        {t("Langkah")} 1
                      </Typography>
                      <Box>
                        <Typography
                          sx={{ color: "var(--primary-color)" }}
                          className="sub-step-login"
                        >
                          {t("maklumatPengguna")}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {emailOTPSuccess && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    <Grid item md={9}>
                      <Typography
                        className={
                          citizenStepCounter > 0
                            ? "step-header-login"
                            : "step-header-login-disabled"
                        }
                      >
                        {t("Langkah")} 2
                      </Typography>
                      <Box>
                        <Typography
                          className={
                            citizenStepCounter > 0
                              ? "sub-step-login"
                              : "sub-step-login-disabled"
                          }
                        >
                          {t("EmailVerification")}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {phoneOTPSuccess && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    <Grid item md={9}>
                      <Typography
                        className={
                          citizenStepCounter > 1
                            ? "step-header-login"
                            : "step-header-login-disabled"
                        }
                      >
                        {t("Langkah")} 3
                      </Typography>
                      <Box>
                        <Typography
                          className={
                            citizenStepCounter > 1
                              ? "sub-step-login"
                              : "sub-step-login-disabled"
                          }
                        >
                          {t("phoneNoVerification")}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item md={3}>
                      {citizenStepCounter > 3 && (
                        <Box
                          sx={{
                            width: 38,
                            height: 38,
                            border: "1px solid var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 22,
                              height: 22,
                              bgcolor: "var(--primary-color)",
                              borderRadius: "50%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                          </Box>
                        </Box>
                      )}
                    </Grid>
                    {isMyDigitalIdNewUser ? null : (
                      <Grid item md={9}>
                        <Typography
                          className={
                            citizenStepCounter > 2
                              ? "step-header-login"
                              : "step-header-login-disabled"
                          }
                        >
                          {t("Langkah")} 4
                        </Typography>
                        <Box>
                          <Typography
                            className={
                              citizenStepCounter > 2
                                ? "sub-step-login"
                                : "sub-step-login-disabled"
                            }
                          >
                            {t("resetPasswordButton")}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} sm={8} md={8}>
                <Box
                  sx={{
                    pr: { sm: 2, md: 9, lg: 9, xl: 9 },
                    pl: { sm: 2, md: 9, lg: 9, xl: 9 },
                    p: 2,
                    display: "grid",
                    justifyContent: "center",
                    alignItems: "center",
                    textAlign: { md: "left" },
                    height: "100%",
                    maxHeight: "100vh",
                    overflowY: "auto",
                    gap: { xs: 2, md: 3 },
                  }}
                >
                  {citizenStepCounter === 0 && (
                    <CreateBukanWn
                      onBack={handlePreviousStep}
                      onNext={() => {
                        handleNextStep();
                      }}
                      setRegIC={setIC}
                      setRegName={setName}
                      setRegEmail={setEmail}
                      setRegPhone={setPhone}
                      setRegIdType={setIdType}
                    />
                  )}
                  {citizenStepCounter === 1 && (
                    <Step1
                      email={email}
                      phoneNumber={phone}
                      onBack={handlePreviousStep}
                      onNext={handleNextStep}
                      onEmailOTPSuccess={handleEmailOTPSuccess}
                    />
                  )}
                  {citizenStepCounter === 2 && (
                    <Step2
                      phoneNumber={phone}
                      onBack={handlePreviousStep}
                      onNext={handleNextStep}
                      onPhoneOTPSuccess={handlePhoneOTPSuccess}
                      IC={IC}
                      name={name}
                      email={email}
                      citizenshipTitle={
                        CitizenshipStatus.find(
                          (item) => item.label === "nonCitizen"
                        )?.value
                      }
                      idType={1}
                      isMyDigitalIdNewUser={isMyDigitalIdNewUser}
                      isUsingMyDigitalId={isUsingMyDigitalId}
                    />
                  )}
                  {citizenStepCounter > 2 && (
                    <Step3
                      IC={IC}
                      name={name}
                      email={email}
                      phoneNumber={phone}
                      idType={idType}
                      citizenshipTitle={
                        CitizenshipStatus.find(
                          (item) => item.label === "nonCitizen"
                        )?.value
                      }
                      onBack={handlePreviousStep}
                      onNext={handleNextStep}
                      onSubmit={() => {
                        // Handle completion of registration process
                      }}
                    />
                  )}
                </Box>
              </Grid>
            </Grid>
          )}
        </Box>
      ) : null}
    </Box>
  );
};
