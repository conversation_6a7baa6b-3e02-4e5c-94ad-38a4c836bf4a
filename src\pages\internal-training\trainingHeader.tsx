import React, {useState} from "react";
import {Box, IconButton, InputAdornment, Stack, TextField, Typography} from "@mui/material";
import {Search} from "@mui/icons-material";
import {useNavigate} from "react-router-dom";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";


const InternalTrainingHeader: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");


  return (
    <>
      <TrainingBreadcrumb />
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          mb: 1,
        }}
      >

      </Box>
    </>
  );
}

export default InternalTrainingHeader;
