import React from 'react';
import { Box, Paper, Tooltip, TooltipProps, Typography } from '@mui/material';
import { format } from 'date-fns';

// Define the structure for a tooltip field
interface TooltipField {
  label: string;
  value?: string | null;
  values?: string[] | null;
  isDate?: boolean;
  dateFormat?: string;
  isTime?: boolean;
}

// Props for the DetailedTooltip component
export interface DetailedTooltipProps {
  fields: TooltipField[];
  children: React.ReactElement;
  placement?: TooltipProps['placement'];
  maxWidth?: number | string;
  backgroundColor?: string;
  textColor?: string;
}

/**
 * A reusable tooltip component that displays detailed information in a structured format
 */
const DetailedTooltip: React.FC<DetailedTooltipProps> = ({
  fields,
  children,
  placement = 'top',
  maxWidth = 600,
  backgroundColor = '#666666D9',
  textColor = 'white',
}) => {
  return (
    <Tooltip
      title={
        <Box
          sx={{
            p: 2,
            maxWidth,
            maxHeight: '400px', // Set maximum height
            overflow: 'auto',   // Enable scrolling
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              background: 'transparent',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#888',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              background: '#666',
            },
          }}
        >
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: 'auto 1fr',
            gap: 1,
            alignItems: 'start'
          }}>
            {fields.map((field, index) => {
              // Skip fields with no value or empty arrays
              if (
                (!field.value && !field.values) ||
                (field.values && field.values.length === 0)
              ) {
                return null;
              }

              // Format date if needed
              let displayValue = field.value;
              if (field.isDate && field.value) {
                try {
                  displayValue = format(
                    new Date(field.value),
                    field.dateFormat || 'dd/MM/yyyy'
                  );
                } catch (error) {
                  console.error('Error formatting date:', error);
                }
              }

              // Format time if needed (assuming time is in HH:mm format)
              if (field.isTime && field.value) {
                try {
                  displayValue = format(
                    new Date(`2000-01-01 ${field.value}`),
                    'h:mm a'
                  );
                } catch (error) {
                  console.error('Error formatting time:', error);
                }
              }

              return (
                <React.Fragment key={index}>
                  <Box
                    sx={{
                      display: "flex",
                      gap: 1,
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography
                      variant="body2"
                      fontWeight="bold"
                      sx={{
                        alignSelf: "start",
                        color: textColor,
                      }}
                    >
                      {field.label}
                    </Typography>
                    <Typography sx={{ color: textColor }}>:</Typography>
                  </Box>

                  {field.values ? (
                    <Box>
                      {field.values.map((value, valueIndex) => (
                        <Typography variant="body2" key={valueIndex} sx={{ color: textColor }}>
                          {value || 'Tiada'}
                        </Typography>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" sx={{ color: textColor }}>
                      {displayValue || 'Tiada'}
                    </Typography>
                  )}
                </React.Fragment>
              );
            })}
          </Box>
        </Box>
      }
      arrow
      placement={placement}
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: backgroundColor,
            color: textColor,
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
            '& .MuiTooltip-arrow': {
              // color: backgroundColor,
            },
            p: 0,
            borderRadius: "10px",
            minWidth: 400,
            maxHeight: '400px', // Match the Box maxHeight
          }
        }
      }}
    >
      {children}
    </Tooltip>
  );
};

export default DetailedTooltip;

