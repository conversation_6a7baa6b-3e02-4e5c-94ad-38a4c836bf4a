import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Pagination,
  Select,
  debounce,
  Typography,
} from "@mui/material";
import { t } from "i18next";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  capitalizeWords,
  formatDate,
  getLocalStorage,
} from "../../../../helpers/utils";
import CustomDataGrid from "../../../../components/datagrid";
import { API_URL } from "../../../../api";
import { useNavigate } from "react-router-dom";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { ApplicationStatusEnum, MALAYSIA } from "../../../../helpers/enums";
import { Edit, HourglassBottomRounded } from "@mui/icons-material";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { FieldValues, useForm } from "react-hook-form";
import { EditIcon, EyeIcon } from "../../../../components/icons";
import DataTable, { IColumn } from "@/components/datatable";
import { CrudFilter } from "@refinedev/core";
import { PermissionNames, pageAccessEnum, useQuery } from "@/helpers";
import {
  FormFieldRow,
  Label,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import AuthHelper from "@/helpers/authHelper";

interface FormValues {
  userId?: number | null;
  negeri?: string | null;
  statusPertubuhan?: string | null;
  jenisPemegangJawatan?: string | null;
  carian?: string | null;
}

function PindaanPerlembagaanTab() {
  const navigate = useNavigate();

  const [amendmentRecordList, setAmendmentRecordList] = useState([]);
  const [total, setTotal] = useState<number>(0);

  const statusPertubuhan = [
    { value: 1, label: "Aktif" },
    { value: 2, label: "Tidak aktif" },
    { value: 3, label: "Digantung" },
    { value: 4, label: "Dalam Proses" },
    { value: 5, label: "Dibubarkan" },
  ];

  const addressList = getLocalStorage("address_list", null);

  const malaysiaList =
    addressList.filter((address: any) => address.pid === MALAYSIA) ?? [];

  const getStateName = (id: string) =>
    malaysiaList.find((state: any) => state.id === parseInt(id))?.name || "-";
  const hasKelulusanUpdatePermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Update
  );

  const stateList = malaysiaList.map((i: any) => ({
    value: i.id,
    label: i.name,
  }));

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1) ?? [];

  const mainCategoriesOptions = mainCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  const { getValues, setValue, watch, control, reset } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
      isQuery: 0,
      organizationCategory: "",
      statusPertubuhan: "",
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const {
    data: searchResult,
    isLoading: isSearchLoading,
    refetch: fetchPendingResult,
  } = useQuery({
    url: `society/roDecision/getDecisionRecord/amendment`,
    autoFetch: false,
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      setAmendmentRecordList(responseData?.data || []);
      setTotal(responseData?.total);
    },
  });

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
    ];

    if (watch("carian")) {
      filters.push({
        field: "carian",
        value: watch("carian"),
        operator: "eq",
      });
    }

    setValue("page", newPage);
    fetchPendingResult({ filters });
  };

  const handleSearch = () => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "searchQuery",
          value: watch("searchQuery"),
          operator: "eq",
        },
        {
          field: "state",
          value: watch("state"),
          operator: "eq",
        },
        {
          field: "status",
          value: watch("status"),
          operator: "eq",
        },
      ],
    });
  };

  const handleClearSearch = () => {
    reset();
  };

  const columns: IColumn[] = [
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.societyName ?? "-";
      },
    },
    {
      field: "societyNo",
      headerName: t("noPertubuhan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.societyNo ?? "-";
      },
    },
    {
      field: "applicationStatusCode",
      headerName: t("statusPermohonan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return t(
          ApplicationStatusEnum[
            (row?.applicationStatusCode as keyof typeof ApplicationStatusEnum) ||
              "-"
          ]
        );
      },
    },
    {
      field: "amendmentType",
      headerName: t("constitutionType"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.amendmentType ? row?.amendmentType : "-";
      },
    },
    {
      field: "approvedDate",
      headerName: t("tarikhPermohonan"),
      flex: 1,
      align: "center",
      renderCell: (params: any) =>
        params?.row?.submissionDate
          ? formatDate(params?.row?.submissionDate)
          : "-",
    },
    {
      field: "stateCode",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      renderCell: (params: any) =>
        params?.row?.stateCode ? getStateName(params?.row?.stateCode) : "-",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        if (hasKelulusanUpdatePermission) {
          const row = params.row;
          return (
            <Box sx={{ display: "flex" }}>
              <IconButton
                onClick={() => {
                  const amendmentId = btoa(row.id);
                  const societyId = btoa(row.societyId);
                  navigate(
                    `/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/pindaan-perlembagaan/${amendmentId}/${societyId}`
                  );
                }}
                sx={{ color: "black", minWidth: 0, p: 0.5 }}
              >
                <EyeIcon
                  sx={{
                    color: "var(--primary-color)",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            </Box>
          );
        }
      },
    },
  ];

  useEffect(() => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "state",
          value: watch("state"),
          operator: "eq",
        },
        {
          field: "status",
          value: watch("status"),
          operator: "eq",
        },
      ],
    });
  }, []);

  return (
    <>
      <Box component="form">
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("amendmentList")}
            </Typography>
            {/* state */}
            <FormFieldRow
              label={<Label text={t("Negeri")} />}
              value={
                <SelectFieldController
                  name="state"
                  control={control}
                  options={stateList}
                  placeholder={t("selectPlaceholder")}
                />
              }
            />
            {/* status permohonan */}
            <FormFieldRow
              label={<Label text={t("Status permohonan")} />}
              value={
                <SelectFieldController
                  name="status"
                  control={control}
                  options={statusPertubuhan}
                  placeholder={t("selectPlaceholder")}
                />
              }
            />
            {/* finding/carian */}
            <Grid container spacing={2} marginBottom={1} alignItems={"center"}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("Carian")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextFieldController
                  name="searchQuery"
                  control={control}
                  // onChange={handleSearchName}
                />
              </Grid>
            </Grid>

            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  // flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    // width: isMobile ? "100%" : "auto",
                  }}
                  onClick={handleClearSearch}
                >
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary
                  onClick={handleSearch}
                  variant="contained"
                  sx={{
                    // width: isMobile ? "100%" : "auto",
                    boxShadow: "none",
                  }}
                >
                  {t("search")}
                </ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("amendmentList")}
            </Typography>

            <DataTable
              columns={columns}
              rows={amendmentRecordList}
              page={page}
              rowsPerPage={pageSize}
              totalCount={total}
              onPageChange={handleChangePage}
              paginationType="custom"
            />
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default PindaanPerlembagaanTab;
