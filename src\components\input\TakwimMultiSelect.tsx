import React, { useRef } from 'react';
import { Grid, Typography, FormControl, Select, MenuItem, Chip, Box } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import CloseIcon from '@mui/icons-material/Close';

interface TakwimMultiSelectProps {
  label: string;
  value: number[]|string[];
  onChange: (value: number[]) => void;
  options: { value: number; label: string }[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
}

export const TakwimMultiSelect: React.FC<TakwimMultiSelectProps> = ({
  label,
  value,
  onChange,
  options,
  placeholder = 'Sila pilih',
  required = false,
  disabled = false,
  error = false,
  helperText,
}) => {
  const [openSelect, setOpenSelect] = React.useState(false);
  const ignoreNextOpen = useRef(false);

  const handleClose = () => {
    setOpenSelect(false);
  };

  const handleOpen = () => {
    if (ignoreNextOpen.current) {
      ignoreNextOpen.current = false;
      return;
    }
    setOpenSelect(true);
  };

  return (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{
            color: '#666666',
            fontSize: '14px',
          }}
        >
          {label}
          {required && <span style={{ color: 'red' }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        <FormControl fullWidth error={error}>
          <Select
            multiple
            size="small"
            value={value}
            onChange={(e) => onChange(e.target.value as number[])}
            displayEmpty
            disabled={disabled}
            open={openSelect}
            onClose={handleClose}
            onOpen={handleOpen}
            renderValue={(selected) => {
              if (selected.length === 0) {
                return <span style={{ color: '#aaa' }}>{placeholder}</span>;
              }
              return (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {(selected as number[]).map((value) => (
                    <Chip
                      key={value}
                      label={options.find(opt => opt.value === value)?.label}
                      size="small"
                      sx={{ backgroundColor: '#4DB6AC', color: 'white'}}
                      onDelete={() => {
                        ignoreNextOpen.current = true;
                        const newSelected = (selected as number[]).filter(item => item !== value);
                        onChange(newSelected);
                      }}
                      deleteIcon={
                        <CloseIcon
                          fontSize="medium"
                          sx={{ color: 'white', '&:hover': { color: '#e0e0e0' } }}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            ignoreNextOpen.current = true;
                            const newSelected = (selected as number[]).filter(item => item !== value);
                            onChange(newSelected);
                          }}
                        />
                      }
                    />
                  ))}
                </Box>
              );
            }}
            IconComponent={(props) => (
              <KeyboardArrowDownIcon {...props} sx={{ color: '#6C6F93' }} />
            )}
            sx={{
              minWidth: 240,
              fontSize: 14,
              color: 'black',
              '& .MuiSelect-select': {
                py: .75,
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#CFD0E2',
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#CFD0E2',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#CFD0E2',
              },
            }}
          >
            {options.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  );
};





