import { Fragment } from "react";
import { useBack } from "@refinedev/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useOutlet } from "react-router-dom";

import { Box, IconButton, Typography } from "@mui/material";

const GeranExternalLayout = () => {
  const back = useBack();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const outlet = useOutlet();

  const breadcrumbs = [
    {
      label: t("geran"),
      path: "/geran",
    },
  ];

  return (
    <Box
      sx={{
        mx: 3,
      }}
    >
      <Box
        sx={{
          mb: 2,
          display: "flex",
          alignItems: "center",
          gap: 1,
          mt: 3,
        }}
      >
        <IconButton onClick={() => back()}>
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
              fill="#666666"
            />
          </svg>
        </IconButton>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {breadcrumbs.map((item, index, array) => (
            <Fragment key={item.path}>
              <Typography
                fontWeight={500}
                sx={{
                  color: "#666666",
                  cursor: "pointer",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
                onClick={() => navigate(item.path)}
              >
                {item.label}
              </Typography>
              {index < array.length - 1 && (
                <svg
                  key={`icon-${item.path}`}
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
                    fill="#666666"
                  />
                </svg>
              )}
            </Fragment>
          ))}
        </Box>
      </Box>
      {outlet}
    </Box>
  );
};

export default GeranExternalLayout;
