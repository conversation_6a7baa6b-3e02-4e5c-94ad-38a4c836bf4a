import React from "react";
import { useTranslation } from "react-i18next";
import { useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, InputAdornment, Typography } from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  SwitchForm,
} from "@/components";

const Form = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const { control, watch } = useFormContext();

  return (
    <Box className={classes.sectionBox}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mb: 2,
        }}
      >
        <Typography className="title" mb={2}>
          Penerima geran JPPM sebelum ini
        </Typography>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Typography className="label">Tanda Jika YA</Typography>
          <SwitchForm name="is_active" />
        </Box>
      </Box>

      {watch("is_active") && (
        <>
          <FormFieldRow
            label={<Label text="Tahun Program" required />}
            value={<TextFieldController control={control} name="test" />}
          />

          <FormFieldRow
            label={<Label text="Jumlah" required />}
            value={
              <TextFieldController
                control={control}
                type="text"
                name="assetValue"
                isNumber
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">RM</InputAdornment>
                  ),
                }}
              />
            }
          />
        </>
      )}
    </Box>
  );
};

const PenerimaGeranForm = React.memo(Form);
export default PenerimaGeranForm;
