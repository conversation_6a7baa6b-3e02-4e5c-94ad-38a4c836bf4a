import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import InputLabel from "@mui/material/InputLabel";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { Select, Option } from "@/components/input";
// import { OrganizationStepper } from "../organization-stepper";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCreate } from "@refinedev/core";
import Input from "@/components/input/Input";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { API_URL } from "@/api";
// import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import { PaymentPrefixes } from "@/helpers";
import dayjs from "dayjs";
export const Kaunter = () => {

  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  //const [societyData, setSocietyData] = useState<any>(null);

  const navigate = useNavigate();

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const { id: societyId } = useParams();
  const location = useLocation();
  const publicOfficerId = location.state?.publicOfficerId;
  const propertyOfficerId = location.state?.propertyOfficerId;

  const createdDate = location.state?.createdDate; 
  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };


  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  }

  const handleCetak = async () => { 
    try {
      if (encodedId) { 
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: publicOfficerId
              ? `Pendaftaran Pegawai Awam (Pembayaran KAUNTER)`
              : `Pendaftaran Pegawai Harta (Pembayaran KAUNTER);`,
            paymentMethod: "C", // O = online, C = counter
            societyId: societyId,
            registerDateTime:dayjs(createdDate).format("YYYY-MM-DDTHH:mm:ss"),
            amount: 10.0,
            referenceNo: publicOfficerId
              ? PaymentPrefixes.PEGAWAI_AWAM + publicOfficerId
              : PaymentPrefixes.PEGAWAI_HARTA + encodedId,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };


  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyByIdData.data)

  /*useEffect(() => {
    const fetchSocietyData = async () => {
      try {
        if (encodedId) {
          const response = await fetch(`${API_URL}/society/${atob(encodedId)}`, {
            headers: {
              portal: localStorage.getItem("portal") || "",
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          });
          const data = await response.json();
          setSocietyData(data);

        }
      } catch (error) {
        console.error("Error fetching society data:", error);
      }
    };

    fetchSocietyData();
  }, [encodedId]);*/

  return (
    <Box sx={{ display: "flex"}}>
      <Box sx={{ width: "100%" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2
                  }}
                >
                  {t("pengesahan")} {t("payment")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2
                }}
              >
                {t("infoPayment")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={1}>
                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyDataRedux?.societyName || ""}
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  value={societyDataRedux?.applicationNo || ""}
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value="Pembayaran Kaunter"
                />
                <Input disabled label={t("paymentAmount")} value="RM 10.00" />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2
                }}
              >
                {t("noteKaunter")}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, marginTop: 3 }}>
              <ButtonOutline onClick={() => navigate(`/pertubuhan/society/${societyId}/senarai/ajk/pegawai/`)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F"
                  },
                  borderRadius: 1,
                  textTransform: "none"
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        {/* <OrganizationStepper activeStep={activeStep} /> */}

        {/* <InfoQACard /> */}
      </Box>
    </Box>
  );
};

export default Kaunter;
