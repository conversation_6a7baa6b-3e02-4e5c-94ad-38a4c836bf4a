import { getChatbotOpened, setChatbotOpenedRedux } from "@/redux/chatbotReducer";
import { useMediaQuery, useTheme } from "@mui/material";
import { createContext, FC, MouseEvent, PropsWithChildren, useContext } from "react";
import { useDispatch, useSelector } from "react-redux";
import { SpeechProvider } from "./SpeechContext";

export type ChatbotContextType = {
  toggleChat(e: MouseEvent): void
  isChatbotOpened: boolean
  isChatbotOpenedTablet: boolean
  isChatbotOpenedMobile: boolean
}

export const ChatbotContext = createContext<ChatbotContextType>(null!)

export const ChatbotProvider: FC<PropsWithChildren> = ({ children }) => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const isChatbotOpened = useSelector(getChatbotOpened);
  const matchMobileSize = useMediaQuery(theme.breakpoints.down("md"));
  const matchTabletSize = useMediaQuery(theme.breakpoints.up("md"));

  const isChatbotOpenedTablet = matchTabletSize && isChatbotOpened
  const isChatbotOpenedMobile = matchMobileSize && isChatbotOpened

  const toggleChat = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    e.stopPropagation()
    console.log('toggleChat called, current state:', isChatbotOpened, 'setting to:', !isChatbotOpened)
    dispatch(setChatbotOpenedRedux(!isChatbotOpened))
  }

  return (
    <ChatbotContext.Provider
      value={{
        toggleChat,
        isChatbotOpened,
        isChatbotOpenedMobile,
        isChatbotOpenedTablet
      }}>
      <SpeechProvider>
        {children}
      </SpeechProvider>
    </ChatbotContext.Provider>
  )
}



export const useChatbotContext = () =>
  useContext(ChatbotContext)
