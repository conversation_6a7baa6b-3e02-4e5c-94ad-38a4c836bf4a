import { useEffect } from "react";
import { Box, Typography, CircularProgress } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ChevronLeftRounded } from "@mui/icons-material";

import { ButtonText } from "@/components/button";
import AuthHelper from "@/helpers/authHelper";
import { useQuery, USER_DETAILS_KEY } from "@/helpers";
import {
  setUserDataRedux,
  setUserPortalRedux,
  setUserTokenRedux,
} from "@/redux/userReducer";
import { useDispatch } from "react-redux";
import { CrudFilter } from "@refinedev/core";

export const PendingMyDigitalIdVerification = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    localStorage.removeItem("isLogout");
  }, []);

  const handleBack = () => {
    navigate("/login");
  };

  const { data, isLoading, refetch } = useQuery({
    url: "user/auth/myDigitalId/token",
    withAuthHeaders: false,
    onSuccess: (data) => {
      if (data?.data?.isNewUser) {
        localStorage.setItem("myDigitalIdNewUser", JSON.stringify(true));
        localStorage.setItem(
          "myDigitalIdNewUserName",
          JSON.stringify(data?.data?.fullName)
        );
        localStorage.setItem(
          "myDigitalIdNewUserID",
          JSON.stringify(data?.data?.identificationNo)
        );
        localStorage.setItem("isUsingMyDigitalId", JSON.stringify(true));

        navigate("/register");
      } else {
        AuthHelper.setToken(data?.data.token);
        localStorage.setItem(
          USER_DETAILS_KEY,
          JSON.stringify(data?.data.userDetails)
        );
        dispatch(setUserTokenRedux(data?.data.token));
        dispatch(setUserDataRedux(data?.data.userDetails));
        const userGroupList = data?.data.userGroupList;

        if (userGroupList.includes(1) && userGroupList.includes(2)) {
          navigate("/selection");
        } else {
          if (userGroupList.includes(1)) {
            localStorage.setItem("portal", "1");
            dispatch(setUserPortalRedux(1));
          } else if (userGroupList.includes(2)) {
            localStorage.setItem("portal", "2");
            dispatch(setUserPortalRedux(2));
            navigate("/internal-user");
          }
          navigate("/");
        }
      }
    },
    autoFetch: false,
  });

  useEffect(() => {
    // if (dummyCode) {
    //   if (dummyCode?.isNewUser === true) {
    //     localStorage.setItem("myDigitalIdNewUser", JSON.stringify(true));
    //     localStorage.setItem(
    //       "myDigitalIdNewUserName",
    //       JSON.stringify(dummyCode?.fullName)
    //     );
    //     localStorage.setItem(
    //       "myDigitalIdNewUserID",
    //       JSON.stringify(dummyCode?.identificationNo)
    //     );
    //     localStorage.setItem("isUsingMyDigitalId", JSON.stringify(true));

    //     navigate("/register");
    //   } else {
    //     AuthHelper.setToken(dummyCode.token);
    //     localStorage.setItem(
    //       USER_DETAILS_KEY,
    //       JSON.stringify(dummyCode.userDetails)
    //     );
    //     dispatch(setUserTokenRedux(dummyCode.token));
    //     dispatch(setUserDataRedux(dummyCode.userDetails));
    //     const userGroupList = dummyCode.userGroupList;

    //     if (userGroupList.includes(1) && userGroupList.includes(2)) {
    //       navigate("/selection");
    //     } else {
    //       if (userGroupList.includes(1)) {
    //         localStorage.setItem("portal", "1");
    //         dispatch(setUserPortalRedux(1));
    //       } else if (userGroupList.includes(2)) {
    //         localStorage.setItem("portal", "2");
    //         dispatch(setUserPortalRedux(2));
    //         navigate("/internal-user");
    //       }
    //       navigate("/");
    //     }
    //   }
    // }
    const searchParams = new URLSearchParams(window.location.search);
    const code = searchParams.get("code");
    if (code) {
      const filters: CrudFilter[] = [
        { field: "code", value: code, operator: "eq" },
      ];

      refetch({ filters });
    }
  }, []);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "#F1F4FA",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "column",
        overflow: "hidden",
        position: "relative",
        backgroundImage: 'url("/bg-feedback.jpg")',
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center center",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: "rgba(153, 153, 153, 0.7)",
          zIndex: 1,
        },
        "& > *": {
          position: "relative",
          zIndex: 2,
        },
      }}
    >
      <>
        <Typography
          sx={{
            fontFamily: "Poppins, sans-serif",
            fontSize: { xs: "18px", sm: "20px" },
            fontWeight: 500,
            textAlign: "center",
            color: "#FFFFFF",
            mb: 4,
          }}
        >
          <img width={520} src={"/myDigitalId.png"} alt={"myDigialId"} />
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: { xs: "100%", sm: "600px" },
            gap: 4,
          }}
        >
          <Box
            sx={{
              display: "grid",
              justifyContent: "center",
              alignItems: "center",
              gap: 2,
              width: "100%",
              mb: 2,
            }}
          >
            <Typography
              sx={{
                color: "white",
                fontWeight: 500,
                fontSize: 25,
                textShadow: "0px 3px 9px black",
              }}
            >
              {t("verificationInProgress")} . . .
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <CircularProgress />
            </Box>
          </Box>

          <ButtonText
            onClick={handleBack}
            className="step-header-login"
            sx={{
              mr: 1.5,
              fontSize: "17px !important",
              fontWeight: 700,
              color: "#fff",
            }}
          >
            <ChevronLeftRounded />
            {t("back")}
          </ButtonText>
        </Box>
      </>
    </Box>
  );
};
