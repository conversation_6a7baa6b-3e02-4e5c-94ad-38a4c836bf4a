import { CommitteeNonCitizenBySocietyRequestBody } from "@/components/form/comittee/noncitizen/BySocietyIdInner";
import dayjs from "@/helpers/dayjs";
import { usejawatankuasaContext } from "@/pages/pertubuhan/ajk/jawatankuasa/jawatankuasaProvider";
import { AjkNonCiizen } from "@/pages/pertubuhan/pernyata-tahunan/interface"
import { useLocation, useParams } from "react-router-dom"

export function useFormManagementCommitteeNonCitizenSocietyByIdInitialValue<
  RequestBody extends CommitteeNonCitizenBySocietyRequestBody = CommitteeNonCitizenBySocietyRequestBody,
  Value extends AjkNonCiizen = AjkNonCiizen
>() {
  const location = useLocation();
  const { id: societyId } = useParams();
  const { society, meetingId, documentIds, appointmentDateG, savedMeetingDate } = usejawatankuasaContext();

  const ajkNonCitizenState: Value | null = location.state?.ajkNonCitizen ?? null

  const getInitialValue = (): RequestBody => {
    // Please don't remove this new value because it's a new requirement for society AJK
    // you have to select meetingId first on page `/pertubuhan/society/:societyId/senarai/ajk/jawatankuasa/update-ajk`
    // before create an AJK
    // <-- [START] -->
    const sharedPayload = {
      meetingId,
      documentId: documentIds,
      appointedDate: savedMeetingDate ?? appointmentDateG
    }
    // <-- [END] -->
    if (ajkNonCitizenState !== null) {
      return {
        ...ajkNonCitizenState,
        ...sharedPayload,
        citizenshipStatus: parseInt(ajkNonCitizenState.citizenshipStatus),
        applicantCountryCode: parseInt(ajkNonCitizenState.applicantCountryCode),
        designationCode: parseInt(ajkNonCitizenState.designationCode as unknown as string),
        visaExpirationDate: ajkNonCitizenState.visaExpirationDate !== null
          ? dayjs(ajkNonCitizenState.visaExpirationDate).toDate()
          : null,
        permitExpirationDate: ajkNonCitizenState.permitExpirationDate !== null
          ? dayjs(ajkNonCitizenState.permitExpirationDate).toDate()
          : null,
      } as unknown as RequestBody
    }
    return {
      id: null,
      societyId,
      societyNo: society?.societyNo ?? 0,
      societyName: society?.societyName ?? "",
      name: "",
      citizenshipStatus: 2,
      identificationType: null,
      identificationNo: "",
      visaExpirationDate: null,
      applicantCountryCode: null,
      gender: null,
      residentialAddress: "",
      residentialCity: null,
      visaNo: null,
      permitNo: null,
      permitExpirationDate: null,
      tujuanDMalaysia: "",
      stayDurationDigit: 0,
      stayDurationUnit: "",
      designationCode: "",
      otherDesignationCode: "",
      ...sharedPayload
    } as unknown as RequestBody
  }

  return {
    getInitialValue
  }
}
