import React from "react";
import { useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, InputAdornment, Typography } from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  FileUploadController,
} from "@/components";

const Form = () => {
  const classes = globalStyles();
  const { control } = useFormContext();

  return (
    <>
      <Box className={classes.sectionBox} mb={1}>
        <Typography
          sx={{
            color: "#FF0000",
            fontWeight: "500",
            fontSize: "12px !important",
            "& span": { color: "#666666", fontWeight: "400" },
          }}
        >
          NOTA
          <span>
            : Maksimum komponen pembelian peralatan keselamatan adalah RM5,000
            sahaja. Manakala Program Keselamatan Komuniti melibatkan jumlah
            perbelanjaan sebanyak RM5,000 sahaja ATAU sehingga RM10,000
          </span>
        </Typography>
      </Box>

      <Box className={classes.sectionBox}>
        <Typography className="title" mb={2}>
          Perbelanjaan pelaksanaan program
        </Typography>

        <ExpenseRow label="Bahan Bercetak" fieldPrefix="bahan_bercetak" />
        <ExpenseRow label="Alat Tulis" fieldPrefix="alat_tulis" />
        <ExpenseRow label="Sijil" fieldPrefix="sijil" />
        <ExpenseRow label="Publisiti" fieldPrefix="publisiti" />
        <ExpenseRow
          label="Bayaran saguhati penceramah"
          fieldPrefix="saguhati_penceramah"
        />
        <ExpenseRow
          label="Kos makan dan minum"
          fieldPrefix="saguhati_penceramah"
        />

        <ExpenseRow
          label="Kos Bayaran Tempat Program (hotel tidak dibenarkan)"
          fieldPrefix="saguhati_penceramah"
        />

        <ExpenseRow
          label="Anggaran harga lain-lain"
          fieldPrefix="saguhati_penceramah"
        />

        <FormFieldRow
          align="flex-start"
          label={<Label text="Lampiran lain-lain" />}
          value={<FileUploadController control={control} name={`other_file`} />}
        />

        <FormFieldRow
          label={<Label text="Lampiran lain-lain" />}
          value={
            <TextFieldController
              control={control}
              name="test"
              isNumber
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">RM</InputAdornment>
                ),
              }}
            />
          }
        />
      </Box>
    </>
  );
};

const ExpenseRow = ({
  label,
  fieldPrefix,
  required = false,
}: {
  label: string;
  fieldPrefix: string;
  required?: boolean;
}) => {
  const { control } = useFormContext();

  return (
    <FormFieldRow
      label={<Label text={label} required={required} />}
      value={
        <Box display="flex" gap={1}>
          <Box flex={1}>
            <TextFieldController
              control={control}
              name={`${fieldPrefix}.quantity`}
              placeholder="Bilangan"
              type="number"
              required={required}
            />
          </Box>
          <Box flex={3}>
            <TextFieldController
              control={control}
              name={`${fieldPrefix}.price`}
              placeholder="Anggaran Harga"
              type="number"
              required={required}
            />
          </Box>
          <Box flex={1}>
            <TextFieldController
              control={control}
              name={`${fieldPrefix}.total`}
              placeholder="Jumlah"
              type="number"
              required={required}
            />
          </Box>
        </Box>
      }
    />
  );
};

const PerbelanjaanProgramForm = React.memo(Form);
export default PerbelanjaanProgramForm;
