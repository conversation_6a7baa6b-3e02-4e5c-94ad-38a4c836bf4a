import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const TrainingIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"
         style={{ color, ...sx }}
         {...props}
    >
      <path d="M3.72222 14.3713H2.55556C1.69645 14.3713 1 13.7061 1 12.8856V2.4857C1 1.66517 1.69645 1 2.55556 1H13.4444C14.3036 1 15 1.66517 15 2.4857V12.8856C15 13.7061 14.3036 14.3713 13.4444 14.3713H12.2778M8 13.6284C9.28866 13.6284 10.3333 12.6307 10.3333 11.3999C10.3333 10.1691 9.28866 9.17132 8 9.17132C6.71134 9.17132 5.66667 10.1691 5.66667 11.3999C5.66667 12.6307 6.71134 13.6284 8 13.6284ZM8 13.6284L8.01668 13.6283L5.53341 16L3.33352 13.8989L5.68195 11.6559M8 13.6284L10.4833 16L12.6832 13.8989L10.3348 11.6559M5.66667 3.97139H10.3333M4.11111 6.57136H11.8889"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
      />
    </svg>

  );
});
