import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { CustomTabsPanel } from "@/components";
import CiptaPembatalan from "./cipta-pembatalan";
import SenaraiPembatalan from "./senarai-pembatalan";

const Pembatalan: React.FC = () => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const isMyLanguage = i18n.language === "my";

  const [initialTab, setInitialTab] = useState(0);

  useEffect(() => {
    if (location.state?.tab === "senarai-pembatalan") {
      setInitialTab(1);
    } else {
      setInitialTab(0);
    }
  }, [location.state]);

  const tabItems = [
    {
      label: isMyLanguage ? "Cipta Pembatalan" : "Create Cancellation",
      content: <CiptaPembatalan />,
    },
    {
      label: isMyLanguage ? "Senarai Pembatalan" : "Cancellation List",
      content: <SenaraiPembatalan />,
    },
  ];

  return <CustomTabsPanel tabs={tabItems} initialTab={initialTab} />;
};

export default Pembatalan;
