import { ButtonPrimary, DataTable } from "@/components";
import {
  ApplicationStatusList,
  capitalizeWords,
  NewSocietyBranchStatus,
  useDebounce,
  useQuery,
} from "@/helpers";
import { ChevronLeft } from "@mui/icons-material";
import { Box, IconButton, Typography } from "@mui/material";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import Footer from "../footer";
import axios from "axios";
import { API_URL } from "@/api";
import { useEffect } from "react";
import { useCustom } from "@refinedev/core";

interface CarianPertubuhanLandingProps {
  searchTerm: string;
  onClose: () => void;
}

export default function CarianPertubuhanLanding({
  searchTerm,
  onClose,
}: CarianPertubuhanLandingProps) {
  const { t } = useTranslation();

  const active = [NewSocietyBranchStatus.AKTIF_1];

  const columns = [
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box
            style={{
              textDecoration: "none",
            }}
          >
            {params?.row?.societyName}
          </Box>
        );
      },
    },
    {
      field: "societyNo",
      headerName: t("ppmNumber"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const societyNo = params?.row?.societyNo;
        const applicationNo = params?.row?.applicationNo;

        return societyNo ?? applicationNo ?? t("-");
      },
      cellClassName: "custom-cell",
    },
    {
      field: "statusCode",
      headerName: t("organizationStatus"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const isActive = active.includes(params?.row?.statusCode);
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Typography
              className="status-pertubuhan-text"
              sx={{
                backgroundColor: "#fff",
                border: `2px solid ${
                  isActive ? "var(--success)" : "var(--error)"
                }`,
              }}
            >
              {isActive ? t("active") : t("inactive")}
            </Typography>
          </Box>
        );
      },
    },
  ];

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
    },
  });

  const debouncedSearchTerm = useDebounce(searchTerm, 1000);

  const page = watch("page");
  const pageSize = watch("pageSize");

  const { data: societyListDataResponse, isLoading } = useCustom({
    url: `${API_URL}/society/searchByNameByPublic`,
    method: "get",
    config: {
      headers: {
        // portal: localStorage.getItem("portal"),
        // authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "societyName",
          value: debouncedSearchTerm,
          operator: "eq",
        },
      ],
    },
    queryOptions: {
      enabled: debouncedSearchTerm !== "",
      retry: false,
      cacheTime: 0,
    },
  });

  const totalList = societyListDataResponse?.data?.data?.total ?? 0;
  const rowData = societyListDataResponse?.data?.data?.data ?? [];

  const handleChangePage = (newPage: number) => setValue("page", newPage);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        backgroundColor: "#F1F4FA",
      }}
    >
      <Box
        sx={{
          flexGrow: 1,
          p: 2,
          mt: 10,
          display: "grid",
          gap: 3,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-start",
            alignItems: "center",
          }}
        >
          <IconButton onClick={onClose}>
            <ChevronLeft />
          </IconButton>
        </Box>
        <Box
          sx={{
            display: "grid",
            justifySelf: "center",
            alignItems: "center",
            width: "50%",
            backgroundColor: "white",
            p: 4,
            mb: 6,
            borderRadius: 3,
          }}
        >
          <Typography sx={{ fontSize: 30, color: "var(--text-grey)" }}>
            {t("searchOrganization")}
          </Typography>
          <Box
            sx={{
              border: "1px solid var(--border-grey)",
              p: 2,
              mt: 4,
              borderRadius: 2,
            }}
          >
            <DataTable
              columns={columns as any}
              rows={rowData}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalList}
              onPageChange={(newPage) => setValue("page", newPage)}
              onPageSizeChange={(newPageSize) => {
                setValue("page", 1);
                setValue("pageSize", newPageSize);
              }}
              isLoading={isLoading}
              paginationType={"custom"}
            />
          </Box>
        </Box>
      </Box>

      {/* footer */}
      <Box
        sx={{
          backgroundColor: "var(--primary-color)",
          padding: "48px 60px 48px 60px",
          "@media (max-width: 800px)": {
            padding: "60px 30px 60px 30px",
          },
          "@media (max-width: 450px)": {
            padding: "60px 15px 60px 15px",
          },
        }}
      >
        <Footer />
      </Box>
    </Box>
  );
}
