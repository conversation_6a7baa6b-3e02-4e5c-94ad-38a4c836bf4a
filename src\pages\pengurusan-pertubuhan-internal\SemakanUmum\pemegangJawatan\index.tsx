import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Pagination,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import { getLocalStorage } from "../../../../helpers/utils";
import CustomDataGrid from "../../../../components/datagrid";
import { API_URL } from "../../../../api";
import { useNavigate } from "react-router-dom";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import {
  ApplicationStatusEnum,
  OrganisationPositionLabel,
  OrganisationPositions,
  PermissionNames,
  pageAccessEnum,
} from "../../../../helpers/enums";
import { Edit, HourglassBottomRounded } from "@mui/icons-material";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { FieldValues, useForm } from "react-hook-form";
import { EditIcon, EyeIcon } from "../../../../components/icons";
import DisabledTextField from "../../../../components/input/DisabledTextField";
import {
  AddressList,
  Ajk,
} from "../../../pertubuhan/pernyata-tahunan/interface";
import useQuery from "../../../../helpers/hooks/useQuery";
import AuthHelper from "@/helpers/authHelper";

interface FormValues {
  userId?: number | null;
  negeri?: string | null;
  statusPertubuhan?: string | null;
  jenisPemegangJawatan?: string | null;
  carian?: string | null;
}

function PemegangJawatan() {
  const navigate = useNavigate();

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [isLoadingData, setIsloadingData] = useState<boolean>(true);
  const [pageSize, setPageSize] = useState(5);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [page, setPage] = useState(0);

  const [formData, setFormData] = useState({
    jenisPemegangJawatan: "",
    negeri: "",
    statusPertubuhan: "",
    carian: "",
  });
  const handleInputCarian = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const categories = getLocalStorage("category_list", []);
  const jenisPemegangJawatan = [
    { jawatan: "AJK", id: 1 },
    { jawatan: "AJKK", id: 2 },
    { jawatan: "AKJ", id: 3 },
  ];
  const negeri = [
    { negeri: "Selangor", id: 1 },
    { negeri: "W. Kuala Lumpur", id: 2 },
    { negeri: "Perak", id: 3 },
    { negeri: "Johor", id: 4 },
    { negeri: "Melaka", id: 5 },
    { negeri: "Terengganu", id: 6 },
    { negeri: "Pahang", id: 7 },
    { negeri: "Penang", id: 8 },
  ];

  const statusPertubuhan = [
    { id: 1, status: "Aktif" },
    { id: 2, status: "Tidak aktif" },
    { id: 3, status: "Digantung" },
    { id: 4, status: "Dalam Proses" },
    { id: 5, status: "Dibubarkan" },
  ];
  const mainCategories = categories.filter((cat: any) => cat.level === 1);
  const subCategories = categories.filter((cat: any) => cat.level === 2);

  const hasKelulusanUpdatePermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Update
  );

  const [ajkList, setAjkList] = useState<Ajk[]>([]);

  const [addressList, setAddressList] = useState<AddressList[]>([]);

  useQuery({
    url: `society/admin/address/list`,
    onSuccess: (data) => {
      const list = data?.data?.data || [];
      setAddressList(list);
    },
  });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const customCell = {
    "text-align": "center" /* Center align content */,
    "background-color": "#f5f5f5" /* Example background color */,
    padding: "10px" /* Padding for better spacing */,
    border: "1px solid #ddd" /* Adding borders */,
  };

  const handleClearSearch = () => {
    setFormData({
      jenisPemegangJawatan: "",
      negeri: "",
      statusPertubuhan: "",
      carian: "",
    });
  };
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};

    const resetData = localData;
    let data = displaySenaraiAjk;

    // if every parameter is empty
    if (
      formData.carian == "" &&
      formData.jenisPemegangJawatan == "" &&
      formData.negeri == "" &&
      formData.statusPertubuhan == ""
    ) {
      data = localData;
    } else {
      // if there is input filled
      if (formData.jenisPemegangJawatan) {
        const selected = jenisPemegangJawatan.find(
          (list) => list.id.toString() == formData.jenisPemegangJawatan
        );
        const filterResult = resetData.filter(
          (list) => list.jawatan == selected?.jawatan
        );
        data = [...filterResult];
      }
      if (formData.negeri) {
        const selectedNegeri = negeri.find(
          (list) => list.id.toString() == formData.negeri
        );
        const filteredNegeri = data.filter(
          (list) => list.negeri == selectedNegeri?.negeri
        );
        data = [...filteredNegeri];
        // data.find((list) => list)
      }
      if (formData.statusPertubuhan) {
        const selectedStatusPertubuhan = statusPertubuhan.find(
          (list) => list.id.toString() == formData.statusPertubuhan
        );
        const filteredStatusPertubuhan = data.filter(
          (list) => list.statusPertubuhan == selectedStatusPertubuhan?.status
        );
        data = [...filteredStatusPertubuhan];
      }
      //check if free text match any of this 4
      if (formData.carian) {
        const searchValue = formData.carian.toLowerCase();
        const filterNamaPemegangJawatan = data.filter((list) =>
          list.namaPemegangJawatan.toLowerCase().includes(searchValue)
        );
        const filteredNoPengenalanDiri = data.filter((list) =>
          list.noPengenalanDiri.includes(searchValue)
        );

        const filteredNoPpm = data.filter((list) =>
          list.noPPM.toLowerCase().includes(searchValue)
        );

        const filterNamaPertubuhan = data.filter((list) =>
          list.namaPertubuhan.toLowerCase().includes(searchValue)
        );

        const combinedResults = [
          ...filterNamaPemegangJawatan,
          ...filteredNoPengenalanDiri,
          ...filteredNoPpm,
          ...filterNamaPertubuhan,
        ];

        // filter if id is overlap
        const uniqueResults = Array.from(
          new Map(combinedResults.map((item) => [item.id, item])).values()
        );
        data = [...uniqueResults];
      }
    }
    setTotal(data.length);
    setDisplaySenaraiAjk(data);

    // FetchUsers(false);
  };

  const columns: GridColDef[] = [
    {
      field: "namaPemegangJawatan",
      headerName: `${t("organizationName")}`,
      // width:117,
      flex: 1.5,
      align: "center",
      headerAlign: "center",

      renderHeader: (params) => (
        <div
          className="custom-header"
          style={{ textAlign: "center", lineHeight: "20px" }}
        >
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>{params.row.namaPemegangJawatan}</div>
        </div>
      ),
    },
    {
      field: "noPengenalanDiri",
      headerName: t("organizationNumber"),
      flex: 1.5,
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
    },
    {
      field: "jawatan",
      headerName: t("noPPMLama"),
      flex: 1.5,
      cellClassName: "",
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
    },
    {
      field: "namaPertubuhan",
      headerName: t("noPPP"),
      flex: 1.5,
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>{params.row.namaPertubuhan}</div>
        </div>
      ),

      // renderCell: (params: any) => params?.row?.constitution_type ?? "-",
    },
  ];

  const localData = [
    {
      id: 1,
      namaPemegangJawatan: "Ali Bin Abu",
      noPengenalanDiri: "960530025467",
      jawatan: "AJK",
      namaPertubuhan: "Kelab Sukan Harmoni",
      noPPM: "PPM-2025-001",
      statusPertubuhan: "Aktif",
      negeri: "Selangor",
    },
    {
      id: 2,
      namaPemegangJawatan: "Fatimah Binti Omar",
      noPengenalanDiri: "940215039862",
      jawatan: "AJKK",
      namaPertubuhan: "Persatuan Kebajikan Rakyat",
      noPPM: "PPM-2024-002",
      statusPertubuhan: "Dalam Proses",
      negeri: "Kuala Lumpur",
    },
    {
      id: 3,
      namaPemegangJawatan: "Siti Nurhaliza",
      noPengenalanDiri: "870825012394",
      jawatan: "AJK",
      namaPertubuhan: "Persatuan Seni Budaya",
      noPPM: "PPM-2023-003",
      statusPertubuhan: "Tidak aktif",
      negeri: "Perak",
    },
    {
      id: 4,
      namaPemegangJawatan: "Ahmad Bin Salim",
      noPengenalanDiri: "920518078451",
      jawatan: "BDH",
      namaPertubuhan: "Kelab Rekreasi Kota",
      noPPM: "PPM-2022-004",
      statusPertubuhan: "Dibubarkan",
      negeri: "Johor",
    },
    {
      id: 5,
      namaPemegangJawatan: "Nurul Huda",
      noPengenalanDiri: "891120054738",
      jawatan: "AJK",
      namaPertubuhan: "Persatuan Amal Cinta",
      noPPM: "PPM-2023-005",
      statusPertubuhan: "Aktif",
      negeri: "Melaka",
    },
    {
      id: 6,
      namaPemegangJawatan: "Mohd Razif",
      noPengenalanDiri: "910614023765",
      jawatan: "AJKK",
      namaPertubuhan: "Kelab Larian Pantai",
      noPPM: "PPM-2021-006",
      statusPertubuhan: "Tidak aktif",
      negeri: "Terengganu",
    },
    {
      id: 7,
      namaPemegangJawatan: "Aminah Binti Ali",
      noPengenalanDiri: "880208036452",
      jawatan: "BDH",
      namaPertubuhan: "Persatuan Kemajuan Desa",
      noPPM: "PPM-2020-007",
      statusPertubuhan: "Dibubarkan",
      negeri: "Pahang",
    },
    {
      id: 8,
      namaPemegangJawatan: "Azman Bin Hassan",
      noPengenalanDiri: "890930045781",
      jawatan: "AJK",
      namaPertubuhan: "Kelab Komuniti Sejahtera",
      noPPM: "PPM-2019-008",
      statusPertubuhan: "Aktif",
      negeri: "Penang",
    },
  ];

  useEffect(() => {
    setDisplaySenaraiAjk(localData);
    setIsloadingData(false);
    setTotal(localData.length);
  }, []);

  return (
    <>
      <Box component="form" onSubmit={handleSubmit}>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("semakanNoPengenalanDiriPemegangJawatan")}
            </Typography>

            <Grid container spacing={2}>
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("idNumberPlaceholder")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  fullWidth
                  name="carian"
                  value={formData.carian}
                  onChange={handleInputCarian}
                  error={!!formErrors.organizationName}
                  helperText={formErrors.organizationName}
                />
              </Grid>
            </Grid>
            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  // flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    // width: isMobile ? "100%" : "auto",
                  }}
                  onClick={handleClearSearch}
                >
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary
                  type="submit"
                  variant="contained"
                  sx={{
                    // width: isMobile ? "100%" : "auto",
                    boxShadow: "none",
                  }}
                >
                  {t("search")}
                </ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatPegawaiPenyemak")}
            </Typography>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("noPengenalanDiriPenyemak")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("namaPegawaiPenyemak")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("tarikhDanMasaSemak")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
          </Box>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              marginTop: "10px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("ajkInformation")}
            </Typography>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}></Grid>
              <Grid item xs={12} sm={8}>
                <img
                  src="/Group.png"
                  alt="Group"
                  style={{
                    borderRadius: "50%",
                    objectFit: "cover",
                  }}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("namaIndividu")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("idNumberPlaceholder")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("noPengenalanBaru")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("noPengenalanLama")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("dateOfBirth")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("tarikhMati")} ({t("jikaBerkanaan")})
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("religion")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("tarafPenduduk")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("gender")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("alamatTetap")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" multiline row={3} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("phoneNumber")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              {/* finding/carian */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("emel")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
              mt: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                textAlign: "center",
                p: 3,
                mb: 2,
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  color: "var(--primary-color)",
                  fontSize: 14,
                  fontWeight: "500 !important",
                }}
              >
                {t("bilanganPertubuhanTerkini")}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: "500 !important",
                }}
              >
                0 Orang
              </Typography>
            </Box>

            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("pertubuhanYangDianggotai")}
              </Typography>

              <Table
                sx={{
                  backgroundColor: "white",
                  borderRadius: "4px",
                  overflow: "hidden",
                  "& .MuiTableCell-root": { fontSize: "14px" },
                }}
              >
                <TableHead>
                  <TableRow sx={{ backgroundColor: "white" }}>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("position")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("organizationName")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("noPPM")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("organizationStatus")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("statusPename")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("negeri")}
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {ajkList.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        sx={{ textAlign: "center", color: "#888" }}
                      >
                        {t("noData")}
                      </TableCell>
                    </TableRow>
                  ) : (
                    ajkList.map((ajk, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {t(
                            `${
                              OrganisationPositions.find(
                                (item) => item?.value === ajk?.designationCode
                              )?.label || ""
                            }`
                          )}
                        </TableCell>
                        <TableCell>{ajk.name}</TableCell>
                        <TableCell>{ajk.email}</TableCell>
                        <TableCell>
                          {
                            addressList.find(
                              (item: any) =>
                                item.shortCode === ajk.employerStateCode
                            )?.name
                          }
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </Box>
          </Box>

          <Box
            sx={{
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
              mt: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                textAlign: "center",
                p: 3,
                mb: 2,
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  color: "var(--primary-color)",
                  fontSize: 14,
                  fontWeight: "500 !important",
                }}
              >
                {t("bilanganCawanganTerkini")}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: "500 !important",
                }}
              >
                0 Orang
              </Typography>
            </Box>

            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("cawanganYangDianggotai")}
              </Typography>

              <Table
                sx={{
                  backgroundColor: "white",
                  borderRadius: "4px",
                  overflow: "hidden",
                  "& .MuiTableCell-root": { fontSize: "14px" },
                }}
              >
                <TableHead>
                  <TableRow sx={{ backgroundColor: "white" }}>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("position")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("namaCawangan")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("nomborCawangan")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("statusCawangan")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("statusPenama")}
                    </TableCell>
                    <TableCell sx={{ color: "#666666" }}>
                      {t("negeri")}
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {ajkList.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        sx={{ textAlign: "center", color: "#888" }}
                      >
                        {t("noData")}
                      </TableCell>
                    </TableRow>
                  ) : (
                    ajkList.map((ajk, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {/* {t(OrganisationPositionLabel[ajk.designationCode])} */}
                          {t(
                            `${
                              OrganisationPositions.find(
                                (item) => item?.value === ajk?.designationCode
                              )?.label || ""
                            }`
                          )}
                        </TableCell>
                        <TableCell>{ajk.name}</TableCell>
                        <TableCell>{ajk.email}</TableCell>
                        <TableCell>
                          {
                            addressList.find(
                              (item: any) =>
                                item.shortCode === ajk.employerStateCode
                            )?.name
                          }
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default PemegangJawatan;
