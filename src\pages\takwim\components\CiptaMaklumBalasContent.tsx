import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  Button,
  Paper,
  Checkbox,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { TakwimPaper } from "@/components/paper";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import { IFeedbackQuestion } from "@/types/eventFeedbackQuestion";
import { eventService } from "@/services/eventService";
import { useTakwim } from "@/contexts/takwimProvider";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import FeedIcon from "@mui/icons-material/Feed";
import { useNotification } from "@refinedev/core";
import { t } from "i18next";

interface Question {
  id: number;
  question: string;
}

const questions: Question[] = [
  { id: 1, question: "Bagaimanakah pem<PERSON>han penceramah?" },
  { id: 2, question: "Bagaimanakah penyampaian penceramah?" },
  { id: 3, question: "Bagaimanakah pengurusan masa program?" },
  { id: 4, question: "Bagaimanakah kemudahan yang disediakan?" },
  { id: 5, question: "Bagaimanakah keberkesanan program?" },
  { id: 6, question: "Bagaimanakah kepuasan keseluruhan program?" },
];

const ratingOptions = ["Sangat Tidak Memuaskan", "Tidak Memuaskan", "Memuaskan", "Sangat Memuaskan", "Luar Biasa"];

const CiptaMaklumBalasContent = () => {
  const { open: openNotification } = useNotification();
  const [feedbackName, setFeedbackName] = useState("");
  const [selectedRatings, setSelectedRatings] = useState<{
    [key: number]: string;
  }>({});
  const [selectedQuestions, setSelectedQuestions] = useState<{
    [key: number]: boolean;
  }>({});
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [feedbackQuestions, setFeedbackQuestions] = useState<
    IFeedbackQuestion[]
  >([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const { setEventFeedBack, eventFeedBack, eventFormData, setEventFormData } =
    useTakwim();
  const [isNotSetAnyFeedback, setIsNotSetAnyFeedback] = useState(
    eventFormData.feedbackName.length < 1 && eventFeedBack.length < 1
  );
  const [isDeleteMode, setIsDeleteMode] = useState(false);

  const handleCreateClick = () => {
    setShowCreateForm(true);
  };

  useEffect(() => {
    fetchFeedbackQuestion();
  }, []);

  const fetchFeedbackQuestion = async () => {
    try {
      // setIsLoading(true);
      const feedbackQuestions = await eventService.getFeedbackQuestion();
      setFeedbackQuestions(feedbackQuestions);

      // setEventAttedees(attendees); // Set the organizer state with the returned data
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions"
      );
    } finally {
      // setIsLoading(false);
    }
  };

  const handleRatingChange = (questionId: number, value: string) => {
    setSelectedRatings((prev) => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const handleQuestionSelect = (questionId: number) => {
    const isSelected = eventFeedBack?.includes(questionId) || false;

    // Create a new array based on the current eventFeedBack
    let selectedQuestion: number[] = [...(eventFeedBack || [])];

    if (isSelected) {
      // Filter out the questionId
      selectedQuestion = selectedQuestion.filter((id) => id !== questionId);
    } else {
      // Add the new questionId
      selectedQuestion = [...selectedQuestion, questionId];
    }

    setEventFeedBack(selectedQuestion);

    setSelectedQuestions((prev) => ({
      ...prev,
      [questionId]: !isSelected,
    }));
  };

  const handleConfirm = async () => {
    if (isDeleteMode) {
      try {
        // Call the delete service

        const result = await eventService.deleteEventFeedbackQuestion(
          eventFormData?.eventNo
        );

        // Reset feedback data after successful deletion
        if (result.code == 200) {
          setEventFormData((prev) => ({
            ...prev,
            feedbackName: "",
          }));
          setEventFeedBack([]);
          setIsNotSetAnyFeedback(true);
          openNotification?.({
            message: t("Maklum balas berjaya dipadam."),
            description:t("Maklum balas berjaya dipadam."),
            type: "success",
          });
        }

        // Close dialog and reset delete mode
        setOpenDialog(false);
        setIsDeleteMode(false);
      } catch (error) {
        throw new Error("Failed to delete feedback");
      }
    } else {
      // Original create logic
      const selectedQuestionsList = eventFeedBack;

      // Your existing create logic here

      setOpenDialog(false);
    }
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="200px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2, color: "error.main" }}>
        <Typography>{error}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 0, display: "grid", gap: 2 }}>
      {!showCreateForm ? (
        // Show the list view when not creating
        <>
          <TakwimPaper>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography
                sx={{
                  color: "#0CA6A6",
                  fontSize: "16px",
                  fontWeight: 500,
                }}
              >
                Sila pilih maklumbalas
              </Typography>
              {eventFormData.feedbackName.length < 1 && (
                <Button
                  variant="outlined"
                  size="medium"
                  onClick={handleCreateClick}
                  sx={{
                    // backgroundColor: "#0CA6A6",
                    "&:hover": {
                      backgroundColor: "#3BB1B1",
                    },
                    textTransform: "none",
                    borderRadius: "4px",
                    px: 2,
                  }}
                >
                  Tambah Maklum Balas
                </Button>
              )}
            </Box>

            {!isNotSetAnyFeedback ? (
              <>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    borderBottom: "1px solid #DADADA",
                    py: 2,
                  }}
                >
                  <Typography sx={{ color: "#666666", fontWeight: 500 }}>
                    Maklumbalas
                  </Typography>
                  <Typography sx={{ color: "#666666", fontWeight: 500 }}>
                    Tindakan
                  </Typography>
                </Box>

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    borderBottom: "1px solid #DADADA",
                    py: 2,
                  }}
                >
                  <Typography
                    sx={{
                      color: eventFormData?.feedbackName
                        ? "#666666"
                        : "lightgray",
                    }}
                  >
                    {eventFormData?.feedbackName
                      ? eventFormData.feedbackName
                      : "nama maklum balas tidak di isi"}
                  </Typography>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <IconButton
                      onClick={handleCreateClick}
                      size="small"
                      sx={{ color: "#0CA6A6" }}
                    >
                      <EditOutlinedIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      sx={{ color: "#FF0000" }}
                      onClick={() => {
                        setIsDeleteMode(true);
                        setOpenDialog(true);
                      }}
                    >
                      <DeleteOutlineOutlinedIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              </>
            ) : (
              <Box
                sx={{
                  padding: "80px 0",
                  textAlign: "center",
                  height: 200,
                }}
              >
                <Typography color="#DADADA">Tiada maklumbalas</Typography>
                <Box>
                  <FeedIcon
                    sx={{
                      color: "#DADADA",
                      fontSize: 100,
                    }}
                  />
                </Box>
              </Box>
            )}
          </TakwimPaper>
        </>
      ) : (
        // Show the create form when creating
        <>
          <TakwimPaper>
            <Box sx={{ mb: 4 }}>
              <Typography sx={{ color: "#4DB6AC", mb: 1 }}>
                Nama maklumbalas
              </Typography>
              <TextField
                fullWidth
                size="small"
                value={eventFormData?.feedbackName}
                onChange={(e) =>
                  setEventFormData((prev) => ({
                    ...prev,
                    feedbackName: e.target.value,
                  }))
                }
                placeholder="Nama maklumbalas"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "white",
                    "& fieldset": {
                      borderColor: "#DADADA",
                    },
                  },
                }}
              />
            </Box>
          </TakwimPaper>
          <Box sx={{ mb: 4 }}>
            <TakwimPaper>
              <Box sx={{ display: "grid", gap: 2 }}>
                {feedbackQuestions?.map((question, idx) => (
                  <Box key={idx} sx={{ display: "flex" }}>
                    <Box sx={{ alignContent: "center" }}>
                      <Checkbox
                        checked={eventFeedBack?.includes(question.id) || false}
                        onChange={() => handleQuestionSelect(question.id)}
                        sx={{
                          color: "#DADADA",
                          "&.Mui-checked": {
                            color: "#4DB6AC",
                          },
                        }}
                      />
                    </Box>
                    <TakwimPaper>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          gap: 2,
                        }}
                      >
                        <Box sx={{ flex: 1 }}>
                          <Typography sx={{ mb: 1, color: "#666666" }}>
                            {question.question}
                          </Typography>
                          <RadioGroup
                            row
                            value={selectedRatings[question.id] || ""}
                            onChange={(e) =>
                              handleRatingChange(question.id, e.target.value)
                            }
                          >
                            {ratingOptions.map((option) => (
                              <FormControlLabel
                                key={option}
                                value={option}
                                control={
                                  <Radio
                                    size="small"
                                    disabled
                                    sx={{
                                      color: "#DADADA",
                                      "&.Mui-checked": {
                                        color: "#4DB6AC",
                                      },
                                    }}
                                  />
                                }
                                label={option}
                                sx={{
                                  mr: 2,
                                  "& .MuiFormControlLabel-label": {
                                    fontSize: "14px",
                                    color: "#666666",
                                  },
                                }}
                              />
                            ))}
                          </RadioGroup>
                        </Box>
                      </Box>
                    </TakwimPaper>
                  </Box>
                ))}
              </Box>
            </TakwimPaper>
          </Box>
        </>
      )}

      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setIsDeleteMode(false);
        }}
        onConfirm={handleConfirm}
        confirmationText={
          isDeleteMode
            ? "Adakah anda pasti untuk memadam maklumbalas ini?"
            : "Adakah anda pasti untuk mencipta maklumbalas ini?"
        }
        successMessage={
          isDeleteMode
            ? "Maklumbalas berjaya dipadam"
            : "Maklumbalas berjaya dicipta"
        }
      />
    </Box>
  );
};

export default CiptaMaklumBalasContent;



