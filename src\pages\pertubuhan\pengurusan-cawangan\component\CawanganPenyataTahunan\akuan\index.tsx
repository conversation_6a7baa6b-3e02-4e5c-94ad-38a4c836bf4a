import {
  <PERSON>,
  Checkbox,
  FormControl<PERSON>abel,
  <PERSON>rid,
  <PERSON>ack,
  Typo<PERSON>,
} from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import Input from "@/components/input/Input";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import NewAlertDialog from "@/components/dialog/newAlert";
import ConfirmationDialog from "@/components/dialog/confirm";
import { ApplicationStatus, useQuery } from "@/helpers";

const index = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const [statementComplete, setStatementComplete] = useState(false);
  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
    setPenyataTahunanSuccess: handleSuccess,
  } = useSenaraiContext();

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    trigger,
    handleSubmit,
    reset: resetForm,
  } = useForm<FieldValues>({
    defaultValues: {
      // akuanSetuju: false,
      akuanPapar: false,
    },
  });

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const { mutate: saveGeneralInfo, isLoading: isLoadingSaveBank } =
    useCustomMutation();

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = {
      // akuanSetujuInduk: data.akuanSetujuInduk,
      // akuanSetuju: data.akuanSetuju,
      // statementId: statementId,
      branchId: branchDataRedux.id,
      societyId: societyId,
      akuanSetuju: true,
    };
    saveGeneralInfo(
      {
        url: `${API_URL}/society/statement/${statementId}/submit`,
        method: "put",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          setIsDialogOpen(false);
          setDialogAlertSaveOpen(true);
        },
      }
    );
  };

  const handleOpenDialog = async () => {
    const isValid = await trigger(["akuanSetujuInduk"]);
    if (isValid) {
      setIsDialogOpen(true);
    }
  };

  const handleOnCloseNewAlert = () => {
    setDialogAlertSaveOpen(false);
    navigate(
      `/pertubuhan/society/${societyId}/senarai/cawangan/view/penyataTahunan`
    );
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              background: "white",
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              py: 2,
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("akuan")}
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                color: "#666666",
                fontSize: 14,
                fontWeight: "400 !important",
              }}
            >
              <span>{t("pengakuan_description_1")}</span>
              <br />
              <br />
              <span>{t("pengakuan_description_2")}</span>
            </Typography>
          </Box>
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              px: 3,
              py: 2,
              mb: 2,
            }}
          >
            <Controller
              name="akuanSetujuInduk"
              control={control}
              rules={{
                // The field must be true (checked) otherwise return an error message.
                validate: (value) => value === true || t("pleaseSelect"),
              }}
              render={({ field, fieldState: { error } }) => (
                <>
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        disabled={isDisabled || statementComplete}
                        sx={{ fontWeight: 100 }}
                        onChange={(e) => {
                          field.onChange(e.target.checked);
                          trigger(field.name);
                        }}
                      />
                    }
                    label={
                      <Typography
                        style={{
                          fontWeight: "normal",
                          fontSize: "14px",
                          color: "#333",
                          lineHeight: "1.5",
                        }}
                      >
                        {t("agreementAcceptance")}{" "}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    }
                  />
                  {error && (
                    <Typography
                      color="error"
                      sx={{ fontWeight: "400!important", fontSize: "14px" }}
                    >
                      {error.message}
                    </Typography>
                  )}
                </>
              )}
            />
          </Box>
          <Stack
            direction="row"
            spacing={2}
            sx={{ mt: 4, pl: 1 }}
            justifyContent="flex-end"
          >
            <ButtonOutline onClick={handleBackActions}>
              {t("back")}
            </ButtonOutline>
            {isDisabled || statementComplete ? null : (
              <ButtonPrimary onClick={handleOpenDialog}>
                {t("hantar")}
              </ButtonPrimary>
            )}
          </Stack>
        </Box>
        <ConfirmationDialog
          open={isDialogOpen}
          turn
          onClose={() => setIsDialogOpen(false)}
          onConfirm={handleSubmit(onSubmit)}
          onCancel={() => setIsDialogOpen(false)}
          title={""}
          message={t("confirmSubmitStatementText")}
        />
      </form>
      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={handleOnCloseNewAlert}
        message={t("confessionRecorded")}
      />
    </>
  );
};

export default index;
