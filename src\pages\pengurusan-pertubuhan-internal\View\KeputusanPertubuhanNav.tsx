import React, { useEffect, useMemo, useState } from "react";
import { Box, Button } from "@mui/material";
import { useNavigate, useLocation, Outlet, redirect } from "react-router-dom";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

interface Tab {
  label: string;
  path: string;
  permissionName: string;
}

const tabs: Tab[] = [
  {
    label: "Keputusan Induk",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk",
    permissionName: "Kelulusan",
  },
  {
    label: "Keputusan Cawangan",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan",
    permissionName: "Keputusan Cawangan",
  },
  {
    label: "Rayuan",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/rayuan",
    permissionName: "Rayuan",
  },
  {
    label: "Senarai Hitam",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/senarai-hitam",
    permissionName: "Senarai Hitam",
  },
  {
    label: "Kuiri",
    path: "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri",
    permissionName: "Kuiri",
  },
];

const KeputusanPertubuhanNav: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const hasPembubaranPermission = AuthHelper.hasPageAccess(
    PermissionNames.PERTUBUHAN.label,
    pageAccessEnum.Read
  );

  if (hasPembubaranPermission) {
    return (
      <Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            background: "#fff",
            borderRadius: "10px",
            p: 1,
            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
            overflowX: "auto",
          }}
        >
          {tabs.map((tab) => (
            <Button
              key={tab.path}
              onClick={() => navigate(tab.path)}
              sx={{
                textTransform: "none",
                fontWeight: location.pathname.includes(tab.path) ? 600 : 400,
                color: location.pathname.includes(tab.path) ? "#fff" : "#333",
                background: location.pathname.includes(tab.path)
                  ? "var(--primary-color)"
                  : "transparent",
                borderRadius: "5px",
                px: 3,
                py: 1,
                transition: "all 0.3s ease",
                "&:hover": {
                  background: location.pathname.includes(tab.path)
                    ? "var(--primary-color)"
                    : "#F1F4FA",
                },
              }}
            >
              {tab.label}
            </Button>
          ))}
        </Box>
        <Box>
          <Outlet />
        </Box>
      </Box>
    );
  }
};

export default KeputusanPertubuhanNav;
