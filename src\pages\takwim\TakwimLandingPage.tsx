import React, { useEffect, useState } from "react";
import { Box, Container, Typography, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { TakwimProvider, useTakwim } from "../../contexts/takwimProvider";
import Navbar from "../../components/navbar";
import Footer from "../landing-page/mainLanding/footer";
import { TakwimCalendarView } from "./components/TakwimCalendarView";
import { TakwimSidebar } from "./components/TakwimSidebar";
import "./takwim-landing.css";
import { Outlet, useLocation } from "react-router-dom";
import { useGetIdentity, useNotification } from "@refinedev/core";
import { IIdentity } from "../../components/header/header-sidebar-authenticated";
import { HeaderSidebarAuthenticated } from "../../components/header/header-sidebar-authenticated";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllEvents,
  fetchAllPublishedEvents,
} from "../../redux/APIcalls/takwimThunks";
import type { AppDispatch, RootState } from "../../redux/store";
import CircularProgress from "@mui/material/CircularProgress";
import { eventService } from "@/services/eventService";
import Alert from "@mui/material/Alert";
import { ApiResponse, IEvent } from "../../types/event";
import BackButton from "@/components/BackButton";

const TakwimLandingPage = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const isMainPage = /^\/takwim\/?$/.test(location.pathname);
  const { data: user } = useGetIdentity<IIdentity>();
  const dispatch = useDispatch<AppDispatch>();
  const { upcomingEvents, setUpcomingEvents, setIsEventAdmin, isEventAdmin } =
    useTakwim();
  const { open: openNotification } = useNotification();

  // Get data from Redux store
  const takwimState = useSelector((state: RootState) => state.takwim);
  // const { data: events, loading, error, selecetedMonthYear } = takwimState;

  // Fetch only once when component mounts
  // useEffect(() => {
  //   dispatch(fetchAllPublishedEvents())
  //     .unwrap()
  //     .then((result) => {
  //       // setUpcomingEvents(events);
  //       console.log("Events updated:", events);
  //       // console.log("Current Redux state:", takwimState); // Check Redux state
  //     })
  //     .catch((error) => {
  //       console.error("Error fetching events:", error);
  //     });
  // }, [dispatch]);

  useEffect(() => {
    fetchEventsByYear(new Date().getFullYear());
  }, [isMainPage]);

  const fetchEventAdmin = async (identificationNo: string | undefined) => {
    try {
      // setIsLoading(true);
      const eAdmin = await eventService.getEventAdmin(identificationNo);

      if (eAdmin.id) {
        setIsEventAdmin(true);
      } else {
        setIsEventAdmin(false);
      }
    } catch (error) {
      console.log("");
    } finally {
      // setIsLoading(false);
    }
  };

  const fetchEventsByYear = async (year: number) => {
    try {
      // setIsLoading(true);
      const eventRes: ApiResponse<IEvent[]> =
        await eventService.getPublishedEvents(year);
      if (eventRes.code === 200) {
        setUpcomingEvents(eventRes.data);
      }

      // setUpcomingEvents(eventRes);
      // const filteredEvents: any =   eventRes?..filter(item => item?.eventStartDate?.toString().includes("2025-05"));

      if (eventRes.code === 500) {
        openNotification?.({
          message: eventRes.msg ?? "Failed to fetch events",
          type: "error",
        });
      }
    } catch (error) {
      throw new Error("Failed to fetch events");
    } finally {
      // setIsLoading(false);
    }
  };

  const renderContent = () => (
    <>
      <Typography
        variant="h5"
        component="h2"
        sx={{
          color: "primary.main",
          fontWeight: 500,
          mb: 3,
          mt: 4,
          textAlign: "center",
        }}
      >
        {t("Takwim Aktiviti JPPM")}
      </Typography>

      <Typography
        variant="body1"
        sx={{
          color: "text.secondary",
          mb: 4,
          textAlign: "center",
          maxWidth: "800px",
          mx: "auto",
        }}
      >
        {t(
          "Terokai pelbagai acara yang akan diadakan sepanjang tahun oleh JPPM. Temui acara yang sesuai dengan keperluan anda dan berpotensi memberikan inspirasi serta sokongan untuk meningkatkan keberkesanan pengurusan persatuan anda."
        )}
      </Typography>
      {/* {isMainPage ? (
        <BackButton label="Takwim" to="" />
      ) : (
        <Box
          // maxWidth={"lg"}
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2,
            mb: 2,
            ml: "auto",
            mr: "auto",
          }}
        >
          <BackButton label="Takwim" to="" />
        </Box>
      )} */}

      {isMainPage ? (
        <>
          <BackButton label="Takwim" to="" />

          <Grid container spacing={1}>
            <Grid
              item
              xs={12}
              md={3}
              sx={{
                position: "static",
                top: 0,
                height: "fit-content", // This ensures the container only takes as much height as needed
              }}
            >
              <TakwimSidebar events={upcomingEvents} />
            </Grid>
            <Grid item xs={12} md={9}>
              <TakwimCalendarView events={upcomingEvents} />
            </Grid>
          </Grid>
        </>
      ) : (
        <Outlet />
      )}
    </>
  );

  // if (loading)
  //   return (
  //     <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
  //       <CircularProgress />
  //     </Box>
  //   );

  return (
    <>
      <div
        style={{
          flexDirection: "column",
          backgroundColor: "#F1F4FA",
        }}
      >
        {!user?.id && <Navbar overrideLoginPopup={true} />}
        {user?.id ? (
          location.pathname.includes("/takwim/update-attendance") ? (
            <Container
              sx={{
                // maxWidth: "1400px !important",
                maxWidth: "100% !important",
                paddingBottom: "100px",
              }}
            >
              {renderContent()}
            </Container>
          ) : (
            <HeaderSidebarAuthenticated>
              <Container
                sx={{
                  maxWidth: "100% !important",
                  paddingBottom: "100px",
                }}
              >
                {renderContent()}
              </Container>
            </HeaderSidebarAuthenticated>
          )
        ) : (
          <>
            <Box
              className="takwim-hero"
              sx={{
                backgroundImage:
                  "url(https://png.pngtree.com/thumb_back/fh260/background/20230625/pngtree-d-hand-highlights-important-date-on-calendar-for-appointment-or-event-image_3672290.jpg)",
              }}
            >
              <Container maxWidth="lg" className="takwim-hero-content">
                <Typography
                  variant="h4"
                  component="h1"
                  className="takwim-hero-title"
                >
                  {t("Takwim Aktiviti Jabatan Pertubuhan Malaysia (JPPM)")}
                </Typography>
              </Container>
            </Box>

            <Container
              sx={{
                maxWidth: "1400px !important",
                marginBottom: "100px",
              }}
            >
              {renderContent()}
            </Container>

            {/* <Box
              sx={{
                backgroundColor: "#0CA6A6",
                padding: "120px 60px 60px 60px",
                "@media (max-width: 800px)": {
                  padding: "60px 30px 0px 30px",
                },
                "@media (max-width: 450px)": {
                  padding: "60px 15px 0px 15px",
                },
              }}
            ></Box> */}
            {!user?.id && <Footer />}
          </>
        )}
      </div>
    </>
  );
};

export default TakwimLandingPage;
