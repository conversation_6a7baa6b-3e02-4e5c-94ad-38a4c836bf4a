import {
  Box,
  Divider,
  Grid,
  IconButton,
  InputBase,
  Menu,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import TuneIcon from "@mui/icons-material/Tune";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import {
  ApplicationStatusEnum,
  ApplicationStatusList,
  HideOrDisplayInherit,
  JenusJuruaudit,
} from "@/helpers/enums";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import useQuery from "@/helpers/hooks/useQuery";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";

const JuruAudit: React.FC = () => {
  const { t } = useTranslation();

  const { id: societyId } = useParams();
  const navigate = useNavigate();

  const { auditorList, fetchAuditorList } = usejawatankuasaContext();

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  useEffect(() => {
    fetchAuditorList();
    setShouldFetch(false);
  }, [shouldFetch]);

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);

  const handleDaftarJuruaudit = () => {
    navigate("create");
  };

  const handleEditJuruaudit = (auditorId: number) => {
    navigate("create", {
      state: {
        auditorId: auditorId,
      },
    });
  };

  const handlViewJuruaudit = (auditorId: number) => {
    navigate("view", {
      state: {
        auditorId: auditorId,
        view: true,
      },
    });
  };

  const { mutate: deleteJuruaudit } = useCustomMutation();

  const confirmDeleteJuruaudit = () => {
    deleteJuruaudit(
      {
        url: `${API_URL}/society/statement/auditor/${auditorId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          setOpenConfirm(false);
          setShouldFetch(true);
        },
      }
    );
  };

  const [auditorId, setAuditorId] = useState<number>();

  const handleDeleteJuruaudit = (auditorId: number) => {
    setAuditorId(auditorId);
    setOpenConfirm(true);
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const isManager = useSelector(getUserPermission);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            Sila pastikan bilangan Juruaudit yang berstatus aktif mengikut
            bilangan di dalam perlembagaan.
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganJuruauditTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {auditorList.length} Orang
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("auditorList")}
          </Typography>
          <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
            {isManager || isAliranTugasAccess ? (
              <ButtonOutline onClick={handleDaftarJuruaudit}>
                {t("registerAuditor")}
              </ButtonOutline>
            ) : (
              <></>
            )}
          </Box>
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: "none",
              border: "1px solid #e0e0e0",
              backgroundColor: "white",
              borderRadius: 2.5 * 1.5,
              p: 1,
              mb: 3,
            }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      fontWeight: "bold",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666666",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {t("name")}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: "bold",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666666",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {t("auditorType")}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: "bold",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666666",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {t("mykad/lesen")}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: "bold",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666666",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {t("email")}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: "bold",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666666",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {t("tarikhLantik")}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: "bold",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666666",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {t("status")}
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{
                      fontWeight: "bold",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666666",
                      p: 1,
                      textAlign: "center",
                    }}
                  ></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {auditorList.length > 0
                  ? auditorList.map((row, index) => (
                      <TableRow key={row.id}>
                        <TableCell
                          sx={{
                            color: "#666666",
                            borderBottom: "1px solid #e0e0e0",
                            p: 1,
                            textAlign: "center",
                          }}
                        >
                          {row.name}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666666",
                            borderBottom: "1px solid #e0e0e0",
                            p: 1,
                            textAlign: "center",
                          }}
                        >
                          {JenusJuruaudit[row?.auditorType]}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666666",
                            borderBottom: "1px solid #e0e0e0",
                            p: 1,
                            textAlign: "center",
                          }}
                        >
                          {row?.auditorType === "D"
                            ? row.identificationNo
                            : row?.licenseNo}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666666",
                            borderBottom: "1px solid #e0e0e0",
                            p: 1,
                            textAlign: "center",
                          }}
                        >
                          {row.email}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666666",
                            borderBottom: "1px solid #e0e0e0",
                            p: 1,
                            textAlign: "center",
                          }}
                        >
                          {row.appointmentDate
                            .map((part, index) =>
                              index > 0
                                ? part.toString().padStart(2, "0")
                                : part
                            ) // Pad month and day to 2 digits
                            .join("-")}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666666",
                            borderBottom: "1px solid #e0e0e0",
                            p: 1,
                            textAlign: "center",
                          }}
                        >
                          {t(
                            ApplicationStatusList.find(
                              (item) => item.id === row.status
                            )?.value || "-"
                          )}
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                        >
                          {row.status === "001" ? (
                            isManager || isAliranTugasAccess ? (
                              <>
                                <IconButton
                                  onClick={() => handlViewJuruaudit(row.id)}
                                >
                                  <EyeIcon
                                    sx={{
                                      color: "#666666",
                                      width: "1rem",
                                      height: "1rem",
                                    }}
                                  />
                                </IconButton>
                                <IconButton
                                  onClick={() => handleEditJuruaudit(row.id)}
                                >
                                  <EditIcon
                                    sx={{
                                      color: "var(--primary-color)",
                                      width: "1rem",
                                      height: "1rem",
                                    }}
                                  />
                                </IconButton>
                                <IconButton
                                  onClick={() => handleDeleteJuruaudit(row.id)}
                                >
                                  <TrashIcon
                                    sx={{
                                      color: "var(--error)",
                                      width: "1rem",
                                      height: "1rem",
                                    }}
                                  />
                                </IconButton>
                              </>
                            ) : (
                              <></>
                            )
                          ) : (
                            <IconButton
                              onClick={() => handlViewJuruaudit(row.id)}
                            >
                              <EyeIcon
                                sx={{
                                  color: "#666666",
                                  width: "1rem",
                                  height: "1rem",
                                }}
                              />
                            </IconButton>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  : null}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>
      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDeleteAudit")}
        message={`${t("confirmDeleteAudit")}?`}
        onConfirm={confirmDeleteJuruaudit}
        onCancel={() => setOpenConfirm(false)}
      />
    </>
  );
};

export default JuruAudit;
