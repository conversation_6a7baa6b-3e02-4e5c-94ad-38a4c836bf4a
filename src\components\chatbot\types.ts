/**
 * Type definitions for the chatbot component
 */

export interface ChatMessageOnlyText {
  text: string;
}

export type ChatMessage<
  Base extends ChatMessageOnlyText = ChatMessageOnlyText
> = Base & {
  sender: "user" | "bot";
  action?: React.ReactNode;
};

export interface GroupedChatMessage extends ChatMessage {
  date: string;
  timestamp: string;
  /**
   * Whether the message has been spoken by text-to-speech
   */
  spoken?: boolean;
  /**
   * Unique ID for the message to prevent duplicates
   */
  messageId?: string;
}

export interface ChatbotResponse {
  response: string;
}

export interface OptionGroup {
  key: string;
  label: string;
  children: string[];
}

export interface ChatbotChatAreaProps {
  /**
   * Whether the chatbot is displayed on a mobile device
   * @default false
   */
  isMobile?: boolean;

  /**
   * Whether to force the chatbot to be open regardless of Redux state
   * Used for the full-page chatbot view
   * @default false
   */
  forceOpen?: boolean;
}

export interface LangflowMessage {
  sender: "User" | "Machine";
  text: string;
  timestamp: string;
}

/**
 * Speech recognition state
 */
export interface SpeechRecognitionState {
  /**
   * Whether speech recognition is active
   */
  isListening: boolean;
  /**
   * Whether speech recognition is supported in the browser
   */
  isSupported: boolean;
  /**
   * Error message if speech recognition fails
   */
  error: string | null;
}

/**
 * Text-to-speech state
 */
export interface TextToSpeechState {
  /**
   * Whether text-to-speech is active
   */
  isSpeaking: boolean;
  /**
   * Whether text-to-speech is supported in the browser
   */
  isSupported: boolean;
  /**
   * ID of the message being spoken
   */
  speakingMessageId: string | null;
  /**
   * Error message if text-to-speech fails
   */
  error: string | null;
}

export interface ChatbotApiResponse {
  outputs?: Array<{
    outputs?: Array<{
      results?: {
        message?: {
          text?: string;
        };
        text?: string;
      };
      artifacts?: {
        message?: string;
      };
      messages?: Array<{
        message?: string;
      }>;
    }>;
  }>;
  result?: {
    outputs?: Array<{
      outputs?: Array<{
        outputs?: {
          message?: {
            message?: string;
          };
        };
      }>;
    }>;
  };
}
