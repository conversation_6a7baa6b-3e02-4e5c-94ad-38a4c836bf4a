import { useEffect, useMemo, useState } from "react";
import { useNotification } from "@refinedev/core";
import { FieldValues, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useSecretaryReformContext } from "../Provider";
import {
  getLocalStorage,
  omitKeysFromObject,
  filterEmptyValuesOnObject,
  useMutation,
  MALAYSIA,
  ListGelaran,
  AddressOptions,
  ExternalStateCodes,
  capitalizeWords,
  useQuery,
  ListGender,
  CitizenshipStatus,
} from "@/helpers";

import { Box, CircularProgress, Grid, Typography } from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  SelectFieldController,
  DatePickerController,
  DisabledTextField,
  PageButton,
  NavButton,
  ButtonPrimary,
  ButtonOutline,
} from "@/components";
import { IApiResponse } from "@/types";
import dayjs from "dayjs";
import { useParams } from "react-router-dom";

const sectionTitleStyle = {
  color: "var(--primary-color)",
  fontSize: "16px",
  fontWeight: "500 !important",
  mb: 2,
};

const SecretaryForm: React.FC = () => {
  const { open } = useNotification();
  const { t, i18n } = useTranslation();
  const { societyId, secretaryId } = useParams();
  const [formValue, setFormValue] = useState<any | null>(null);
  const { handleNext, isFeedback, isViewOnly, isEditable } =
    useSecretaryReformContext();
  const occupationList = getLocalStorage("occupation_list", []);
  const {
    control,
    watch,
    handleSubmit,
    trigger,
    setValue,
    getValues,
    reset: resetForm,
  } = useFormContext();

  const disabledFieldNames = [
    "identificationNo",
    "titleCode",
    "committeeName",
    "gender",
    "citizenshipStatus",
    "residenceCountryCode",
    "email",
    "hpNoCode",
    "hpNo",
  ];

  const onResetPreservingDisabled = () => {
    resetForm(formValue);
  };

  const jobCode = watch("jobCode");
  const employerAddressStatus = watch("employerAddressStatus");
  const currentLanguage = i18n.language;
  const isMalaysiaLanguage = currentLanguage === "my";

  const addressList = getLocalStorage("address_list", []);
  const employerAddressOptions = AddressOptions(t);

  const disabledState = isViewOnly || isFeedback;

  const disabledEmployer = ["Pesara", "Tidak Bekerja"].includes(jobCode);
  const stateOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === MALAYSIA)
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList]
  );
  const residenceDistrictOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === Number(watch("residenceStateCode")))
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("residenceStateCode")]
  );
  const employerDistrictOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === Number(watch("employerStateCode")))
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("employerStateCode")]
  );

  const allCountryOptions = useMemo(
    () =>
      addressList
        ?.filter((i: any) => i.level === 0)
        ?.map((item: any) => ({
          value: item.id,
          label: item.name,
        }))
        ?.filter((i: any) => i.value !== MALAYSIA),
    [addressList]
  );

  const { fetch: updateSecretary, isLoading: isUpdatingSecretary } =
    useMutation<IApiResponse<{ secretaryId: number }>>({
      url: `society/secretary/principal/${secretaryId}`,
      method: "put",
      onSuccess: (data) => {
        const code = data?.data?.code;
        if (code !== 200)
          return open?.({
            type: "error",
            message: t("error"),
            description: "Failed to create secretary",
          });
        // setValue("id", secretaryId);
        handleNext(Number(secretaryId));
      },
    });

  const { fetch: createSecretary, isLoading: isCreatingSecretary } =
    useMutation<IApiResponse<{ id: number }>>({
      url: "society/secretary/principal/create",
      method: "post",
      onSuccess: (data) => {
        const code = data?.data?.code;
        const secretaryId = data?.data?.data?.id;

        if (code !== 200)
          return open?.({
            type: "error",
            message: t("error"),
            description: "Failed to create secretary",
          });

        setValue("id", secretaryId);
        handleNext(Number(secretaryId));
      },
    });

  const addEmployerKeysToSkip = (
    data: FieldValues,
    keysToSkip: Set<string>
  ) => {
    const employerKeys = [
      "employerName",
      "employerAddress",
      "employerAddressStatus",
      "employerPostcode",
      "employerCountryCode",
      "employerStateCode",
      "employerDistrictCode",
      "employerCityCode",
      "employerCity",
    ];

    const addressStatusDependentKeys = [
      "employerPostcode",
      "employerStateCode",
      "employerDistrictCode",
      "employerCityCode",
      "employerCity",
    ];
    if (["Pesara", "Tidak Bekerja"].includes(data.jobCode))
      employerKeys.forEach((key) => keysToSkip.add(key));

    if (data.employerAddressStatus === "0")
      addressStatusDependentKeys.forEach((key) => keysToSkip.add(key));

    if (data.employerAddressStatus === "1")
      keysToSkip.add("employerCountryCode");
  };

  const onSubmit = (data: FieldValues) => {
    const keysToSkip = new Set([
      "workTelNoCode",
      "hpNoCode",
      "homeTelNoCode",
      "isUserApplicant",
    ]);

    addEmployerKeysToSkip(data, keysToSkip);

    const filteredSecretaryValues = omitKeysFromObject(
      data,
      Array.from(keysToSkip)
    );

    const payload = filterEmptyValuesOnObject({
      ...filteredSecretaryValues,
      workTelNo: `${data.workTelNoCode} ${data.workTelNo}`,
      homeTelNo: `${data.homeTelNoCode} ${data.homeTelNo}`,
      hpNo: `${data.hpNoCode} ${data.hpNo}`,
      applicationStatusCode: 1,
      societyId: societyId,
    });
    if (secretaryId) {
      updateSecretary(payload);
      return;
    } else {
      createSecretary(payload);
    }
  };

  const convertIcToDate = (data: string) => {
    if (!data) {
      return;
    }
    const yyMMdd = data.substring(0, 6);
    let parsedDate = dayjs(yyMMdd, "YYMMDD");
    const year = parsedDate.year();
    const currentYear = dayjs().year();
    if (year < 100) {
      const fullYear = year + (year > currentYear % 100 ? 1900 : 2000);
      parsedDate = parsedDate.year(fullYear);
    }
    const formattedDate = parsedDate.format("YYYY-MM-DD");
    return formattedDate;
  };

  useEffect(() => {
    if (!watch("identificationNo") || watch("identificationNo").length !== 12) {
      setValue("gender", null);
    } else {
      if (parseInt(watch("identificationNo")?.slice(-1)) % 2 === 0) {
        setValue("gender", "P");
      } else {
        setValue("gender", "L");
      }
    }

    // Auto-populate residenceCountryCode only, not placeOfBirth
    if (!watch("identificationNo") || watch("identificationNo").length < 8) {
      return;
    }

    const digits = Number(watch("identificationNo").substring(6, 8));
    const matchingState = ExternalStateCodes.find(
      (state) => state.value === digits
    );

    const stateList = addressList
      ?.filter((item: any) => item.pid == MALAYSIA)
      .map((item: any) => ({
        label: capitalizeWords(item.name, null, true),
        value: `${item.code}`,
      }));

    if (matchingState) {
      const matchingStateItem = stateList?.find(
        (item: any) => item.label === matchingState.label
      );

      if (matchingStateItem) {
        setValue("residenceCountryCode", Number(matchingStateItem.value));
      }
    }
  }, [watch("identificationNo")]);

  useEffect(() => {
    const idNoString =
      typeof watch("identificationNo") === "number"
        ? String(watch("identificationNo"))
        : watch("identificationNo") ?? "";

    if (idNoString.length >= 6) {
      setValue("dateOfBirth", convertIcToDate(watch("identificationNo")));
    }
  }, [watch("identificationNo")]);

  const { refetch: refetchProfile, isLoading: isLoadingProfile } = useQuery({
    url: `user/profile/getCurrentProfile`,
    autoFetch: false,
    onSuccess: (data) => {
      const profileData = data?.data?.data;
      setValue(
        "titleCode",
        profileData?.titleCode ? profileData?.titleCode : "-"
      );
      setValue("committeeName", profileData?.name);
      setValue("gender", profileData?.gender);
      setValue("committeeName", profileData?.name);
      setValue("email", profileData?.email);
      assignPhoneNumber({ hpNo: profileData?.mobilePhone ?? profileData?.hpNo }, { attributeName: "hpNo" });
      assignPhoneNumber(profileData, { attributeName: "workTelNo" });
      assignPhoneNumber(profileData, { attributeName: "homeTelNo" });
      setValue("residencePostcode", profileData?.postcode);
      setValue("residenceStateCode", profileData?.stateCode);
      setValue("residenceDistrictCode", profileData?.districtCode);
      setValue("residenceCity", profileData?.city);
      setValue("residenceAddress", profileData?.address);
      setValue("citizenshipStatus", Number(profileData?.citizenshipTitle));
      // This keeps the disabled fields as-is with no clear
      setFormValue(getValues());
    },
  });

  const assignPhoneNumber = (data: Record<string, string>, { attributeName }: { attributeName: string }) => {
    if (["60", "+60"].some((val) => data?.[attributeName]?.startsWith(val))) {
      const codeIndex = ["60", "+60"].findIndex((val) => data?.[attributeName].startsWith(val))
      setValue(`${attributeName}Code`, ["60", "+60"][codeIndex]);
      const attributeNameValue = ["", "undefined"].includes(data?.[attributeName].trim())
        ? ""
        : data?.[attributeName].slice(codeIndex === 0 ? 2 : 3).trim();
      assignPhoneNumber({ [attributeName]: attributeNameValue }, { attributeName })
    } else {
      setValue(attributeName, data?.[attributeName] ?? "");
    }
  }

  const { refetch: fetchSecretary, isLoading: isLoadingSecretary } = useQuery({
    url: `society/secretary/principal/${secretaryId}`,
    autoFetch: false,
    onSuccess: (res) => {
      const data = res?.data?.data;
      if (!data) return;

      setValue("titleCode", data?.titleCode ? data?.titleCode : "-");
      setValue("gender", data?.gender); //EDIT
      setValue("committeeName", data?.committeeName);
      setValue("jobCode", data?.jobCode);
      setValue("email", data?.email);
      assignPhoneNumber(data, { attributeName: "hpNo" });
      assignPhoneNumber(data, { attributeName: "workTelNo" });
      assignPhoneNumber(data, { attributeName: "homeTelNo" });
      setValue("residencePostcode", data?.residencePostcode);
      setValue("residenceStateCode", data?.residenceStateCode);
      setValue("residenceDistrictCode", data?.residenceDistrictCode);
      setValue("dateOfBirth", data?.dateOfBirth);
      setValue("residenceCountryCode", data?.residenceCountryCode);
      setValue("residenceCity", data?.residenceCity);
      setValue("residenceAddress", data?.residenceAddress);
      setValue("citizenshipStatus", isNaN(parseInt(data?.citizenshipStatus))
        ? data.citizenshipStatus === "malaysia" ? 1 : 2
        : Number(data?.citizenshipStatus)
      );
      // This keeps the disabled fields as-is with no clear
      setFormValue(getValues());
    },
  });

  useEffect(() => {
    if (secretaryId) {
      fetchSecretary();
    } else {
      refetchProfile();
    }
  }, [secretaryId]);

  return (
    <form noValidate onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography sx={sectionTitleStyle}>
          {isMalaysiaLanguage ? "Setiausaha Baru" : "New Secretary"}
        </Typography>

        <Grid container spacing={2} pl={2} pt={2}>
          <FormFieldRow
            label={<Label text={t("position")} />}
            value={<DisabledTextField value={t("secretary")} />}
          />
        </Grid>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography sx={sectionTitleStyle}>
          {isMalaysiaLanguage
            ? "Maklumat Peribadi Setiausaha baru"
            : "Personal Information of the new Secretary"}
        </Typography>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Grid container spacing={2} pl={2} pt={2}>
            <FormFieldRow
              label={<Label text={t("idType")} required />}
              value={<DisabledTextField value={t("mykad")} />}
            />

            <FormFieldRow
              label={<Label text={t("idNumber")} />}
              value={
                <TextFieldController
                  type="number"
                  control={control}
                  name="identificationNo"
                  disabled
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("gelaran")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="titleCode"
                  options={ListGelaran}
                  disabled
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("fullName")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeName"
                  disabled
                  required
                />
              }
            />
            <FormFieldRow
              label={<Label text={t("gender")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="gender"
                  options={ListGender.map((option) => ({
                    ...option,
                    label: t(option.label),
                  }))}
                  disabled={!!getValues("gender")}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("citizen")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="citizenshipStatus"
                  options={CitizenshipStatus.map((option) => ({
                    ...option,
                    label: t(option.label),
                  }))}
                  disabled
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("dateOfBirth")} required />}
              value={
                <DatePickerController
                  control={control}
                  name="dateOfBirth"
                  disabled={disabledState}
                  required
                />
              }
            />
            <FormFieldRow
              label={<Label text={t("placeOfBirth")} />}
              value={
                <TextFieldController
                  control={control}
                  name="placeOfBirth"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("pekerjaan")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="jobCode"
                  options={occupationList}
                  disabled={disabledState}
                  rules={{
                    required: t("fieldRequired"),
                  }}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("residentialAddress")} />}
              value={
                <TextFieldController
                  control={control}
                  name="residenceAddress"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("state")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="residenceStateCode"
                  options={stateOptions}
                  disabled={disabledState}
                  rules={{
                    required: t("fieldRequired"),
                  }}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("district")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="residenceDistrictCode"
                  options={residenceDistrictOptions}
                  disabled={!watch("residenceStateCode") || disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("city")} />}
              value={
                <TextFieldController
                  control={control}
                  name="residenceCity"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("postcode")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="residencePostcode"
                  disabled={disabledState}
                  inputProps={{ maxLength: 5 }}
                  maxRows={5}
                  rules={{
                    required: t("fieldRequired"),
                    validate: (value: string) => {
                      if (!/^\d{5}$/.test(value)) {
                        return t("residencePostcode");
                      }
                      return true;
                    },
                  }}
                  isPostcode
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("email")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="email"
                  type="email"
                  disabled
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("mobilePhoneNumber")} required />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="hpNoCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 6 }}
                    sxControl={{ width: "80px" }}
                    disabled
                    required
                  />
                  <TextFieldController
                    control={control}
                    disabled
                    name="hpNo"
                    type="tel"
                    required
                  />
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("officePhoneNumber")} />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="workTelNoCode"
                    type="telCode"
                    inputProps={{ maxLength: 6 }}
                    sx={{ width: "80px" }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="workTelNo"
                    type="tel"
                  />
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("homePhoneNumber")} />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="homeTelNoCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 6 }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="homeTelNo"
                    type="tel"
                  />
                </Box>
              }
            />
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography
            sx={{
              color: "#FF0000",
              fontSize: "16px",
              fontWeight: "500 !important",
            }}
          >
            {t("peringatan")} :
          </Typography>

          <Typography
            sx={{
              color: "#666666",
              fontSize: "14px",
            }}
          >
            {isMalaysiaLanguage
              ? "Bagi ahli jawatankuasa yang TIDAK BEKERJA/ PESARA, maklumat majikan tidak perlu diisi"
              : "For committee members who are UNWORKED/RETIRED, employer information does not need to be filled in"}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography sx={sectionTitleStyle}>{t("employerInfo")}</Typography>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Grid container spacing={2} pl={2} pt={2}>
            <FormFieldRow
              label={<Label text={t("employerName")} />}
              value={
                <TextFieldController
                  control={control}
                  name="employerName"
                  disabled={disabledState || disabledEmployer}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("employerAddress")} />}
              value={
                <SelectFieldController
                  control={control}
                  name="employerAddressStatus"
                  options={employerAddressOptions}
                  disabled={disabledState || disabledEmployer}
                />
              }
            />

            {employerAddressStatus === "0" && (
              <>
                <FormFieldRow
                  label={<Label text={t("country")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="employerCountryCode"
                      options={allCountryOptions}
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={""} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="employerAddress"
                      disabled={disabledState || disabledEmployer}
                      multiline
                      rows={3}
                    />
                  }
                />
              </>
            )}

            {employerAddressStatus === "1" && (
              <>
                <FormFieldRow
                  label={<Label text={""} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="employerAddress"
                      disabled={disabledState || disabledEmployer}
                      multiline
                      rows={3}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("state")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="employerStateCode"
                      options={stateOptions}
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("district")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="employerDistrictCode"
                      options={employerDistrictOptions}
                      disabled={
                        !watch("employerStateCode") ||
                        disabledState ||
                        disabledEmployer
                      }
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("city")} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="employerCity"
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("postcode")} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="employerPostcode"
                      inputProps={{ maxLength: 5 }}
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />
              </>
            )}
          </Grid>
        </Box>
      </Box>

      {
        <>
          {!isFeedback && (!secretaryId || isEditable) ? (
            <Box
              sx={{
                width: "fit-content",
                marginTop: "24px",
                marginLeft: "auto",
                display: "flex",
                gap: "11px",
              }}
            >
              <ButtonOutline onClick={onResetPreservingDisabled}>
                {t("semula")}
              </ButtonOutline>

              <ButtonPrimary type="submit" disabled={isCreatingSecretary}>
                {isCreatingSecretary && <CircularProgress size={15} />}
                {t("update")}:
              </ButtonPrimary>
            </Box>
          ) : null}
        </>
      }
    </form>
  );
};

export default SecretaryForm;
