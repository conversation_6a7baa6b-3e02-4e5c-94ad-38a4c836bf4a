import { Box, Typography } from "@mui/material";
import {
  useCallback,
  useLayoutEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from "react";
import { useGetIdentity } from "@refinedev/core";
import { IUser } from "@/types";
import { useTranslation } from "react-i18next";
import { useQuery } from "@/helpers";
import { ChatFormInner } from "./ChatFormInner";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm, FormProvider } from "react-hook-form";
import { ChatMessageOnlyText } from "./types";
import { MessageDisplay } from "./MessageDisplay";
import { OptionPanel } from "./OptionPanel";
import { useChatbot } from "./useChatbot";
import { getOptionGroups } from "./utils";
import { object, string } from "yup";
import { Form } from "@/components/form";
import { useLocation } from "react-router-dom";
import { listenForSpeechInput, clearSpeechInput } from "./SimpleCrossPageComm";

/**
 * Full-page chatbot component without the popup styling
 * Simplified version with clean cross-page communication and duplicate prevention
 */
export const FullPageChatArea = forwardRef<
  any,
  { enableCrossPageCommunication?: boolean }
>((props, ref) => {
  const { enableCrossPageCommunication = true } = props;
  const { i18n } = useTranslation();
  const { data: user } = useGetIdentity<IUser>();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatFormRef = useRef<{ focus: () => void }>(null);
  const location = useLocation();

  // Check if we're on the speech input page
  const isOnSpeechInputPage = location.pathname === "/speech-input";

  // Get locale
  const locale = ["bm", "my"].includes(i18n.language) ? "ms" : i18n.language;

  // Fetch society list data
  const { data: societyListDataResponse, refetch: refetchSocietyList } =
    useQuery({
      autoFetch: false,
      url: "society/getUserSociety",
      filters: [
        { field: "pageSize", value: 5, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
      ],
    });

  // Get option groups based on user status
  const optionGroups = getOptionGroups(locale, !!user?.identificationNo);

  // Use the chatbot hook for state management
  const {
    messages,
    currentBotMessage,
    displayedBotText,
    showOptionPanel,
    selectedParentKey,
    loading,
    error,
    setAutoPlayEnabled,
    handleOptionSelect,
    handleBackToOptions,
    handleSubmitMessage,
  } = useChatbot({
    user,
    locale,
    isOpen: true, // Always treat as open
    refetchSocietyList,
    societyListDataResponse,
    isWidget: false,
  });

  // Focus input when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      if (chatFormRef.current && !loading) {
        chatFormRef.current.focus();
      }
    }, 300);
    return () => clearTimeout(timer);
  }, [loading]);

  // Focus input after bot finishes typing
  useEffect(() => {
    if (currentBotMessage === null && !loading) {
      const timer = setTimeout(() => {
        if (chatFormRef.current) {
          chatFormRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [currentBotMessage, loading]);

  // Focus input when option panel is shown
  useEffect(() => {
    if (showOptionPanel && !loading) {
      const timer = setTimeout(() => {
        if (chatFormRef.current) {
          chatFormRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [showOptionPanel, loading]);

  // Disable auto-play on speech input page
  useEffect(() => {
    if (isOnSpeechInputPage) {
      setAutoPlayEnabled(false);
    } else {
      setAutoPlayEnabled(true);
    }
  }, [isOnSpeechInputPage, setAutoPlayEnabled]);

  // Form setup
  const formMethods = useForm<ChatMessageOnlyText>({
    defaultValues: { text: "" },
    resolver: yupResolver(object({ text: string().required() })),
    mode: "onChange",
  });

  // Handle form submission
  const handleSend = async (data: ChatMessageOnlyText) => {
    if (!data.text.trim()) return;
    formMethods.reset({ text: "" });
    await handleSubmitMessage(data.text);
  };

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  // Reset form when bot is typing
  useLayoutEffect(() => {
    if (currentBotMessage !== null) {
      formMethods.reset({ text: "" });
    }
  }, [currentBotMessage, formMethods]);

  // Scroll to bottom when messages change
  useLayoutEffect(() => {
    scrollToBottom();
  }, [messages, currentBotMessage, loading, scrollToBottom]);

  // Scroll to bottom when component mounts
  useLayoutEffect(() => {
    scrollToBottom();
  }, [scrollToBottom]);

  // Enhanced deduplication using multiple tracking mechanisms
  const processedMessagesRef = useRef<Set<string>>(new Set());
  const lastProcessedTextRef = useRef<string>("");
  const lastProcessedTimeRef = useRef<number>(0);

  // Method to submit speech input programmatically
  const submitSpeechInput = useCallback(
    (text: string, messageId?: string) => {
      if (!text.trim()) return;

      const now = Date.now();
      const trimmedText = text.trim();

      // Multiple deduplication checks

      // 1. Check if we've already processed this exact message ID
      if (messageId && processedMessagesRef.current.has(messageId)) {
        // console.log("FullPageChatArea: Skipping already processed message ID:", messageId);
        return;
      }

      // 2. Check if this is the same text as the last message
      if (trimmedText === lastProcessedTextRef.current) {
        // console.log("FullPageChatArea: Skipping duplicate text:", trimmedText);
        return;
      }

      // 3. Check if this message is too close to the last one (within 2 seconds)
      if (now - lastProcessedTimeRef.current < 2000) {
        // console.log("FullPageChatArea: Skipping message too close to previous one");
        return;
      }

      // console.log("FullPageChatArea: Processing speech input:", trimmedText);

      // Update tracking refs
      if (messageId) {
        processedMessagesRef.current.add(messageId);
      }
      lastProcessedTextRef.current = trimmedText;
      lastProcessedTimeRef.current = now;

      // Clean up old processed messages (keep only last 50)
      if (processedMessagesRef.current.size > 50) {
        const messagesArray = Array.from(processedMessagesRef.current);
        processedMessagesRef.current.clear();
        messagesArray
          .slice(-25)
          .forEach((id) => processedMessagesRef.current.add(id));
      }

      // Set the text in the form field and submit
      formMethods.setValue("text", trimmedText);
      setTimeout(() => {
        handleSend({ text: trimmedText });
      }, 100);
    },
    [formMethods, handleSend]
  );

  // Set up cross-page communication listener (only if enabled)
  useEffect(() => {
    if (!enableCrossPageCommunication) {
      // console.log("FullPageChatArea: Cross-page communication disabled for this instance");
      return;
    }

    // console.log("FullPageChatArea: Setting up cross-page listener");

    // Clear any pending speech input when component mounts to prevent stale data
    clearSpeechInput();

    // Set up listener for cross-page speech input using the simplified API
    const cleanup = listenForSpeechInput((text: string, messageId: string) => {
      // console.log("FullPageChatArea: Received cross-page speech input:",text,"ID:",messageId);
      submitSpeechInput(text, messageId);
    });

    // Return cleanup function
    return () => {
      // console.log("FullPageChatArea: Cleaning up cross-page listener");
      cleanup();
      // Clear any pending messages when component unmounts
      clearSpeechInput();
    };
  }, [submitSpeechInput, enableCrossPageCommunication]);

  // Clear processed messages when navigating away
  useEffect(() => {
    return () => {
      processedMessagesRef.current.clear();
      lastProcessedTextRef.current = "";
      lastProcessedTimeRef.current = 0;
    };
  }, []);

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    submitSpeechInput,
  }));

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: "100vh",
        maxWidth: "100%",
        overflow: "hidden",
        overflowX: "hidden",
        bgcolor: "background.paper",
        borderRadius: { xs: 0, sm: 2 },
        boxShadow: { xs: 0, sm: 3 },
        minWidth: 0, // Important for flex child to prevent overflow
      }}
    >
      {/* Messages */}
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          overflowX: "hidden",
          minHeight: 0, // Important for flex child
          minWidth: 0, // Important for flex child to prevent overflow
          width: "100%",
          maxWidth: "100%",
        }}
      >
        <MessageDisplay
          messages={messages}
          currentBotMessage={currentBotMessage}
          displayedBotText={displayedBotText}
          loading={loading}
          error={error}
          userProfilePicture={user?.profilePicture || ""}
          messagesEndRef={messagesEndRef}
          locale={locale}
          disableSpeech={true}
          isWidget={false}
        />
      </Box>

      {/* Options Panel */}
      <OptionPanel
        showOptionPanel={showOptionPanel}
        loading={loading}
        currentBotMessage={currentBotMessage}
        selectedParentKey={selectedParentKey}
        optionGroups={optionGroups}
        locale={locale}
        onOptionSelect={handleOptionSelect}
        onBackToOptions={handleBackToOptions}
      />

      <Box
        sx={{
          position: "sticky",
          bottom: 0,
          bgcolor: "background.paper",
          borderTop: "1px solid #eee",
          px: 2,
          py: 1.5,
          zIndex: 1,
        }}
      >
        {/* Input Form */}
        <FormProvider {...formMethods}>
          <Form<ChatMessageOnlyText>
            onSubmit={handleSend}
            defaultValues={{ text: "" }}
            key={currentBotMessage || "default"}
          >
            <ChatFormInner ref={chatFormRef} />
          </Form>
        </FormProvider>
      </Box>
    </Box>
  );
});
