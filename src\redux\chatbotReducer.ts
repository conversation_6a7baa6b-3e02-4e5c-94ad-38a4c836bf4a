import { createAction, createSlice } from "@reduxjs/toolkit"

export interface ChatbotState {
  opened: boolean
  position: "left" | "center" | "right"
}

const initialState: ChatbotState = {
  opened: false,
  position: "right"
}

const chatbot = 'chatbot'

export const setChatbotOpenedRedux = createAction<ChatbotState["opened"]>(`${chatbot}/setChatbotOpenedRedux`)

export const setChatbotButtonPositionRedux = createAction<ChatbotState["position"]>(`${chatbot}/setChatbotButtonPositionRedux`)

export const chatbotSlice = createSlice({
  name: chatbot,
  initialState,
  reducers: {},
  selectors: {
    getChatbotOpened: (state) => state.opened,
    getChatbotButtonPosition: (state) => state.position
  },
  extraReducers: (builder) =>
    builder
      .addCase(setChatbotOpenedRedux, (state, action) => {
        console.log('Redux: Setting chatbot opened state from', state.opened, 'to', action.payload)
        state.opened = action.payload
      })
      .addCase(setChatbotButtonPositionRedux, (state, action) => {
        state.position = action.payload
      })
})

export const { getChatbotOpened, getChatbotButtonPosition } = chatbotSlice.selectors

export const chatbotReducer = chatbotSlice.reducer

export default chatbotSlice
