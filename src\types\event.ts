export interface IEventOrganiser {
  id: number;
  name?: string;
  phoneNumber?: string;
  identificationNo?: string;
}

export interface PrivateEventSocitie {
  societyId: number;
}

export interface IEvent {
  id: number;
  eventNo: string;
  eventName: string;
  eventAdminId: number;
  description: string;
  collaboratorName: string;

  // Dates
  startTime: string;
  endTime: string;
  eventStartDate: Date | null | string;
  eventEndDate: Date | null | string;
  regStartDate: string;
  regEndDate: string;
  createdDate: string | null;
  modifiedDate: string | null;

  // Location details
  address1: string;
  address2: string | null;
  state: number[];
  postcode: string;
  city: string[];
  district: number[],
  venue: string;
  mapUrl: string;

  // Organizers and participants
  eventOrganisers: IEventOrganiser[];
  maxParticipants: number | null;
  hasMax: boolean;
  organisationLevel: string;
  organisationCategory: number|null;
  picContactNo: string | null;

  // Status and visibility
  status: string | null;
  visibility: "PRIVATE" | "PUBLIC";
  published: boolean;

  // Private event specific
  societiesId: number[] | null;
  organiserId: number | null;
  privateEventSocieties: PrivateEventSocitie[] | null;

  // Audit fields
  createdBy: number | null;
  modifiedBy: number | null;

  feedbackName: string;
  position: number[];
  bannerUrl?: string | null;

  // location
  stateAddress: number | null;
  districtAddress: number | null;
  cityAddress: string;
  postcodeAddress: string;
  totalRegisteredParticipants: number;
}

// Type guard to check if an object is an IEvent
export function isEvent(obj: any): obj is IEvent {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'number' &&
    typeof obj.eventNo === 'string' &&
    typeof obj.eventName === 'string'
  );
}

export interface ApiResponse<T> {
  data: T;
  status: string;
  msg?: string;
  code?: number;
}
// Type for API response
export interface IEventResponse {
  status: 'SUCCESS' | 'ERROR';
  code: number;
  msg: string | null;
  data: IEvent[];
  timeStamp: string;
}


export interface EventFormData {
  posterPreview: any;
  dateError: any;
  eventName: string;
  eventAdminId: number;
  description: string;
  collaboratorName: string;
  eventNo: string;

  // Dates
  startTime: string;
  endTime: string;
  eventStartDate: Date | null |string;
  eventEndDate: Date | null | string;
  regStartDate: string;
  regEndDate: string;

  // Location details
  address1: string;
  address2: string | null;
  state: number[] ;
  postcode: string;
  venue: string;
  city: string[];
  district: number[];
  mapUrl: string;

  // Organizers and participants
  maxParticipants: number | null;
  hasMax: boolean;
  organiserId: number | null;
  societiesId: number[] | null;
  organisationLevel: string;
  organisationCategory: number|null;
  picContactNo: string | null;

  // Status and visibility
  status: string | null;
  visibility: "PRIVATE" | "PUBLIC";
  published: boolean;

  feedbackName: string;
  position: number[];
  bannerUrl:string;
  stateAddress: number | null;
  districtAddress: number | null;
  cityAddress: string;
  postcodeAddress: string;
}

export interface BannerUploadResponse {
  bannerImageUrl: string;
  bannerImageName: string;
}
