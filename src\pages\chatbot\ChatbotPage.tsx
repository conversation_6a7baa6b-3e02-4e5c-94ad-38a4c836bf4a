import React, { useEffect, useRef, useState } from 'react';
import { Box, useTheme, Tooltip, IconButton, Typography } from '@mui/material';
import { useDispatch } from 'react-redux';
import { setChatbotOpenedRedux } from '@/redux/chatbotReducer';
import { SpeechProvider } from '@/contexts/chatbot/SpeechContext';
import { FullPageChatArea } from '@/components/chatbot/FullPageChatArea';
import { Refresh as RefreshIcon } from '@mui/icons-material';


export const BetaBadgeTopRight = () => (
  <Box
    sx={{
      position: "absolute",
      top: 0,
      right: 0,
      width: 100,
      height: 100,
      zIndex: 20,
      pointerEvents: "none",
    }}
  >
    <Box
      sx={{
        position: "absolute",
        width: 160,
        transform: "rotate(45deg)",
        top: 10,
        // right: -40,
        backgroundColor: "red",
        textAlign: "center",
        py: 0.4,
        boxShadow: 2,
      }}
    >
      <Typography
        variant="caption"
        sx={{
          color: "white",
          fontWeight: "bold",
          fontSize: 16,
          // letterSpacing: 1,
        }}
      >
        BETA
      </Typography>
    </Box>
  </Box>
);
/**
 * Full-page chatbot component
 * This page displays only the chatbot in a full-screen layout
 */
const ChatbotPage: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const fullPageChatAreaRef = useRef<any>(null);
  const [refreshKey, setRefreshKey] = useState<number>(0);

  // Function to force refresh the chat area
  const handleRefresh = () => {
    console.log('ChatbotPage: Forcing refresh of chat area');
    setRefreshKey(prev => prev + 1);
  };

  // Ensure the chatbot is opened when this page is visited
  // and hide the floating chatbot widget
  useEffect(() => {
    // Set chatbot state to closed to prevent the floating widget from showing
    dispatch(setChatbotOpenedRedux(false));

    console.log('ChatbotPage: Page mounted, ready to load messages from API');

    // We don't need to check for pending messages anymore since we're using the API
    // The messages will be retrieved from the API on page load

    // Log that we're using API-based message history
    console.log('ChatbotPage: Using API-based message history');

    // Clean up when leaving the page
    return () => {
      dispatch(setChatbotOpenedRedux(false));
      console.log('ChatbotPage: Page unmounted');
    };
  }, [dispatch]);

  return (
    <SpeechProvider>
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          minHeight: 0, // Important for flex child
        }}
      >
        {/* Refresh button */}
        {/* <Tooltip title="Refresh chat history">
          <IconButton
            onClick={handleRefresh}
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              zIndex: 1000,
              bgcolor: 'rgba(255, 255, 255, 0.8)',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.9)',
              },
            }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip> */}

        {/* Chat area with key for forcing refresh - enables cross-page communication */}
        <Box sx={{ flex: 1, width: '100%' }}>
          <FullPageChatArea
            ref={fullPageChatAreaRef}
            key={`chat-area-${refreshKey}`}
            enableCrossPageCommunication={true}
          />
        </Box>
      </Box>
      {/* Optional beta badge */}
      <BetaBadgeTopRight />
    </SpeechProvider>
  );
};

export default ChatbotPage;
