import * as Yup from "yup";
import { useTranslation } from "react-i18next";

export const useAssetSchema = () => {
  const { t } = useTranslation();

  const assetSchema = Yup.object().shape({
    assetType: Yup.string().required(t("validation.required")),
    assetValue: Yup.number()
      .transform((value, originalValue) => {
        if (typeof originalValue === "string") {
          const cleaned = originalValue.replace(/,/g, "");
          return Number(cleaned);
        }
        return value;
      })
      .typeError(t("validation.mustBeNumber"))
      .required(t("validation.required"))
      .min(0, t("validation.minValue", { value: 0 })),
    reason: Yup.string().required(t("validation.required")),
    otherReason: Yup.string().when("reason", ([reason], schema) => {
      return reason === "other"
        ? schema.required(t("validation.required"))
        : schema.notRequired();
    }),
  });

  return assetSchema;
};