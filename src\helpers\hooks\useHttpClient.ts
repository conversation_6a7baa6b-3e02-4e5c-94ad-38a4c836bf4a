import Axios from "axios"
import { useNavigate } from "react-router-dom"
import { HttpError } from "@refinedev/core"
import dataProvider from "@refinedev/simple-rest"

import { API_URL } from "@/api"

/**
 * @todo uncomment those codes if integration with redux
 * is no longer breaking our current app
 */
// <-- [START] -->
// import { useSelector } from "react-redux"
// import isEqual from "lodash.isequal"
// import { getAuthorizationHeader } from "@/redux/userReducer"
// <-- [END] -->

export const baseURL = import.meta.env.VITE_BACKEND_URL ?? API_URL

export function useHttpClient() {
  const navigate = useNavigate()

  const httpClient = Axios.create({
    baseURL,
    /**
     * @todo migrate to fetch instead xhr
     */
    adapter: "fetch"
  })

  httpClient.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.status === 401) {
        navigate('/login', { replace: true })
      } else if (error.status === 403) {
        navigate('/forbidden')
      }
      /**
       * this codes mimic interceptors used in the {@link dataProvider}
       */
      const customError: HttpError = {
        ...error,
        message: error.response?.data?.message,
        statusCode: error.response?.status,
      };
      return Promise.reject(customError)
    }
  )

  /**
   * @todo uncomment those codes if integration with redux
   * is no longer breaking our current app
   */
  // <-- [START] -->
  // const authorizationHeaders = useSelector(getAuthorizationHeader, isEqual)

  // if (authorizationHeaders !== null) {
  //   httpClient.defaults.headers['portal'] = authorizationHeaders.portal
  //   httpClient.defaults.headers['Authorization'] = authorizationHeaders.Authorization
  // } else {
  //   delete httpClient.defaults.headers['portal']
  //   delete httpClient.defaults.headers['Authorization']
  // }
  // <-- [END] -->

  return httpClient
}
