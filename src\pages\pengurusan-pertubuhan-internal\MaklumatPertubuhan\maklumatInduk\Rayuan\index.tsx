import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import {
  ApplicationStatusList,
  DecisionOptionsCodeR<PERSON>uan,
  SebabRyuanList,
} from "../../../../../helpers/enums";
import { ButtonPrimary } from "../../../../../components/button";
import DisabledTextField from "../../../../../components/input/DisabledTextField";
import { useQuery } from "@/helpers";
import { useEffect, useState } from "react";
import Input from "@/components/input/Input";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

function Rayuan() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const appeal = location.state?.appeal;
  const [appealDecision, setAppealDecision] = useState<any>({});
  const {
    data: appealDataById,
    isLoading: isLoadingAppealDataById,
    refetch,
  } = useQuery({
    url: `society/admin/appeal/${appeal?.id}/getAdminRecord`,
  });

  useEffect(() => {
    if (appealDataById?.data?.data) {
      setAppealDecision(appealDataById?.data?.data);
    }
  }, [appealDataById?.data?.data]);

  console.log("appealDecision", appealDecision);
  const decisionOptions = DecisionOptionsCodeRayuan(t);

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {appeal.societyName ?? "-"}
              <br />
              {appeal.societyNo ?? "-"}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            backgroundColor: "white",
            padding: "18px 16px",
            borderRadius: "14px",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="16px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("sebabRayuan")}
            </Typography>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationName")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={appealDecision?.societyName ?? "-"} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationNumber")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={appealDecision?.societyNo ?? "-"} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("sebabRayuan")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    SebabRyuanList.find(
                      (item) => item.value === appealDecision?.idSebab
                    )?.label ?? "-"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("secretaryName")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={appealDecision?.secretaryName ?? "-"}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("noPengenalanSetiausaha")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={appealDecision?.secretaryIdentificationNo ?? "-"}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("statusSemasa")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={t(
                    ApplicationStatusList.find(
                      (item: any) =>
                        Number(item.id) ===
                        Number(appealDecision?.applicationStatusCode)
                    )?.value ?? "-"
                  )}
                />
              </Grid>
            </Grid>
            <br />
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("pppBertanggungjawab")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={appealDecision?.pppName ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("kppBertanggungjawab")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={appealDecision?.kppName ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("pendaftarSyor")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={appealDecision?.registerarName ?? "-"}
                />
              </Grid>
            </Grid>
            <br />
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("diluluskanOleh")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={appealDecision?.approvedBy ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("keputusan")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    DecisionOptionsCodeRayuan(t).find(
                      (option) => option.value === appealDecision?.decision
                    )?.label ?? "-"
                  }
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("decisionDate")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={appealDecision?.decisionDate ?? "-"}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Typography sx={labelStyle}>{t("note")}</Typography>
              </Grid>
              <Grid item xs={8}>
                <Box
                  sx={{
                    p: 1,
                    background: "#E8E9E8",
                    borderRadius: "5px",
                    border: "1px solid #DADADA !important",
                    "& fieldset": { borderRadius: "5px" },
                    "& .MuiInputBase-input.Mui-disabled": {
                      WebkitTextFillColor: "#666666",
                    },
                    "& .MuiInputBase-input": {
                      color: "black",
                    },
                    [`& input[type="number"]`]: {
                      MozAppearance: "textfield",
                      appearance: "textfield",
                    },
                    "& input[type=number]::-webkit-outer-spin-button": {
                      WebkitAppearance: "none",
                      margin: 0,
                    },
                    "& input[type=number]::-webkit-inner-spin-button": {
                      WebkitAppearance: "none",
                      margin: 0,
                    },
                    "& input::placeholder, & textarea::placeholder": {
                      fontSize: "14px !important",
                    },
                    color: "var(--text-grey)",
                  }}
                >
                  <FasalDisplayContent
                    clauseContent={appealDecision?.decisionNote ?? "-"}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>
    </>
  );
}

export default Rayuan;
