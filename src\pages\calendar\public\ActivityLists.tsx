import Grid from "@mui/material/Grid/Grid";
import { useTranslation } from "react-i18next";

import { useEffect, useState } from "react";

import { JumbotronPublic } from "../../../components/jumbotron/Public";
import { CardCalendarActivityList } from "../../../components/card/calendar/ActivityList";
import { CardCalendarSidebar } from "../../../components/card/calendar/Sidebar";

import "./activity-lists.css";

const CalendarPublicActivityListsPage = () => {
  const [loading, setLoading] = useState(true)

  const { t } = useTranslation()

  useEffect(() => {
    setTimeout(() => {
      setLoading(false)
    }, 3000)
  }, [])

  return (
    <>
      <JumbotronPublic
        breadcrumbs={[
          {
            url: "/takwim",
            label: t('calendar'),
            navigationHighlighted: () => true
          },
          {
            url: "/takwim/aktiviti",
            label: t('calendarActivityLists')
          }
        ]}
        title={t('calendarActivityLists')}
      />
      <div className="content">
        <Grid container spacing={2}>
          <Grid item md={3}>
            <CardCalendarSidebar loadActivities={false} />
          </Grid>
          <Grid item sm={12} md={9} sx={{ width: "100%" }}>
            <div
              className="list-view"
            >
              <CardCalendarActivityList loading={loading} />
              <CardCalendarActivityList loading={loading} />
              <CardCalendarActivityList loading={loading} />
              <CardCalendarActivityList loading={loading} />
              <CardCalendarActivityList loading={loading} />
            </div>
          </Grid>
        </Grid>
      </div>
    </>
  );
};

export default CalendarPublicActivityListsPage;
