import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Card,
  TableContainer,
  Paper,
  TablePagination,
  IconButton,
  TextField,
  InputAdornment,
} from "@mui/material";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import useQuery from "../../../../../helpers/hooks/useQuery";
import { useForm } from "@refinedev/react-hook-form";
import { FieldValues } from "react-hook-form";
import { SearchIcon } from "@/components/icons";
import { useSelector } from "react-redux";

export const CawanganDocument: React.FC = () => {
  const [dokumenList, setDokumenList] = useState<any[]>([]);
  const { t } = useTranslation();

  const { id } = useParams();
  const [searchQuery, setSearchQuery] = useState("");
  //@ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const { watch, setValue } = useForm<FieldValues>({
    defaultValues: {
      identificationNo: "",
      page: 0,
      rowsPerPage: 10,
      searchQuery: undefined,
    },
  });

  const {
    data,
    isLoading,
    refetch: fetchDocumentList,
  } = useQuery({
    url: "society/document/documentByParam",
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: id,
      },
      { field: "pageSize", operator: "eq", value: watch("rowsPerPage") },
      { field: "pageNo", operator: "eq", value: watch("page") + 1 },
      {
        field: "searchQuery",
        value: searchQuery,
        operator: "eq",
      },
      {
        field: "branchId",
        value: branchDataRedux?.id,
        operator: "eq",
      },
    ],
  });

  const handleChangePage = (_: unknown, newPage: number) => {
    setValue("page", newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setValue("rowsPerPage", newRowsPerPage);
    setValue("page", 0);
  };

  const handleViewDocument = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const handleDownloadDocument = async (filePath: string, name: string) => {
    const response = await fetch(filePath);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  useEffect(() => {
    fetchDocumentList();
  }, []);

  useEffect(() => {
    fetchDocumentList();
  }, [watch("rowsPerPage"), watch("page"), watch("identificationNo")]);

  useEffect(() => {
    if (data?.data?.data) {
      // const documents = data.data.data.map((doc: any) => ({
      //   ...doc,
      //   documentName: doc.url.split("/").pop()?.split("?")[0],
      // }));
      setDokumenList(data?.data?.data || []);
    }
  }, [data]);

  const totalRecords = data?.data?.data?.total ?? 0;

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      setValue("page", 1);
      setSearchQuery(value);
    }
  };

  return (
    <>
      <Box>
        <Card
          sx={{
            px: 3,
            pt: 1,
            pb: 4,
            borderRadius: "15px",
            boxShadow: "none",
            height: "100%",
            width: "100%",
          }}
        >
          <Box mt={3}>
            <Box
              sx={{
                border: "1px solid #e0e0e0",
                borderRadius: "15px",
                px: 2,
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  color: "var(--primary-color)",
                  fontWeight: 600,
                  px: 4,
                  pt: 3,
                }}
              >
                {t("senaraiDokumen")}
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                placeholder={t("documentName")}
                sx={{
                  display: "block",
                  boxSizing: "border-box",
                  maxWidth: 570,
                  marginInline: "auto",
                  height: "40px",
                  background: "var(--border-grey)",
                  opacity: 0.5,
                  my: 4,
                  border: "1px solid var(--text-grey)",
                  borderRadius: "10px",
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                    "& fieldset": {
                      border: "none",
                    },
                  },
                }}
                onKeyDown={onSearchKeyDown}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          color: "var(--text-grey-disabled)",
                          marginLeft: "8px",
                        }}
                      />
                    </InputAdornment>
                  ),
                }}
              />
              <TableContainer
                component={Paper}
                sx={{
                  boxShadow: "none",
                  backgroundColor: "white",
                  borderRadius: 2.5 * 1.5,
                  p: 1,
                  mb: 3,
                }}
              >
                <Table
                  sx={{
                    minWidth: 650,
                    backgroundColor: "white",
                    borderRadius: "4px",
                    overflow: "hidden",
                  }}
                >
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ width: "2%" }} />
                      <TableCell sx={{ fontWeight: 400 }}>
                        {t("documentName")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {t("keterangan")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }} />
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {dokumenList.map((row: any, index: any) => (
                      <TableRow key={index}>
                        <TableCell sx={{ fontWeight: 300 }}>
                          {`${index + 1}.`}
                        </TableCell>
                        <TableCell sx={{ fontWeight: 300, textAlign: "left" }}>
                          {row.name}
                        </TableCell>
                        <TableCell sx={{ fontWeight: 300 }}>
                          {row.note}
                        </TableCell>
                        <TableCell sx={{ fontWeight: 300 }}>
                          <Box sx={{ textAlign: "right" }}>
                            <IconButton
                              onClick={() => handleViewDocument(row.url)}
                            >
                              <img
                                src={"/eyeIcon.svg"}
                                alt="Filter Icon"
                                width="14"
                                height="14"
                              />
                            </IconButton>
                            <IconButton
                              onClick={() =>
                                handleDownloadDocument(row.url, row.name)
                              }
                            >
                              <img
                                src={"/downloadIcon.svg"}
                                alt="Filter Icon"
                                width="14"
                                height="14"
                              />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {!dokumenList.length && !isLoading && (
                  <Typography
                    fontSize="0.875rem"
                    fontWeight={300}
                    textAlign="center"
                    lineHeight="24px"
                    marginTop="18px"
                    borderBottom={"1px solid rgba(224, 224, 224, 1)"}
                    paddingBottom="16px"
                  >
                    {t("tiadaData")}
                  </Typography>
                )}

                {dokumenList.length > 0 && (
                  <TablePagination
                    component="div"
                    count={totalRecords}
                    page={watch("page")}
                    onPageChange={handleChangePage}
                    rowsPerPage={watch("rowsPerPage")}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage={t("rowsPerPage")}
                  />
                )}
              </TableContainer>
            </Box>
          </Box>
        </Card>
      </Box>
    </>
  );
};

export default CawanganDocument;
