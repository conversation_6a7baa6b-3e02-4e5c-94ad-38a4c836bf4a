import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import InputLabel from "@mui/material/InputLabel";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import { Select, Option } from "../../../components/input";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCreate } from "@refinedev/core";
import Input from "../../../components/input/Input";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../api";
import { LoadingOverlay } from "../../../components/loading";
import { useSelector } from "react-redux";
import dayjs from "dayjs";
import { getLocalStorage, PaymentPrefixes } from "@/helpers";

export const Online = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const carianRedux = useSelector(
    (state: { carian: any }) => state.carian.data
  );
  const navigate = useNavigate();
  const location = useLocation();
  const userDetail = getLocalStorage("user-details", false);
  const params = new URLSearchParams(window.location.search);
  // const encodedId = params.get("id");

  const [searchParams] = useSearchParams();
  const [societyData, setSocietyData] = useState<any>(null);
  const [isLoadingData, setIsLoadingData] = useState(false);
  // const appealId = searchParams.get("appealId");
  const societyId = searchParams.get("societyId");
  const currentDate = dayjs().format("DD-MM-YYYY");
  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";
  useEffect(() => {
    const fetchSocietyData = async () => {
      try {
        setIsLoadingData(true);
        // const encodedId = searchParams.get("id") || "";
        // const decodedId = encodedId;
        // console.log("decodedId", decodedId);
        const response = await fetch(`${API_URL}/society/${societyId}/basic`, {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          setSocietyData(data.data);
        }
      } catch (error) {
        console.error("Error fetching society data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchSocietyData();
  }, [searchParams]);

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const handleCetak = async () => {
    try {
      if (societyId) {
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: "Carian Maklumat (Pembayaran ONLINE)",
            paymentMethod: "o", // O = online, C = counter
            societyId: societyId,
            branchId: "",
            registerDateTime: dayjs(location?.state?.createdDate).format(
              "YYYY-MM-DDTHH:mm:ss"
            ),
            referenceNo: PaymentPrefixes.CARIAN + location?.state?.id,
            amount: carianRedux.amount,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isLoadingData} />

        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            {/* Payment Header Box */}
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("pengesahan")} {t("payment")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2,
                }}
              >
                {t("infoPayment")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={3}>
                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyData?.societyName || ""}
                />
                <Input
                  disabled
                  label={t("tarikhPermohonan")}
                  value={location?.state?.createdDate}
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  value={carianRedux.currentInfoId || ""}
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value={"Pembayaran Online"}
                />
                <Input
                  disabled
                  label={t("paymentType")}
                  value="Carian Maklumat (Pembayaran ONLINE)"
                />
                <Input
                  disabled
                  label={t("namePemohon")}
                  value={userName || ""}
                />
                <Input
                  disabled
                  label={t("email")}
                  value={userDetail?.email || ""}
                />
                <Input
                  disabled
                  label={t("idNumberPlaceholder")}
                  value={userDetail?.identificationNo || ""}
                />
                <Input disabled label={t("kodOsolAmanah")} value="724999" />
                <Input
                  disabled
                  label={t("jabatan")}
                  value="Jabatan Pendaftaran Pertubuhan"
                />
                <Input
                  disabled
                  label={t("pusatPenerimaan")}
                  value="Ibu Pejabat Jab. Pendataran Pertubuhan"
                />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2,
                }}
              >
                {t("noteOnline")}
              </Typography>
            </Box>

            {/* Action Buttons */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 3,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  color: "white",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
              <ButtonPrimary
                onClick={() => navigate(`term?societyId=${societyId}`)}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("bayar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmBuyDokumen")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>
    </Box>
  );
};

export default Online;
