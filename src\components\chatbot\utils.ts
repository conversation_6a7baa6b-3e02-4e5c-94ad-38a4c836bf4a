import { GroupedChatMessage, LangflowMessage, OptionGroup } from "./types";
import { IUser } from "@/types";
import { USER_DETAILS_KEY } from "@/helpers";

/**
 * Creates a full URL by combining the current host with a path
 * @param path The path to append to the host URL
 * @returns The complete URL
 */
const createUrl = (path: string): string => {
  // Get the current protocol and host
  const protocol = window.location.protocol;
  const host = window.location.host;
  // Remove any leading slashes from path
  const cleanPath = path.replace(/^\/+/, '');
  return `${protocol}//${host}/${cleanPath}`;
};

/**
 * Gets a session ID for the chatbot
 * @param user The current user, if logged in
 * @returns A unique session ID for logged-in users, or a temporary ID for guests
 */
export function getSessionId(user: IUser | undefined): string {
  // First try to get identificationNo from the passed user object
  if (user?.identificationNo) return user.identificationNo;

  // If not available, try to get it from localStorage using the constant
  const userDetailsStr = localStorage.getItem(USER_DETAILS_KEY);
  if (userDetailsStr) {
    try {
      const userDetails = JSON.parse(userDetailsStr);
      if (userDetails?.identificationNo) {
        return userDetails.identificationNo;
      }
    } catch (e) {
      // console.error("Error parsing user details from localStorage:", e);
    }
  }

  // For non-logged-in users, generate a temporary session ID that won't be stored
  // This ensures guests don't have persistent chat history
  return `guest-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
}

/**
 * Creates a new chat message
 * @param sender The sender of the message (user or bot)
 * @param text The message text
 * @param messageId Optional unique ID for the message
 * @returns A formatted chat message with timestamp and ID
 */
export function createMessage(sender: "bot" | "user", text: string, messageId?: string): GroupedChatMessage {
  const now = new Date();
  return {
    sender,
    text,
    timestamp: now.toISOString(),
    date: now.toDateString(),
    messageId: messageId || `${sender}_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`
  };
}

/**
 * Converts Langflow API messages to our internal message format
 * @param data Array of messages from Langflow API
 * @returns Formatted and sorted chat messages
 */
export function convertLangflowMessages(data: LangflowMessage[]): GroupedChatMessage[] {
  if (!data || !Array.isArray(data)) {
    // console.error('Invalid data passed to convertLangflowMessages:', data);
    return [];
  }

  // console.log(`Converting ${data.length} Langflow messages`);

  // Filter out any invalid messages
  const validMessages = data.filter(msg =>
    msg &&
    typeof msg === 'object' &&
    msg.sender &&
    msg.text &&
    msg.timestamp
  );

  if (validMessages.length < data.length) {
    // console.warn(`Filtered out ${data.length - validMessages.length} invalid messages`);
  }

  // Convert and sort messages
  return validMessages
    .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
    .map((msg) => {
      // Generate a unique ID for each message
      const messageId = `${msg.sender === "User" ? "user" : "bot"}_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`;

      return {
        sender: msg.sender === "User" ? "user" : "bot",
        text: msg.text,
        timestamp: msg.timestamp,
        date: new Date(msg.timestamp).toDateString(),
        messageId: messageId,
      };
    });
}

/**
 * Gets the appropriate greeting message based on locale
 * @param locale The current locale
 * @returns A localized greeting message
 */
export function getGreetingMessage(locale: string): string {
  return locale === "ms"
    ? `Hai! Saya Rosie 👋. Boleh saya bantu?
    
Pemakluman:
• 🗣️ Sila gunakan tatabahasa serta tanda baca yang sesuai semasa berinteraksi.
• 📝 Setiap komunikasi dan pertanyaan akan direkodkan untuk tujuan pemantauan dan penambahbaikan perkhidmatan.
• 🔧 Harap maklum bahawa Rosie masih dalam fasa pengujian.`
    : `Hello, I am Rosie 👋. How may I help you?
      
Notice:
• 🗣️ Please use complete sentences with appropriate punctuation when interacting.
• 📝 All communications and queries will be recorded for monitoring and service improvement purposes.
• 🔧 Kindly note that I am still in the testing phase and in continuous learning for improvement.`;
}

/**
 * Gets predefined responses based on locale
 * @param locale The current locale
 * @returns A record of predefined responses
 */
export function getPredefinedResponses(locale: string): Record<string, string> {
  return locale === "ms"
    ? {
        "daftar akaun": `Daftar Akaun:
  • Tekan pautan ini: [Halaman Daftar Akaun](${createUrl('register')})
  • Pilih status kewarganegaraan anda.
  • Isi semua maklumat yang diperlukan dan klik Teruskan.
  • Lakukan pengesahan E-mel dan Nombor Telefon.
  • Untuk pengesahan E-mel, masukkan OTP yang dihantar ke alamat e-mel anda.
  • Untuk pengesahan Nombor Telefon, masukkan OTP yang diterima melalui nombor telefon anda.
  • Selamat mencuba! 😊`,
        "lupa kata laluan": `Lupa Kata Laluan:
  • Klik Log Masuk di halaman utama, kemudian pilih pautan [Halaman Lupa Kata Laluan](${createUrl('forgot-password')}).
  • Masukkan jenis pengenalan diri, nombor pengenalan, dan alamat e-mel yang didaftarkan, kemudian klik Teruskan.
  • E-mel untuk penetapan semula kata laluan akan dihantar secara automatik ke alamat e-mel yang didaftarkan. (Pastikan alamat e-mel tersebut aktif)
  • Klik pautan pengaktifan dalam e-mel tersebut dan ikut arahan yang diberikan.
  • Selamat mencuba! 😊`,
        "log masuk": `Log Masuk:
  • Klik [Halaman Log Masuk](${createUrl('login')}) di halaman utama.
  • Masukkan nombor pengenalan diri dan kata laluan yang telah didaftarkan.
  • Seterusnya, klik butang Log Masuk.
  • Selamat mencuba! 😊`,
        "tetapkan semula kata laluan": `Tetapkan Semula Kata Laluan:
  • Tekan pautan ini: [Halaman Profil](${createUrl('profile')})
  • Pilih Tukar Kata Laluan.
  • Isikan Kata Laluan Lama dan Kata Laluan Baru.
  • Klik Kemaskini untuk menyimpan perubahan.
  • Selamat mencuba! 😊`,
        "kemaskini profil": `Kemaskini Profil:
  • Tekan pautan ini: [Halaman Profil](${createUrl('profile')})
  • Pilih Gelaran.
  • Masukkan Nombor Telefon Bimbit atau E-mel yang baru.
  • Klik Kemaskini untuk menyimpan perubahan.
  • Selamat mencuba! 😊`,
        "daftar pertubuhan": `Daftar Pertubuhan:
  • Tekan pautan ini: [Halaman Daftar Pertubuhan](${createUrl('pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am')})
  • Isi maklumat yang diperlukan seperti Nama Pertubuhan, Alamat Tempat Urusan, Alamat Surat Menyurat, dan Maklumat Perhubungan.
  • Pastikan semua langkah pendaftaran diselesaikan, termasuk bahagian Maklumat Am, Mesyuarat Penubuhan, Perlembagaan, Senarai AJK, Dokumen Sokongan, dan Pembayaran.
  • Selamat mencuba! 😊`,
        "daftar ahli": `Daftar Ahli:
  • Akses Dashboard dan klik pada butang 'Daftar Ahli'.
  • Popup Daftar Ahli akan dipaparkan.
  • Buat carian pertubuhan dengan memasukkan Nama Pertubuhan atau No Pertubuhan.
  • Jika carian berjaya, sistem akan memaparkan butiran pertubuhan yang dicari.
  • Pengguna dibenarkan untuk sertai pertubuhan dengan menekan butang 'Sertai'.
  • Pengguna dibenarkan untuk batal sertai pertubuhan dengan menekan butang 'Batal'.
  • Selamat mencuba! 😊`,
        "maklum balas": `Maklum Balas:
  • Tekan pautan ini: [Halaman Maklum Balas](${createUrl('feedback/cadanganBaru')})
  • Isikan cadangan atau maklum balas anda berkaitan sistem atau perkhidmatan.
  • Muat naik dokumen sokongan, tangkapan skrin atau fail berkaitan.
  • Isikan maklumat peribadi anda bagi tujuan rujukan atau maklum balas lanjut.
  • Selamat mencuba! 😊`,
        "daftar pertubuhan ": `Daftar Pertubuhan:
  • Log Masuk dan Akses halaman utama.
  • Tekan pautan 'Daftar Pertubuhan'.
  • Isi maklumat yang diperlukan seperti Nama Pertubuhan, Alamat Tempat Urusan, Alamat Surat Menyurat, dan Maklumat Perhubungan.
  • Pastikan semua langkah pendaftaran diselesaikan, termasuk bahagian Maklumat Am, Mesyuarat Penubuhan, Perlembagaan, Senarai AJK, Dokumen Sokongan, dan Pembayaran.
  • Selamat mencuba! 😊`,
      }
    : {
        "register account": `Register Account:
  • Click this link: [Register Page](${createUrl('register')})
  • Choose your citizenship status.
  • Fill in all the required details and click Continue.
  • Verify your Email and Phone Number.
  • For Email verification, enter the OTP sent to your email address.
  • For Phone Number verification, enter the OTP received on your phone.
  • Good luck! 😊`,
        "forgot password": `Forgot Password:
  • Click Login on the homepage, then select the [Forgot Password Page](${createUrl('forgot-password')}) link.
  • Enter your identification type, identification number, and the registered email address, then click Continue.
  • A password reset email will be automatically sent to the entered email address. (Make sure the email is active)
  • Click the activation link in that email and follow the instructions.
  • Good luck! 😊`,
        "login": `Login:
  • Click [Login Page](${createUrl('login')}) on the homepage.
  • Enter your identification number and the registered password.
  • Then click the Login button.
  • Good luck! 😊`,
        "reset password": `Reset Password:
  • Click this link: [Profile Page](${createUrl('profile')})
  • Select Change Password.
  • Enter your Old Password and New Password.
  • Click Update to save the changes.
  • Good luck! 😊`,
        "update profile": `Update Profile:
  • Click this link: [Profile Page](${createUrl('profile')})
  • Select a title.
  • Enter a new mobile phone number or email address.
  • Click Update to save the changes.
  • Good luck! 😊`,
        "register society": `Register Society:
  • Click this link: [Register Society](${createUrl('pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am')})
  • Fill in the required information such as Name of Organization, Business Address, Mailing Address, and Contact Information.
  • Ensure all registration steps are completed, including General Information, Founding Meeting, Constitution, Committee List, Supporting Documents, and Payment.
  • Good luck! 😊`,
        "join society": `Join Society:
  • Organization users access the Dashboard and click the 'Register Member' button.
  • The Register Member popup will be displayed.
  • Organization users are allowed to search for an organization by entering the Organization Name or Registration Number.
  • If the search is successful, the system will display the details of the searched organization.
  • Organization users can choose to join the organization by clicking the 'Join' button.
  • Organization users can cancel the join request by clicking the 'Cancel' button.
  • Good luck! 😊`,
        "feedback": `Feedback:
  • Click this link: [Feedback Page](${createUrl('feedback/cadanganBaru')})
  • Enter your suggestions or feedback regarding the system or services.
  • Upload supporting documents, screenshots, or related files.
  • Provide your personal information for reference or further response.
  • Good luck! 😊`,
        "register account ": `Register an Organisation: 
  • Log in and access the main page. 
  • Click on the 'Register Organisation' link. 
  • Fill in the required information such as Organisation Name, Business Address, Mailing Address, and Contact Information. 
  • Ensure all registration steps are completed, including the General Information, Founding Meeting, Constitution, Committee List, Supporting Documents, and Payment sections. 
  • Good luck! 😊`,
      };
}

/**
 * Gets option groups based on user status and locale
 * @param locale The current locale
 * @param isLoggedIn Whether the user is logged in
 * @returns Array of option groups
 */
export function getOptionGroups(locale: string, isLoggedIn: boolean): OptionGroup[] {
  const parentGroups: OptionGroup[] = [
    {
      key: "account",
      label: locale === "ms" ? "Akaun Pengguna" : "User Account",
      children:
        locale === "ms"
          ? [
              "daftar akaun",
              "lupa kata laluan",
              "log masuk",
              "tetapkan semula kata laluan",
              "kemaskini profil",
            ]
          : ["register account", "forgot password", "login", "reset password", "update profile"],
    },
    {
      key: "pertubuhan",
      label: locale === "ms" ? "Pengurusan Pertubuhan" : "Society Management",
      children:
        locale === "ms"
          ? [
              "daftar pertubuhan",
              "daftar ahli",
              "bilangan pertubuhan yang disertai",
              "maklum balas",
            ]
          : [
              "register society",
              "join society",
              "numbers of societies joined",
              "feedback"
            ],
    },
  ];

  const guestGroup: OptionGroup[] = [{
    key: "account",
    label: locale === "ms" ? "Akaun Pengguna" : "User Account",
    children:
      locale === "ms"
        ? ["daftar akaun", "lupa kata laluan", "log masuk"]
        : ["register account", "forgot password", "login"],
  },{
      key: "pertubuhan",
      label: locale === "ms" ? "Pengurusan Pertubuhan" : "Society Management",
      children:
        locale === "ms"
          ? [
              "daftar pertubuhan ",
            ]
          : [
              "register society ",
            ],
    }];

  return isLoggedIn ? parentGroups : guestGroup;
}
