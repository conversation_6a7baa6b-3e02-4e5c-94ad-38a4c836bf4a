import React, { ReactNode } from 'react';
import { Paper, PaperProps } from '@mui/material';

interface TakwimPaperProps extends PaperProps {
  children: ReactNode;
}

export const TakwimPaper: React.FC<TakwimPaperProps> = ({ children, ...props }) => {
  return (
    <Paper
      elevation={1}
      sx={{
        border: "1px solid #dadada99",
        borderRadius: "1.25rem",
        padding: "1.5rem",
        boxShadow: "none",
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Paper>
  );
};

export default TakwimPaper;
