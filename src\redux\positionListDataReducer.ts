import { createSlice } from '@reduxjs/toolkit';

interface PositionListStore {
  data: [];
  searchParams: { [key: string]: any };
  loading: boolean;
  error: string | null;
}

const initialState: PositionListStore = {
  data: [],
  searchParams: {},
  loading: false,
  error: null,
};

export const positionListDataSlice = createSlice({
  name: 'positionListData',
  initialState,
  reducers: {
    setPositionListDataRedux(state, action) {
      state.data = action.payload;
    },
    setPositionListLoading(state, action) {
      state.loading = action.payload;
    },
    setPositionListError(state, action) {
      state.error = action.payload;
    },
  },
});

export const { setPositionListDataRedux, setPositionListLoading, setPositionListError } = positionListDataSlice.actions;
export default positionListDataSlice.reducer;
