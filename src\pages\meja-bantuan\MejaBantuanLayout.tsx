import React, { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Button, Grid, IconButton, Typography } from "@mui/material";
import Sidebar from "../../components/layout/Sidebar";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { getNavItem, MENU_INTERNAL, NavItem } from "@/helpers/menuConfig";

type LayoutProps = {
  children: React.ReactNode;
};

export const MejaBantuanLayout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const { id } = useParams();
  const [activeSection, setActiveSection] = useState("maklumat");
  const [breadcrumbText, setBreadcrumbText] = useState("");
  const [urlMenu, setUrlMenu] = useState<string>();

  const [TabsOption, setTabsOption] = useState<NavItem[]>([]);
  const [selectedTab, setSelectedTab] = useState(0);

  const hasKaunterPermission = AuthHelper.hasAuthority([
    `${PermissionNames.MEJA_BANTUAN1.label}:${pageAccessEnum.Read}`,
    `${PermissionNames.MEJA_BANTUAN2.label}:${pageAccessEnum.Read}`,
    `${PermissionNames.MEJA_BANTUAN3.label}:${pageAccessEnum.Read}`,
  ]);

  useEffect(() => {
    if (!hasKaunterPermission) {
      navigate("/meja-bantuan/aduan-makluman");
    }
  }, [hasKaunterPermission, navigate]);

  // menu permission
  const menus = getNavItem(MENU_INTERNAL(), "meja-bantuan");
  const [allowedMenuItems, setAllowedMenuItems] = useState<NavItem[]>(menus);

  useEffect(() => {
    const finalMenuItems = allowedMenuItems.filter((item) => {
      if (item.permissions.length == 0) {
        // If the permissions (length = 0), it means no permission is required.
        return true;
      } else {
        return AuthHelper.hasAuthority(item.permissions);
      }
    });

    if (JSON.stringify(finalMenuItems) !== JSON.stringify(allowedMenuItems)) {
      setAllowedMenuItems(finalMenuItems);
    }
  }, [allowedMenuItems]);
  // menu permission

  useEffect(() => {
    const activeSection = allowedMenuItems.findIndex((section) =>
      location.pathname.includes(section.path)
    );
    const result = allowedMenuItems[activeSection];

    if (activeSection !== -1) {
      setUrlMenu(result.path);
      setSelectedTab(0);

      setTabsOption(result.subItems || []);
      if (result.subItems) navigate(result.subItems[0].path);
    }
  }, []);

  const handleSectionClick = (sectionIndex: number, sectionId: string) => {
    const section = allowedMenuItems.find((s) => s.id === sectionId);
    if (section) {
      if (section?.subItems?.length) {
        setSelectedTab(0);
        setTabsOption(section.subItems);
        setUrlMenu(section.path);
        navigate(section.subItems[0].path);
      } else {
        navigate(section.path);
        setUrlMenu(section?.path);
      }
      setActiveSection(section.id);
      setBreadcrumbText(t(section.label));
    }
  };

  useEffect(() => {
    if (location.pathname?.includes("aduan-cadangan")) {
      setSelectedTab(0);
    } else if (location.pathname?.includes("kepuasan-pelanggan")) {
      setSelectedTab(1);
    } else if (location.pathname?.includes("faq-makluman")) {
      setSelectedTab(2);
    }
  }, [location]);

  const breadcrumbs = [
    { label: t("mejaBantuan"), path: "/meja-bantuan" },
    { label: breadcrumbText, path: `/meja-bantuan/${activeSection}` },
  ];

  if (location.pathname.includes("pembayaran-kaunter")) {
    breadcrumbs.push({
      label: t("pembayaranKaunter"),
      path: `meja-bantuan/perkhidmatan/pembayaran-kaunter`,
    });

    if (location.pathname.includes("kemaskini")) {
      breadcrumbs.push({
        label: t("update"),
        path: `meja-bantuan/perkhidmatan/pembayaran-kaunter/kemaskini`,
      });
    }
  } else if (location.pathname.includes("rekod-pembayaran")) {
    breadcrumbs.push({
      label: t("rekodPembayaran"),
      path: `meja-bantuan/perkhidmatan/rekod-pembayaran`,
    });
  } else if (location.pathname.includes("semakan-kaunter-individu")) {
    breadcrumbs.push({
      label: t("semakanKaunterIndividu"),
      path: `meja-bantuan/perkhidmatan/semakan-kaunter-individu`,
    });
  } else if (location.pathname.includes("aduan-cadangan")) {
    breadcrumbs.push({
      label: t("aduanCadangan"),
      path: `/meja-bantuan/aduan-makluman/aduan-cadangan`,
    });

    if (location.pathname.includes("kemaskini")) {
      breadcrumbs.push({
        label: t("update"),
        path: `meja-bantuan/aduan-makluman/aduan-cadangan/kemaskini/:id`,
      });
    }
  } else if (location.pathname.includes("kepuasan-pelanggan")) {
    breadcrumbs.push({
      label: t("kepuasanPelanggan"),
      path: `/meja-bantuan/aduan-makluman/kepuasan-pelanggan`,
    });
  } else if (location.pathname.includes("faq-makluman")) {
    breadcrumbs.push({
      label: t("faqMakluman"),
      path: `/meja-bantuan/aduan-makluman/faq-makluman`,
    });

    if (location.pathname.includes("kemaskini")) {
      breadcrumbs.push({
        label: t("update"),
        path: `meja-bantuan/aduan-makluman/faq-makluman/kemaskini`,
      });
    }
  }

  return (
    <>
      <Box
        sx={{
          mb: "31px",
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <IconButton onClick={() => navigate("/internal-user")}>
          <ChevronLeftIcon />
        </IconButton>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {breadcrumbs.map((item, index) =>
            item.label && !item.label.includes("Aduan/Makluman") ? (
              <React.Fragment key={item.path}>
                <Typography
                  fontSize={18}
                  fontWeight={500}
                  sx={{
                    color: "#666666",
                    cursor: "pointer",
                    "&:hover": {
                      textDecoration: "underline",
                    },
                  }}
                  onClick={() => navigate(item.path)}
                >
                  {item.label}
                </Typography>

                {index < breadcrumbs.length - 1 && <ChevronLeftIcon />}
              </React.Fragment>
            ) : null
          )}
        </Box>
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={2}>
          <Sidebar
            sx={{
              // minWidth: "190px",
              padding: "37px 26px",
              height: "fit-content",
              display: "flex",
              flexDirection: "column",
              gap: "10px",
              textAlign: "center",
            }}
          >
            {allowedMenuItems.map((section: any, idx: number) => {
              const { label, path, children } = section;

              const isActive =
                location.pathname.startsWith(path) ||
                (children &&
                  children.some((child: any) =>
                    location.pathname.startsWith(child.path)
                  ));

              return (
                <Button
                  key={label + idx}
                  sx={{
                    color: isActive ? "#fff" : "#666666",
                    background: isActive
                      ? "var(--primary-color)"
                      : "transparent",
                    "&:hover": {
                      background: "var(--primary-color)",
                    },
                    borderRadius: "20px",
                    textTransform: "capitalize",
                    lineHeight: "20px",
                    py: 1.5,
                  }}
                  onClick={() => handleSectionClick(idx, section.id)}
                >
                  {label}
                </Button>
              );
            })}
          </Sidebar>
        </Grid>
        <Grid item xs={10}>
          <Box
            sx={{
              display: "flex",
              background: "#fff",
              borderRadius: "10px",
              p: 1,
              boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
              overflowX: "auto",
            }}
          >
            {TabsOption.map((tab, index) => (
              <Button
                key={index}
                onClick={() => {
                  setSelectedTab(index);
                  navigate(tab.path);
                }}
                sx={{
                  textTransform: "none",
                  fontWeight: selectedTab === index ? 600 : 400,
                  color: selectedTab === index ? "#fff" : "#333",
                  background:
                    selectedTab === index
                      ? "var(--primary-color)"
                      : "transparent",
                  borderRadius: "5px",
                  px: 3,
                  py: 1,
                  width: "100%",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    background:
                      selectedTab === index
                        ? "var(--primary-color)"
                        : "#F1F4FA",
                  },
                }}
              >
                {t(tab.label)}
              </Button>
            ))}
          </Box>
          {hasKaunterPermission && (
            <Box sx={{ flexGrow: 1, pt: 1 }}>{children}</Box>
          )}
        </Grid>
      </Grid>
    </>
  );
};

export default MejaBantuanLayout;
