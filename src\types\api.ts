export interface IApiBaseResponse {
  status: string;
  code: number;
  msg: string;
  timeStamp: string;
}

export interface IPaginatedData<T> {
  total: number;
  data: T;
  pageNo: number;
  pageSize: number;
  empty: boolean;
}

export interface IApiPaginatedResponse<T> extends IApiBaseResponse {
  data?: IPaginatedData<T[]> | null | undefined;
}

export interface IApiResponse<T> extends IApiBaseResponse {
  data?: T | null | undefined;
}
