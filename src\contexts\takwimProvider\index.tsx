import React, { createContext, useContext, useState, ReactNode } from "react";
import dayjs, { Dayjs } from "dayjs";
import { EventFormData, IEvent } from "@/types/event";
import { IEventAttendee } from "@/types/eventAttendee";

interface TakwimContextType {
  selectedMonthYear: dayjs.Dayjs;
  setSelectedMonthYear: (date: dayjs.Dayjs) => void;
  isJoined: boolean;
  setIsJoined: (value: boolean) => void;
  upcomingEvents: IEvent[];
  setUpcomingEvents: (events: IEvent[]) => void;
  isEventAdmin: boolean;
  setIsEventAdmin: (value: boolean) => void;
  eventFormData: EventFormData;
  setEventFormData: (
    data: EventFormData | ((prev: EventFormData) => EventFormData)
  ) => void;
  eventFeedBack: number[];
  setEventFeedBack: (data: number[]) => void;
  userAttendance: IEventAttendee | null;
  setUserAttendance: (data: IEventAttendee | null) => void;
  isEventEnded: boolean;
  setIsEventEnded: (value: boolean) => void;
  selectedFilterDates: Dayjs[];
  setSelectedFilterDates: React.Dispatch<React.SetStateAction<Dayjs[]>>;
}

const TakwimContext = createContext<TakwimContextType | undefined>(undefined);

export const TakwimProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // Initialize with current month and year only, setting day to 1
  const [selectedMonthYear, setSelectedMonthYear] = useState(
    dayjs().startOf("month")
  );
  const [isJoined, setIsJoined] = useState(false);
  const [upcomingEvents, setUpcomingEvents] = useState<IEvent[]>([]);
  const [isEventAdmin, setIsEventAdmin] = useState(false);
  const [eventFeedBack, setEventFeedBack] = useState<number[]>([]);
  const [userAttendance, setUserAttendance] = useState<IEventAttendee | null>(
    null
  );
  const [isEventEnded, setIsEventEnded] = useState(false);
  const [selectedFilterDates, setSelectedFilterDates] = useState<Dayjs[]>([]);

  const [eventFormData, setEventFormData] = useState<EventFormData>({
    posterPreview: null,
    dateError: null,
    eventNo: "",
    eventName: "",
    eventAdminId: 0,
    description: "",
    collaboratorName: "",
    startTime: "",
    endTime: "",
    eventStartDate: null,
    eventEndDate: null,
    regStartDate: "",
    regEndDate: "",
    address1: "",
    address2: null,
    state: [],
    postcode: "",
    venue: "",
    mapUrl: "",
    maxParticipants: null,
    hasMax: false,
    organiserId: null,
    status: null,
    visibility: "PUBLIC", // Always set a default value
    published: false,
    feedbackName: "",
    // privateEventSocieties: null,
    societiesId: null,
    position: [],
    city: [],
    district: [],
    organisationLevel: "",
    bannerUrl: "",
    organisationCategory: null,
    picContactNo: null,
    stateAddress: null,
    districtAddress: null,
    cityAddress: "",
    postcodeAddress: "",
  });

  const handleSetSelectedMonth = (date: dayjs.Dayjs) => {
    // Ensure we only store month and year by setting to start of month
    setSelectedMonthYear(date.startOf("month"));
  };

  return (
    <TakwimContext.Provider
      value={{
        selectedMonthYear,
        setSelectedMonthYear: handleSetSelectedMonth,
        isJoined,
        setIsJoined,
        upcomingEvents,
        setUpcomingEvents,
        isEventAdmin,
        setIsEventAdmin,
        eventFormData,
        setEventFormData,
        eventFeedBack,
        setEventFeedBack,
        userAttendance,
        setUserAttendance,
        isEventEnded,
        setIsEventEnded,
        selectedFilterDates,
        setSelectedFilterDates,
      }}
    >
      {children}
    </TakwimContext.Provider>
  );
};

// Custom hook for using the Takwim context
export const useTakwim = () => {
  const context = useContext(TakwimContext);
  if (context === undefined) {
    throw new Error("useTakwim must be used within a TakwimProvider");
  }
  return context;
};

export default TakwimProvider;

