import React, { useEffect } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

type ReadStatusType = {
  [key: number]: Boolean;
};

interface AccordionProps {
  subTitle: string;
  currentIndex: number;
  currentExpand: Number | false;
  readStatus: ReadStatusType;
  children: React.ReactNode;
  onChangeFunc: (
    item: number
  ) => (event: React.SyntheticEvent, isExpanded: boolean) => void;
}

const KelulusanAccordion: React.FC<AccordionProps> = ({
  subTitle,
  currentIndex,
  currentExpand,
  readStatus,
  children,
  onChangeFunc,
}) => {
  const isExpanded = currentExpand === currentIndex;
  const isRead = !!readStatus[currentIndex];

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "flex-start",
        gap: 2,
      }}
    >
      <Box
        sx={{
          width: "100%",
          mt: 2,
          backgroundColor: "#FFFFFF",
          borderRadius: "10px",
        }}
      >
        <Box
          sx={{
            borderRadius: "10px",
          }}
        >
          <Accordion
            // defaultExpanded
            expanded={isExpanded}
            onChange={onChangeFunc(currentIndex)}
          >
            <AccordionSummary
              // expandIcon={
              //   <ExpandMoreIcon />
              // }
              sx={{ position: "relative", borderRadius: 1, px: 1 }}
            >
              <Box
                sx={{
                  position: "relative",
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                  width: "100%",
                }}
              >
                <Typography
                  sx={{
                    fontWeight: "500!important",
                    color: "var(--primary-color)",
                  }}
                >
                  {subTitle}
                </Typography>
                <Box
                  sx={{
                    position: "absolute",
                    right: 15,
                    top: "50%",
                    transform: `translateY(-50%) rotate(${
                      isExpanded ? 180 : 0
                    }deg)`,
                    transition: "transform 0.3s ease",
                  }}
                >
                  <ExpandMoreIcon />
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails sx={{ padding: 2, pt: 0 }}>
              {isExpanded && children}
            </AccordionDetails>
          </Accordion>
        </Box>
        {/* ========== */}
      </Box>
      <Box
        sx={{
          background: "#fff",
          height: "100px",
          width: "100px",
          p: "20px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          borderRadius: "10px",
          mt: "20px",
        }}
      >
        <Box
          sx={{
            width: "30px",
            height: "30px",
            border: isRead ? "1px solid #c4c4c4" : "1px solid #c4c4c4",
            backgroundColor: isRead ? "var(--primary-color)" : "transparent",
            borderRadius: "4px",
            flexShrink: 0,
          }}
        />
      </Box>
    </Box>
  );
};

export default KelulusanAccordion;
