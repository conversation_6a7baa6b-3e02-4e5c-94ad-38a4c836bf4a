import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import {
  globalStyles,
  useQuery,
  getMainCategories,
  getStateNameById,
  OrganisationPositionLabel,
  getSectionByCode,
  SocietyCancellationRevertReason,
  useUploadPresignedUrl,
  useMutation,
  DocumentUploadType,
  getSubCategories,
} from "@/helpers";
import dayjs from "dayjs";

import {
  DisabledTextField,
  FormFieldRow,
  Label,
  TextFieldController,
  ButtonPrimary,
  DataTable,
  IColumn,
  CustomSkeleton,
  SelectFieldController,
  FileUploadController,
  DialogConfirmation,
} from "@/components";
import { Box, Typography, IconButton } from "@mui/material";

import { EditIcon } from "@/components/icons";

import {
  IApiResponse,
  IApiPaginatedResponse,
  ICommittee,
  ISocietyDetail,
  ICancelledSociety,
} from "@/types";

const historyDummy = [
  {
    date: "01/02/2024",
    notisType: "13(1)",
    description: "Notis Pembatalan",
  },
];

const SenaraiPembatalanForm: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();
  const classes = globalStyles();
  const isMyLanguage = i18n.language === "my";

  const societyCancellationRevertReason = useMemo(
    () =>
      SocietyCancellationRevertReason.filter(
        (reason) => reason.value !== "APPEAL"
      ),
    []
  );

  const [dialogOpen, setDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const historyColumn: IColumn[] = [
    { field: "date", headerName: t("date"), flex: 1, align: "center" },
    {
      field: "notisType",
      headerName: t("typeOfNotice"),
      flex: 1,
      align: "center",
    },
    {
      field: "description",
      headerName: t("descriptionNotice"),
      flex: 1,
      align: "center",
    },
    {
      field: "status",
      headerName: t("action"),
      align: "center",
      renderCell: () => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton>
              <EditIcon color="#1DC1C1" />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const committeeColumn: IColumn<ICommittee>[] = [
    {
      field: "titleCode",
      headerName: t("position"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
      renderCell: ({ row }) => {
        return t(OrganisationPositionLabel[Number(row?.designationCode)]);
      },
    },
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
    },
    {
      field: "",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
      renderCell: ({ row }) => {
        return getStateNameById(row?.residentialStateCode ?? "");
      },
    },
  ];

  const { upload: uploadFile, isLoading: isUploading } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      const documentId = data?.data?.data?.id ?? null;

      if (documentId) createSocietyCancellationRevertAction(documentId);
    },
  });

  const {
    data: cancellationDetailRes,
    isLoading: isLoadingCancellationDetail,
  } = useQuery<IApiResponse<ICancelledSociety>>({
    url: `society/cancellation/${id}`,
    enabled: !!id,
  });

  const {
    data: ajkListResponse,
    refetch: fetchAjk,
    isLoading: isLoadingAjkList,
  } = useQuery<IApiPaginatedResponse<ICommittee>>({
    url: "society/committee/listAjk",
    autoFetch: false,
  });

  const {
    fetch: createSocietyCancellationRevert,
    isLoading: isCreatingSocietyCancellationRevert,
  } = useMutation<IApiResponse<number>>({
    url: "society/cancellation/revert/create",
    onSuccess: (res) => {
      const responseCode = res?.data?.code;

      if (responseCode === 200) {
        setIsSuccess(true);
        setTimeout(
          () =>
            navigate("..", {
              state: {
                tab: "cipta-pembatalan",
              },
            }),
          2000
        );
      }
    },
  });

  const ajkList = ajkListResponse?.data?.data?.data ?? [];
  const cancellationDetail = cancellationDetailRes?.data?.data;

  const {
    data: societyDetailResponse,
    refetch: fetchSociety,
    isLoading: isLoadingSocietyDetail,
  } = useQuery<IApiResponse<ISocietyDetail>>({
    url: `society/${cancellationDetail?.societyId}`,
    autoFetch: false,
  });

  const societyDetail = societyDetailResponse?.data?.data;
  const mainCategory = getMainCategories(
    Number(societyDetail?.categoryCodeJppm)
  );
  const subCategory = getSubCategories(Number(societyDetail?.subCategoryCode));

  const { control, handleSubmit, setValue, getValues } = useForm<FieldValues>({
    defaultValues: {
      societyId: "",
      societyNo: "",
      societyCancellationId: "",
      revertReason: "",
      revertDate: dayjs().format("YYYY-MM-DD"),
      note: "",
      documentId: "",
    },
  });

  const onSubmit = () => setDialogOpen(true);

  const uploadDocument = () => {
    const formValues = getValues();

    if (!selectedFile) return;

    uploadFile({
      params: {
        type: DocumentUploadType.SOCIETY_CANCELLATION_REVERT,
        societyId: Number(id),
        societyNo: formValues.societyNo,
      },
      file: selectedFile,
    });
  };

  const createSocietyCancellationRevertAction = (documentId: number) => {
    const formValues = getValues();
    const payload = {
      ...formValues,
      documentId,
    };

    createSocietyCancellationRevert(payload);
  };

  useEffect(() => {
    if (cancellationDetail) {
      setValue("societyId", cancellationDetail?.societyId ?? "");
      setValue("societyNo", cancellationDetail?.societyNo ?? "");
      setValue("societyCancellationId", cancellationDetail?.id ?? "");

      fetchSociety();
      fetchAjk({
        filters: [
          {
            field: "societyId",
            operator: "eq",
            value: cancellationDetail?.societyId ?? 0,
          },
        ],
      });
    }
  }, [cancellationDetail]);

  if (isLoadingSocietyDetail || isLoadingAjkList || isLoadingCancellationDetail)
    return <CustomSkeleton height={50} number={4} />;

  return (
    <>
      <Box className={classes.section} mb={1}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            {isMyLanguage ? "Info Pertubuhan" : "Organization Info"}
          </Typography>

          <FormFieldRow
            label={<Label text={t("ppmNumber")} />}
            value={
              <DisabledTextField
                value={
                  societyDetail?.societyNo ??
                  cancellationDetail?.societyNo ??
                  "-"
                }
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationName")} />}
            value={
              <DisabledTextField
                value={
                  societyDetail?.societyName ??
                  cancellationDetail?.societyName ??
                  "-"
                }
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationCategory")} />}
            value={
              <DisabledTextField value={mainCategory?.categoryNameBm ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationSubCategory2")} />}
            value={
              <DisabledTextField value={subCategory?.categoryNameBm ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("tarikhDaftar")} />}
            value={
              <DisabledTextField value={societyDetail?.registeredDate ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("branchedStatus")} />}
            value={
              <DisabledTextField
                value={
                  societyDetail?.hasBranch ? "BERCAWANGAN" : "TIDAK BERCAWANGN"
                }
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("alamatPertubuhan")} />}
            value={<DisabledTextField value={societyDetail?.address ?? "-"} />}
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text={t("organizationCommittee")} />}
            value={
              <Box
                sx={{
                  background: "#E8E9E8",
                  borderRadius: "5px",
                  overflow: "hidden",
                }}
              >
                <DataTable
                  columns={committeeColumn}
                  rows={ajkList}
                  page={1}
                  rowsPerPage={10}
                  totalCount={10}
                  pagination={false}
                  isLoading={isLoadingAjkList}
                />
              </Box>
            }
          />
        </Box>
      </Box>

      {/* SEJARAH NOTICE HIDE */}
      {/* <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            {isMyLanguage ? "Sejarah Notis" : "Notice History"}
          </Typography>

          <DataTable
            columns={historyColumn}
            rows={historyDummy}
            page={1}
            rowsPerPage={10}
            totalCount={10}
          />
        </Box>
      </Box> */}

      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography className="title" mb={1}>
            {isMyLanguage
              ? " Keterangan Pembatalan"
              : "Cancellation Information"}
          </Typography>

          <FormFieldRow
            label={<Label text={t("cancellationDate")} />}
            value={
              <DisabledTextField
                value={cancellationDetail?.cancelledDate ?? "-"}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("section")} />}
            value={
              <DisabledTextField
                value={
                  getSectionByCode(cancellationDetail?.section ?? "")
                    ?.description ?? "-"
                }
              />
            }
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text={t("ulasan")} />}
            value={
              <DisabledTextField
                value={cancellationDetail?.reason ?? "-"}
                multiline
                row={3}
              />
            }
          />
        </Box>

        <ButtonPrimary
          type="submit"
          sx={{ marginLeft: "auto" }}
          className={classes.btnSubmit}
        >
          {t("next")}
        </ButtonPrimary>
      </Box>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Box className={classes.section} mb={2}>
          <Box className={classes.sectionBox} mb={2}>
            <Typography className="title" mb={1}>
              {isMyLanguage
                ? "Pengaktifan semula pertubuhan"
                : "Reactivation of the organization"}
            </Typography>

            <FormFieldRow
              label={
                <Label text={isMyLanguage ? "Penarikan Semula" : "Recall"} />
              }
              value={
                <SelectFieldController
                  control={control}
                  name="revertReason"
                  options={societyCancellationRevertReason}
                  requiredCondition={() => true}
                />
              }
            />

            <FormFieldRow
              align="flex-start"
              label={<Label text={t("note")} />}
              value={
                <TextFieldController
                  control={control}
                  name="note"
                  multiline
                  rows={3}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("uploadDocument")} />}
              value={
                <FileUploadController
                  control={control}
                  name="documentId"
                  onFileSelect={(file) => setSelectedFile(file)}
                  accept=".doc,.docx,.txt,.pdf"
                  allowedTypes={[
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "text/plain",
                  ]}
                  required
                />
              }
            />
          </Box>

          <ButtonPrimary
            type="submit"
            sx={{ marginLeft: "auto" }}
            className={classes.btnSubmit}
          >
            {t("next")}
          </ButtonPrimary>
        </Box>
      </form>

      <DialogConfirmation
        isMutating={isCreatingSocietyCancellationRevert || isUploading}
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onAction={uploadDocument}
        onConfirmationText={
          isMyLanguage
            ? "Adakah anda pasti untuk menarik semula Pembatalan Pertubuhan ini?"
            : "Are you sure you want to revoke this Organization Cancellation?"
        }
        isSuccess={isSuccess}
        onSuccessText={
          isMyLanguage
            ? "Pertubuhan ini telah berjaya dikuatkusa kembalikan !"
            : "This organization has been successfully restored!"
        }
      />
    </>
  );
};

export default SenaraiPembatalanForm;
