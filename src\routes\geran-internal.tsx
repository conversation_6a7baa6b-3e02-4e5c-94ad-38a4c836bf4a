/* eslint-disable react-refresh/only-export-components */
import { Navigate, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import { useSelector } from "react-redux";
import { getUserPortal } from "@/redux/userReducer";
import { PORTAL_INTERNAL } from "@/helpers";

import { PageLoader } from "@/components";
const GeranInternalLayout = lazy(() => import("@/pages/geran-internal/Layout"));
const CarianPermohonan = lazy(
  () => import("@/pages/geran-internal/carian-permohonan")
);
const LaporanPelaksanaan = lazy(
  () => import("@/pages/geran-internal/laporan-pelaksanaan")
);
const LaporanPermohonan = lazy(
  () => import("@/pages/geran-internal/laporan-permohonan")
);

const routeComponents = (
  <Route
    path="geran-internal"
    element={
      <Suspense fallback={<PageLoader />}>
        <GeranInternalLayout />
      </Suspense>
    }
  >
    <Route index element={<Navigate to="carian-permohonan" />} />
    <Route path="carian-permohonan" element={<CarianPermohonan />} />
    <Route path="laporan-pelaksanaan" element={<LaporanPelaksanaan />} />
    <Route path="laporan-permohonan" element={<LaporanPermohonan />} />
  </Route>
);

export const geranInternal = {
  routes:
    localStorage.getItem("portal") === PORTAL_INTERNAL &&
    import.meta.env.VITE_APP_ENV !== "production" ? (
      <></>
    ) : null,
};

export const useGeranInternalRoutes = () => {
  const userPortal = useSelector(getUserPortal);
  const routes =
    userPortal === parseInt(PORTAL_INTERNAL) &&
    import.meta.env.VITE_APP_ENV !== "production" &&
    routeComponents;

  return {
    routes,
  };
};
