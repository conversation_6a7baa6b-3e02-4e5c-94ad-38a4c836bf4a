import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Typography,
  useTheme,
  Button,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useSpeech } from "@/contexts/chatbot/SpeechContext";
import { useDispatch } from "react-redux";
import { setChatbotOpenedRedux } from "@/redux/chatbotReducer";
import { useTranslation } from "react-i18next";
import { SpeechProvider } from "@/contexts/chatbot/SpeechContext";
import { sendSpeechInputToChatbot } from "@/components/chatbot/SimpleCrossPageComm";
import { setupSpeechRecognition } from '@/components/chatbot/SpeechUtils';

/**
 * Speech Input Page
 * A full-screen page with a tap & hold to speak button
 * Simplified: Only captures speech and sends to chatbot page
 */
const SpeechInputPage: React.FC = () => {
  const dispatch = useDispatch();

  // Ensure the chatbot is closed when this page is visited
  useEffect(() => {
    dispatch(setChatbotOpenedRedux(false));
  }, [dispatch]);

  return (
    <SpeechProvider>
      <SpeechInputContent />
    </SpeechProvider>
  );
};

/**
 * Normalize text to handle variations of "Rosie"
 * @param text The text to normalize
 * @returns Normalized text
 */
const normalizeRosieName = (text: string): string => {
  const rosiePattern = /\b(?:k[aeiou]?|)?r[ou]s{1,2}[iey]+\b/gi; // Matches Rosie, Rusi, Kerusi, Rossi, etc.
  return text.replace(rosiePattern, 'Rosie');
};

/**
 * Speech Input Content
 * This component contains the actual speech input functionality
 * It's wrapped in the necessary providers in the parent component
 */
const SpeechInputContent: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { i18n } = useTranslation();
  const { speechRecognition, startListening, stopListening } = useSpeech();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const locale = ["bm", "my"].includes(i18n.language) ? "ms" : i18n.language;

  const [isListening, setIsListening] = useState(false);
  const [recognizedText, setRecognizedText] = useState("");
  const [messageSent, setMessageSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [liveTranscript, setLiveTranscript] = useState("");

  // Store all final speech results
  const allResultsRef = useRef<string[]>([]);
  const recognitionRef = useRef<any>(null);

  // Start recording
  const startRecording = () => {
    setIsListening(true);
    setRecognizedText("");
    setMessageSent(false);
    setError(null);
    setLiveTranscript("");
    allResultsRef.current = [];
    recognitionRef.current = setupSpeechRecognition({
      locale: locale === 'ms' ? 'ms-MY' : 'en-US',
      onInterim: (interim) => setLiveTranscript(interim),
      onFinal: (final) => {
        allResultsRef.current.push(normalizeRosieName(final));
        setLiveTranscript("");
      },
      onError: (e) => {
        setError('Speech recognition error');
        setIsListening(false);
      },
      onEnd: () => setIsListening(false),
    });
    recognitionRef.current.start();
  };

  // Stop recording
  const stopRecording = () => {
    setIsListening(false);
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    // Wait 500ms to ensure all results are received
    setTimeout(() => {
      const fullText = allResultsRef.current.join(" ");
      setLiveTranscript("");
      if (fullText) {
        setRecognizedText(fullText);
        setError(null);
        sendSpeechInputToChatbot(fullText);
        setMessageSent(true);
        setTimeout(() => {
          setMessageSent(false);
        }, 3000);
      }
      allResultsRef.current = [];
    }, 500);
  };

  const handleMicrophoneClick = () => {
    if (isListening) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const handleGoToChatbot = () => {
    dispatch(setChatbotOpenedRedux(true));
    navigate("/chatbot");
  };

  const handleAskAgain = () => {
    setRecognizedText("");
    setMessageSent(false);
    setError(null);
    setLiveTranscript("");
    allResultsRef.current = [];
  };

  useEffect(() => {
    return () => {
      allResultsRef.current = [];
      if (recognitionRef.current) recognitionRef.current.stop();
    };
  }, []);

  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        bgcolor: "black",
        color: "white",
        position: "relative",
      }}
    >
      <Typography
        variant={isMobile ? "h3" : "h2"}
        component="h1"
        sx={{
          mb: 4,
          fontWeight: "bold",
          textAlign: "center",
          px: 2,
          fontSize: isMobile ? "1.75rem" : undefined,
        }}
      >
        {locale === "ms"
          ? isListening
            ? "Klik untuk berhenti"
            : "Klik untuk berbual"
          : isListening
          ? "Click to Stop"
          : "Click to Speak"}
      </Typography>

      {/* Microphone button */}
      <Box
        sx={{
          width: isMobile ? 100 : 120,
          height: isMobile ? 100 : 120,
          borderRadius: "50%",
          bgcolor: isListening ? "error.main" : "primary.main",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          cursor: "pointer",
          transition: "all 0.3s ease",
          "&:hover": {
            transform: "scale(1.05)",
            boxShadow: "0 0 20px rgba(255, 255, 255, 0.3)",
          },
          "&:active": {
            transform: "scale(0.95)",
          },
          position: "relative",
          "&::after": {
            content: '""',
            position: "absolute",
            width: "30%",
            height: "30%",
            borderRadius: "50%",
            bgcolor: "white",
            opacity: isListening ? 0.8 : 0.6,
            transition: "all 0.3s ease",
          },
        }}
        onClick={handleMicrophoneClick}
      />

      {/* Status text */}
      <Typography
        variant="h6"
        sx={{
          mt: 4,
          textAlign: "center",
          minHeight: 48,
          px: 2,
        }}
      >
        {isListening && liveTranscript
          ? `${locale === "ms" ? "Sedang berkata:" : "Speaking:"} "${liveTranscript}"`
          : messageSent
          ? (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <CircularProgress size={24} sx={{ mr: 2, color: "white" }} />
              {locale === "ms" ? `Memproses: "${recognizedText}"` : `Processing: "${recognizedText}"`}
            </Box>
          )
          : isListening
          ? locale === "ms" ? "Mendengar..." : "Listening..."
          : recognizedText
          ? `${locale === "ms" ? "Anda" : "You"}: "${recognizedText}"`
          : locale === "ms"
          ? 
          ""
          // "Tekan dan tahan butang untuk bercakap"
          : 
          ""
          // "Press and hold the button to speak"
          }
      </Typography>

      {/* Action buttons */}
      {(recognizedText || messageSent) && (
        <Box
          sx={{
            mt: 4,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            gap: 2,
            width: isMobile ? "100%" : "auto",
            px: isMobile ? 2 : 0,
          }}
        >
          <Button
            variant="contained"
            color="primary"
            onClick={handleAskAgain}
            fullWidth={isMobile}
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              fontWeight: "bold",
              textTransform: "none",
              fontSize: isMobile ? "0.9rem" : "1rem",
            }}
          >
            {locale === "ms" ? "Tanya Soalan Lain" : "Ask Another Question"}
          </Button>

          <Button
            variant="outlined"
            color="primary"
            onClick={handleGoToChatbot}
            fullWidth={isMobile}
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              fontWeight: "bold",
              textTransform: "none",
              fontSize: isMobile ? "0.9rem" : "1rem",
              borderColor: "white",
              color: "white",
              "&:hover": {
                borderColor: theme.palette.primary.main,
                bgcolor: "rgba(255, 255, 255, 0.1)",
              },
            }}
          >
            {locale === "ms" ? "Pergi ke Chatbot" : "Go to Chatbot"}
          </Button>
        </Box>
      )}

      {/* Error message if speech recognition fails */}
      {(error) && (
        <Typography
          color="error"
          sx={{
            mt: 2,
            textAlign: "center",
            px: 2,
          }}
        >
          {error}
        </Typography>
      )}
    </Box>
  );
};

export default SpeechInputPage;
