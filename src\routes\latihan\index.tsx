import {Route} from "react-router-dom";
import TrainingIndex from "@/pages/training";
import TrainingDetailsIndex from "@/pages/training/trainingDetails";
import CertificateDetailsIndex from "@/pages/training/certificateDetails";
import Certificate from "@/pages/training/certificateDetails/certificate";
import TrainingReview from "@/pages/training/trainingDetails/trainingReview";
import TrainingInfo from "@/pages/training/trainingDetails/trainingInfo";
import InternalTrainingIndex from "@/pages/internal-training";
import InternalTrainingCreate from "@/pages/internal-training/createTraining";

export const latihan = {
  routes: (
    <>
      <Route path="/latihan">
        <Route index element={<TrainingIndex/>}/>
        <Route path="maklumat">
          <Route index element={<TrainingDetailsIndex />} />
        </Route>
        <Route path="info">
          <Route index element={<TrainingInfo />} />
        </Route>
        <Route path="quiz">
          <Route index element={<CertificateDetailsIndex />} />
        </Route>
        <Route path="sijil">
          <Route index element={<Certificate />} />
          <Route path="detail">
            <Route index element={<TrainingReview />} />
          </Route>
        </Route>
      </Route>
      <Route path="/latihan-internal">
        <Route index element={<InternalTrainingIndex/>}/>
        <Route path="create">
          <Route index element={<InternalTrainingCreate />} />
        </Route>
        <Route path="update/:id">
          <Route index element={<InternalTrainingCreate isUpdate={true} />} />
        </Route>
      </Route>
    </>
  ),
};
