import React from 'react';
import { Box, IconButton, Typography } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronLeft } from '@mui/icons-material';

interface BackButtonProps {
  label?: string;
  to?: string;
  onClick?: () => void;
}

const BackButton: React.FC<BackButtonProps> = ({
  label = 'Back',
  to,
  onClick
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (to) {
      navigate(to);
    } else if (location.pathname === '/takwim') {
      // If we're already at /takwim, don't navigate back
      return;
    } else {
      navigate(-1);
    }
  };



  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        cursor: 'pointer',
        mb: 2,
      }}
      onClick={handleClick}
    >
      <IconButton
        size="small"
        sx={{
          color: '#666666',
          p: 0,
        }}
      >
        <ChevronLeft />
      </IconButton>
      <Typography
        sx={{
          color: '#666666',
          fontSize: '18px',
          fontWeight: 500,
        }}
      >
        {label}
      </Typography>
    </Box>
  );
};

export default BackButton;

