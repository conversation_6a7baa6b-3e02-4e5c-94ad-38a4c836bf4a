import React, { useState } from "react";
import { ButtonPrimary } from "@/components/button";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import TableSumbangan from "./TableSumbangan";
import { useLocation, useNavigate } from "react-router-dom";
import { EditIcon } from "@/components/icons";
import useQuery from "@/helpers/hooks/useQuery";
import { Sumbangan } from "../interface";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { ApplicationStatus } from "@/helpers";

export const ListSumbanganDariLuarNegara = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const [statementComplete, setStatementComplete] = useState(false);
  const [sumbanganList, setSumbanganList] = useState<Sumbangan[]>([]);

  const { refetch } = useQuery({
    url: `society/statement/statement-contribution/list`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "contributionCode", operator: "eq", value: 1 },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const list = data?.data?.data?.data || [];
      console.log(list);
      setSumbanganList(list);
    },
  });

  // Mock data for sumbanganList

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const add = () => {
    navigate("createSumbangan", {
      state: {
        societyId: societyId,
        branchId: branchDataRedux.id,
        statementId: statementId,
        year: year,
        contributionCode: "1",
      },
    });
  };

  const handleEdit = (id: number, contributionCode: string) => {
    navigate("createSumbangan", {
      state: {
        societyId: societyId,
        statementId: statementId,
        branchId: branchDataRedux.id,
        year: year,
        contributionId: id,
        contributionCode: contributionCode,
      },
    });
  };

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [contributionId, setContributionId] = useState<number | undefined>();
  const handleConfirmDelete = (id: number) => {
    setContributionId(id);
    setOpenConfirm(true);
  };

  const { mutate: deleteContribution } = useCustomMutation();

  const handleDeleteContribution = () => {
    deleteContribution(
      {
        url: `${API_URL}/society/statement/statement-contribution/${contributionId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          refetch();
          setOpenConfirm(false);
        },
      }
    );
  };

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  return (
    <Stack spacing={2}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            py: 2,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              borderRadius: "16px",
              fontSize: "14px",
              fontWeight: "500 !important",
            }}
          >
            {t("contributionFromAbroad")}
          </Typography>
        </Box>

        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            py: 2,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("listOfContributionsFromAbroad")}
          </Typography>
          <Stack
            direction="row"
            spacing={2}
            mt={2}
            sx={{ pl: 1, width: "100%" }}
            justifyContent="flex-end"
          >
            {isDisabled || statementComplete ? null : (
              <ButtonPrimary
                sx={{
                  bgcolor: "transparent",
                  color: "#666666",
                  boxShadow: "none",
                  border: "1px solid #67D1D1",
                  fontSize: "12px",
                  fontWeight: "500 !important",
                }}
                onClick={add}
              >
                {t("daftarSumbanganDariLuarNegara")}
              </ButtonPrimary>
            )}
          </Stack>
          <TableSumbangan
            isDisable={isDisabled || statementComplete}
            items={sumbanganList}
            headerCountry={t("countryOfOrigin")}
            handleEdit={handleEdit}
            handleDelete={handleConfirmDelete}
          />
        </Box>
      </Box>

      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDeleteContribution")}
        message={`${t("confirmDeleteContribution")}?`}
        onConfirm={handleDeleteContribution}
        onCancel={() => setOpenConfirm(false)}
      />
    </Stack>
  );
};

export default ListSumbanganDariLuarNegara;
