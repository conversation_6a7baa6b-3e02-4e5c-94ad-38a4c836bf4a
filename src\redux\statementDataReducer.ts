import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "./store";

// Define a type for the slice state


// Define the initial state using that type
const initialState = {
  data: {},
  isViewStatement:false,
};

export const statementDataSlice = createSlice({
  name: "statementData",
  initialState,
  reducers: {
    setStatementDataRedux: (state, action) => {
      state.data = action.payload
    },
    setIsViewStatement: (state, action) => {
      state.isViewStatement = action.payload
    },
  },
});

export const { setStatementDataRedux , setIsViewStatement } = statementDataSlice.actions;

export default statementDataSlice.reducer;
