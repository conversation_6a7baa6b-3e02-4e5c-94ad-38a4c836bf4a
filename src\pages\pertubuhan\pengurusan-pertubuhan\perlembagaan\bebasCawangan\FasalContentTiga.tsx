import {
  Box,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  FormControl,
  Select,
  FormHelperText,
  MenuItem,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "@/helpers/utils";
import { FasalBebasProps } from "../Fasal";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";
import { RegExNumbers } from "@/helpers";
const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export const FasalContentTigaBebasCawangan: React.FC<FasalBebasProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const requiredText = ["jenis mesyuarat agung"];

  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);

  const [namaPertubuhan, setNamaPertubuhan] = useState("");

  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [kekerapanPelaksanaan, setKekerapanPelaksanaan] = useState(
    t("setiapTahun")
  );
  const [tempohPelaksanaan, setTempohPelaksanaan] = useState("");
  const [notisPanggilanMesyuarat, setNotisPanggilanMesyuarat] = useState("");
  const [tarafPertubuhan, setTarafPertubuhan] = useState("");
  const [clauseContentId, setClauseContentId] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [clauseContentEditable, setClauseContentEditable] = useState(
    clause.clauseContent
  );
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }
    return errors;
  };

  const checkBebasEditFields = (
    requiredFieldText: string[],
    clauseContentEditable: string
  ) => {
    const missingFields = requiredFieldText.filter(
      (field) => !clauseContentEditable.includes(`<<${field}>>`)
    );
    if (missingFields.length > 0) {
      alert(
        `Kandungan tidak lengkap! Sila masukkan:\n\n${missingFields
          .map((field) => `<<${field}>>`)
          .join("\n")}\n\ndi dalam kandungan anda`
      );
      return true;
    }
    return false;
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const handleChange = (e: any) => {
    setClauseContentEditable(e.target.value);
  };

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause12);
      setDataId(clause.id);
      //setNamaPertubuhan(clause.societyName);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      setPemilihanAjk(clause.constitutionValues[0]?.definitionName);
      setClauseContentEditable(
        clause.clauseModifyContent
          ? clause.clauseModifyContent
          : clause.clauseContent
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clauseContentEditable;
  // clauseContent = clauseContent.replaceAll(/<<kekerapan pelaksanaan mesyuarat agung baru>>/gi, `<b>${kekerapanPelaksanaan || '<<kekerapan pelaksanaan mesyuarat agung baru>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  // clauseContent = clauseContent.replaceAll(/<<tempoh pelaksanaan mesyuarat agung baru>>/gi, `<b>${tempohPelaksanaan || '<<tempoh pelaksanaan mesyuarat agung baru>>'}</b>`);
  // clauseContent = clauseContent.replaceAll(/<<notis panggilan mesyuarat>>/gi, `<b>${notisPanggilanMesyuarat || '<<notis panggilan mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  const handlerChangeClauseContentEditable = (val: string) => {
    setClauseContentEditable(val);
  };

  return (
    <>
      <Grid container>
        {/* name section */}
        <FasalNameCom clauseId={clauseId} name={name} />
        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Typography sx={{ mb: 1, ...sectionStyle }}>{name}</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("jenisMesyuaratAgung")}{" "}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.pemilihanAjk}
                  >
                    <Select
                      size="small"
                      disabled={isViewMode}
                      value={pemilihanAjk}
                      displayEmpty
                      onChange={(e) => {
                        setPemilihanAjk(e.target.value as string);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          pemilihanAjk: "",
                        }));
                      }}
                    >
                      <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                      <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                    </Select>
                    {formErrors.pemilihanAjk && (
                      <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <ReminderEditable />
            <EditableFasalTextArea
              requiredFieldText={requiredText}
              clauseContentEditable={clauseContentEditable}
              setClauseContentEditable={handlerChangeClauseContentEditable}
            />
          </Box>

          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <ContentBox clauseContent={clauseContent} />
            <CheckContent checked={checked} onChange={handleChangeCheckbox} />
            {/* submit */}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  display: isViewMode ? "none" : "block",
                }}
                onClick={() => {
                  const errors = validateForm();
                  if (Object.keys(errors).length > 0) {
                    setFormErrors(errors);
                    return;
                  }
                  const isMissingRequired = checkBebasEditFields(
                    requiredText,
                    clauseContentEditable
                  );
                  if (isMissingRequired) {
                    return;
                  }
                  handleSaveContent({
                    i18n,
                    societyId: societyDataRedux.id,
                    societyName: societyDataRedux.societyName,
                    clauseContentId,
                    dataId,
                    isEdit,
                    clauseNo: clauseNo,
                    clauseName: clauseName,
                    createClauseContent,
                    editClauseContent,
                    description: clauseContent,
                    modifiedTemplate: clauseContentEditable,
                    constitutionValues: [
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: pemilihanAjk,
                        titleName: "Jenis Mesyuarat Agung",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohPelaksanaan,
                        titleName:
                          "Tempoh pelaksanaan mesyuarat agung baru daripada tarikh terakhir Mesyuarat Agung",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: kekerapanPelaksanaan,
                        titleName: "Kekerapan pelaksanaan mesyuarat agung baru",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: notisPanggilanMesyuarat,
                        titleName: "Notis panggilan mesyuarat",
                      },
                    ],
                    clause: "clause3",
                    clauseCount: 3,
                  });
                }}
                disabled={isCreatingContent || isEditingContent || !checked}
              >
                {isCreatingContent || isEditingContent
                  ? t("saving")
                  : t("save")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default FasalContentTigaBebasCawangan;
