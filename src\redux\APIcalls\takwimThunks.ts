import { createAsyncThunk } from '@reduxjs/toolkit';
import { setTakwimDataRedux, setTakwimError, setTakwimLoading } from '../takwimDataReducer';
import { setSelectedEvent } from '../slices/takwimSlice';
import { API_URL } from '@/api';
import { IEvent, IEventResponse } from '@/types/event';
import { RootState } from '../store';

export const fetchAllEvents = createAsyncThunk(
  'takwim/fetchAll',
  async (_, { dispatch }) => {
    dispatch(setTakwimLoading(true));
    try {
      const response = await fetch(`/society/event/getAll`, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      console.log('API Response:', data);

      if (data.status === 'SUCCESS' && Array.isArray(data.data)) {
        dispatch(setTakwimDataRedux(data.data));
        return data.data;
      } else {
        throw new Error('Invalid data format received');
      }
    } catch (error: any) {
      const errorMessage = error.message || "An error occurred";
      dispatch(setTakwimError(errorMessage));
      throw error;
    } finally {
      dispatch(setTakwimLoading(false));
    }
  }
);

export const fetchEventByEventNo = createAsyncThunk(
  'takwim/fetchAll',
  async (eventNo: string|undefined, { dispatch}) => {
    dispatch(setTakwimLoading(true));
    try {
      const response = await fetch(`/society/event/${eventNo}`, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      console.log('API Response EVENTS Details:', data);

      if (data.status === 'SUCCESS' && Array.isArray(data.data)) {
        dispatch(setTakwimDataRedux(data.data));
        return data.data;
      } else {
        throw new Error('Invalid data format received');
      }
    } catch (error: any) {
      const errorMessage = error.message || "An error occurred";
      dispatch(setTakwimError(errorMessage));
      throw error;
    } finally {
      dispatch(setTakwimLoading(false));
    }
  }
);



export const fetchAllPublishedEvents = createAsyncThunk(
  'takwim/fetchAll',
  async (_, { dispatch }) => {
    dispatch(setTakwimLoading(true));
    try {
      const response = await fetch(`/society/event/getPublished`, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      console.log('API Response:', data);

      if (data.status === 'SUCCESS' && Array.isArray(data.data)) {
        dispatch(setTakwimDataRedux(data.data));
        return data.data;
      } else {
        throw new Error('Invalid data format received');
      }
    } catch (error: any) {
      const errorMessage = error.message || "An error occurred";
      dispatch(setTakwimError(errorMessage));
      throw error;
    } finally {
      dispatch(setTakwimLoading(false));
    }
  }
);


// Helper to check authentication status
export const isAuthenticated = () => {
  try {
    const token = localStorage.getItem("refine-auth");
    return !!token;
  } catch {
    return false;
  }
};

// Example of adding more thunks
export const fetchEventDetails = createAsyncThunk(
  'takwim/fetchEventDetails',
  async (eventId: string, { dispatch, rejectWithValue }) => {
    dispatch(setTakwimLoading(true));
    try {
      const response = await fetch(`/society/event/${eventId}`, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();
      return data.data;
    } catch (error: any) {
      const errorMessage = error.message || "An error occurred";
      dispatch(setTakwimError(errorMessage));
      return rejectWithValue(errorMessage);
    } finally {
      dispatch(setTakwimLoading(false));
    }
  }
);

// Add more thunks as needed















