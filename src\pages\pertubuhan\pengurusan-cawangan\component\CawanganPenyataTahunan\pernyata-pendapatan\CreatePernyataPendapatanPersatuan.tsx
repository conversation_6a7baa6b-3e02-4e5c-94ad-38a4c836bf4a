import React from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, Typography } from "@mui/material";
import Input from "@/components/input/Input";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";

export const CreatePernyataanPendapatanPersatuan: React.FC<{
  t: TFunction;
  control: Control<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  checked: boolean;
}> = ({ t, watch, setValue, getValues, control, checked }) => {
  // const isAliranTugasAccess = true;
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);

  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography
              variant="subtitle1"
              sx={{
                color: "var(--primary-color)",
                borderRadius: "16px",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t(title)}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            {items.map((item, index) => (
              <Controller
                name={item.variable}
                control={control}
                key={index}
                defaultValue={getValues(item.variable)} // Default value or empty string
                render={({ field }) => (
                  <Input
                    {...field}
                    label={t(item.label)}
                    type="currrency"
                    placeholder="0"
                    textColor="var(--primary-color)"
                    fontWeight="500!important"
                    onChange={(e) => {
                      let checkNum = e.target.value.replace(/[^\d]/g, "");

                      if (checkNum.length > 12) {
                        checkNum = checkNum.slice(0, 12);
                      }

                      const currentValue =
                        (getValues(item.variable) as number) || 0; // Get the current field value
                      const newValue = checkNum === "" ? 0 : Number(checkNum);
                      const totalIncome =
                        (getValues("totalIncome") as number) || 0; // Get the current totalIncome value

                      // Subtract the previous value, then add the new value
                      const updatedTotalIncome =
                        totalIncome - currentValue + newValue;

                      // Update both the current field and totalIncome
                      field.onChange(newValue); // Update this field's value
                      setValue("totalIncome", updatedTotalIncome); // Update totalIncome
                    }}
                    disabled={
                      checked || (!isManager && !isAliranTugasAccess)
                      // item.variable == "totalIncome"
                      //   ? true
                      //   : false || !isAliranTugasAccess
                    }
                  />
                )}
              />
            ))}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {/* {renderInputGroup("operatingIncomeSection", [
        { label: "entranceFeeIncome", variable: "feeIncome" },
        { label: "membershipFeeIncome", variable: "feeIncomeMember" },
        { label: "donationIncome", variable: "donationIncome" },
        { label: "otherOperatingIncome", variable: "othersIncome" },
      ])}

      {renderInputGroup("fundraisingActivitiesIncomeSection", [
        { label: "dinnerEventIncome", variable: "foodRevenue" },
        { label: "charitySaleIncome", variable: "bookRevenue" },
        { label: "servicesIncome", variable: "serviceRevenue" },
        { label: "otherFundraisingIncome", variable: "otherRevenue" },
      ])}

      {renderInputGroup("investmentIncomeSection", [
        { label: "rentalIncome", variable: "rentalInvestment" },
        { label: "dividendIncome", variable: "dividendInvestment" },
        { label: "fixedDepositInterestIncome", variable: "interestInvestment" },
        { label: "propertyProfitIncome", variable: "propertyInvestment" },
        { label: "otherInvestmentIncome", variable: "otherInvestment" },
      ])}

      {renderInputGroup("grantsSection", [
        { label: "governmentAgencyGrant", variable: "govGrant" },
        { label: "privateAgencyGrant", variable: "privateGrant" },
        { label: "individualGrant", variable: "individualGrant" },
        { label: "otherGrants", variable: "otherGrant" },
      ])}

      {renderInputGroup("lainLainPendapatan", [
        { label: "lainLainPendapatan", variable: "otherIncome" },
      ])} */}

      {renderInputGroup("", [
        { label: "totalIncome", variable: "totalIncome" },
      ])}
    </>
  );
};

export default CreatePernyataanPendapatanPersatuan;
