/**
 * ElevenLabs integration utilities for speech-to-text and text-to-speech functionality
 */

import { ElevenLabsClient } from "@elevenlabs/elevenlabs-js";

// Initialize ElevenLabs client
let elevenLabsClient: ElevenLabsClient | null = null;

/**
 * Initialize ElevenLabs client with API key
 * @param apiKey ElevenLabs API key
 */
export const initElevenLabs = (apiKey: string): void => {
  try {
    elevenLabsClient = new ElevenLabsClient({
      apiKey: apiKey,
    });
    // console.log('ElevenLabs client initialized successfully');
  } catch (error) {
    // console.error('Failed to initialize ElevenLabs client:', error);
  }
};

/**
 * Check if ElevenLabs is available and initialized
 */
export const isElevenLabsAvailable = (): boolean => {
  return elevenLabsClient !== null;
};

/**
 * Convert audio blob to text using ElevenLabs speech-to-text
 * @param audioBlob The audio blob to transcribe
 * @param locale The locale for transcription (defaults to 'ms' for Malaysian)
 * @param diarize Whether to annotate who is speaking
 * @returns Promise with transcription result
 */
export const transcribeAudioWithElevenLabs = async (
  audioBlob: Blob,
  locale: string = 'ms',
  diarize: boolean = false
): Promise<string | null> => {
  if (!elevenLabsClient) {
    // console.error('ElevenLabs client not initialized');
    return null;
  }

  try {
    // console.log('Starting ElevenLabs transcription...');
    
    // Convert blob to File object if needed
    const audioFile = audioBlob instanceof File ? audioBlob : new File([audioBlob], 'audio.webm', { type: audioBlob.type });
    
    const transcription = await elevenLabsClient.speechToText.convert({
      file: audioFile,
      modelId: "scribe_v1",
      languageCode: locale,
      diarize: true, // Whether to annotate who is speaking
    });

    // console.log('ElevenLabs transcription completed:', transcription);
    
    // Extract text from the transcription result
    if (transcription && typeof transcription === 'object') {
      // Handle different possible response formats
      if ('text' in transcription) {
        return transcription.text as string;
      } else if ('transcript' in transcription) {
        return (transcription as any).transcript;
      } else if ('results' in transcription && Array.isArray((transcription as any).results)) {
        // Handle array of results
        const results = (transcription as any).results;
        return results.map((result: any) => result.text || result.transcript).join(' ');
      }
    }
    
    // If transcription is a string, return it directly
    if (typeof transcription === 'string') {
      return transcription;
    }
    
    // console.warn('Unexpected transcription format:', transcription);
    return null;
  } catch (error) {
    // console.error('ElevenLabs transcription error:', error);
    return null;
  }
};

/**
 * Convert text to speech using ElevenLabs
 * @param text The text to convert to speech
 * @param voiceId The voice ID to use (optional, defaults to a Malaysian voice)
 * @param locale The locale for voice selection
 * @returns Promise with audio blob
 */
export const textToSpeechWithElevenLabs = async (
  text: string,
  voiceId?: string,
  locale: string = 'ms'
): Promise<Blob | null> => {
  if (!elevenLabsClient) {
    // console.error('ElevenLabs client not initialized');
    return null;
  }

  try {
    // console.log('Starting ElevenLabs text-to-speech...');
    
    // Use a default voice ID if none provided
    const defaultVoiceId = voiceId || 'pNInz6obpgDQGcFmaJgB'; // Adam voice (English)
    
    // Optimize API call with faster settings
    const audio = await elevenLabsClient.textToSpeech.convert(
      defaultVoiceId,
      {
        text: text,
        modelId: "eleven_multilingual_v2",
        voiceSettings: {
          stability: 0.3, // Reduced for faster generation
          similarityBoost: 0.5, // Reduced for faster generation
          style: 0.0,
          useSpeakerBoost: false // Disabled for faster generation
        },
        outputFormat: "mp3_44100_96" // Reduced quality for faster generation
      }
    );

    // console.log('ElevenLabs text-to-speech completed');
    
    // Optimize blob creation
    if (audio instanceof ReadableStream) {
      const reader = audio.getReader();
      const chunks: Uint8Array[] = [];
      let totalLength = 0;
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
        totalLength += value.length;
      }
      
      const audioData = new Uint8Array(totalLength);
      let offset = 0;
      for (const chunk of chunks) {
        audioData.set(chunk, offset);
        offset += chunk.length;
      }
      
      return new Blob([audioData], { type: 'audio/mp3' });
    } else if (audio instanceof ArrayBuffer) {
      return new Blob([audio], { type: 'audio/mp3' });
    } else {
      // console.warn('Unexpected audio format from ElevenLabs:', typeof audio);
      return null;
    }
  } catch (error) {
    // console.error('ElevenLabs text-to-speech error:', error);
    return null;
  }
};

/**
 * Detect the actual MIME type of an audio blob by examining its header
 * @param blob The audio blob to analyze
 * @returns Promise with detected MIME type
 */
const detectAudioMimeType = async (blob: Blob): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      const arrayBuffer = reader.result as ArrayBuffer;
      const uint8Array = new Uint8Array(arrayBuffer.slice(0, 12));
      
      // Check for common audio file signatures
      if (uint8Array[0] === 0xFF && (uint8Array[1] & 0xE0) === 0xE0) {
        // MP3 signature
        resolve('audio/mpeg');
      } else if (
        uint8Array[0] === 0x52 && uint8Array[1] === 0x49 && 
        uint8Array[2] === 0x46 && uint8Array[3] === 0x46 &&
        uint8Array[8] === 0x57 && uint8Array[9] === 0x41 &&
        uint8Array[10] === 0x56 && uint8Array[11] === 0x45
      ) {
        // WAV signature
        resolve('audio/wav');
      } else if (
        uint8Array[0] === 0x4F && uint8Array[1] === 0x67 &&
        uint8Array[2] === 0x67 && uint8Array[3] === 0x53
      ) {
        // OGG signature
        resolve('audio/ogg');
      } else if (
        uint8Array[4] === 0x66 && uint8Array[5] === 0x74 &&
        uint8Array[6] === 0x79 && uint8Array[7] === 0x70
      ) {
        // MP4/AAC signature
        resolve('audio/mp4');
      } else {
        // Default to MP3 for unknown formats
        resolve('audio/mpeg');
      }
    };
    reader.readAsArrayBuffer(blob.slice(0, 12));
  });
};

/**
 * Create and manage audio context for better browser compatibility
 */
class AudioContextManager {
  private static instance: AudioContextManager;
  private audioContext: AudioContext | null = null;
  private isInitialized = false;

  static getInstance(): AudioContextManager {
    if (!AudioContextManager.instance) {
      AudioContextManager.instance = new AudioContextManager();
    }
    return AudioContextManager.instance;
  }

  async initializeContext(): Promise<AudioContext | null> {
    if (this.isInitialized && this.audioContext) {
      return this.audioContext;
    }

    try {
      // Create audio context with proper browser compatibility
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContextClass) {
        // console.warn('Web Audio API not supported');
        return null;
      }

      this.audioContext = new AudioContextClass();
      
      // Resume context if it's suspended (required by some browsers)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      this.isInitialized = true;
      // console.log('Audio context initialized successfully');
      return this.audioContext;
    } catch (error) {
      // console.error('Failed to initialize audio context:', error);
      return null;
    }
  }

  getContext(): AudioContext | null {
    return this.audioContext;
  }

  async resumeContext(): Promise<void> {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }
  }
}

/**
 * Play audio blob using Web Audio API with fallback to HTML Audio
 * @param audioBlob The audio blob to play
 * @param onStart Callback when audio starts playing
 * @param onEnd Callback when audio ends
 * @param onError Callback when an error occurs
 */
export const playAudioBlob = async (
  audioBlob: Blob,
  onStart?: () => void,
  onEnd?: () => void,
  onError?: (error: string) => void
): Promise<HTMLAudioElement | null> => {
  try {
    // Pre-initialize audio context
    const audioContextManager = AudioContextManager.getInstance();
    const audioContext = await audioContextManager.initializeContext();
    
    if (!audioContext) {
      // console.warn('Web Audio API not available, falling back to HTML Audio');
      return playWithHTMLAudio(audioBlob, 'audio/mp3', onStart, onEnd, onError);
    }

    // Create audio element immediately
    const audioElement = new Audio();
    audioElement.preload = 'auto'; // Preload the audio
    
    // Start loading the audio immediately
    const objectUrl = URL.createObjectURL(audioBlob);
    audioElement.src = objectUrl;
    
    // Set up event listeners
    audioElement.onplay = () => {
      onStart?.();
    };
    
    audioElement.onended = () => {
      URL.revokeObjectURL(objectUrl);
      onEnd?.();
    };
    
    audioElement.onerror = (e) => {
      URL.revokeObjectURL(objectUrl);
      onError?.(`Audio playback error: ${e}`);
    };

    // Start playing as soon as possible
    try {
      await audioElement.play();
    } catch (error) {
      // console.error('Error playing audio:', error);
      onError?.(`Failed to play audio: ${error}`);
      return null;
    }

    return audioElement;
  } catch (error) {
    // console.error('Error in playAudioBlob:', error);
    onError?.(`Failed to play audio: ${error}`);
    return null;
  }
};

/**
 * Play audio using Web Audio API
 */
const playWithWebAudioAPI = async (
  audioBlob: Blob,
  audioContext: AudioContext,
  mimeType: string,
  onStart?: () => void,
  onEnd?: () => void,
  onError?: (error: string) => void
): Promise<HTMLAudioElement | null> => {
  try {
    // Resume audio context if suspended
    if (audioContext.state === 'suspended') {
      await audioContext.resume();
    }

    // Convert blob to array buffer
    const arrayBuffer = await audioBlob.arrayBuffer();
    // console.log('Audio blob converted to ArrayBuffer, size:', arrayBuffer.byteLength);

    // Decode audio data
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
    // console.log('Audio decoded successfully, duration:', audioBuffer.duration, 'seconds');

    // Create audio source
    const source = audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioContext.destination);

    // Set up event handlers
    source.onended = () => {
      // console.log('Web Audio API playback ended');
      if (onEnd) onEnd();
    };

    // Start playback
    source.start(0);
    // console.log('Web Audio API playback started');
    
    if (onStart) onStart();

    // Create a dummy audio element for compatibility
    const dummyAudio = new Audio();
    dummyAudio.pause = () => {
      source.stop();
    };

    return dummyAudio;
  } catch (error) {
    // console.error('Web Audio API playback failed:', error);
    throw error;
  }
};

/**
 * Play audio using HTML Audio Element with enhanced compatibility
 */
const playWithHTMLAudio = (
  audioBlob: Blob,
  detectedMimeType: string,
  onStart?: () => void,
  onEnd?: () => void,
  onError?: (error: string) => void
): HTMLAudioElement => {
  const audio = new Audio();
  
  // List of MIME types to try, starting with detected type
  const mimeTypesToTry = [
    detectedMimeType,
    'audio/mpeg',
    'audio/mp3',
    'audio/wav',
    'audio/ogg',
    'audio/mp4',
    'audio/aac',
    'audio/webm',
    '' // Let browser auto-detect
  ].filter((type, index, arr) => arr.indexOf(type) === index); // Remove duplicates

  let currentMimeIndex = 0;
  let audioUrl: string | null = null;

  const cleanup = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      audioUrl = null;
    }
  };

  const tryNextMimeType = (): void => {
    if (currentMimeIndex >= mimeTypesToTry.length) {
      const finalError = 'All audio formats failed - browser may not support audio playback';
      // console.error(finalError);
      cleanup();
      if (onError) onError(finalError);
      return;
    }

    const mimeType = mimeTypesToTry[currentMimeIndex];
    // console.log(`Trying HTML Audio with MIME type: ${mimeType || 'auto-detect'} (attempt ${currentMimeIndex + 1}/${mimeTypesToTry.length})`);

    // Clean up previous URL
    cleanup();

    try {
      // Create blob with current MIME type
      const compatibleBlob = new Blob([audioBlob], { type: mimeType });
      audioUrl = URL.createObjectURL(compatibleBlob);

      // Reset audio element
      audio.src = '';
      audio.load();

      // Set up event handlers (only once)
      if (currentMimeIndex === 0) {
        audio.oncanplaythrough = () => {
          // console.log(`Audio can play through with ${mimeType || 'auto-detect'}`);
          if (onStart) onStart();
        };

        audio.onended = () => {
          // console.log('HTML Audio playback ended');
          cleanup();
          if (onEnd) onEnd();
        };

        audio.onerror = () => {
          // console.warn(`Audio error with ${mimeType || 'auto-detect'}:`, audio.error);
          currentMimeIndex++;
          setTimeout(tryNextMimeType, 100); // Small delay before trying next type
        };

        audio.onloadstart = () => {
          // console.log(`Audio load started with ${mimeType || 'auto-detect'}`);
        };
      }

      // Set audio properties
      audio.preload = 'auto';
      audio.volume = 1.0;
      audio.src = audioUrl;

      // Try to play
      const playPromise = audio.play();
      
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            // console.log(`HTML Audio playback started successfully with ${mimeType || 'auto-detect'}`);
          })
          .catch(error => {
            // console.warn(`HTML Audio play promise rejected with ${mimeType || 'auto-detect'}:`, error);
            
            if (error.name === 'NotAllowedError') {
              const errorMsg = 'Audio playback not allowed - user interaction required';
              cleanup();
              if (onError) onError(errorMsg);
            } else {
              // Try next MIME type
              currentMimeIndex++;
              setTimeout(tryNextMimeType, 100);
            }
          });
      }
    } catch (error) {
      // console.error(`Failed to create audio URL for ${mimeType || 'auto-detect'}:`, error);
      currentMimeIndex++;
      setTimeout(tryNextMimeType, 100);
    }
  };

  // Start trying MIME types
  tryNextMimeType();

  return audio;
};

/**
 * Record audio from microphone and return as blob
 * @param duration Maximum recording duration in milliseconds (default: 30 seconds)
 * @returns Promise with audio blob
 */
export const recordAudio = async (duration: number = 30000): Promise<Blob | null> => {
  try {
    // Request microphone access
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      } 
    });

    // Create MediaRecorder
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus'
    });

    const audioChunks: Blob[] = [];

    return new Promise((resolve, reject) => {
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());
        
        if (audioChunks.length > 0) {
          const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
          resolve(audioBlob);
        } else {
          resolve(null);
        }
      };

      mediaRecorder.onerror = (event) => {
        // console.error('MediaRecorder error:', event);
        stream.getTracks().forEach(track => track.stop());
        reject(new Error('Recording failed'));
      };

      // Start recording
      mediaRecorder.start();

      // Stop recording after specified duration
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
        }
      }, duration);
    });
  } catch (error) {
    // console.error('Error accessing microphone:', error);
    return null;
  }
};

/**
 * Enhanced speech recognition that combines Web Speech API with ElevenLabs
 * @param locale The locale for recognition
 * @param useElevenLabs Whether to use ElevenLabs as primary method
 * @param onResult Callback for successful recognition
 * @param onError Callback for errors
 * @param onEnd Callback when recognition ends
 * @returns Object with start and stop functions
 */
export const createEnhancedSpeechRecognition = (
  locale: string,
  useElevenLabs: boolean = false,
  onResult: (text: string, source: 'web-speech' | 'elevenlabs') => void,
  onError: (error: string) => void,
  onEnd: () => void
) => {
  let isRecording = false;
  let mediaRecorder: MediaRecorder | null = null;
  let stream: MediaStream | null = null;

  const start = async () => {
    if (isRecording) {
      // console.warn('Speech recognition already in progress');
      return;
    }

    isRecording = true;

    if (useElevenLabs && isElevenLabsAvailable()) {
      // Use ElevenLabs for speech recognition
      try {
        // console.log('Starting ElevenLabs speech recognition...');
        
        // Get microphone access
        stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          } 
        });

        // Create MediaRecorder
        mediaRecorder = new MediaRecorder(stream, {
          mimeType: 'audio/webm;codecs=opus'
        });

        const audioChunks: Blob[] = [];

        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            audioChunks.push(event.data);
          }
        };

        mediaRecorder.onstop = async () => {
          // Stop all tracks to release microphone
          if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
          }
          
          if (audioChunks.length > 0) {
            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            
            // Transcribe with ElevenLabs
            const transcription = await transcribeAudioWithElevenLabs(audioBlob, locale);
            
            if (transcription && transcription.trim()) {
              onResult(transcription.trim(), 'elevenlabs');
            } else {
              onError('No speech detected or transcription failed');
            }
          } else {
            onError('No audio data recorded');
          }
          
          isRecording = false;
          onEnd();
        };

        mediaRecorder.onerror = (event) => {
          // console.error('MediaRecorder error:', event);
          if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
          }
          isRecording = false;
          onError('Recording failed');
          onEnd();
        };

        // Start recording
        mediaRecorder.start();

        // Auto-stop after 30 seconds
        setTimeout(() => {
          if (mediaRecorder && mediaRecorder.state === 'recording') {
            mediaRecorder.stop();
          }
        }, 30000);

      } catch (error) {
        // console.error('ElevenLabs speech recognition error:', error);
        isRecording = false;
        onError(`ElevenLabs recognition failed: ${error}`);
        onEnd();
      }
    } else {
      // Fallback to Web Speech API
      // console.log('Using Web Speech API as fallback...');
      
      // Import and use existing Web Speech API logic
      const { createSpeechRecognition, startSpeechRecognition } = await import('./SpeechUtils');
      
      const recognition = createSpeechRecognition(locale);
      if (recognition) {
        startSpeechRecognition(
          recognition,
          (text) => {
            isRecording = false;
            onResult(text, 'web-speech');
          },
          (error) => {
            isRecording = false;
            onError(error);
          },
          () => {
            isRecording = false;
            onEnd();
          }
        );
      } else {
        isRecording = false;
        onError('Speech recognition not supported');
        onEnd();
      }
    }
  };

  const stop = () => {
    if (!isRecording) return;

    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }
    
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      stream = null;
    }
    
    isRecording = false;
  };

  return { start, stop, isRecording: () => isRecording };
};

/**
 * Check if microphone access is available
 */
export const checkMicrophoneAccess = async (): Promise<boolean> => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch (error) {
    // console.error('Microphone access denied:', error);
    return false;
  }
};

/**
 * Get available ElevenLabs voices
 * @returns Promise with available voices
 */
export const getElevenLabsVoices = async (): Promise<any[]> => {
  if (!elevenLabsClient) {
    // console.error('ElevenLabs client not initialized');
    return [];
  }

  try {
    const voices = await elevenLabsClient.voices.getAll();
    // console.log('Available ElevenLabs voices:', voices);
    return voices.voices || [];
  } catch (error) {
    // console.error('Error getting ElevenLabs voices:', error);
    return [];
  }
};

/**
 * Get available audio input devices
 */
export const getAudioInputDevices = async (): Promise<MediaDeviceInfo[]> => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    return devices.filter(device => device.kind === 'audioinput');
  } catch (error) {
    // console.error('Error getting audio devices:', error);
    return [];
  }
};
