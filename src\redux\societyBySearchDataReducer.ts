import { createSlice } from '@reduxjs/toolkit';

interface RoleStore {
  data: {
    data: any[];
    total: number
  };
  searchParams: { [key: string]: any };
  loading: boolean;
  error: string | null;
}

const initialState: RoleStore = {
  data: { data: [], total: 0 },
  searchParams: {},
  loading: false,
  error: null,
};

export const societyBySearchDataSlice = createSlice({
  name: 'societyBySearchData',
  initialState,
  reducers: {
    setSocietyBySearchSearchParams(state, action) {
      state.searchParams = action.payload;
    },
    setSocietyBySearchDataRedux(state, action) {
      state.data = action.payload;
    },
    setSocietyBySearchLoading(state, action) {
      state.loading = action.payload;
    },
    setSocietyBySearchError(state, action) {
      state.error = action.payload;
    },
  },
});

export const { setSocietyBySearchSearchParams, setSocietyBySearchDataRedux, setSocietyBySearchLoading, setSocietyBySearchError } = societyBySearchDataSlice.actions;
export default societyBySearchDataSlice.reducer;
