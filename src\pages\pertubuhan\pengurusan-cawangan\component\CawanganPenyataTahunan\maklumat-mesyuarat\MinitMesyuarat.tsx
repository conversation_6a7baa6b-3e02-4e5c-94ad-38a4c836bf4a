import { Box, Grid, IconButton, InputAdornment, LinearProgress, TextField, Typography } from '@mui/material'
import React, { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next';
import { API_URL } from '@/api';
import useUploadDocument from '@/helpers/hooks/useUploadDocument';
import { useCustom } from '@refinedev/core';
import { EyeIcon } from "@/components/icons";
import { useSelector } from 'react-redux';

type props = {
  subTitleStyle: any
};

const MinitMesyuarat: React.FC<props> = ({ subTitleStyle }) => {
  const { t } = useTranslation();
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };
  const { upload } = useUploadDocument({
    type: "dokumen",
  });
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (!files) return;

    Array.from(files).forEach((file) => {
      setUploadingFiles((prev) => [
        ...prev,
        {
          name: file.name,
          progress: 0,
          size: formatFileSize(file.size),
        },
      ]);

      upload({ file }, () => {
        setUploadingFiles((prev) =>
          prev.map((f) =>
            f.name === file.name ? { ...f, progress: 100, isComplete: true } : f
          )
        );
      });
    });
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const response = await fetch(
        `${API_URL}/society/document/${documentId}`,
        {
          method: "DELETE",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );

      if (response.ok) {
        // Remove from uploaded files list
        setUploadedFiles((prev) =>
          prev.filter((file) => file.id !== documentId)
        );
        handleMenuClose();
      }
    } catch (error) {
      console.error("Delete error:", error);
    }
  };
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  interface UploadingFile {
    name: string;
    progress: number;
    size: string;
    isComplete?: boolean;
  }


  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach((file) => {
      // Add to uploading state for UI feedback
      setUploadingFiles((prev) => [
        ...prev,
        {
          name: file.name,
          progress: 0,
          size: formatFileSize(file.size),
        },
      ]);

      // Upload using the hook
      upload({ file }, () => {
        // Update uploadingFiles to show completion
        setUploadingFiles((prev) =>
          prev.map((f) =>
            f.name === file.name ? { ...f, progress: 100, isComplete: true } : f
          )
        );
      });
    });
  };


  const handlePreviewDocument = (fileUrl: string) => {
    window.open(fileUrl, "_blank");
  };


  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/237/getPendingBySocietyId`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const societyDetail =
    societyData?.data?.data?.meetingGetAllPendingResponses[0];

  const [formData, setFormData] = useState({
    meetingType: "",
    meetingPlace: "",
    meetingMethod: "",
    platformType: "",
    meetingPurpose: "",
    tarikhMesyuarat: "",
    masaMula: "",
    masaTamat: "",
    meetingTime: "",
    meetingAddress: "",
    state: "",
    district: "",
    city: "",
    postcode: "",
    totalAttendees: "",
    meetingContent: "",
    ucapanAluanPengerusi: "",
    perkaraDibincangkan: "",
    halHalLain: "",
    penutup: "",
    providedBy: "",
    confirmBy: "",
    meetingName: "",
    meetingInfo: "",
    meetingDetails: "",
  });

  return (
    <Box
      sx={{
        p: 3,
        mt: 3,
        border: "1px solid #D9D9D9",
        // backgroundColor: "#FCFCFC",
        borderRadius: "14px",
      }}
    >
      <Typography variant="h6" component="h2" sx={subTitleStyle}>
        {t("meetingMinutes")}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <Typography variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}>
            {t("minitMesyuarat")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            multiline
            disabled
            rows={1}
            value={"123"}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="view content"
                    edge="end"
                  >
                    <EyeIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <Typography variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}>
            {t("ucapanAluanPengerusi")}
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            multiline
            disabled
            sx={{
              background: "#E8E9E8"
            }}
            rows={4}
            value={formData.ucapanAluanPengerusi}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <Typography variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}>{t("perkaraPerkara")}</Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            disabled
            sx={{
              background: "#E8E9E8"
            }}
            multiline
            rows={4}
            value={formData.perkaraDibincangkan}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <Typography variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}>{t("penutup")}</Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            multiline
            disabled
            sx={{
              background: "#E8E9E8"
            }}
            rows={4}
            value={formData.penutup}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <Typography variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}>{t("disahkanOleh")}</Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            disabled
            sx={{
              background: "#E8E9E8"
            }}
            value={formData.providedBy}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <Typography variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}>{t("disahkanOleh")}</Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            disabled
            sx={{
              background: "#E8E9E8"
            }}
            value={formData.confirmBy}
          />
        </Grid>
      </Grid>
    </Box>
  )
}

export default MinitMesyuarat
