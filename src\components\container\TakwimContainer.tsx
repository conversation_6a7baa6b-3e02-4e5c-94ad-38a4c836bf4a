import { Paper, PaperProps } from '@mui/material';
import React, { ReactNode } from 'react';

interface TakwimContainerProps extends PaperProps {
  children: ReactNode;
}

export const TakwimContainer: React.FC<TakwimContainerProps> = ({ children, ...props }) => {
  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: "20px",
        p: 3,
        overflow: "hidden",
        backgroundColor: "#FFFFFF",
        boxShadow: "0px 12px 24px rgba(0, 0, 0, 0.08)",
        width: "100%",
        display: "grid",
        gap: 2,
        mb: 2,
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Paper>
  );
};

export default TakwimContainer;
