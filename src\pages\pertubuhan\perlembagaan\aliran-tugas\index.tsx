import { ButtonOutline, ButtonPrimary, DataTable } from "@/components";
import {
  Box,
  Stack,
  Typography,
  IconButton,
  MenuItem,
  Menu,
  SelectChangeEvent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import CloseIcon from "@mui/icons-material/Close";
import React, { useState } from "react";
import { CrudFilter, useCustom, useCustomMutation } from "@refinedev/core";
import { IColumn } from "@/components/datatable";
import { API_URL } from "@/api";
import { Switch as CustomSwitch } from "@/components/switch";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Ajk,
  AjkNonCiizen,
  AliranTugas,
} from "../../pernyata-tahunan/interface";
import {
  ApplicationStatusList,
  OrganisationPositionLabel,
  useQuery,
} from "@/helpers";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const AligranTugasPerlembagaan = ({disabled=false} : {disabled?:boolean}) => {
  const { id } = useParams();
  const { t } = useTranslation();

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [isAliranTugas, setIsAliranTugas] = useState<boolean | undefined>(
    false
  );

  const [aliranTugas, setAliranTugas] = useState<AliranTugas[]>([]);
  const [aliranTugasList, setAliranTugasList] = useState<AliranTugas[]>([]);
  const [aliranTugasMemberList, setAliranTugasMemberList] = useState<
    AliranTugas[]
  >([]);
  const [ajkList, setAjkList] = useState<Ajk[]>([]);
  const [ajkTotal, setAjkTotal] = useState<Ajk[]>([]);
  const [nonCitizenAjkList, setNonCitizenAjkList] = useState<AjkNonCiizen[]>(
    []
  );
  const [taskId, setTaskId] = useState<number>();
  const [committeeId, setCommitteeId] = useState<number>();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const [isDeactivateModalOpen, setIsDeactivateModalOpen] = useState(false);
  const [openModalActiveFlow, setOpenModalActiveFlow] = useState(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, id: number) => {
    setAnchorEl(event.currentTarget);
    setOpenMenuId(id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };

  const handleConfirmActivate = (taskId: number, committeeId: number) => {
    setCommitteeId(committeeId);
    setTaskId(taskId);
    setOpenModalActiveFlow(true);
  };

  const handleConfirmDeactivate = (taskId: number, committeeId: number) => {
    setCommitteeId(committeeId);
    setTaskId(taskId);
    setIsDeactivateModalOpen(true);
  };

  const handleActivate = () => {
    setOpenModalActiveFlow(false);
    activeDeactivateTask(true);
  };

  const handleDeactivate = () => {
    setIsDeactivateModalOpen(false);
    activeDeactivateTask(false);
  };

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
    ];
    setPage(newPage);
    // fetchResult({ filters });
  };

  const columnsAliranTugasList: IColumn[] = [
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>{row?.name}</Box>
        );
      },
    },
    {
      field: "designationCode",
      headerName: t("position"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {t(
              OrganisationPositionLabel[
                row.designationCode as keyof typeof OrganisationPositionLabel
              ] || "unknown"
            )}
          </Box>
        );
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box
            sx={{
              border:
                row?.status ===
                ApplicationStatusList.find((item) => item.value === "AKTIF")?.id
                  ? "0.5px solid #00B69B"
                  : "0.5px solid #FF0000",
              backgroundColor:
                row?.status ===
                ApplicationStatusList.find((item) => item.value === "AKTIF")?.id
                  ? "#00B69B66"
                  : "#FF000080",
              textAlign: "center",
              borderRadius: "30px",
              p: "5px 3px",
              fontSize: "12px",
              color: "white",
            }}
          >
            {t("active")}
          </Box>
        );
      },
    },
    {
      field: "taskDeactivateDate",
      headerName: t("AKTIF"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.taskActivateDate ? row?.taskActivateDate : "-"}
          </Box>
        );
      },
    },
    {
      field: "taskDeactivateDate",
      headerName: t("deactivationDate"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.taskDeactivateDate ? row?.taskDeactivateDate : "-"}
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box sx={{ textAlign: "end" }}>
            <IconButton onClick={(event) => handleMenuOpen(event, row.id)}>
              <MoreVertIcon sx={{ color: "black" }} />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenuId === row.id}
              onClose={handleMenuClose}
              slotProps={{
                paper: {
                  elevation: 0,
                  sx: {
                    backgroundColor: "white",
                    color: "black",
                    boxShadow: "0 4px 4px rgba(0, 0, 0, 0.25)",
                    "& .MuiMenuItem-root": {
                      color: "black",
                      "& .MuiSvgIcon-root": {
                        color: "black",
                      },
                    },
                    "& .MuiDivider-root": {
                      my: 0.5,
                    },
                  },
                },
              }}
              transformOrigin={{
                horizontal: "right",
                vertical: "top",
              }}
              anchorOrigin={{
                horizontal: "right",
                vertical: "bottom",
              }}
            >
              <MenuItem
                onClick={() =>
                  handleConfirmDeactivate(row.id, row.societyCommitteeId)
                }
              >
                {t("deactivate")}
              </MenuItem>
            </Menu>
          </Box>
        );
      },
    },
  ];

  const columnsAliranTugasMemberList: IColumn[] = [
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>{row?.name}</Box>
        );
      },
    },
    {
      field: "designationCode",
      headerName: t("position"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {t(
              OrganisationPositionLabel[
                row.designationCode as keyof typeof OrganisationPositionLabel
              ] || "unknown"
            )}
          </Box>
        );
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box
            sx={{
              border: ApplicationStatusList.find(
                (item) => item.value === "TIDAK_AKTIF"
              )?.id
                ? "0.5px solid #00B69B"
                : "0.5px solid #FF0000",
              backgroundColor: ApplicationStatusList.find(
                (item) => item.value === "TIDAK_AKTIF"
              )?.id
                ? "#00B69B66"
                : "#FF000080",
              textAlign: "center",
              borderRadius: "30px",
              p: "5px 3px",
              fontSize: "12px",
              color: "white",
            }}
          >
            {t("inactive")}
          </Box>
        );
      },
    },
    {
      field: "taskDeactivateDate",
      headerName: t("AKTIF"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.taskActivateDate ? row?.taskActivateDate : "-"}
          </Box>
        );
      },
    },
    {
      field: "taskDeactivateDate",
      headerName: t("deactivationDate"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.taskDeactivateDate ? row?.taskDeactivateDate : "-"}
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box sx={{ textAlign: "end" }}>
            <IconButton onClick={(event) => handleMenuOpen(event, row.id)}>
              <MoreVertIcon sx={{ color: "black" }} />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenuId === row.id}
              onClose={handleMenuClose}
              slotProps={{
                paper: {
                  elevation: 0,
                  sx: {
                    backgroundColor: "white",
                    color: "black",
                    boxShadow: "0 4px 4px rgba(0, 0, 0, 0.25)",
                    "& .MuiMenuItem-root": {
                      color: "black",
                      "& .MuiSvgIcon-root": {
                        color: "black",
                      },
                    },
                    "& .MuiDivider-root": {
                      my: 0.5,
                    },
                  },
                },
              }}
              transformOrigin={{
                horizontal: "right",
                vertical: "top",
              }}
              anchorOrigin={{
                horizontal: "right",
                vertical: "bottom",
              }}
            >
              <MenuItem
                onClick={() =>
                  handleConfirmActivate(row.id, row.societyCommitteeId)
                }
              >
                {t("AKTIF")}
              </MenuItem>
            </Menu>
          </Box>
        );
      },
    },
  ];

  // ====
  const { refetch: fetchAjkList } = useQuery({
    url: `society/committee/listAjk`,
    filters: [
      { field: "societyId", operator: "eq", value: id },
      { field: "status", operator: "eq", value: "001" },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      setAjkList(responseData?.data || []);
      setAjkTotal(responseData?.total);
    },
  });

  // ========

  const handleSwitchAliranTugas = (flag: boolean) => {
    setIsAliranTugas(flag);
    switchTask(flag);
  };

  const { mutate: switchAliranTugas } = useCustomMutation();

  const switchTask = (flag: boolean) => {
    switchAliranTugas(
      {
        url: flag
          ? `${API_URL}/society/${id}/committee_task/activate`
          : `${API_URL}/society/${id}/committee_task/deactivate`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: () => {
          fetchAjkList();
          fetchAliranTugas();
        },
      }
    );
  };

  const { refetch: fetchAliranTugas } = useQuery({
    url: `society/committee-task/list`,
    filters: [{ field: "societyId", operator: "eq", value: id }],
    autoFetch: false,
    onSuccess: (data) => {
      const response = data?.data?.data || undefined;
      if (response) {
        // setAliranTugas(response|| undefined);
        setAliranTugasList(
          response.filter((e: any) => {
            return e.status === "001";
          })
        );
        setAliranTugasMemberList(
          response.filter((e: any) => {
            return e.status === "008";
          })
        );
      }
    },
  });

  const { mutate: deactivate } = useCustomMutation();

  const activeDeactivateTask = (flag: boolean) => {
    // Add logic to save the new task
    deactivate(
      {
        url: flag
          ? `${API_URL}/society/committee-task/${taskId}/activate?societyId=${id}`
          : `${API_URL}/society/committee-task/${taskId}/deactivate?societyId=${id}`,
        method: "put",
        values: {
          committeeId: committeeId,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: () => {
          // setShouldFetch(true)
        },
      }
    );
  };

  return (
    <>
      <Stack
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
        gap={2}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Box>
            <Stack
              direction="row"
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("taskFlow")}
              </Typography>
              <CustomSwitch
                disabled={disabled}
                checked={isAliranTugas}
                onChange={(e) => handleSwitchAliranTugas(e.target.checked)}
              />
            </Stack>

            {isAliranTugas ? (
              <DataTable
                columns={columnsAliranTugasList}
                rows={aliranTugasList}
                page={page}
                rowsPerPage={pageSize}
                totalCount={Number(ajkTotal) || 0}
                onPageChange={handleChangePage}
                customNoDataText={t("noData")}
                pagination={false}
              />
            ) : null}
          </Box>
        </Box>
      </Stack>

      {isAliranTugas ? (
        <Stack
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
            mb: 2,
          }}
          gap={2}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
            }}
          >
            <Box>
              <Stack
                direction="row"
                sx={{
                  alignItems: "center",
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("taskFlowList")}
                </Typography>
              </Stack>

              {isAliranTugas ? (
                <DataTable
                  columns={columnsAliranTugasMemberList}
                  rows={aliranTugasMemberList}
                  page={page}
                  rowsPerPage={pageSize}
                  totalCount={Number(ajkTotal) || 0}
                  onPageChange={handleChangePage}
                  customNoDataText={t("noData")}
                  pagination={false}
                />
              ) : null}
            </Box>
          </Box>
        </Stack>
      ) : null}

      {/* active dialog */}
      <Dialog
        open={openModalActiveFlow}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#000",
            minWidth: "400px",
            maxWidth: "100%",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2.5 }}>
          <Box
            sx={{
              py: 0.5,
              borderRadius: 2.5,
            }}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontWeight: "bold", fontSize: 14 }}
            >
              {t("pengaktifanAlirFlow")}?
            </Typography>
            <IconButton
              onClick={() => {
                setOpenModalActiveFlow(false);
              }}
              size="small"
            >
              <CloseIcon sx={{ color: "black" }} />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ py: 2, px: 2.5 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="body1" sx={{ fontSize: 14 }}>
              {t("confirmActiveTugas")}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ py: 2, px: 3 }}>
          <ButtonOutline
            onClick={() => {
              setOpenModalActiveFlow(false);
            }}
            sx={{ mr: 2 }}
          >
            {t("no")}
          </ButtonOutline>
          <ButtonPrimary
            onClick={() => {
              handleActivate();
            }}
          >
            {t("yes")}
          </ButtonPrimary>
        </DialogActions>
      </Dialog>

      {/* deactive dialog */}
      <Dialog
        open={isDeactivateModalOpen}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#000",
            minWidth: "400px",
            maxWidth: "100%",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2.5 }}>
          <Box
            sx={{
              py: 0.5,
              borderRadius: 2.5,
            }}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontWeight: "bold", fontSize: 14 }}
            >
              {t("deactivateTaskFlow")}?
            </Typography>
            <IconButton
              onClick={() => setIsDeactivateModalOpen(false)}
              size="small"
            >
              <CloseIcon sx={{ color: "black" }} />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ py: 2, px: 2.5 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="body1" sx={{ fontSize: 14 }}>
              {t("confirmDeactivateTaskFlow")}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ py: 2, px: 3 }}>
          <ButtonOutline
            onClick={() => setIsDeactivateModalOpen(false)}
            sx={{ mr: 2 }}
          >
            {t("no")}
          </ButtonOutline>
          <ButtonPrimary onClick={handleDeactivate}>{t("yes")}</ButtonPrimary>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AligranTugasPerlembagaan;
