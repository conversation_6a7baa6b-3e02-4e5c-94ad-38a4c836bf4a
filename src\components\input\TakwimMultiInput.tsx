import React, { useState } from 'react';
import { <PERSON>rid, <PERSON>po<PERSON>, <PERSON>Field, Box, Chip } from '@mui/material';

interface TakwimMultiInputProps {
  label: string;
  value: string[];
  onChange: (values: string[]) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  delimiter?: string;
}

export const TakwimMultiInput: React.FC<TakwimMultiInputProps> = ({
  label,
  value = [],
  onChange,
  onFocus,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  error = false,
  helperText,
  delimiter = ','
}) => {
  const [inputValue, setInputValue] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === delimiter) {
      e.preventDefault();
      addValue();
    }
  };

  const addValue = () => {
    if (inputValue.trim()) {
      // Split by delimiter and filter out empty strings
      const newValues = inputValue
        .split(delimiter)
        .map(v => v.trim())
        .filter(v => v);
      
      // Add new values and remove duplicates
      const updatedValues = [...new Set([...value, ...newValues])];
      onChange(updatedValues);
      setInputValue('');
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    addValue();
    if (onBlur) onBlur();
  };

  const handleDelete = (valueToDelete: string) => {
    onChange(value.filter(v => v !== valueToDelete));
  };

  return (
    <Grid container spacing={2} alignItems="flex-start">
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{
            color: '#666666',
            fontSize: '14px',
            mt: 1,
          }}
        >
          {label}
          {required && <span style={{ color: 'red' }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        <TextField
          fullWidth
          size="small"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={onFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          error={error}
          helperText={helperText}
          sx={{
            backgroundColor: 'white',
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                borderColor: error ? 'red' : '#DADADA',
              },
              '&:hover fieldset': {
                borderColor: '#DADADA',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#DADADA',
              },
            },
            '& .MuiInputBase-input': {
              fontSize: '14px',
              padding: '8.5px 14px',
            },
          }}
        />
        {value.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
            {value.map((item, index) => (
              <Chip
                key={index}
                label={item}
                size="small"
                onDelete={() => handleDelete(item)}
                sx={{ backgroundColor: '#4DB6AC', color: 'white' }}
              />
            ))}
          </Box>
        )}
      </Grid>
    </Grid>
  );
};