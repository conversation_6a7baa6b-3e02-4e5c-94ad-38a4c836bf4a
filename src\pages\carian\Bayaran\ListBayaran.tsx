import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import InputLabel from "@mui/material/InputLabel";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { Stack, Card } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import { Select, Option } from "../../../components/input";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCreate, useCustom } from "@refinedev/core";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
// import { StepperRegisterCawangan } from "../RegisterCawangan/components/Stepper";
import { useCustomMutation } from "@refinedev/core";
import { ApplicationStatus } from "../../../helpers/enums";
import { API_URL } from "../../../api";
import { getLocalStorage } from "../../../helpers/utils";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setCurrentInfoId } from "@/redux/carianReducer";

function PembayaranCawangan() {
  const carianRedux = useSelector(
    (state: { carian: any }) => state.carian.data
  );

  const { t } = useTranslation();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));
  const dispatch = useDispatch();
  const [paymentMethod, setPaymentMethod] = useState("");
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loadingPayment, setLoadingPayment] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const navigate = useNavigate();

  const [isChecked, setIsChecked] = useState(false);

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  //const params = new URLSearchParams(window.location.search);
  const { id: societyId } = useParams();
  //TODO not recommended as local storage will easily override please take note
  const [searchParams] = useSearchParams();
  const { mutate: makePayment, isLoading: isMakePaymentLoading } =
    useCustomMutation();
  const { mutate: createInfoSearch, isLoading: isLoadingIsCreatingInfoSearch } =
    useCustomMutation();
  const searchInformationCreate = async () => {
    setLoadingPayment(true);
    const searchInformationCreatetPayload = {
      societyId: carianRedux.currentSocietyId,
      committeeYear: "2023",
      acknowledgement: true,
      paymentMethod: paymentMethod === "online" ? "O" : "K",
      paymentType: "CARIAN_MAKLUMAT",
      paymentTotal: carianRedux.amount,
      applicationStatusCode: paymentMethod === "online" ? "6" : "5",
      documentTemplateCode: carianRedux.dokumen,
    };
    createInfoSearch(
      {
        method: "post",
        url: `${API_URL}/society/searchInformation/create`,
        values: searchInformationCreatetPayload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        errorNotification: (data) => {
          return {
            message: t("error"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          const infoId = data?.data?.data?.id;
          const createdDate = data?.data?.data?.createdDate;
          dispatch(setCurrentInfoId(infoId));
          if (paymentMethod === "online") {
            navigate(`online?societyId=${carianRedux.currentSocietyId}`, {
              state: { id: infoId, createdDate: createdDate },
            });
          } else {
            handleKaunterPayment(infoId, createdDate);
          }
        },
      }
    );
  };

  const handleKaunterPayment = async (infoId: any, createdDate: string) => {
    const getUserDetails = localStorage.getItem("user-details");
    const email = getUserDetails ? JSON.parse(getUserDetails).email : "";

    makePayment(
      {
        method: "post",
        url: `${API_URL}/society/payment/makePayment`,
        values: {
          societyId: carianRedux.currentSocietyId,
          searchInformationId: infoId,
          amount: carianRedux.amount,
          paymentMethod: "K",
          paymentType: "Carian Maklumat (Pembayaran KAUNTER)",
          email: email,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        errorNotification: (data) => {
          return {
            message: t("error"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setLoadingPayment(false);
          navigate(`kaunter?&societyId=${carianRedux.currentSocietyId}`, {
            state: { id: infoId, createdDate: createdDate },
          });
        },
      }
    );
  };

  const { data: paymentStatus, isLoading } = useCustom({
    url: `${API_URL}/society/admin/integration/payment/status`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const onlinePaymentEnabled: boolean =
    paymentStatus?.data?.data?.payment?.toLowerCase() == "enabled";
  const alert: boolean = paymentStatus?.data?.data?.alert;

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <Box sx={{ display: "flex" }}>
      <Card
        sx={{
          borderRadius: "16px",
          mr: 3,
          padding: "40px 30px",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
          height: "50%",
          display: "flex",
          width: { xs: "100%", md: "190px" },
          flexDirection: "column",
          flexShrink: 0,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontWeight: "400",
            marginBottom: "16px",
          }}
        >
          {t("langkahCarianDokumen")}
        </Typography>
        <Stack gap={2}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              marginBottom: "8px",
            }}
          >
            <Box
              sx={{
                width: "16px",
                height: "16px",
                border: "1px solid var(--primary-color)",
                backgroundColor: "var(--primary-color)",
                borderRadius: "4px",
                marginRight: "8px",
                flexShrink: 0,
              }}
            />
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontWeight: "500",
                fontSize: "14px",
              }}
            >
              {t("carianPertubuhanDanMaklumat")}
            </Typography>
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              marginBottom: "8px",
            }}
          >
            <Box
              sx={{
                width: "16px",
                height: "16px",
                border: "1px solid var(--primary-color)",
                backgroundColor: "var(--primary-color)",
                borderRadius: "4px",
                marginRight: "8px",
                flexShrink: 0,
              }}
            />
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontWeight: "500",
                fontSize: "14px",
              }}
            >
              {t("bayaran")}
            </Typography>
          </Box>
        </Stack>
      </Card>
      <Box sx={{ display: "flex", gap: 2 }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                  }}
                >
                  {t("payment")}
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontFamily: "Poppins",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: "21px",
                  textAlign: "left",
                  mb: 2,
                }}
              >
                {t("agreementText")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1,
                }}
              >
                <Checkbox
                  id="akuan-setuju-terima"
                  checked={isChecked}
                  onChange={handleCheckboxChange}
                  sx={{
                    color: "#00A7A7",
                    "&.Mui-checked": {
                      color: "#00A7A7",
                    },
                    padding: "0",
                  }}
                />
                <InputLabel
                  htmlFor="akuan-setuju-terima"
                  required
                  sx={{
                    color: "#333333",
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: 1.4,
                    "& .MuiFormLabel-asterisk": {
                      color: "#FF0000",
                    },
                  }}
                >
                  {t("agreementAcceptance")}
                </InputLabel>
              </Box>
            </Box>

            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 3,
              }}
            >
              <Box sx={{ mb: 2 }}>
                <Box sx={{ mb: 1 }}>
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      color: "#00A7A7",
                      fontSize: 16,
                      fontWeight: 600,
                    }}
                  >
                    {t("paymentMethod")}
                  </Typography>
                </Box>
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                >
                  <Grid item xs={4}>
                    {/* <Typography>1</Typography> */}
                  </Grid>
                  <Grid item xs={8}>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        color: "#FF0000",
                        marginLeft: "15px",
                      }}
                    >
                      {alert}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <InputLabel
                      htmlFor="cara-pembayaran"
                      required
                      sx={{
                        color: "#333333",
                        fontSize: 14,
                        fontWeight: 400,
                        minWidth: "150px",
                        "& .MuiFormLabel-asterisk": {
                          color: "#FF0000",
                        },
                      }}
                    >
                      {t("paymentMethod")}
                    </InputLabel>
                  </Grid>
                  <Grid item xs={8}>
                    <Select
                      value={paymentMethod}
                      onChange={handleChange}
                      id="cara-pembayaran"
                      t={t}
                      sx={{
                        width: "100%",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E5E5",
                          borderRadius: 1,
                        },
                      }}
                    >
                      <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                      {onlinePaymentEnabled ? (
                        <Option value="online">{t("pembayaranOnline")}</Option>
                      ) : (
                        <Option value="online" disabled>
                          {t("pembayaranOnline")}
                        </Option>
                      )}
                    </Select>
                  </Grid>
                </Grid>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontSize: 12,
                  marginTop: 10,
                  textAlign: "center",
                }}
              >
                {t("paymentNote")}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <ButtonPrimary
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  color: "white",
                  borderRadius: 1,
                  textTransform: "none",
                }}
                disabled={!isChecked || !paymentMethod || loadingPayment}
                onClick={handleSubmit}
              >
                {t("hantar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          PaperProps={{
            style: {
              borderRadius: "8px",
              width: "400px",
              padding: "24px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          }}
        >
          <DialogContent sx={{ textAlign: "center", p: 0, mb: 3 }}>
            <Typography
              sx={{
                fontSize: 16,
                color: "#333333",
                fontWeight: 500,
              }}
            >
              {t("confirmBuyDokumen")}
            </Typography>
          </DialogContent>

          <DialogActions
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 1,
              p: 0,
            }}
          >
            <ButtonPrimary
              onClick={() => {
                handleCloseDialog();
                searchInformationCreate();
              }}
              sx={{
                color: "white",
              }}
            >
              {t("ya")}
            </ButtonPrimary>

            <Typography
              onClick={handleCloseDialog}
              sx={{
                fontFamily: "Poppins",
                fontSize: "14px",
                fontWeight: 500,
                lineHeight: "12px",
                textAlign: "center",
                textDecoration: "underline",
                textDecorationStyle: "solid",
                textUnderlinePosition: "from-font",
                textDecorationSkipInk: "none",
                color: "#828282",
                cursor: "pointer",
              }}
            >
              {t("Kembali")}
            </Typography>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>
    </Box>
  );
}

export default PembayaranCawangan;
