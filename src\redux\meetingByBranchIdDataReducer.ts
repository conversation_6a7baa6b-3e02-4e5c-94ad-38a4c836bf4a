import { createSlice } from '@reduxjs/toolkit';

interface MeetingByBranchIdStore {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: MeetingByBranchIdStore = {
  data: null,
  loading: false,
  error: null,
};

export const meetingByBranchIdDataSlice = createSlice({
  name: 'meetingByBranchIdData',
  initialState,
  reducers: {
    setMeetingByBranchIdDataRedux(state, action) {
      state.data = action.payload;
    },
    setMeetingByBranchIdLoading(state, action) {
      state.loading = action.payload;
    },
    setMeetingByBranchIdError(state, action) {
      state.error = action.payload;
    },
  },
});

export const { setMeetingByBranchIdDataRedux, setMeetingByBranchIdLoading, setMeetingByBranchIdError } = meetingByBranchIdDataSlice.actions;
export default meetingByBranchIdDataSlice.reducer;
