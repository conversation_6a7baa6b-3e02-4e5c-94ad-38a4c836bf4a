import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useParams } from "react-router-dom";
import {
  Box,
  CircularProgress,
  Grid,
  Stack,
  Typography,
  useMediaQuery,
  Theme,
} from "@mui/material";
import {
  ApplicationStatusList,
  DecisionOptionsCode,
} from "../../../../helpers/enums";
// import DialogConfirmation from "../../keputusan-induk-pembubaran/views/DialogConfirmation";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import TextFieldController from "../../../../components/input/TextFieldController";
import SelectFieldController from "../../../../components/input/select/SelectFieldController";
import AccordionComp from "../../View/Accordion";
import MaklumatAmSection from "./views/MaklumatAmSection";
import MaklumatMesyuaratSection from "./views/MaklumatMesyuaratSection";
import MaklumatPindaanSection from "./views/MaklumatPindaanSection";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { getLocalStorage, useMutation, useQuery } from "@/helpers";
import { DialogConfirmation } from "@/components";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

function KeputusanCawanganPindaanNamaAlamatComp() {
  const { id, branchAmendmentId } = useParams();
  const societyId = atob(id || "");
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const { t } = useTranslation();
  const navigate = useNavigate();
  const decisionOptions = DecisionOptionsCode(t).filter(
    (item) => item.value != 36
  );
  const [roList, setRoList] = useState([]);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [currentBranchAmendInfo, setCurrentBranchAmendInfo] =
    useState<any>(null);
  const [societyInfo, setSocietyInfo] = useState<{
    societyName: string;
    societyNo: string;
  }>({
    societyName: "",
    societyNo: "",
  });
  const { mutate: updateApproval, isLoading: isUpdatingStatus } =
    useCustomMutation();

  const {
    data: societyData,
    isLoading: isSocietyDataLoading,
    refetch: fetchSocietyData,
  } = useQuery({
    url: `society/${societyId}`,
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      setSocietyInfo({
        societyName: responseData?.societyName,
        societyNo: responseData?.societyNo,
      });
    },
  });

  const {
    data: branchAmendmentData,
    isLoading: isbranchAmendmentDataLoading,
    refetch: fetchBranchAmendment,
  } = useQuery({
    url: `society/external/branchAmendment/${branchAmendmentId}`,
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      setCurrentBranchAmendInfo(responseData);
    },
  });

  const { control, handleSubmit, getValues, watch, setValue, setError } =
    useForm<FieldValues>({
      defaultValues: {
        roApprovalType: "BRANCH_AMENDMENT",
        societyId: societyId,
        rejectReason: "",
        branchAmendmentId: "",
        note: "",
      },
    });

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});

  const sectionItems = [
    {
      subTitle: t("generalInformation"),
      component: (
        <MaklumatAmSection
          info={currentBranchAmendInfo}
          societyInfo={societyInfo}
        />
      ),
    },
    {
      subTitle: t("maklumatMesyuarat"),
      component: <MaklumatMesyuaratSection info={currentBranchAmendInfo} />,
    },
    {
      subTitle: t("maklumatPindaan"),
      component: (
        <MaklumatPindaanSection
          info={currentBranchAmendInfo}
          societyInfo={societyInfo}
        />
      ),
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);
      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const onSubmit = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);

  const handleFormSubmit = () => {
    setValue("branchAmendmentId", branchAmendmentId);
    const payload = getValues();
    updateApproval(
      {
        url: `${API_URL}/society/roDecision/updateApprovalStatus`,
        method: "patch",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanSuccessPindaanName"),
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanErrorPindaanName"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setIsSuccess(true);
          navigate(-1);
        },
      }
    );
  };

  //=======================================
  const { fetch: updateRo } = useMutation({
    url: "society/liquidate/approval/ro",
    method: "put",
  });

  const { mutate: updateApprovalRo, isLoading: isUpdatingApprovalRo } =
    useCustomMutation();

  const {
    control: ControlRo,
    handleSubmit: handleSubmitRo,
    getValues: getValuesRo,
    watch: watchRo,
    setValue: setValueRo,
    setError: setErrorRo,
  } = useForm<FieldValues>({
    defaultValues: {
      id: branchAmendmentId,
      ro: "",
      note: "",
      branchAmendmentId: "",
    },
  });

  const { data: roListData, isLoading: isRoListLoading } = useCustom({
    url: `${API_URL}/society/user/getRoList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        branchId: currentBranchAmendInfo?.branchId,
      },
    },
    queryOptions: {
      enabled: !!currentBranchAmendInfo?.branchId,
      onSuccess(data) {
        const roListData = data?.data?.data || [];
        const options = roListData.map((data: any) => ({
          label: data?.name,
          value: data?.id,
        }));
        setRoList(options);
      },
    },
  });

  const onSubmitRo = () => {
    const payload = getValuesRo();
    const data = {
      roId: payload.roId,
      noteRo: payload.noteRo,
      roApprovalType: "BRANCH_AMENDMENT",
      branchAmendmentId: getValuesRo("id"),
    };
    updateApprovalRo({
      url: `${API_URL}/society/roDecision/updateRo`,
      method: "patch",
      values: data,
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      successNotification: () => {
        return {
          message: t("messageKeputusanPermohonanSuccess"),
          type: "success",
        };
      },
      errorNotification: () => {
        return {
          message: t("messageKeputusanPermohonanError"),
          type: "error",
        };
      },
    });
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {societyInfo?.societyName} <br />
              {societyInfo?.societyNo}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          {isbranchAmendmentDataLoading ? (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <CircularProgress />
            </Box>
          ) : (
            sectionItems.map((item, index) => {
              return (
                <AccordionComp
                  key={index}
                  subTitle={item.subTitle}
                  currentIndex={index + 1}
                  currentExpand={currentExpandSection}
                  readStatus={readStatus}
                  onChangeFunc={handleChangeCurrentExpandSection}
                >
                  {item.component}
                </AccordionComp>
              );
            })
          )}
        </Box>

        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form onSubmit={handleSubmitRo(onSubmitRo)}>
            {/* ===== */}
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("responsibleRO")} 
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="roId"
                    control={ControlRo}
                    options={roList} 
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={ControlRo}
                    name="noteRo"
                    multiline
                    defaultValue={getValuesRo("noteRo")}
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
            <Grid
              item
              xs={12}
              sx={{
                my: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrimary type="submit" sx={{ py: 1 }}>
                {t("update")} 
              </ButtonPrimary>

              {/* <ButtonPrimary
                type="submit"
                sx={{
                  display: "block",
                  backgroundColor: "var(--primary-color)",
                  width: "100px",
                  minWidth: "unset",
                  height: "32px",
                  color: "white",
                  "&:hover": { backgroundColor: "#19ADAD" },
                  textTransform: "none",
                  fontWeight: 400,
                  fontSize: "8px",
                }}
              >
                {t("update")}sadasd
              </ButtonPrimary> */}
            </Grid>
          </form>
          {/* ===== */}

          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography color={"primary"}>{t("keputusan")}</Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("applicationStatus")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <SelectFieldController
                    control={control}
                    name="applicationStatusCode"
                    options={decisionOptions}
                    disabled={isFormDisabled}
                    sx={{
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    defaultValue={getValues("note")}
                    name="note"
                    multiline
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                    required={watch("applicationStatusCode") === 36}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box
              sx={{
                mt: 5,
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              <ButtonOutline sx={{ py: 1 }} onClick={() => navigate(-1)}>
                {t("back2")}
              </ButtonOutline>
              <ButtonPrimary
                type="submit"
                disabled={isFormDisabled}
                sx={{ py: 1 }}
              >
                {t("update")}
              </ButtonPrimary>
            </Box>
          </form>
        </Box>
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isUpdatingStatus}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        // decisionLabel={getValues("applicationStatusCode")}
        decisionLabel={
          ApplicationStatusList.find(
            (item) => item.id === getValues().applicationStatusCode
          )?.value || "-"
        }
      />
    </>
  );
}

export default KeputusanCawanganPindaanNamaAlamatComp;
