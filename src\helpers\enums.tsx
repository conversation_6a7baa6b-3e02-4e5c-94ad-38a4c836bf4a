import { t, TFunction } from "i18next";

import { ISection } from "@/types";

const ApplicationStatus = {
  BELUM_DIHANTAR: 1,
  MENUNGGU_KEPUTUSAN: 2,
  LULUS: 3,
  TOLAK: 4,
  MENUNGGU_BAYARAN_KAUNTER: 5,
  MENUNGGU_BAYARAN_ONLINE: 6,
  MAKLUMAT_TIDAK_LENGKAP: 7,
  LUPUT: 8,
  DIBENARKAN: 9,
  TIDAK_DIBENARKAN: 10,
  AKTIF: 11,
  MENUNGGU_PENGAKTIFAN_CAWANGAN: 12,
  TIADA_MAKLUMAT_MIGRASI: 13,
  TAMAT_TEMPOH_CARIAN: 14,
  SETIAUSAHA_BUKAN_WARGA: 15,
  BUBAR: 16,
  SELESAI: 17,
  DISYORKAN: 18,
  TIDAK_DISYORKAN: 19,
  BATAL: 20,
  MENUNGGU_SYOR_PEGAWAI_PEMPROSES: 21,
  TEST_DATA: 22,
  MENUNGGU_MAKLUMBALAS: 23,
  PERTELINGKAHAN: 25,
  TAR<PERSON><PERSON>_BALIK: 26,
  BATAL_KHAS: 27,
  MENUNGGU_PENGESAHAN_JAWATANKUASA_INDUK: 28,
  LULUS_BERSYARAT: 29,
  DALAM_TINDAKAN_KIV: 30,
  KUIRI: 36,
  PINDAH: 37,
  MENUNGGU_PENGESAHAN_BAYARAN: 38,
  MENUNGGU_KEPUTUSAN_MENTERI: 39,
  MENUNGGU_ULASAN: 40,
  MENUNGGU_ULASAN_AGENSI_LUAR: 41,
  NOTIS_MESYUARAT_DIHANTAR: 42,
  INAKTIF: 43,
  BAYARAN_GAGAL: 44,
};

const CitizenshipStatus = [
  {
    label: "citizen",
    value: 1,
  },
  {
    label: "nonCitizen",
    value: 2,
  },
];

//NEW STATUS
const NewSocietyBranchStatus = {
  BATAL_1: "003",
  TOLAK_1: "002",
  AKTIF_1: "001",
  TARIK_BALIK_1: "004",
  BUBAR_1: "005",
  BUBAR_SEMENTARA_1: "006",
  BARU_1: "007",
  TIDAK_AKTIF_1: "008",
  DIBENARKAN_1: "009",
  DILUPUTKAN_1: "010",
  DIGUGURKAN_1: "998",
  TIADA_MAKLUMAT_1: "999",
  TEST_1: "678",
  PINDA_NAMA_1: "011",
  PERTELINGKAHAN_SIASATAN_1: "101",
  BATAL_KHAS_1: "102",
  TIDAK_AKTIF_PINDAH_1: "103",
  DALAM_PERHATIAN_1: "104",
  TIDAK_AKTIF_2: "306",
};

//TODO "003" - "306" translation in my.json/en.json
// enum yang gampang dan tanpa if else hell
export const ApplicationStatusEnum: Record<number | string, string> = {
  "-1": "PADAM",
  0: "-",
  1: "BELUM DIHANTAR",
  2: "MENUNGGU KEPUTUSAN",
  3: "LULUS",
  4: "TOLAK",
  5: "MENUNGGU_BAYARAN_KAUNTER",
  6: "MENUNGGU_BAYARAN_ONLINE",
  7: "MAKLUMAT TIDAK_LENGKAP",
  8: "LUPUT",
  9: "DIBENARKAN",
  10: "TIDAK DIBENARKAN",
  11: "AKTIF",
  12: "MENUNGGU PENGAKTIFAN CAWANGAN",
  13: "TIADA MAKLUMAT MIGRASI",
  14: "TAMAT TEMPOH CARIAN",
  15: "SETIAUSAHA BUKAN WARGA",
  16: "BUBAR",
  17: "SELESAI",
  18: "DISYORKAN",
  19: "TIDAK DISYORKAN",
  20: "BATAL",
  21: "MENUNGGU SYOR PEGAWAI PEMPROSES",
  22: "TEST DATA",
  23: "MENUNGGU MAKLUMBALAS",
  25: "PERTELINGKAHAN",
  26: "TARIK BALIK",
  27: "BATAL KHAS",
  28: "MENUNGGU PENGESAHAN JAWATANKUASA INDUK",
  29: "LULUS BERSYARAT",
  30: "DALAM TINDAKAN KIV",
  36: "KUIRI",
  37: "PINDAH",
  38: "MENUNGGU PENGESAHAN BAYARAN",
  39: "MENUNGGU KEPUTUSAN MENTERI",
  40: "MENUNGGU ULASAN",
  41: "MENUNGGU ULASAN AGENSI LUAR",
  42: "NOTIS MESYUARAT DIHANTAR",
  43: "INAKTIF",
  44: "BAYARAN GAGAL",
  "-001": "PADAM",
  "003": "BATAL",
  "002": "TOLAK",
  "001": "AKTIF",
  "004": "TARIK BALIK",
  "005": "BUBAR",
  "006": "BUBAR SEMENTARA",
  "007": "BARU",
  "008": "TIDAK AKTIF",
  "009": "DIBENARKAN",
  "010": "DILUPUTKAN",
  "998": "DIGUGURKAN",
  "999": "TIADA MAKLUMAT",
  "678": "TEST",
  "011": "PINDA NAMA",
  "101": "PERTELINGKAHAN SIASATAN",
  "102": "BATAL KHAS",
  "103": "TIDAK AKTIF_PINDAH",
  "104": "DALAM PERHATIAN",
  "306": "TIDAK AKTIF",
};

export const StatusPermohonan: Record<number | string, string> = {
  0: "-",
  /*1: "BELUM DIHANTAR",
  2: "MENUNGGU BAYARAN KAUNTER",
  3: "MENUNGGU BAYARAN ONLINE",
  4: "DIBENARKAN",
  5: "LUPUT",
  6: "MENUNGGU KEPUTUSAN",
  7: "KUIRI",
  8: "LULUS",
  9: "TOLAK",*/
  1: "BELUM DIHANTAR",
  2: "MENUNGGU KEPUTUSAN",
  3: "LULUS",
  4: "TOLAK",
  5: "MENUNGGU BAYARAN KAUNTER",
  6: "MENUNGGU BAYARAN ONLINE",
  7: "MAKLUMAT TIDAK LENGKAP",
  8: "LUPUT",
  9: "DIBENARKAN",
};

export const SebabRyuanEnum = {
  0: "-",
  1: "CancellationRegistration2A",
  2: "RejectionApplication7",
  3: "RejectionExemption9A1a_9A1b_9A1c",
  4: "RejectionApplication11",
  5: "RejectionApplicationBranch12",
  6: "CancellationOrganisation13",
  7: "Order_13A1",
  8: "Order_13A2",
  9: "RejectionApplicationAppointment144",
  10: "Order_145",
  11: "CancellationOrganisation16",
  12: "RejectionApplicationOffice49",
};

export const JenusJuruaudit = {
  L: "Bertauliah",
  D: "Dalaman",
};

const MeetingMethods = {
  BERSEMUKA: "8" as const,
  ATAS_TALIAN: "9" as const,
  HYBRID: "10" as const,
};

const MeetingMethodsEnum = {
  "8": `BERSEMUKA`,
  "9": `ATAS_TALIAN`,
  "10": `HYBRID`,
};

const MeetingContent = {
  YA: "ya" as const,
  TIDAK: "tidak" as const,
};

//staging malaysia is 152 development malaysia is 1
// const MALAYSIA = 152
const MALAYSIA = 152;

const ValidationTime = 3000;

const ValidationTimeTwo = 15 * 60;
const ValidationTimeTwoMin = 2 * 60;

const HideOrDisplayInherit = "inherit";
const HideOrDisplayFlex = "flex";

const AuditTrailType = [
  {
    id: 1,
    name: "BRANCH_EDIT",
    value: "CAWANGAN",
    label: "Kemaskini Cawangan",
  },
  { id: 2, name: "BRANCH_CREATE", value: "CAWANGAN", label: "Tambah Cawangan" },
  {
    id: 3,
    name: "BRANCH_APPROVAL_EDIT",
    value: "CAWANGAN",
    label: "Kemaskini Cawangan",
  },
  {
    id: 4,
    name: "BRANCH_APPROVAL_DECISION_EDIT",
    value: "CAWANGAN",
    label: "Kemaskini Cawangan",
  },
  {
    id: 5,
    name: "BRANCH_EXTENSION_EDIT",
    value: "CAWANGAN",
    label: "Edit Cawangan",
  },
  {
    id: 6,
    name: "MEETING_CREATE",
    value: "MESYUARAT",
    label: "Tambah Mesyuarat",
  },
  {
    id: 7,
    name: "MEETING_EDIT",
    value: "MESYUARAT",
    label: "Kemaskini Mesyuarat",
  },
  {
    id: 8,
    name: "AMENDMENT_CREATE",
    value: "PINDAAN",
    label: "Tambah Pindaan",
  },
  {
    id: 9,
    name: "AMENDMENT_EDIT",
    value: "PINDAAN",
    label: "Kemaskini Pindaan",
  },
  {
    id: 10,
    name: "SOCIETY_CREATE",
    value: "PERTUBUHAN",
    label: "Tambah Pertubuhan",
  },
  {
    id: 11,
    name: "SOCIETY_EDIT",
    value: "PERTUBUHAN",
    label: "Kemaskini Pertubuhan",
  },
  {
    id: 12,
    name: "NON_CITIZEN_COMMITTEE_CREATE",
    value: "AJK BUKAN WARGANEGARA",
    label: "Tambah Ajk Bukan Warganegara",
  },
  {
    id: 13,
    name: "NON_CITIZEN_COMMITTEE_EDIT",
    value: "AJK BUKAN WARGANEGARA",
    label: "Kemaskini Ajk Bukan Warganegara",
  },
  { id: 14, name: "COMMITTEE_CREATE", value: "AJK", label: "Tambah Ajk" },
  { id: 15, name: "COMMITTEE_EDIT", value: "AJK", label: "Kemaskini Ajk" },
  {
    id: 16,
    name: "BRANCH_COMMITTEE_CREATE",
    value: "AJK CAWANGAN",
    label: "Tambah Ajk Cawangan",
  },
  {
    id: 17,
    name: "TRUSTEE_HOLDER_CREATE",
    value: "PEMEGANG AMANAH",
    label: "Tambah Pemegang Amanah",
  },
  {
    id: 18,
    name: "TRUSTEE_HOLDER_UPDATE",
    value: "PEMEGANG AMANAH",
    label: "Kemaskini Pemegang Amanah",
  },
  {
    id: 19,
    name: "TRUSTEE_HOLDER_DEACTIVATE",
    value: "PEMEGANG AMANAH",
    label: "Nyahaktif Pemegang Amanah",
  },
  {
    id: 20,
    name: "PUBLIC_OFFICER_CREATE",
    value: "PEGAWAI AWAM",
    label: "Tambah Pegawai Awam",
  },
  {
    id: 21,
    name: "PUBLIC_OFFICER_UPDATE",
    value: "PEGAWAI AWAM",
    label: "Kemaskini Pegawai Awam",
  },
  {
    id: 22,
    name: "PUBLIC_OFFICER_DELETE",
    value: "PEGAWAI AWAM",
    label: "Padam Pegawai Awam",
  },
  {
    id: 23,
    name: "LIQUIDATION_CREATE",
    value: "PEMBUBARAN",
    label: "Tambah Pembubaran",
  },
  {
    id: 24,
    name: "LIQUIDATION_EDIT",
    value: "PEMBUBARAN",
    label: "Kemaskini Pembubaran",
  },
  { id: 25, name: "APPEAL_CREATE", value: "RAYUAN", label: "Tambah Rayuan" },
  { id: 26, name: "APPEAL_EDIT", value: "RAYUAN", label: "Kemaskini Rayuan" },
  {
    id: 27,
    name: "STATEMENT_CREATE",
    value: "PENYATA TAHUNAN",
    label: "Tambah Penyata Tahunan",
  },
  {
    id: 28,
    name: "STATEMENT_EDIT",
    value: "PENYATA TAHUNAN",
    label: "Kemaskini Penyata Tahunan",
  },
  {
    id: 29,
    name: "INTERNAL_SOCIETY_REGISTRATION_UPDATE_RO",
    value: "PERTUBUHAN (DALAMAN)",
    label: "Kemaskini Pendaftaran Masyarakat Dalaman Pengarah",
  },
  {
    id: 30,
    name: "INTERNAL_SOCIETY_REGISTRATION_DECISION",
    value: "PERTUBUHAN (DALAMAN)",
    label: "Keputusan Pendaftaran Masyarakat Dalaman",
  },
  {
    id: 31,
    name: "INTERNAL_SOCIETY_EXTERNAL_REVIEW_UPDATE_RO",
    value: "PERTUBUHAN (DALAMAN)",
    label: "Masyarakat Dalaman Semakan Luar Kemaskini",
  },
  {
    id: 32,
    name: "INTERNAL_SOCIETY_EXTERNAL_REVIEW_DECISION",
    value: "PERTUBUHAN (DALAMAN)",
    label: "Edit Rayuan",
  },
  {
    id: 33,
    name: "INTERNAL_CONSTITUTION_AMENDMENT_UPDATE_RO",
    value: "PINDAAN PERLEMBAGAAN (DALAMAN)",
    label: "Kemaskini Pindaan Perlembagaan (Dalaman)",
  },
  {
    id: 34,
    name: "INTERNAL_CONSTITUTION_AMENDMENT_DECISION",
    value: "PINDAAN PERLEMBAGAAN (DALAMAN)",
    label: "Keputusan Pindaan Perlembagaan (Dalaman)",
  },
  {
    id: 35,
    name: "INTERNAL_SOCIETY_LIQUIDATION_UPDATE_RO",
    value: "PEMBUBARAN (DALAMAN)",
    label: "Kemaskini Pembubaran (Dalaman)",
  },
  {
    id: 36,
    name: "INTERNAL_SOCIETY_LIQUIDATION_DECISION",
    value: "PEMBUBARAN (DALAMAN)",
    label: "Keputusan Pembubaran (Dalaman)",
  },
  {
    id: 37,
    name: "INTERNAL_BRANCH_LIQUIDATION_UPDATE_RO",
    value: "PEMBUBARAN CAWANGAN (DALAMAN)",
    label: "Kemaskini Pembubaran Cawangan (Dalaman)",
  },
  {
    id: 38,
    name: "INTERNAL_BRANCH_LIQUIDATION_DECISION",
    value: "PEMBUBARAN CAWANGAN (DALAMAN)",
    label: "Keputusan Pembubaran Cawangan (Dalaman)",
  },
  {
    id: 39,
    name: "INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_UPDATE_RO",
    value: "AJK BUKAN WARGANEGARA (DALAMAN)",
    label: "Kemaskini AJK Bukan Warganegara (Dalaman)",
  },
  {
    id: 40,
    name: "INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_DECISION",
    value: "AJK BUKAN WARGANEGARA (DALAMAN)",
    label: "Keputusan AJK Bukan Warganegara (Dalaman)",
  },
  {
    id: 41,
    name: "INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_UPDATE_RO",
    value: "AJK BUKAN WARGANEGARA CAWANGAN (DALAMAN)",
    label: "Kemaskini AJK Bukan Warganegara Cawangan (Dalaman)",
  },
  {
    id: 42,
    name: "INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_DECISION",
    value: "AJK BUKAN WARGANEGARA CAWANGAN (DALAMAN)",
    label: "Keputusan AJK Bukan Warganegara Cawangan (Dalaman)",
  },
  {
    id: 43,
    name: "INTERNAL_SOCIETY_SECRETARY_RENEWAL_UPDATE_RO",
    value: "PEMBAHARUAN SETIAUSAHA (DALAMAN)",
    label: "Kemaskini Pembaharuan Setiausaha (Dalaman)",
  },
  {
    id: 44,
    name: "INTERNAL_SOCIETY_SECRETARY_RENEWAL_DECISION",
    value: "PEMBAHARUAN SETIAUSAHA (DALAMAN)",
    label: "Keputusan Pembaharuan Setiausaha (Dalaman)",
  },
  {
    id: 45,
    name: "INTERNAL_BRANCH_SECRETARY_RENEWAL_UPDATE_RO",
    value: "PEMBAHARUAN SETIAUSAHA CAWANGAN (DALAMAN)",
    label: "Kemaskini Pembaharuan Setiausaha Cawangan (Dalaman)",
  },
  {
    id: 46,
    name: "INTERNAL_BRANCH_SECRETARY_RENEWAL_DECISION",
    value: "PEMBAHARUAN SETIAUSAHA CAWANGAN (DALAMAN)",
    label: "Keputusan Pembaharuan Setiausaha Cawangan (Dalaman)",
  },
  {
    id: 47,
    name: "INTERNAL_SOCIETY_PUBLIC_OFFICER_UPDATE_RO",
    value: "PEGAWAI AWAM (DALAMAN)",
    label: "Kemaskini Pegawai Awam (Dalaman)",
  },
  {
    id: 48,
    name: "INTERNAL_SOCIETY_PUBLIC_OFFICER_DECISION",
    value: "PEGAWAI AWAM (DALAMAN)",
    label: "Keputusan Pegawai Awam (Dalaman)",
  },
  {
    id: 49,
    name: "INTERNAL_SOCIETY_PROPERTY_OFFICER_UPDATE_RO",
    value: "PEGAWAI HARTA (DALAMAN)",
    label: "Kemaskini Pegawai Harta (Dalaman)",
  },
  {
    id: 50,
    name: "INTERNAL_SOCIETY_PROPERTY_OFFICER_DECISION",
    value: "PEGAWAI HARTA (DALAMAN)",
    label: "Keputusan Pegawai Harta (Dalaman)",
  },
  {
    id: 51,
    name: "INTERNAL_BRANCH_PUBLIC_OFFICER_UPDATE_RO",
    value: "PEGAWAI AWAM CAWANGAN (DALAMAN)",
    label: "Kemaskini Pegawai Awam Cawangan (Dalaman)",
  },
  {
    id: 52,
    name: "INTERNAL_BRANCH_PUBLIC_OFFICER_DECISION",
    value: "PEGAWAI AWAM CAWANGAN (DALAMAN)",
    label: "Keputusan Pegawai Awam Cawangan (Dalaman)",
  },
  {
    id: 53,
    name: "INTERNAL_BRANCH_PROPERTY_OFFICER_UPDATE_RO",
    value: "PEGAWAI HARTA CAWANGAN (DALAMAN)",
    label: "Kemaskini Pegawai Harta Cawangan (Dalaman)",
  },
  {
    id: 54,
    name: "INTERNAL_BRANCH_PROPERTY_OFFICER_DECISION",
    value: "PEGAWAI HARTA CAWANGAN (DALAMAN)",
    label: "Keputusan Pegawai Harta Cawangan (Dalaman)",
  },
  {
    id: 55,
    name: "INTERNAL_BRANCH_EXTENSION_TIME_UPDATE_RO",
    value: "LANJUTAN MASA CAWANGAN (DALAMAN)",
    label: "Kemaskini Lanjutan Masa Cawangan (Dalaman)",
  },
  {
    id: 56,
    name: "INTERNAL_BRANCH_EXTENSION_TIME_DECISION",
    value: "LANJUTAN MASA CAWANGAN (DALAMAN)",
    label: "Keputusan Lanjutan Masa Cawangan (Dalaman)",
  },
  {
    id: 57,
    name: "INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_UPDATE_RO",
    value: "PINDAAN CAWANGAN (DALAMAN)",
    label: "Kemaskini Pindaan Cawangan (Dalaman)",
  },
  {
    id: 58,
    name: "INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_DECISION",
    value: "PINDAAN CAWANGAN (DALAMAN)",
    label: "Keputusan Pindaan Cawangan (Dalaman)",
  },
  {
    id: 59,
    name: "ROLE_CATEGORY_SEARCH",
    value: "PENGURUSAN PERANAN",
    label: "Cari Pengurusan Peranan",
  },
  {
    id: 60,
    name: "ROLE_CATEGORY_ADD",
    value: "PENGURUSAN PERANAN",
    label: "Tambah Pengurusan Peranan",
  },
  {
    id: 61,
    name: "ROLE_CATEGORY_UPDATE",
    value: "PENGURUSAN PERANAN",
    label: "Kemaskini Pengurusan Peranan",
  },
  {
    id: 62,
    name: "ROLE_CATEGORY_VIEW",
    value: "PENGURUSAN PERANAN",
    label: "Lihat Pengurusan Peranan",
  },
  {
    id: 63,
    name: "JPPM_USER_SEARCH",
    value: "PENGURUSAN PENGGUNA",
    label: "Cari Pengurusan Pengguna",
  },
  {
    id: 64,
    name: "JPPM_USER_VIEW",
    value: "PENGURUSAN PENGGUNA",
    label: "Lihat Pengurusan Pengguna",
  },
  {
    id: 65,
    name: "JPPM_USER_ADD",
    value: "PENGURUSAN PENGGUNA",
    label: "Tambah Pengurusan Pengguna",
  },
  {
    id: 66,
    name: "JPPM_USER_UPDATE",
    value: "PENGURUSAN PENGGUNA",
    label: "Kemaskini Pengurusan Pengguna",
  },
  {
    id: 67,
    name: "JPPM_USER_APPROVAL",
    value: "PENGURUSAN PENGGUNA",
    label: "Kelulusan Pengurusan Pengguna",
  },
  {
    id: 68,
    name: "EXTERNAL_USER_SEARCH",
    value: "PENGURUSAN PENGGUNA",
    label: "Cari Pengurusan Pengguna",
  },
  {
    id: 69,
    name: "EXTERNAL_USER_UPDATE",
    value: "PENGURUSAN PENGGUNA",
    label: "Kemaskini Pengurusan Pengguna",
  },
  {
    id: 70,
    name: "EXTERNAL_USER_DEACTIVATE",
    value: "PENGURUSAN PENGGUNA",
    label: "Nyahaktif Pengurusan Pengguna",
  },
  {
    id: 71,
    name: "EXTERNAL_USER_VIEW",
    value: "PENGURUSAN PENGGUNA",
    label: "Lihat Pengurusan Pengguna",
  },
  {
    id: 72,
    name: "EVENT_DETAILS_CREATE",
    value: "TAKWIM",
    label: "Tambah Takwim",
  },
  {
    id: 73,
    name: "EVENT_DETAILS_UPDATE",
    value: "TAKWIM",
    label: "Kemaskini Takwim",
  },
  {
    id: 74,
    name: "EVENT_DETAILS_PUBLISH",
    value: "TAKWIM",
    label: "Terbitkan Takwim",
  },
  {
    id: 75,
    name: "EVENT_DETAILS_DELETE",
    value: "TAKWIM",
    label: "Padam Takwim",
  },
  {
    id: 76,
    name: "EVENT_PARTICIPANT_CANCEL",
    value: "TAKWIM",
    label: "Batalkan Pendaftaran Peserta",
  },
  {
    id: 77,
    name: "EVENT_DETAILS_UNPUBLISH",
    value: "TAKWIM",
    label: "Nyahterbit Takwim",
  },
];

const AuditTrailEnum: Record<string, string> = {
  BRANCH_EDIT: "Kemaskini Cawangan",
  BRANCH_CREATE: "Tambah Cawangan",
  BRANCH_APPROVAL_EDIT: "Kemaskini Cawangan",
  BRANCH_APPROVAL_DECISION_EDIT: "Kemaskini Cawangan",
  BRANCH_EXTENSION_EDIT: "Edit Cawangan",

  MEETING_CREATE: "Tambah Mesyuarat",
  MEETING_EDIT: "Kemaskini Mesyuarat",

  AMENDMENT_CREATE: "Tambah Pindaan",
  AMENDMENT_EDIT: "Kemaskini Pindaan",

  SOCIETY_CREATE: "Tambah Pertubuhan",
  SOCIETY_EDIT: "Kemaskini Pertubuhan",

  NON_CITIZEN_COMMITTEE_CREATE: "Tambah Ajk Bukan Warganegara",
  NON_CITIZEN_COMMITTEE_EDIT: "Kemaskini Ajk Bukan Warganegara",
  COMMITTEE_CREATE: "Tambah Ajk",
  COMMITTEE_EDIT: "Kemaskini Ajk",
  BRANCH_COMMITTEE_CREATE: "Tambah Ajk Cawangan",

  TRUSTEE_HOLDER_CREATE: "Tambah Pemegang Amanah",
  TRUSTEE_HOLDER_UPDATE: "Kemaskini Pemegang Amanah",
  TRUSTEE_HOLDER_DEACTIVATE: "Nyahaktif Pemegang Amanah",

  PUBLIC_OFFICER_CREATE: "Tambah Pegawai Awam",
  PUBLIC_OFFICER_UPDATE: "Kemaskini Pegawai Awam",
  PUBLIC_OFFICER_DELETE: "Padam Pegawai Awam",

  LIQUIDATION_CREATE: "Tambah Pembubaran",
  LIQUIDATION_EDIT: "Kemaskini Pembubaran",

  APPEAL_CREATE: "Tambah Rayuan",
  APPEAL_EDIT: "Kemaskini Rayuan",

  STATEMENT_CREATE: "Tambah Penyata Tahunan",
  STATEMENT_EDIT: "Kemaskini Penyata Tahunan",

  INTERNAL_SOCIETY_REGISTRATION_UPDATE_RO:
    "Kemaskini Pendaftaran Masyarakat Dalaman Pengarah",
  INTERNAL_SOCIETY_REGISTRATION_DECISION:
    "Keputusan Pendaftaran Masyarakat Dalaman",
  INTERNAL_SOCIETY_EXTERNAL_REVIEW_UPDATE_RO:
    "Masyarakat Dalaman Semakan Luar Kemaskini",
  INTERNAL_SOCIETY_EXTERNAL_REVIEW_DECISION: "Edit Rayuan",
  INTERNAL_CONSTITUTION_AMENDMENT_UPDATE_RO:
    "Kemaskini Pindaan Perlembagaan (Dalaman)",
  INTERNAL_CONSTITUTION_AMENDMENT_DECISION:
    "Keputusan Pindaan Perlembagaan (Dalaman)",
  INTERNAL_SOCIETY_LIQUIDATION_UPDATE_RO: "Kemaskini Pembubaran (Dalaman)",
  INTERNAL_SOCIETY_LIQUIDATION_DECISION: "Keputusan Pembubaran (Dalaman)",
  INTERNAL_BRANCH_LIQUIDATION_UPDATE_RO:
    "Kemaskini Pembubaran Cawangan (Dalaman)",
  INTERNAL_BRANCH_LIQUIDATION_DECISION:
    "Keputusan Pembubaran Cawangan (Dalaman)",
  INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_UPDATE_RO:
    "Kemaskini AJK Bukan Warganegara (Dalaman)",
  INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_DECISION:
    "Keputusan AJK Bukan Warganegara (Dalaman)",
  INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_UPDATE_RO:
    "Kemaskini AJK Bukan Warganegara Cawangan (Dalaman)",
  INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_DECISION:
    "Keputusan AJK Bukan Warganegara Cawangan (Dalaman)",
  INTERNAL_SOCIETY_SECRETARY_RENEWAL_UPDATE_RO:
    "Kemaskini Pembaharuan Setiausaha (Dalaman)",
  INTERNAL_SOCIETY_SECRETARY_RENEWAL_DECISION:
    "Keputusan Pembaharuan Setiausaha (Dalaman)",
  INTERNAL_BRANCH_SECRETARY_RENEWAL_UPDATE_RO:
    "Kemaskini Pembaharuan Setiausaha Cawangan (Dalaman)",
  INTERNAL_BRANCH_SECRETARY_RENEWAL_DECISION:
    "Keputusan Pembaharuan Setiausaha Cawangan (Dalaman)",
  INTERNAL_SOCIETY_PUBLIC_OFFICER_UPDATE_RO: "Kemaskini Pegawai Awam (Dalaman)",
  INTERNAL_SOCIETY_PUBLIC_OFFICER_DECISION: "Keputusan Pegawai Awam (Dalaman)",
  INTERNAL_SOCIETY_PROPERTY_OFFICER_UPDATE_RO:
    "Kemaskini Pegawai Harta (Dalaman)",
  INTERNAL_SOCIETY_PROPERTY_OFFICER_DECISION:
    "Keputusan Pegawai Harta (Dalaman)",
  INTERNAL_BRANCH_PUBLIC_OFFICER_UPDATE_RO:
    "Kemaskini Pegawai Awam Cawangan (Dalaman)",
  INTERNAL_BRANCH_PUBLIC_OFFICER_DECISION:
    "Keputusan Pegawai Awam Cawangan (Dalaman)",
  INTERNAL_BRANCH_PROPERTY_OFFICER_UPDATE_RO:
    "Kemaskini Pegawai Harta Cawangan (Dalaman)",
  INTERNAL_BRANCH_PROPERTY_OFFICER_DECISION:
    "Keputusan Pegawai Harta Cawangan (Dalaman)",
  INTERNAL_BRANCH_EXTENSION_TIME_UPDATE_RO:
    "Kemaskini Lanjutan Masa Cawangan (Dalaman)",
  INTERNAL_BRANCH_EXTENSION_TIME_DECISION:
    "Keputusan Lanjutan Masa Cawangan (Dalaman)",
  INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_UPDATE_RO:
    "Kemaskini Pindaan Cawangan (Dalaman)",
  INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_DECISION:
    "Keputusan Pindaan Cawangan (Dalaman)",

  ROLE_CATEGORY_SEARCH: "Cari Pengurusan Peranan",
  ROLE_CATEGORY_ADD: "Tambah Pengurusan Peranan",
  ROLE_CATEGORY_UPDATE: "Kemaskini Pengurusan Peranan",
  ROLE_CATEGORY_VIEW: "Lihat Pengurusan Peranan",

  JPPM_USER_SEARCH: "Cari Pengurusan Pengguna",
  JPPM_USER_VIEW: "Lihat Pengurusan Pengguna",
  JPPM_USER_ADD: "Tambah Pengurusan Pengguna",
  JPPM_USER_UPDATE: "Kemaskini Pengurusan Pengguna",
  JPPM_USER_APPROVAL: "Kelulusan Pengurusan Pengguna",
  EXTERNAL_USER_SEARCH: "Cari Pengurusan Pengguna",
  EXTERNAL_USER_UPDATE: "Kemaskini Pengurusan Pengguna",
  EXTERNAL_USER_DEACTIVATE: "Nyahaktif Pengurusan Pengguna",
  EXTERNAL_USER_VIEW: "Lihat Pengurusan Pengguna",
  EVENT_DETAILS_CREATE: "Tambah Takwim",
  EVENT_DETAILS_UPDATE: "Kemaskini Takwim",
  EVENT_DETAILS_PUBLISH: "Terbitkan Takwim",
  EVENT_DETAILS_DELETE: "Padam Takwim",
  EVENT_PARTICIPANT_CANCEL: "Batalkan Pendaftaran Peserta",
  EVENT_DETAILS_UNPUBLISH: "Nyahterbit Takwim",
};

const ListGelaran = [
  { value: "1", label: "BRIGIDIER JENERAL DATO'" },
  { value: "2", label: "CIK" },
  { value: "3", label: "DATIN" },
  { value: "4", label: "DATIN AMAR" },
  { value: "5", label: "DATIN DR." },
  { value: "6", label: "DATIN PADUKA" },
  { value: "7", label: "DATIN PATINGGI" },
  { value: "8", label: "DATIN PROFESSOR" },
  { value: "9", label: "DATIN PROFESSOR DR." },
  { value: "10", label: "DATIN SETIA" },
  { value: "11", label: "DATIN SRI" },
  { value: "12", label: "DATIN SRI CEMPAKA" },
  { value: "13", label: "DATIN SRI DR." },
  { value: "14", label: "DATO'" },
  { value: "15", label: "DATO' DR." },
  { value: "16", label: "DATO' IR DR." },
  { value: "17", label: "DATO' IR." },
  { value: "18", label: "DATO' PADUKA DR." },
  { value: "19", label: "DATO' PROFESSOR MADYA DR." },
  { value: "20", label: "DATO' SENARA MUDA" },
  { value: "21", label: "DATO' SERI" },
  { value: "22", label: "DATO' SRI" },
  { value: "23", label: "DATU" },
  { value: "24", label: "DATUK" },
  { value: "25", label: "DATUK AMAR" },
  { value: "26", label: "DATUK BENTARA LUAR" },
  { value: "27", label: "DATUK BENTARA RAJA" },
  { value: "28", label: "DATUK DR." },
  { value: "29", label: "DATUK IR." },
  { value: "30", label: "DATUK PATINGGI" },
  { value: "31", label: "DATUK PROFESSOR" },
  { value: "32", label: "DATUK PROFESSOR DR." },
  { value: "33", label: "DATUK SETIA" },
  { value: "34", label: "DATUK SETIA WANGSA" },
  { value: "35", label: "DATUK SRI" },
  { value: "36", label: "DATUK SRI AMAR DIRAJA" },
  { value: "37", label: "DATUK SRI DR." },
  { value: "38", label: "DATUK WIRA" },
  { value: "39", label: "DATUK WIRA JAYA" },
  { value: "40", label: "DOKTOR" },
  { value: "41", label: "DULI YANG MAHA MULIA" },
  { value: "42", label: "ENCIK" },
  { value: "43", label: "HAJI" },
  { value: "44", label: "HAJJAH" },
  { value: "45", label: "IR" },
  { value: "46", label: "IR DR." },
  { value: "47", label: "JENERAL" },
  { value: "48", label: "KE BAWAH DULI YANG TERAMAT MULIA" },
  { value: "49", label: "KEBAWAH DULI YANG MAHA MULIA" },
  { value: "50", label: "KOLONEL" },
  { value: "51", label: "LT. KOLONEL" },
  { value: "52", label: "MAJOR" },
  { value: "53", label: "PROFESSOR" },
  { value: "54", label: "PROFESSOR DATO' DR." },
  { value: "55", label: "PROFESSOR DIRAJA" },
  { value: "56", label: "PROFESSOR DR." },
  { value: "57", label: "PROFESSOR MADYA" },
  { value: "58", label: "PROFESSOR MADYA DR." },
  { value: "59", label: "PUAN" },
  { value: "60", label: "PUAN SRI" },
  { value: "61", label: "PUAN SRI DATIN" },
  { value: "62", label: "PUAN SRI DATIN PROFESSOR" },
  { value: "63", label: "PUAN SRI DR" },
  { value: "64", label: "PUAN SRI UTAMA" },
  { value: "65", label: "TAN SRI" },
  { value: "66", label: "TAN SRI DATO'" },
  { value: "67", label: "TAN SRI DATO' DR." },
  { value: "68", label: "TAN SRI DATO' IR." },
  { value: "69", label: "TAN SRI DATUK" },
  { value: "70", label: "TAN SRI DATUK DR." },
  { value: "71", label: "TAN SRI DATUK PROFESSOR" },
  { value: "72", label: "TAN SRI DATUK PROFESSOR DR." },
  { value: "73", label: "TAN SRI DATUK SERI" },
  { value: "74", label: "TAN SRI DR." },
  { value: "75", label: "TAN SRI GENERAL" },
  { value: "76", label: "TAN SRI PROFESSOR" },
  { value: "77", label: "TO' PUAN" },
  { value: "78", label: "TOH PUAN" },
  { value: "79", label: "TUAN" },
  { value: "80", label: "TUN" },
  { value: "81", label: "YANG AMAT MULIA" },
  { value: "82", label: "YANG MULIA" },
];

const ConstitutionType = {
  None: [0, ""],
  IndukNGO: [1, "Perlembagaan Induk NGO"],
  CawanganNGO: [2, "Perlembagaan Bercawangan Semua NGO"],
  IndukAgama: [3, "Perlembagaan Induk Keagamaan"],
  CawanganAgama: [4, "Perlembagaan Bercawangan Keagamaan"],
  FaedahBersama: [5, "Perlembagaan Faedah Bersama"],
  Bebas: [6, "Perlembagaan Bebas"],
  CawanganBebas: [7, "Perlembagaan Bebas Bercawangan"],
};

/**
 * @todo add translateKey to remaining possible values
 */
const MeetingTypeOption = [
  {
    value: 1,
    label: "Mesyuarat Penubuhan",
    translateKey: "establishmentMeeting",
  },
  {
    value: 2,
    label: "Mesyuarat Agung",
    translateKey: "generalMeeting",
  },
  {
    value: 3,
    label: "Mesyuarat Agung Luar Biasa / Khas",
    translateKey: "ExtraordinaryGeneralMeeting",
  },
  {
    value: 4,
    label: "Mesyuarat AJK",
    translateKey: "committeeMeeting",
  },
  {
    value: 5,
    label: "Mesyuarat Agung Khas (Pembubaran)",
  },
];

const IdTypes = [
  {
    label: "mykad",
    value: "1",
  },
  {
    label: "Polis",
    value: "2",
  },
  {
    label: "Nombor Tentera",
    value: "3",
  },
  {
    label: "MyPR",
    value: "4",
  },
  {
    label: "Passport",
    value: "5",
  },
  {
    label: "nomborVisa",
    value: "6",
  },
  {
    label: "nomborPermit",
    value: "7",
  },
];

const OrganisationPositions = [
  { label: "pengerusi", value: 1, rank: 1 },
  { label: "presiden", value: 17, rank: 1 },
  { label: "pengarah", value: 18, rank: 1 },
  { label: "yangDipertua", value: 41, rank: 1 },
  { label: "pengerusiCawangan", value: 31, rank: 1 },
  { label: "presidenCawangan", value: 32, rank: 1 },
  { label: "pengarahCawangan", value: 33, rank: 1 },
  { label: "yangDipertuaCawangan", value: 44, rank: 1 },
  { label: "timbalanPengerusi", value: 2, rank: 2 },
  { label: "timbalanPresiden", value: 19, rank: 2 },
  { label: "timbalanPengarah", value: 20, rank: 2 },
  { label: "timbalanYangDipertua", value: 42, rank: 2 },
  { label: "timbalanPengerusiCawangan", value: 53, rank: 2 },
  { label: "timbalanPresidenCawangan", value: 54, rank: 2 },
  { label: "timbalanYangDipertuaCawangan", value: 55, rank: 2 },
  { label: "naibPengerusi", value: 8, rank: 3 },
  { label: "naibPresiden", value: 22, rank: 3 },
  { label: "naibPengarah", value: 23, rank: 3 },
  { label: "naibYangDipertua", value: 43, rank: 3 },
  { label: "naibPengerusiCawangan", value: 34, rank: 3 },
  { label: "naibPresidenCawangan", value: 35, rank: 3 },
  { label: "naibPengarahCawangan", value: 36, rank: 3 },
  { label: "naibYangDipertuaCawangan", value: 45, rank: 3 },
  { label: "setiausaha", value: 3, rank: 4 },
  { label: "setiausahaAgung", value: 21, rank: 4 },
  { label: "setiausahaCawangan", value: 37, rank: 4 },
  { label: "penolongSetiausaha", value: 4, rank: 5 },
  { label: "penolongSetiausahaAgung", value: 24, rank: 5 },
  { label: "penolongSetiausahaCawangan", value: 38, rank: 5 },
  { label: "bendahari", value: 5, rank: 6 },
  { label: "bendahariAgung", value: 25, rank: 6 },
  { label: "ketuaBendahari", value: 26, rank: 6 },
  { label: "bendahariKehormat", value: 27, rank: 6 },
  { label: "bendahariCawangan", value: 39, rank: 6 },
  { label: "bendahariKehormatCawangan", value: 46, rank: 6 },
  { label: "penolongBendahari", value: 9, rank: 7 },
  { label: "penolongBendahariAgung", value: 28, rank: 7 },
  { label: "penolongKetuaBendahari", value: 29, rank: 7 },
  { label: "penolongBendahariKehormat", value: 30, rank: 7 },
  { label: "penolongBendahariCawangan", value: 40, rank: 7 },
  { label: "penolongBendahariKehormatCawangan", value: 47, rank: 7 },
  { label: "ahliJawatankuasaBiasa", value: 6, rank: 8 },
  { label: "ahliJawatankuasaBiasaCawangan", value: 48, rank: 8 },
  { label: "penggunaBiasa", value: 7, rank: 9 },
  { label: "ahliBiasa", value: 10, rank: 10 },
  { label: "perwakilanNegeri", value: 11, rank: 11 },
  { label: "lainLain", value: 12, rank: 12 },
  { label: "biroEkonomi", value: 13, rank: 13 },
  { label: "biroKebajikan", value: 14, rank: 14 },
  { label: "biroSukanDanSosial", value: 15, rank: 15 },
  { label: "biroAgama", value: 16, rank: 16 },
  { label: "ahliBersekutu", value: 49, rank: 10 },
  { label: "ahliKehormat", value: 50, rank: 10 },
  { label: "seumurHidup", value: 51, rank: 10 },
  { label: "ahliRemaja", value: 52, rank: 10 },
];

const FeedbackComplainType = [
  {
    label: "Aduan ralat sistem",
    value: 1,
  },
  {
    label: "Pertanyaan Pengurusan Pertubuhan",
    value: 2,
  },
];

const FeedbackComplainType_New = [
  {
    label: "jenis_MaklumBalas",
    value: "MAKLUM_BALAS",
  },
  {
    label: "aduan",
    value: "ADUAN",
  },
  {
    label: "jenis_IsuSistem",
    value: "ISU_SISTEM",
  },
];

const FeedbackLevel = [
  {
    label: "Tinggi",
    value: 1,
  },
  {
    label: "Sederhana",
    value: 2,
  },
  {
    label: "Rendah",
    value: 3,
  },
];

const FeedbackStatus = [
  {
    label: "NEW",
    value: 1,
  },
  {
    label: "DALAM_TINDAKAN",
    value: 2,
  },
  {
    label: "COMPLETE",
    value: 3,
  },
];

const OccupationList = [
  { value: "no", label: "notWorking" },
  { value: "yes", label: "working" },
];

const OrganisationPositionLabel: Record<number, string> = {
  1: "pengerusi", //
  17: "presiden",
  18: "pengarah",
  31: "pengerusiCawangan",
  32: "presidenCawangan",
  33: "pengarahCawangan",
  2: "timbalanPengerusi",
  19: "timbalanPresiden",
  20: "timbalanPengarah",
  8: "naibPengerusi",
  22: "naibPresiden",
  23: "naibPengarah",
  34: "naibPengerusiCawangan",
  35: "naibPresidenCawangan",
  36: "naibPengarahCawangan",
  3: "setiausaha",
  21: "setiausahaAgung",
  37: "setiausahaCawangan",
  4: "penolongSetiausaha", //
  24: "penolongSetiausahaAgung",
  38: "penolongSetiausahaCawangan",
  5: "bendahari",
  25: "bendahariAgung",
  26: "ketuaBendahari",
  27: "bendahariKehormat",
  39: "bendahariCawangan",
  9: "penolongBendahari",
  28: "penolongBendahariAgung",
  29: "penolongKetuaBendahari",
  30: "penolongBendahariKehormat",
  40: "penolongBendahariCawangan",
  6: "ahliJawatanBiasa", //
  7: "penggunaBiasa",
  10: "ahliBiasa",
  11: "perwakilanNegeri",
  12: "lainLain",
  13: "biroEkonomi",
  14: "biroKebajikan",
  15: "biroSukanDanSosial",
  16: "biroAgama",
};

const ListUserStatus = [
  { value: 1, label: "userStatusActive" },
  { value: 2, label: "userStatusPending" },
  { value: 3, label: "userStatusInactive" },
  { value: 4, label: "userStatusDeactivated" },
];

const ListCategoryRoleStatus = [
  { value: 1, label: "active" },
  { value: "0", label: "inactive" },
];

const DecisionOptions = [
  {
    value: "Lulus",
    label: "lulus",
  },
  {
    value: "Tolak",
    label: "tolak",
  },
];

const DecisionOptionsCode = (intl: TFunction<"translation", undefined>) => [
  {
    value: 3,
    label: intl("lulus"),
  },
  {
    value: 4,
    label: intl("tolak"),
  },
  {
    value: 36,
    label: intl("kuiri"),
  },
];

const DecisionOptionsCodeRayuan = (
  intl: TFunction<"translation", undefined>
) => [
  {
    value: 3,
    label: intl("lulus"),
  },
  {
    value: 4,
    label: intl("tolak"),
  },
  { value: 29, label: intl("LULUS_BERSYARAT") },
];

const BranchStatusList = [
  { label: "BATAL", value: "003" },
  { label: "TOLAK", value: "002" },
  { label: "AKTIF", value: "001" },
  { label: "TARIK_BALIK", value: "004" },
  { label: "BUBAR", value: "005" },
  { label: "BUBAR_SEMENTARA", value: "006" },
  { label: "BARU", value: "007" },
  { label: "TIDAK_AKTIF", value: "008" },
  { label: "DIBENARKAN", value: "009" },
  { label: "DILUPUTKAN", value: "010" },
  { label: "DIGUGURKAN", value: "998" },
  { label: "TIADA_MAKLUMAT", value: "999" },
  { label: "TEST", value: "678" },
  { label: "PINDA_NAMA", value: "011" },
  { label: "PERTELINGKAHAN_SIASATAN", value: "101" },
  { label: "BATAL_KHAS", value: "102" },
  { label: "TIDAK_AKTIF_PINDAH", value: "103" },
  { label: "DALAM_PERHATIAN", value: "104" },
  { label: "TIDAK_AKTIF_2", value: "306" },
  { label: "TIDAK BERKENAAN", value: null },
];

const StatusCodeList = [
  { label: "BATAL", value: "003" },
  { label: "TOLAK", value: "002" },
  { label: "AKTIF", value: "001" },
  { label: "TARIK_BALIK", value: "004" },
  { label: "BUBAR", value: "005" },
  { label: "BUBAR_SEMENTARA", value: "006" },
  { label: "BARU", value: "007" },
  { label: "TIDAK_AKTIF", value: "008" },
  { label: "DIBENARKAN", value: "009" },
  { label: "DILUPUTKAN", value: "010" },
  { label: "DIGUGURKAN", value: "998" },
  { label: "TIADA_MAKLUMAT", value: "999" },
  { label: "TEST", value: "678" },
  { label: "PINDA_NAMA", value: "011" },
  { label: "PERTELINGKAHAN_SIASATAN", value: "101" },
  { label: "BATAL_KHAS", value: "102" },
  { label: "TIDAK_AKTIF_PINDAH", value: "103" },
  { label: "DALAM_PERHATIAN", value: "104" },
  { label: "TIDAK_AKTIF1", value: "306" },
  { label: "PADAM", value: "-001" },
];
/**
 * @deprecated this enum will be replaced by {@link StatusCodeList} in order to sync on BE side.
 * please use {@link StatusCodeList} instead.
 */
const SocietyStatusList = StatusCodeList;

const NotificationColors = [
  "#4880FF",
  "#FF0000",
  "#FFD100",
  "#FE8888",
  "var(--primary-color)",
];

const ListGender = [
  {
    value: "L",
    label: "male",
  },
  {
    value: "P",
    label: "female",
  },
];

const ROApprovalType = {
  SOCIETY_REGISTRATION: {
    code: "SOCIETY_REGISTRATION",
    type: "SOCIETY",
    description: "Society Registration",
  },
  SOCIETY_LIQUIDATION: {
    code: "SOCIETY_LIQUIDATION",
    type: "LIQUIDATION",
    description: "Society Liquidation",
  },
  BRANCH_LIQUIDATION: {
    code: "BRANCH_LIQUIDATION",
    type: "LIQUIDATION",
    description: "Branch Liquidation",
  },
  SOCIETY_AMENDMENT: {
    code: "SOCIETY_AMENDMENT",
    type: "AMENDMENT",
    description: "Society Amendment",
  },
  SOCIETY_APPEAL: {
    code: "SOCIETY_APPEAL",
    type: "APPEAL",
    description: "Society Appeal",
  },
  SOCIETY_NEW_SECRETARY: {
    code: "SOCIETY_NEW_SECRETARY",
    type: "NEW_SECRETARY",
    description: "Society New Secretary",
  },
  SOCIETY_NON_CITIZEN: {
    code: "SOCIETY_NON_CITIZEN",
    type: "NON_CITIZEN",
    description: "Society Non Citizen",
  },
  BRANCH_REGISTRATION: {
    code: "BRANCH_REGISTRATION",
    type: "BRANCH",
    description: "Branch Registration",
  },
};

const PaymentTypeList = [
  { id: "RESIT001", value: "Pendaftaran Pertubuhan Baru (Pembayaran ONLINE)" },
  { id: "RESIT001", value: "Pendaftaran Pertubuhan Baru (Pembayaran KAUNTER)" },
  { id: "RESIT002", value: "Pendaftaran Cawangan (Pembayaran ONLINE)" },
  { id: "RESIT002", value: "Pendaftaran Cawangan (Pembayaran KAUNTER)" },
  { id: "RESIT003", value: "Pindaan Undang-Undang (Pembayaran ONLINE)" },
  { id: "RESIT003", value: "Pindaan Undang-Undang (Pembayaran KAUNTER)" },
  { id: "RESIT004", value: "Pendaftaran Pegawai Harta (Pembayaran ONLINE)" },
  { id: "RESIT004", value: "Pendaftaran Pegawai Harta (Pembayaran KAUNTER)" },
  { id: "RESIT005", value: "Pendaftaran Pegawai Awam (Pembayaran ONLINE)" },
  { id: "RESIT005", value: "Pendaftaran Pegawai Awam (Pembayaran KAUNTER)" },
  { id: "RESIT006", value: "Carian Maklumat (Pembayaran ONLINE)" },
  { id: "RESIT006", value: "Carian Maklumat (Pembayaran KAUNTER)" },
  { id: "RESIT007", value: "Rayuan (Pembayaran ONLINE)" },
  { id: "RESIT007", value: "Rayuan (Pembayaran KAUNTER)" },
  { id: "RESIT009", value: "Pindaan Cawangan (Pembayaran ONLINE)" },
  { id: "RESIT009", value: "Pindaan Cawangan (Pembayaran KAUNTER)" },
];

const ApplicationStatusList = [
  { id: -1, value: "PADAM" },
  { id: 0, value: "-" },
  { id: 1, value: "BELUM_DIHANTAR" },
  { id: 2, value: "MENUNGGU_KEPUTUSAN" },
  { id: 3, value: "LULUS" },
  { id: 4, value: "TOLAK" },
  { id: 5, value: "MENUNGGU_BAYARAN_KAUNTER" },
  { id: 6, value: "MENUNGGU_BAYARAN_ONLINE" },
  { id: 7, value: "MAKLUMAT_TIDAK_LENGKAP" },
  { id: 8, value: "LUPUT" },
  { id: 9, value: "DIBENARKAN" },
  { id: 10, value: "TIDAK_DIBENARKAN" },
  { id: 11, value: "AKTIF" },
  { id: 12, value: "MENUNGGU_PENGAKTIFAN_CAWANGAN" },
  { id: 13, value: "TIADA_MAKLUMAT_MIGRASI" },
  { id: 14, value: "TAMAT_TEMPOH_CARIAN" },
  { id: 15, value: "SETIAUSAHA_BUKAN_WARGA" },
  { id: 16, value: "BUBAR" },
  { id: 17, value: "SELESAI" },
  { id: 18, value: "DISYORKAN" },
  { id: 19, value: "TIDAK_DISYORKAN" },
  { id: 20, value: "BATAL" },
  { id: 21, value: "MENUNGGU_SYOR_PEGAWAI_PEMPROSES" },
  { id: 22, value: "TEST_DATA" },
  { id: 23, value: "MENUNGGU_MAKLUMBALAS" },
  { id: 25, value: "PERTELINGKAHAN" },
  { id: 26, value: "TARIK_BALIK" },
  { id: 27, value: "BATAL_KHAS" },
  { id: 28, value: "MENUNGGU_PENGESAHAN_JAWATANKUASA_INDUK" },
  { id: 29, value: "LULUS_BERSYARAT" },
  { id: 30, value: "DALAM_TINDAKAN_KIV" },
  { id: 36, value: "KUIRI" },
  { id: 37, value: "PINDAH" },
  { id: 38, value: "MENUNGGU_PENGESAHAN_BAYARAN" },
  { id: 39, value: "MENUNGGU_KEPUTUSAN_MENTERI" },
  { id: 40, value: "MENUNGGU_ULASAN" },
  { id: 41, value: "MENUNGGU_ULASAN_AGENSI_LUAR" },
  { id: 42, value: "NOTIS_MESYUARAT_DIHANTAR" },
  { id: 43, value: "INAKTIF" },
  { id: 44, value: "BAYARAN_GAGAL" },
  { id: "-001", value: "PADAM" },
  { id: "003", value: "BATAL" },
  { id: "002", value: "TOLAK" },
  { id: "001", value: "AKTIF" },
  { id: "004", value: "TARIK_BALIK" },
  { id: "005", value: "BUBAR" },
  { id: "006", value: "BUBAR_SEMENTARA" },
  { id: "007", value: "BARU" },
  { id: "008", value: "TIDAK_AKTIF" },
  { id: "009", value: "DIBENARKAN" },
  { id: "010", value: "DILUPUSKAN" },
  { id: "998", value: "DIGUGURKAN" },
  { id: "999", value: "TIADA_MAKLUMAT" },
  { id: "678", value: "TEST" },
  { id: "011", value: "PINDA_NAMA" },
  { id: "101", value: "PERTELINGKAHAN_SIASATAN" },
  { id: "102", value: "BATAL_KHAS" },
  { id: "103", value: "TIDAK_AKTIF_PINDAH" },
  { id: "104", value: "DALAM_PERHATIAN" },
  { id: "306", value: "TIDAK_AKTIF" },
];

export const GenderType = [
  {
    identName: "LELAKI",
    code: "L",
    description: "MALE",
    translateKey: "male",
  },
  {
    identName: "PEREMPUAN",
    code: "P",
    description: "FEMALE",
    translateKey: "female",
  },
];

const MiniApplicationStatus = [
  {
    value: 3,
    label: "LULUS",
  },
  {
    value: 4,
    label: "TOLAK",
  },
  {
    value: 29,
    label: "LULUS BERSYARAT",
  },
];

const RegExText = new RegExp("^[a-zA-Z ]*$");
const RegExNumbers = new RegExp("^[0-9]*$");

const CommiteeEnum = {
  Pengerusi: "1",
  Presiden: "17",
  Pengarah: "18",
  "Timbalan Pengerusi": "2",
  "Timbalan Presiden": "19",
  "Timbalan Pengarah": "20",
  "Naib Pengerusi": "8",
  "Naib Presiden": "22",
  "Naib Pengarah": "23",
  "Naib Yang Dipertua": "43",
  Setiausaha: "3",
  "Setiausaha Agung": "21",
  "Penolong Setiausaha": "4",
  "Penolong Setiausaha Agung": "24",
  Bendahari: "5",
  "Bendahari Agung": "25",
  "Ketua Bendahari": "26",
  "Bendahari Kehormat": "27",
  "Penolong Bendahari": "9",
  "Penolong Bendahari Agung": "28",
  "Penolong Ketua Bendahari": "29",
  "Penolong Bendahari Kehormat": "30",
  "Ahli Jawatankuasa Biasa": "6",
  Chairman: "1",
  President: "17",
  Director: "18",
  "Deputy Chairman": "2",
  "Deputy President": "19",
  "Deputy Director": "20",
  "Vice Chairman": "8",
  "Vice President": "22",
  "Vice Director": "23",
  Secretary: "3",
  "General Secretary": "21",
  "Assistant Secretary": "4",
  "General Assistant Secretary": "24",
  Treasurer: "25",
  "General Treasurer": "25",
  "Chief Treasurer": "26",
  "Honorary Treasurer": "27",
  "Assistant Treasurer": "9",
  "GeneralAssistant Treasurer": "28",
  "Chief Assistant Treasurer": "29",
  "Honorary Assistant Treasurer": "30",
  //"Ahli Jawatankuasa Biasa":"6",
  "Pengguna Biasa": "7",
  "Ahli Biasa": "10",
  "Perwakilan Negeri": "11",
  "Lain Lain": "12",
  "Biro Ekonomi": "13",
  "Biro Kebajikan": "14",
  "Biro Sukan dan Sosial": "15",
  "Biro Agama": "16",
};

const SebabRyuanList = [
  { value: 0, label: "-" },
  { value: 1, label: "Pembatalan pendaftaran pertubuhan di bawah Seksyen 2A." },
  { value: 2, label: "Penolakan permohonan pendaftaran di bawah Seksyen 7." },
  {
    value: 3,
    label:
      "Penolakan permohonan pengecualian Seksyen 9A(1)(a), 9A(1)(b) dan 9A(1)(c).",
  },
  { value: 4, label: "Penolakan permohonan pindaan Undang-undang Seksyen 11." },
  {
    value: 5,
    label: "Penolakan permohonan penubuhan cawangan bawah Seksyen 12.",
  },
  { value: 6, label: "Pembatalan pendaftaran pertubuhan di bawah Seksyen 13." },
  { value: 7, label: "Perintah 13A(1)." },
  { value: 8, label: "Perintah 13A(2)." },
  {
    value: 9,
    label: "Penolakan permohonan perlantikan juruaudit bawah Seksyen 14(4).",
  },
  { value: 10, label: "Perintah 14(5)." },
  {
    value: 11,
    label: "Pembatalan pendaftaran pertubuhan di bawah Seksyen 16.",
  },
  {
    value: 12,
    label:
      "Penolakan permohonan memegang jawatan bagi mereka yang disenarai-hitamkan di bawah Seksyen 49.",
  },
];

const KelulusanKuiri = [
  { value: "JPPM NEGERI", label: "JPPM NEGERI" },
  { value: "PEMOHON", label: "PEMOHON" },
];

const OrganisationPositionsAhli = [
  { label: "ahliBiasa", value: 10 },
  { label: "ahliBersekutu", value: 49 },
  { label: "ahliKehormat", value: 50 },
  { label: "seumurHidup", value: 51 },
  { label: "ahliRemaja", value: 52 },
];

const PaymentPrefixes = {
  RAYUAN: "RAY-",
  CARIAN: "CAR-",
  PEGAWAI_AWAM: "AWM-",
  PEGAWAI_HARTA: "HRT-",
};

const designation: Record<
  number | string,
  { name: string; level: number; category: number }
> = {
  1: { name: "Pengerusi", level: 1, category: 1 },
  2: { name: "Timbalan Pengerusi", level: 2, category: 1 },
  3: { name: "Setiausaha", level: 4, category: 1 },
  4: { name: "Penolong Setiausaha", level: 5, category: 1 },
};

const MimeTypeMap: Record<string, string> = {
  // TEXT & Doc Formats
  "text/plain": "TXT",
  "application/rtf": "RTF",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "DOCX",
  "application/msword": "DOC",
  "application/vnd.oasis.opendocument.text": "ODT",
  "application/pdf": "PDF",

  // Spreadsheet formats
  "application/vnd.ms-excel": "XLS",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "XLSX",
  "application/vnd.oasis.opendocument.spreadsheet": "ODS",

  // Presentation formats
  "application/vnd.ms-powerpoint": "PPT",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    "PPTX",
  "application/vnd.oasis.opendocument.presentation": "ODP",

  // Other document formats
  "application/x-tex": "TEX",
  "application/json": "JSON",
  "application/xml": "XML",
  "text/csv": "CSV",
  "text/html": "HTML",
  "text/markdown": "MD",

  // Image formats
  "image/png": "PNG",
  "image/jpeg": "JPG",
  "image/jpg": "JPG",
  "image/gif": "GIF",
  "image/webp": "WEBP",
  "image/bmp": "BMP",
  "image/svg+xml": "SVG",
  "image/tiff": "TIFF",
  "image/x-icon": "ICO",
};

const AddressOptions = (t: TFunction<"translation", undefined>) => [
  {
    value: "0",
    label: t("abroad"),
  },
  {
    value: "1",
    label: t("inCountry"),
  },
];

const pageAccessEnum = {
  Create: 1,
  Read: 2,
  Update: 4,
  Delete: 8,
  All: 15, // 1+2+4+8
} as const;

const PermissionNames = {
  KELULUSAN: {
    name: "Kelulusan",
    label: "KEL-PI",
  },
  UMUM: {
    name: "PenyelenggaraanUmum",
    label: "PU-MBS",
  },
  PERTUBUHAN: {
    name: "Pertubuhan",
    label: "PP-SP",
  },
  MEJA_BANTUAN1: {
    name: "MejaBantuan1",
    label: "MB-KA",
  },
  MEJA_BANTUAN2: {
    name: "MejaBantuan2",
    label: "MB-NB",
  },
  MEJA_BANTUAN3: {
    name: "MejaBantuan3",
    label: "MB-SA",
  },
  // dont have in DB
  PENYELENGGARAAN: {
    name: "Penyelenggaraan",
    label: "Penyelenggaraan",
  },
  // dont have in DB
  PENYELENGGARAAN_PERTUBUHAN: {
    name: "PenyelenggaraanPertubuhan",
    label: "Penyelenggaraan Pertubuhan",
  },
  TAKWIM: {
    name: "Takwim Aktiviti",
    label: "TAKWIM-AKT",
  },
};

const MaklumBalasKategori = {
  MEMUASKAN: "memuaskan",
  SANGAT_MEMUASKAN: "sangatMemuaskan",
  TIDAK_MEMUASKAN: "tidakMemuaskan",
};

const OrganizationLevelOption = [
  { label: "Negeri", value: "Negeri" },
  { label: "Kebangsaan", value: "Kebangsaan" },
  { label: "Lain-lain", value: "Lain-lain" },
];

const CountryOptions = [
  {
    label: "Malaysia",
    value: "MYS",
  },
  {
    label: "Singapura",
    value: "SGP",
  },
  {
    label: "Indonesia",
    value: "IDN",
  },
];

const COMMITTEE_TASK_TYPE = {
  PERLEMBAGAAN: "PERLEMBAGAAN",
  PENGURUSAN_AJK: "PENGURUSAN_AJK",
  PENGURUSAN_MESYUARAT: "PENGURUSAN_MESYUARAT",
  PENYATAAN_TAHUNAN: "PENYATAAN_TAHUNAN",
} as const;

const DurationOptions = [
  { value: "day", label: "day" },
  { value: "month", label: "month" },
  { value: "year", label: "year" },
];

const months = [
  { value: "1 Januari", label: "1 Januari" },
  { value: "1 Februari", label: "1 Februari" },
  { value: "1 Mac", label: "1 Mac" },
  { value: "1 April", label: "1 April" },
  { value: "1 Mei", label: "1 Mei" },
  { value: "1 Jun", label: "1 Jun" },
  { value: "1 Julai", label: "1 Julai" },
  { value: "1 Ogos", label: "1 Ogos" },
  { value: "1 September", label: "1 September" },
  { value: "1 Oktober", label: "1 Oktober" },
  { value: "1 November", label: "1 November" },
  { value: "1 Disember", label: "1 Disember" },
];

const TrainingEnums = {
  Assigned: 1,
  History: 2,
  All: 3,
};

//https://www.jpn.gov.my/my/kod-negeri
const ExternalStateCodes = [
  { value: 1, label: "Johor" },
  { value: 21, label: "Johor" },
  { value: 22, label: "Johor" },
  { value: 23, label: "Johor" },
  { value: 24, label: "Johor" },
  { value: 2, label: "Kedah" },
  { value: 25, label: "Kedah" },
  { value: 26, label: "Kedah" },
  { value: 27, label: "Kedah" },
  { value: 3, label: "Kelantan" },
  { value: 28, label: "Kelantan" },
  { value: 29, label: "Kelantan" },
  { value: 4, label: "Melaka" },
  { value: 30, label: "Melaka" },
  { value: 5, label: "Negeri Sembilan" },
  { value: 31, label: "Negeri Sembilan" },
  { value: 59, label: "Negeri Sembilan" },
  { value: 6, label: "Pahang" },
  { value: 32, label: "Pahang" },
  { value: 33, label: "Pahang" },
  { value: 7, label: "Pulau Pinang" },
  { value: 34, label: "Pulau Pinang" },
  { value: 35, label: "Pulau Pinang" },
  { value: 8, label: "Perak" },
  { value: 36, label: "Perak" },
  { value: 37, label: "Perak" },
  { value: 38, label: "Perak" },
  { value: 39, label: "Perak" },
  { value: 9, label: "Perlis" },
  { value: 40, label: "Perlis" },
  { value: 10, label: "Selangor" },
  { value: 41, label: "Selangor" },
  { value: 42, label: "Selangor" },
  { value: 43, label: "Selangor" },
  { value: 44, label: "Selangor" },
  { value: 11, label: "Terengganu" },
  { value: 45, label: "Terengganu" },
  { value: 46, label: "Terengganu" },
  { value: 12, label: "Sabah" },
  { value: 47, label: "Sabah" },
  { value: 48, label: "Sabah" },
  { value: 49, label: "Sabah" },
  { value: 13, label: "Sarawak" },
  { value: 50, label: "Sarawak" },
  { value: 51, label: "Sarawak" },
  { value: 52, label: "Sarawak" },
  { value: 53, label: "Sarawak" },
  { value: 14, label: "Wilayah Persekutuan Kuala Lumpur" },
  { value: 54, label: "Wilayah Persekutuan Kuala Lumpur" },
  { value: 55, label: "Wilayah Persekutuan Kuala Lumpur" },
  { value: 56, label: "Wilayah Persekutuan Kuala Lumpur" },
  { value: 57, label: "Wilayah Persekutuan Kuala Lumpur" },
  { value: 15, label: "Wilayah Persekutuan Labuan" },
  { value: 58, label: "Wilayah Persekutuan Labuan" },
  { value: 16, label: "Wilayah Persekutuan Putrajaya" },
  { value: 82, label: "Negeri Tidak Diketahui" },
];

const SocietyCancellationSectionEnums = [
  {
    code: "9A" as const,
    description: "SECTION_9A",
  },
  {
    code: "13" as const,
    description: "SECTION_913",
  },
];

const SectionOptions: ISection[] = [
  {
    code: "SECTION_13_1_A",
    description: "13(1)(a)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_B",
    description: "13(1)(b)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_I",
    description: "13(1)(c)(i)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_II",
    description: "13(1)(c)(ii)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_III",
    description: "13(1)(c)(iii)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_IV",
    description: "13(1)(c)(iv)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_V",
    description: "13(1)(c)(v)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_VI",
    description: "13(1)(c)(vi)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_VII",
    description: "13(1)(c)(vii)",
    section: "13",
    isBlacklistPermanent: true,
  },
  {
    code: "SECTION_13_1_C_VIII",
    description: "13(1)(c)(viii)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_C_IX",
    description: "13(1)(c)(ix)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_13_1_D",
    description: "13(1)(d)",
    section: "13",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_9A_1_A",
    description: "9A(1)(a)",
    section: "9A",
    isBlacklistPermanent: true,
  },
  {
    code: "SECTION_9A_1_B",
    description: "9A(1)(b)",
    section: "9A",
    isBlacklistPermanent: false,
  },
  {
    code: "SECTION_9A_1_C",
    description: "9A(1)(c)",
    section: "9A",
    isBlacklistPermanent: true,
  },
  {
    code: "SECTION_9A_1_D",
    description: "9A(1)(d)",
    section: "9A",
    isBlacklistPermanent: true,
  },
  {
    code: "SECTION_9A_1_E",
    description: "9A(1)(e)",
    section: "9A",
    isBlacklistPermanent: true,
  },
];

const liabilityRestrictionSectionOptions = SectionOptions.filter(
  (item) =>
    item.section === "9A" ||
    !["SECTION_13_1_A", "SECTION_13_1_C_V", "SECTION_13_1_D"].includes(
      item.code
    )
);

const SocietyCancellationRevertReason = [
  { value: "COURT_ORDER", label: "Perintah Mahkamah" },
  { value: "APPEAL", label: "Rayuan" },
  { value: "OTHERS", label: "Lain-lain" },
];

//Quill Color set for seperate styling
const quillColors = [
  // Row 1
  "#000000", // Black
  "#434343", // Dark Gray 1
  "#666666", // Dark Gray 2
  "#999999", // Medium Gray
  "#cccccc", // Light Gray 1
  "#efefef", // Light Gray 2
  "#ffffff", // White

  // Row 2
  "#980000", // Dark Red
  "#ff0000", // Red
  "#ff9900", // Orange
  "#ffff00", // Yellow
  "#00ff00", // Green
  "#00ffff", // Cyan
  "#4a86e8", // Blue

  // Row 3
  "#0000ff", // Dark Blue
  "#9900ff", // Purple
  "#ff00ff", // Magenta
  "#ffcccc", // Pink
  "#ffcc99", // Light Orange
  "#ffffcc", // Light Yellow
  "#ccffcc", // Light Green

  // Row 4
  "#ccffff", // Light Cyan
  "#cfe2f3", // Light Blue
  "#d9d2e9", // Light Purple
  "#ead1dc", // Light Magenta
  "#dd7e6b", // Brown 1
  "#ea9999", // Brown 2
  "#f9cb9c", // Tan

  // Row 5
  "#ffe599", // Light Tan
  "#b6d7a8", // Olive Green
  "#a2c4c9", // Teal
  "#a4c2f4", // Sky Blue
  "#b4a7d6", // Lavender
  "#d5a6bd", // Mauve
  "#e69138", // Dark Orange
];

export {
  PermissionNames,
  MeetingTypeOption,
  ApplicationStatus,
  MeetingMethods,
  MeetingMethodsEnum,
  MeetingContent,
  MALAYSIA,
  ValidationTime,
  ValidationTimeTwo,
  HideOrDisplayInherit,
  HideOrDisplayFlex,
  NewSocietyBranchStatus,
  AuditTrailType,
  ListGelaran,
  AuditTrailEnum,
  ConstitutionType,
  IdTypes,
  OrganisationPositions,
  OrganisationPositionLabel,
  ListUserStatus,
  DecisionOptions,
  DecisionOptionsCode,
  ListGender,
  BranchStatusList,
  ROApprovalType,
  MiniApplicationStatus,
  ApplicationStatusList,
  RegExText,
  RegExNumbers,
  CommiteeEnum,
  SebabRyuanList,
  KelulusanKuiri,
  designation,
  OccupationList,
  MimeTypeMap,
  SocietyStatusList,
  StatusCodeList,
  ValidationTimeTwoMin,
  PaymentTypeList,
  AddressOptions,
  ListCategoryRoleStatus,
  CitizenshipStatus,
  MaklumBalasKategori,
  OrganizationLevelOption,
  NotificationColors,
  DecisionOptionsCodeRayuan,
  CountryOptions,
  COMMITTEE_TASK_TYPE,
  months,
  FeedbackComplainType,
  FeedbackComplainType_New,
  FeedbackLevel,
  FeedbackStatus,
  TrainingEnums,
  OrganisationPositionsAhli,
  ExternalStateCodes,
  PaymentPrefixes,
  DurationOptions,
  SocietyCancellationSectionEnums,
  SectionOptions,
  liabilityRestrictionSectionOptions,
  SocietyCancellationRevertReason,
  quillColors,
  pageAccessEnum,
};
