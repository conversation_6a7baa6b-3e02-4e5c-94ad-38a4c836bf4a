import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSenaraiContext } from "../../SenaraiContext";
import { usePembubaranContext } from "../PembubaranProvider";
import { useLocation } from "react-router-dom";
import { FieldValues, useForm, useFormContext } from "react-hook-form";
import {
  globalStyles,
  useUploadPresignedUrl,
  useGetDocument,
  DocumentUploadType,
  useQuery,
} from "@/helpers";

import {
  Box,
  Typography,
  Grid,
  FormControlLabel,
  Checkbox,
  FormHelperText,
  CircularProgress,
} from "@mui/material";
import {
  FormFieldRow,
  DisabledTextField,
  Label,
  ButtonPrimary,
  FileUploadController,
  CustomSkeleton,
  Switch,
  ButtonOutline
} from "@/components";

import { IApiResponse, ISubmittedStatement } from "@/types";

const FinancialForm: React.FC = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();

  const { handleNextPembubaran: handleNext, handleBackPembubaran: handleBack } =
    useSenaraiContext();
  const { statementFinancialData, isViewOnly } = usePembubaranContext();
  const { getValues: liquidationFormValues } = useFormContext();

  const classes = globalStyles();
  const currentLanguage = i18n.language;
  const isCreatePage = location.pathname.endsWith("/create");

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState("");
  const [isChecked, setIsChecked] = useState(false);

  const {
    data: submittedStatementRes,
    isLoading: isLoadingSubmittedStatement,
  } = useQuery<IApiResponse<ISubmittedStatement[]>>({
    url: "society/statement/submittedStatements",
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: liquidationFormValues().societyId,
      },
    ],
  });

  const { upload: uploadFile, isLoading: isUploading } = useUploadPresignedUrl({
    onSuccessUpload: () => handleNext(),
  });

  const { getDocument, isLoading: isLoadingDocument } = useGetDocument({
    onSuccess: (data) => {
      if (data) {
        setFilePreview(data?.url ?? "");
        setValue("document", data?.name ?? "");
      }
    },
  });

  const listSubmittedStatement = submittedStatementRes?.data?.data ?? [];

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FieldValues>({
    defaultValues: {
      document: "",
    },
  });

  const onSubmit = () => {
    if (isViewOnly) return handleNext();

    if (selectedFile) {
      const liquidationValues = liquidationFormValues();

      uploadFile({
        params: {
          type: DocumentUploadType.FINANCIAL_STATEMENT,
          societyId: Number(liquidationValues.societyId),
          societyNo: liquidationValues.societyNo,
          liquidationId: liquidationValues.id,
        },
        file: selectedFile,
      });
    } else {
      handleNext();
    }
  };

  useEffect(() => {
    const liquidationValues = liquidationFormValues();

    getDocument([
      {
        field: "societyId",
        operator: "eq",
        value: liquidationValues.societyId,
      },
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.FINANCIAL_STATEMENT,
      },
      {
        field: "liquidationId",
        operator: "eq",
        value: liquidationValues.id,
      },
    ]);
  }, []);

  useEffect(() => {
    if (!isCreatePage) setIsChecked(true);
  }, [isCreatePage]);

  if (isLoadingSubmittedStatement || isLoadingDocument)
    return <CustomSkeleton number={3} />;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={1}>
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight={500}
            mb="20px"
          >
            {currentLanguage === "my"
              ? "Senarai Penyata dihantar"
              : "List of Statements sent"}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 2, mb: 4 }}>
            {listSubmittedStatement?.map((year) => (
              <Grid item xs={3} key={year.year} sx={{ display: "flex" }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={year.submitted}
                      sx={{
                        color: "#00bcd4",
                        "&.Mui-checked": {
                          color: "#00bcd4",
                        },
                        p: 0.5,
                      }}
                    />
                  }
                  label={<Label text={year.year} />}
                  labelPlacement="start"
                  sx={{
                    flex: 1,
                    m: 0,
                    justifyContent: "center",
                  }}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>

      {isCreatePage && (
        <Box className={classes.section} mb={2}>
          <Box className={classes.sectionBox} mb={2}>
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight={500}
              mb="20px"
            >
              {currentLanguage === "my" ? "Pengakuan" : "Confession"}
            </Typography>

            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography className="label" maxWidth="60%">
                {currentLanguage === "my"
                  ? "Dengan ini saya mengaku bahawa persatuan ini tidak mempunyai sebarang Pendapatan & Perbelanjaan pada tahun kewangan ini."
                  : "I hereby declare that this association does not have any Income & Expenses in this financial year."}
              </Typography>

              <Switch onChange={(e) => setIsChecked(e.target.checked)} />
            </Box>
          </Box>
        </Box>
      )}

      <Box className={classes.section}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight={500}
          >
            Maklumat Kewangan
          </Typography>
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                {t("totalIncome")}
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalIncome || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                {t("jumlahPerbelanjaan")}
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalExpense || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                Jumlah Aset
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalAsset || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                Jumlah libailiti
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalLiability || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <Box
            sx={{
              mb: "20px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight={500}
            >
              {currentLanguage === "my"
                ? "Muat Naik Penyata Kewangan"
                : "Upload Financial Statements"}
            </Typography>
            {errors && errors.document && (
              <FormHelperText sx={{ color: "red", fontSize: "12px" }}>
                {errors?.document?.message as string}
              </FormHelperText>
            )}
          </Box>

          <FormFieldRow
            align="flex-start"
            label={
              <Label
                text={
                  currentLanguage === "my"
                    ? "Penyata Kewangan yang telah selesai diaudit"
                    : "Completed audited financial statements"
                }
                required={!isChecked}
              />
            }
            value={
              <FileUploadController
                control={control}
                name="document"
                accept=".pdf"
                allowedTypes={["application/pdf"]}
                maxFileSize={25 * 1024 * 1024}
                onFileSelect={(file) => setSelectedFile(file)}
                helperTextPlacement="OUTSIDE"
                filePreview={filePreview}
                disabled={isViewOnly || isChecked}
                required={!isViewOnly && !isChecked}
              />
            }
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>

          <ButtonPrimary type="submit" disabled={isUploading}>
            {isUploading && <CircularProgress size={10} />}
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
    </form>
  );
};

export default FinancialForm;
