import { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import Input from "../../../../../components/input/Input";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { API_URL } from "../../../../../api";
import { LoadingOverlay } from "../../../../../components/loading";
// import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import { PaymentPrefixes } from "@/helpers";
import dayjs from "dayjs";

export const Online = () => {
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const navigate = useNavigate();

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [searchParams] = useSearchParams();
  const [isLoadingData, setIsLoadingData] = useState(false);

  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyByIdData.data);
  const { id: societyId } = useParams();
  const location = useLocation();
  const publicOfficerId = location.state?.publicOfficerId;
  const propertyOfficerId = location.state?.propertyOfficerId;
  const publicOfficerName = location.state?.publicOfficerName;
  const createdDate = location.state?.createdDate;
  const isAwamPage = location.pathname.includes("awam");
  const paymentType = t(
    `${isAwamPage ? "civilServant" : "propertyOfficer"}Registration`
  );

  useEffect(() => {
    const fetchSocietyData = async () => {
      try {
        setIsLoadingData(true);
        const encodedId = searchParams.get("id") || "";
        const decodedId = atob(encodedId);
      } catch (error) {
        console.error("Error fetching society data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchSocietyData();
  }, [searchParams]);

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const handleCetak = async () => {
    try {
      if (encodedId) {
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: `${paymentType} (${t("onlineUpperscoredPayment")})`,
            paymentMethod: "o", // O = online, C = counter
            societyId: encodedId,
            branchId: "",
            registerDateTime: dayjs(createdDate).format("YYYY-MM-DDTHH:mm:ss"),
            amount: 10.0,
            referenceNo:
              isAwamPage && publicOfficerId
                ? PaymentPrefixes.PEGAWAI_AWAM + publicOfficerId
                : PaymentPrefixes.PEGAWAI_HARTA + encodedId,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <Box sx={{ display: "flex" }}>
      <Box sx={{ width: "100%" }}>
        <LoadingOverlay isLoading={isLoadingData} />

        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            {/* Payment Header Box */}
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("pengesahan")} {t("payment")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2,
                }}
              >
                {t("theEntityApplicationHasBeenRecorded", {
                  entity: paymentType,
                })}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={3}>
                {Array.isArray(publicOfficerName) ? (
                  publicOfficerName.map((officer, index) => (
                    <Input
                      disabled
                      {...(index === 0 && { label: t("officerName") })}
                      label={t("officerName")}
                      name={`officerName-${index}`}
                      value={officer.name || ""}
                      {...(index !== 0 && { isLabel: false })}
                    />
                  ))
                ) : (
                  <Input
                    disabled
                    label={t("officerName")}
                    value={publicOfficerName || ""}
                  />
                )}

                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyDataRedux?.societyName || ""}
                />
                <Input
                  disabled
                  label={t("tarikhPermohonan")}
                  value={createdDate}
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  value={
                    publicOfficerId && isAwamPage
                      ? PaymentPrefixes.PEGAWAI_AWAM + publicOfficerId
                      : encodedId
                      ? PaymentPrefixes.PEGAWAI_HARTA + encodedId
                      : ""
                  }
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value={"Pembayaran Online"}
                />
                <Input disabled label={t("paymentType")} value={paymentType} />
                <Input
                  disabled
                  label={t("namePemohon")}
                  value={userName || ""}
                />
                <Input
                  disabled
                  label={t("email")}
                  value={societyDataRedux?.email || ""}
                />
                <Input
                  disabled
                  label={t("idNumberPlaceholder")}
                  value={societyDataRedux?.identificationNo || ""}
                />
                <Input disabled label={t("kodOsolAmanah")} value="724999" />
                <Input
                  disabled
                  label={t("jabatan")}
                  value="Jabatan Pendaftaran Pertubuhan"
                />
                <Input
                  disabled
                  label={t("pusatPenerimaan")}
                  value="Ibu Pejabat Jab. Pendataran Pertubuhan"
                />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2,
                }}
              >
                {t("noteOnline")}
              </Typography>
            </Box>

            {/* Action Buttons */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 15,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  color: "white",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
              <ButtonPrimary
                onClick={() =>
                  navigate("term?id=" + encodedId, {
                    state: {
                      societyId: societyId,
                      ...(propertyOfficerId && { propertyOfficerId }),
                      ...(publicOfficerId && { publicOfficerId }),
                    },
                  })
                }
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("bayar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        {/* <OrganizationStepper activeStep={activeStep} />
        <InfoQACard /> */}
      </Box>
    </Box>
  );
};

export default Online;
