import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Alert,
  Menu,
  MenuItem,
  useMediaQuery,
  Theme,
  Card,
  CardContent,
  Grid,
  Button,
  Divider,
  SvgIcon,
} from "@mui/material";
import {
  Search,
  FilterList,
  Add,
  MoreVert,
  Campaign,
  Circle,
  Edit,
  Visibility,
  Description,
  Payment,
  Delete,
  Timer,
} from "@mui/icons-material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { DataGrid, useGridApiRef } from "@mui/x-data-grid";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import {
  NewSocietyBranchStatus,
  ApplicationStatusEnum,
  HideOrDisplayFlex,
  HideOrDisplayInherit,
  CitizenshipStatus,
  GenderType,
  ListGender,
  IdTypes,
  OrganisationPositionsAhli,
} from "../../../../helpers/enums";
import Input from "../../../../components/input/Input";
import { ListGelaran } from "../../../../helpers/enums";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { getLocalStorage } from "@/helpers";
import dayjs from "dayjs";

const DaftarAhli: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { id: societyId, memberId } = useParams();
  const [openSearchModal, setOpenSearchModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);
  const [searchPertubuhan, setSearchPertubuhan] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const apiRef = useGridApiRef();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const isMyLanguage = i18n.language === "my";

  const editableAndDeleteable = [
    NewSocietyBranchStatus.BARU_1,
    NewSocietyBranchStatus.DIBENARKAN_1,
    NewSocietyBranchStatus.AKTIF_1,
  ];

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchPertubuhan);
    }, 1000);

    return () => {
      clearTimeout(handler);
    };
  }, [searchPertubuhan]);

  const handleClick = (event: React.MouseEvent<HTMLElement>, index: number) => {
    setAnchorEl(event.currentTarget);
    setSelectedRowIndex(index);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedRowIndex(null);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchPertubuhan(e.target.value);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-menu" : undefined;

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const [similarId, setsimilarId] = useState(false);
  const memberData = location.state?.row;
  const isEdit = location.state?.isEdit;
  const isManager = location?.state?.isManager;
  const isAliranModuleAccess = location?.state?.isAliranModuleAccess;
  const userDetail = getLocalStorage("user-details", {});

  useEffect(() => {
    if (userDetail?.identificationNo === memberId) {
      setsimilarId(true);
    } else {
      setsimilarId(false);
    }
  }, [userDetail]);

  const [titleCode, setTitleCode] = useState(
    memberData ? memberData.titleCode : ""
  );
  const [titleCodeError, setTitleCodeError] = useState("");
  const [fullName, setFullName] = useState(memberData ? memberData.name : "");
  const [fullNameError, setFullNameError] = useState("");
  const [idType, setIdType] = useState(
    memberData ? memberData.identificationType : ""
  );
  const [idTypeError, setIdTypeError] = useState("");
  const [idNumber, setIdNumber] = useState(
    memberData ? memberData.identificationNo : ""
  );
  const [idNumberError, setIdNumberError] = useState("");
  const [gender, setGender] = useState(memberData ? memberData.gender : "");
  const [genderError, setGenderError] = useState("");
  const [citizen, setCitizen] = useState(
    memberData ? parseInt(memberData.nationalityStatus) : ""
  );
  const [citizenError, setCitizenError] = useState("");
  const [email, setEmail] = useState(memberData ? memberData.email : "");
  const [emailError, setEmailError] = useState("");
  const [phoneNumber, setPhoneNumber] = useState(
    memberData ? memberData.phoneNumber : ""
  );
  const [phoneNumberError, setPhoneNumberError] = useState("");
  const [membershipNo, setMembershipNo] = useState(
    memberData ? memberData.membershipNo : ""
  );
  const [membershipNoError, setMembershipNoError] = useState("");
  const [membershipRegistrationDate, setMemberRegistrationDate] = useState(
    memberData ? memberData.membershipRegistrationDate : ""
  );
  const [designationCode, setDesignationCode] = useState(
    memberData ? memberData.designationCode : ""
  );
  const [telephoneNumber, setTelephoneNumber] = useState(
    memberData ? memberData.telephoneNumber : ""
  );

  const { mutate: registerMember } = useCreate();
  const { mutate: editMember, isLoading } = useCustomMutation();

  const [userICCorrect, setUserICCorrect] = useState(true);
  const [userNameMatchIC, setUserNameMatchIC] = useState(true);

  const handleRegisterMember = () => {
    let validated = validateEmail();

    if (
      (Number(citizen) === 1 &&
        Number(idType) === 1 &&
        idNumber?.length < 12) ||
      idNumber?.length < 1
    ) {
      setIdNumberError("invalidIdNumber");
    } else {
      setIdNumberError("");
    }
    if (!titleCode) {
      setTitleCodeError(t("validation.required"));
      validated = false;
    }
    if (!fullName) {
      setFullNameError(t("validation.required"));
      validated = false;
    }
    if (!idType) {
      setIdTypeError(t("validation.required"));
      validated = false;
    }
    if (!idNumber) {
      setIdNumberError(t("validation.required"));
      validated = false;
    }
    if (!gender) {
      setGenderError(t("validation.required"));
      validated = false;
    }
    if (!citizen) {
      setCitizenError(t("validation.required"));
      validated = false;
    }
    if (!phoneNumber) {
      setPhoneNumberError(t("validation.required"));
      validated = false;
    }
    if (!membershipNo) {
      setMembershipNoError(t("validation.required"));
      validated = false;
    }
    if (!validated) {
      return;
    }
    setTitleCodeError("");
    setFullNameError("");
    setIdTypeError("");
    setIdNumberError("");
    setGenderError("");
    setCitizenError("");
    setPhoneNumberError("");

    if (memberId) {
      editMember(
        {
          url: `${API_URL}/society/${societyId}/members/${memberId}/edit`,
          method: "put",
          values: {
            titleCode: titleCode,
            name: fullName,
            identificationType: idType,
            identificationNo: idNumber,
            gender: gender,
            nationalityStatus: citizen,
            email: email,
            phoneNumber: phoneNumber,
            telephoneNumber: telephoneNumber,
            membershipRegistrationDate: membershipRegistrationDate,
            designationCode: designationCode,
            membershipNo: membershipNo,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            navigate(-1);
            if (data?.data?.status === "SUCCESS") {
              return {
                message: isMyLanguage
                  ? "Ahli berjaya dikemaskini"
                  : "Committee Update Successfully",
                type: "success",
              };
            } else {
              return {
                message: isMyLanguage
                  ? "Pengguna Sudah Berada Dalam Masyarakat Ini"
                  : "User Already In This Society",
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: t(data?.data?.msg),
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
        }
      );
    } else {
      registerMember({
        resource: `society/${societyId}/members/register`,
        values: {
          titleCode: titleCode,
          name: fullName,
          identificationType: idType,
          identificationNo: idNumber,
          gender: gender,
          nationalityStatus: citizen,
          email: email,
          phoneNumber: phoneNumber,
          telephoneNumber: telephoneNumber,
          membershipRegistrationDate: membershipRegistrationDate,
          designationCode: designationCode,
          membershipNo: membershipNo,
        },
        meta: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            navigate(-1);
            return {
              message: isMyLanguage
                ? "Ahli Berjaya Mendaftar"
                : "Member Registered Successfully",
              type: "success",
            };
          } else {
            return {
              message: isMyLanguage
                ? "Pengguna Sudah Berada Dalam Masyarakat Ini"
                : "User Already In This Society",
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: t(data?.data?.msg),
            type: "error",
          };
        },
      });
    }
  };

  const validateEmail = () => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError(t("emailRequired"));
      return false;
    }
    if (!regex.test(email)) {
      setEmailError(t("invalidEmail"));
      return false;
    }
    setEmail(email);
    setEmailError("");
    return true;
  };

  const handleClearForm = () => {
    // Clear all form fields
    setTitleCode("");
    setFullName("");
    setIdType("");
    setIdNumber("");
    setGender("");
    setCitizen("");
    setEmail("");
    setPhoneNumber("");
    setMembershipNo("");
    setMemberRegistrationDate("");
    setDesignationCode("");
    setTelephoneNumber("");

    // Clear all error states
    setTitleCodeError("");
    setFullNameError("");
    setIdTypeError("");
    setIdNumberError("");
    setGenderError("");
    setCitizenError("");
    setEmailError("");
    setPhoneNumberError("");
    setMembershipNoError("");

    // Reset validation states
    setUserICCorrect(true);
    setUserNameMatchIC(true);
  };

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newOList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    console.log(newOList);
    setIdTypeTranslatedList(newOList);
  }, [t]);

  const isAllow = (isAliranModuleAccess || isManager || similarId) && isEdit;

  const [designationCodeTranslatedList, setDesignationCodeTranslatedList] =
    useState<{ value: number; label: string }[]>([]);

  useEffect(() => {
    const validLabels = [
      "ahliBiasa",
      "ahliBersekutu",
      "ahliKehormat",
      "seumurHidup",
      "ahliRemaja",
    ];

    const newPList = OrganisationPositionsAhli.filter((item) =>
      validLabels.includes(item.label)
    ).map((item) => ({
      ...item,
      label: t(item.label),
    }));

    setDesignationCodeTranslatedList(newPList);
  }, [t]);

  console.log("membershipRegistrationDate", membershipRegistrationDate);

  const { data, isLoading: isLoadingICValidation } = useCustom<any>({
    url: `${API_URL}/user/auth/validateId`,
    method: "get",
    config: {
      query: {
        identificationNo: idNumber,
        name: fullName?.trim().toUpperCase(),
        sessionIdentificationNo: idNumber,
      },
    },
    queryOptions: {
      // enabled: idNumber.length === 12,
      enabled:
        (Number(idType) === 1 || Number(idType) === 4) &&
        Number(citizen) === 1 &&
        idNumber?.length > 11 &&
        fullName !== "",
      retry: false,
      cacheTime: 0,
    },
    successNotification(data) {
      //reset before new call
      setUserICCorrect(false);
      setUserNameMatchIC(false);
      // setVerifyAllFetch(false);

      const { name, message, status, userExist, integrationOff } =
        data?.data?.data || {};

      if (!integrationOff) {
        if (status === "Y") {
          setUserICCorrect(true);
          if (name) {
            setUserNameMatchIC(true);
          }
        } else {
          setUserICCorrect(false);
          setUserNameMatchIC(true);
        }
      }

      return false;
    },
    errorNotification(error) {
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      return false;
    },
  });

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatPeribadiAhli")}
          </Typography>
          <Input
            //textColor="#666666"
            value={titleCode}
            onChange={(e) => setTitleCode(e.target.value)}
            required
            disabled={!isAllow}
            label={t("gelaran")}
            options={ListGelaran}
            type="select"
            error={!!titleCodeError}
            helperText={titleCodeError}
          />
          <Input
            //textColor="#666666"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            required
            disabled={!isAllow}
            label={t("fullName")}
            error={!!fullNameError || !userNameMatchIC}
            helperText={
              fullNameError || !userNameMatchIC ? t("invalidName") : undefined
            }
          />
          <Input
            value={idType}
            disabled={!!memberId || !isAllow}
            onChange={(e) => {
              if (e.target.value === "1") {
                setIdNumber("");
                setIdType(e.target.value);
                // Auto-select Warganegara when MyKad is selected
                setCitizen(1);
              } else {
                setUserICCorrect(true);
                setUserNameMatchIC(true);
                setIdType(e.target.value);
                // Auto-select Bukan warganegara when other ID types are selected
                setCitizen(2);
              }
            }}
            required
            type="select"
            // options={idTypeTranslatedList}
            options={idTypeTranslatedList.filter(
              (item) => item.value !== "2" && item.value !== "3"
            )}
            label={t("idType")}
            error={!!idTypeError}
            helperText={idTypeError}
          />
          <Input
            value={idNumber}
            disabled={!!memberId || !isAllow}
            onChange={(e) => {
              if (idType === "1" || citizen === 1) {
                const numericValue = e.target.value.replace(/[^0-9]/g, "");
                if (numericValue.length <= 12) {
                  setIdNumber(numericValue);
                }
              } else {
                setIdNumber(e.target.value);
              }
            }}
            required
            label={t("idNumber")}
            error={!!idNumberError || !userICCorrect}
            helperText={
              idNumberError || !userICCorrect ? t("invalidName") : undefined
            }
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
              required: true,
            }}
          />
          <Input
            value={gender}
            required
            onChange={(e) => setGender(e.target.value)}
            type="select"
            options={ListGender.map((gender) => ({
              ...gender,
              label: t(gender.label),
            }))}
            disabled={!isAllow}
            label={t("gender")}
            error={!!genderError}
            helperText={genderError}
          />
          <Input
            value={citizen}
            disabled={true}
            // onChange={(e) => setCitizen(e.target.value)}
            onChange={(e) => {
              if (e.target.value === 1) {
                setIdNumber("");
                setCitizen(e.target.value);
              } else {
                setUserICCorrect(true);
                setUserNameMatchIC(true);
                setCitizen(e.target.value);
              }
            }}
            required
            type="select"
            options={CitizenshipStatus.map((item) => ({
              ...item,
              label: t(item.label), // Apply translation on the label
            }))}
            label={t("citizen")}
            error={!!citizenError}
            helperText={citizenError}
          />
          <Input
            //textColor="#666666"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            type="email"
            label={t("email")}
            disabled={!isAllow}
            error={!!emailError}
            helperText={emailError}
          />
          <Input
            //textColor="#666666"
            value={phoneNumber}
            onChange={(e) => {
              const numericValue = e.target.value.replace(/[^0-9]/g, "");
              setPhoneNumber(numericValue);
            }}
            required
            label={t("phoneNumber")}
            error={!!phoneNumberError}
            disabled={!isAllow}
            helperText={phoneNumberError}
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
            }}
          />
          {/* <Input
            //textColor="#666666"
            value={telephoneNumber}
            onChange={(e) => setTelephoneNumber(e.target.value)}
            disabled={!isAllow}
            label={t("homeNumber")}
          /> */}
          <Input
            //textColor="#666666"
            value={membershipNo}
            onChange={(e) => setMembershipNo(e.target.value)}
            required
            label={t("membershipNo")}
            error={!!membershipNoError}
            disabled={!isAllow}
            helperText={membershipNoError}
          />
          <Input
            //textColor="#666666"
            value={
              membershipRegistrationDate
                ? dayjs(membershipRegistrationDate).format("DD-MM-YYYY")
                : ""
            }
            onChange={(e) => setMemberRegistrationDate(e.target.value)}
            disabled={!isAllow}
            type="date"
            label={t("memberApprovalDate")}
          />
          <Input
            //textColor="#666666"
            value={Number(designationCode)}
            onChange={(e) => setDesignationCode(e.target.value)}
            disabled={!isAllow}
            type="select"
            options={designationCodeTranslatedList}
            label={t("memberType")}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            mt: 2,
          }}
        >
          <ButtonOutline variant="outlined" onClick={handleClearForm}>
            {t("semula")}
          </ButtonOutline>
          {isAllow ? (
            <ButtonPrimary onClick={handleRegisterMember}>
              {memberId ? t("update") : t("registerButton")}
            </ButtonPrimary>
          ) : null}
        </Box>
      </Box>
    </>
  );
};

export default DaftarAhli;
