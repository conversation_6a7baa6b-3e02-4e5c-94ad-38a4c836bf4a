import { <PERSON>, Grid, Select<PERSON>hangeEvent, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import Input from "@/components/input/Input";
import { useTranslation } from "react-i18next";
import { AddressList, Meeting } from "../interface";
import useQuery from "@/helpers/hooks/useQuery";
import { MALAYSIA, MeetingMethods } from "@/helpers/enums";
import { useSelector } from "react-redux";

type props = {
  sectionStyle: any;
  meeting: Meeting | undefined;
};
const AlamatTempatMesyuarat: React.FC<props> = ({ sectionStyle, meeting }) => {
  const { t } = useTranslation();
  const [addressList, setAddressList] = useState<AddressList[]>([]);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  useQuery({
    url: `society/admin/address/list`,
    onSuccess: (data) => {
      const list = data?.data?.data || [];
      setAddressList(list);
    },
  });
   
  if (meeting?.meetingMethod == MeetingMethods.ATAS_TALIAN) {
    return;
  }

  return (
    <Box
      sx={{
        background: "white",
        border: "1px solid rgba(0, 0, 0, 0.12)",
        borderRadius: "14px",
        p: 3,
        mb: 2,
      }}
    >
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("alamatTempatMesyuarat")}
      </Typography>
      <Grid item xs={12}>
        <Input
          label={t("alamatTempatMesyuarat")}
          disabled
          value={meeting?.meetingAddress}
        />
        <Input
          label={t("negeri")}
          type="select"
          value={meeting ? parseInt(meeting?.state) : 0}
          disabled
          options={addressList
            .filter((item: any) => item.pid === MALAYSIA)
            .map((item: any) => ({ value: item.id, label: item.name }))}
        />
        <Input
          label={t("daerah")}
          type="select"
          value={meeting ? parseInt(meeting?.district) : 0}
          disabled
          options={addressList.map((item: any) => ({
            value: item.id,
            label: item.name,
          }))}
        />
        <Input label={t("bandar")} disabled value={meeting?.city} />
        <Input label={t("poskod")} disabled value={meeting?.postcode} />
      </Grid>
    </Box>
  );
};

export default AlamatTempatMesyuarat;
