import React, {useEffect, useState} from "react";
import {Box, Checkbox, Grid, SxProps, TextField, Theme, Typography} from "@mui/material";
import {TrainingChapter} from "@/pages/internal-training/createStepTwo";
import {TrainingAnswer, TrainingQuestion} from "@/pages/internal-training/createStepThree";
import {useTranslation} from "react-i18next";
import {boolean} from "yup";

interface QuestionFragmentProps {
  no: number,
  headerStyle: SxProps<Theme>,
  labelStyle: SxProps<Theme>,
  borderStyle: SxProps<Theme>,
  data: TrainingQuestion,
  handleDataChange: (i: number, data: TrainingQuestion) => void
}

const QuestionFragment: React.FC<QuestionFragmentProps> = ({no, headerStyle, borderStyle, labelStyle, data, handleDataChange}) => {

  const {t, i18n} = useTranslation();

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState<TrainingQuestion>({
    id: data.id,
    question: data.question,
    answers: [
      /*{
        answer: data.answers[0].answer,
        correct: data.answers[0].correct,
        sequenceOrder: data.answers[0].sequenceOrder,
      },
      {
        answer: data.answers[1].answer,
        correct: data.answers[1].correct,
        sequenceOrder: data.answers[1].sequenceOrder,
      },
      {
        answer: data.answers[2].answer,
        correct: data.answers[2].correct,
        sequenceOrder: data.answers[2].sequenceOrder,
      },
      {
        answer: data.answers[3].answer,
        correct: data.answers[3].correct,
        sequenceOrder: data.answers[3].sequenceOrder,
      },*/
      {
        answer: "",
        correct: false,
        sequenceOrder: 1,
      },
      {
        answer: "",
        correct: false,
        sequenceOrder: 2,
      },
      {
        answer: "",
        correct: false,
        sequenceOrder: 3,
      },
      {
        answer: "",
        correct: false,
        sequenceOrder: 4,
      },
    ],
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const {name, value} = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({...prev, [name]: ""}));
  };

  const handleAnswerBox = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, i: number) => {
    const temp: TrainingAnswer[] = formData.answers.slice();
    temp[i].answer = e.target.value;
    setFormData((prevState) => ({
      ...prevState,
      answers: temp,
    }));
  }

  const handleCheckBox = (e: React.ChangeEvent<HTMLInputElement>, i: number) => {
    const temp: TrainingAnswer[] = formData.answers.slice();
    temp[i].correct = e.target.checked;
    setFormData((prevState) => ({
      ...prevState,
      answers: temp,
    }));
  }

  useEffect(() => {
    if(Object.keys(data).length > 0 && data.question && data.answers && data.answers.length > 0){
      console.log("data",data);
      const temp: TrainingQuestion = {
        id: data.id,
        question: data.question,
        answers: data.answers,
      }
      setFormData(temp);
    }
  }, [data]);

  useEffect(() => {
    handleDataChange(no-1,formData);
  },[formData])

  return (<>
    <Box
      sx={borderStyle}
    >
      <Typography
        sx={headerStyle}
      >
        Kuiz
      </Typography>
      <Grid container spacing={2} sx={{mt: 1}}>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {`${t("question")} ${no}`} <span style={{color: "red"}}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size={"small"}
            fullWidth
            required
            name="question"
            value={formData.question}
            error={!!formErrors.question}
            onChange={handleInputChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {`${t("answer")}`} <span style={{color: "red"}}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Box
            sx={borderStyle}
          >
            <Grid container spacing={2} sx={{mt: 0}}>
              <Grid item xs={1} >
                <Typography sx={{...labelStyle, mt: 0, pt: 0}}>
                  {`${t("bil")}`}
                </Typography>
              </Grid>
              <Grid item xs={9}>
                <Typography sx={{...labelStyle, mt: 0, pt: 0}}>
                  {`${t("answer")}`}
                </Typography>
              </Grid>
              <Grid item xs={2}>
                <Typography sx={{...labelStyle, mt: 0, pt: 0}}>
                  {`${t("correctAnswer")}`}
                </Typography>
              </Grid>
              {formData.answers.map((el, i) => {
                return <Grid key={i} container spacing={2} sx={{mt: 0}}>
                  <Grid item xs={1}>
                    <Typography sx={{...labelStyle, pl:3}}>
                      {`${i+1}`}
                    </Typography>
                  </Grid>
                  <Grid item xs={9}>
                    <TextField
                      size={"small"}
                      fullWidth
                      required
                      name={`answer${i}`}
                      value={el.answer}
                      onChange={(e) => handleAnswerBox(e, i)}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <Checkbox
                      checked={el.correct}
                      onChange={(e) => handleCheckBox(e, i)}
                      sx={{pl:3}}
                    />
                  </Grid>
                </Grid>
              })}
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Box>
  </>);
}

export default QuestionFragment;
