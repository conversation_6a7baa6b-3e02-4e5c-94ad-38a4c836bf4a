import React, {useState} from "react";
import {Typography} from "@mui/material";
import Box from "@mui/material/Box";
import TrainingSidebar from "@/pages/training/trainingDetails/trainingSidebar";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import TrainingInfoFragment from "@/pages/training/trainingDetails/trainingInfoFragment";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
import {useLocation} from "react-router-dom";
import {useCustomMutation} from "@refinedev/core";
import {DocumentUploadType} from "@/helpers";
import {useTranslation} from "react-i18next";


const TrainingInfo: React.FC = () => {

  const {t, i18n} = useTranslation();
  const location = useLocation();
  const [step, setStep] = useState(0);

  const course = location.state?.item;
  console.log("course",course);

  const {data: enrolledTrainingData, isLoading: isEnrolledTrainingLoading} = useCustom({
    url: `${API_URL}/society/training/courses/${course.trainingCourseId}/materials`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const enrolledTraining = enrolledTrainingData?.data?.data || [];
  console.log("enrolledTraining", enrolledTraining)

  const {mutate: update, isLoading: isLoadingCreate} = useCustomMutation();
  const Update = (currentPage: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    update(
      {
        url: `${API_URL}/society/training/enrollments/progress`,
        method: "post",
        values: {
          id: course.id,
          step: currentPage+1,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setStep(currentPage)
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };


  const handleNext = (currentPage: number) => {
    if(currentPage < enrolledTraining.length+4){
      console.log("currentPage",currentPage)
      setStep(currentPage);
      Update(currentPage);
    }
  }

  return (
    <>
      <TrainingBreadcrumb/>
      <Box sx={{
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-evenly",
        gap:1
      }}>
        <TrainingSidebar course={course} item={enrolledTraining}  currentPage={step}/>
        <TrainingInfoFragment course={course} item={enrolledTraining} quiz={true} handleNext={handleNext} isReview={false} />
      </Box>

    </>
  );
}

export default TrainingInfo;
