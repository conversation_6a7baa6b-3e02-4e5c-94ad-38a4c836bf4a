import PembayaranLayout from "@/pages/pembayaran/PembayaranLayout";
import PembayaranKaunter from "@/pages/pembayaran/perkhidmatan-kaunter/pembayaran-kaunter";
import { Route, Outlet, Navigate } from "react-router-dom";
import Kemaskini from "../../pages/pembayaran/perkhidmatan-kaunter/pembayaran-kaunter/Kemaskini";
import RekodPembayaran from "../../pages/pembayaran/perkhidmatan-kaunter/rekod-pembayaran";

export const pembayaran = {
  routes: (
    <Route
      element={
        <PembayaranLayout>
          <Outlet />
        </PembayaranLayout>
      }
    >
      <Route path="pembayaran">
        <Route index element={<Navigate to="perkhidmatan" replace />} />
        <Route path="perkhidmatan">
          <Route index element={<Navigate to="pembayaran-kaunter" replace />} />
          <Route path="pembayaran-kaunter" element={<PembayaranKaunter />} />
          <Route path="pembayaran-kaunter/kemaskini" element={<Kemaskini />} />

          <Route path="rekod-pembayaran" element={<RekodPembayaran />} />
        </Route>
      </Route>
    </Route>
  ),
};
