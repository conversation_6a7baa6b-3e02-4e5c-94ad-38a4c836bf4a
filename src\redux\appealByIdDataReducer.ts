import { createSlice } from '@reduxjs/toolkit';

interface AppealByIdStore {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: AppealByIdStore = {
  data: null,
  loading: false,
  error: null,
};

export const appealByIdDataSlice = createSlice({
  name: 'appealByIdData',
  initialState,
  reducers: {
    setAppealByIdDataRedux(state, action) {
      state.data = action.payload;
    },
    setAppealByIdLoading(state, action) {
      state.loading = action.payload;
    },
    setAppealByIdError(state, action) {
      state.error = action.payload;
    },
  },
});

export const { setAppealByIdDataRedux, setAppealByIdLoading, setAppealByIdError } = appealByIdDataSlice.actions;
export default appealByIdDataSlice.reducer;
