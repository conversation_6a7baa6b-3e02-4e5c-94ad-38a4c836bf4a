import { LogicalFilter } from "@refinedev/core";
import { FieldValues, UseFormReturn } from "react-hook-form";
import { omitKeysFromObject } from "../utils";

interface UseAdvancedFilterProps {
  pageKey?: string;
  pageSizeKey?: string;
  formMethods: UseFormReturn<FieldValues>;
  baseFilters?: LogicalFilter[];
}

export const useQueryFilterForm = ({
  formMethods,
  pageKey = "page",
  baseFilters = [],
}: UseAdvancedFilterProps) => {
  const { getValues } = formMethods;

  const getBaseFilters = (pageSize: number, page: number): LogicalFilter[] => [
    { field: "pageSize", value: pageSize, operator: "eq" },
    { field: pageKey, value: page, operator: "eq" },
    ...baseFilters,
  ];

  const buildFilters = (
    pageSize: number,
    page: number,
    additionalFilters?: Record<string, any>
  ): LogicalFilter[] => {
    const filters = getBaseFilters(pageSize, page);
    const keysToOmit = baseFilters.map((filter) => filter.field);

    const formValues = getValues();
    const filteredValues = omitKeysFromObject(formValues, keysToOmit);

    Object.entries(filteredValues).forEach(([field, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        filters.push({
          field,
          value,
          operator: "eq",
        });
      }
    });

    if (additionalFilters) {
      Object.entries(additionalFilters).forEach(([field, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          filters.push({
            field,
            value,
            operator: "eq",
          });
        }
      });
    }

    return filters;
  };

  return { buildFilters, getBaseFilters };
};
