/* eslint-disable react-refresh/only-export-components */
import { lazy, Suspense } from "react";
import { Navigate, Route } from "react-router-dom"

import { useSelector } from "react-redux";
import { PageLoader } from "@/components";

import { PenguatkuasaanInternalMain } from "@/pages/penguatkuasaan/internal/Main"

const Pembatalan = lazy(() => import("@/pages/penguatkuasaan/internal/pembatalan"));
const CiptaPembatalanForm = lazy(() => import("@/pages/penguatkuasaan/internal/pembatalan/cipta-pembatalan/Form"));
const SenaraiPembatalanForm = lazy(() => import("@/pages/penguatkuasaan/internal/pembatalan/senarai-pembatalan/Form"));

import { PORTAL_INTERNAL } from "@/helpers"
import { PenguatkuasaanInternalSekatanLiabiliti } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Base"
import { NavbarEnforcement } from "@/components/navbar/Enforcement"
import { PenguatkuasaanInternalSekatanLiabilitiCreate } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create"
import { LayoutEnforcementDetails } from "@/components/layout/EnforcementDetails"
import { PenguatkuasaanInternalSekatanLiabilitiCreate002 } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create002"

import { getUserPortal } from "@/redux/userReducer"

const routeComponents = (
  <Route path="penguatkuasaan"
        element={
          <Suspense fallback={<PageLoader />}>
            <PenguatkuasaanInternalMain />
          </Suspense>
        }
      >
    <Route element={<NavbarEnforcement />}>
      {/**
       * @todo change to aduan if penguatkuasaan aduan UI is ready
       */}
      <Route index element={<Navigate to="pembatalan" />} />
      <Route path="aduan" element={<h2>Aduan</h2>} />
      <Route path="siasatan" element={<h2>Siasatan</h2>} />
      <Route path="pembatalan">
          <Route index element={<Pembatalan />} />
          <Route path="cipta-pembatalan/:id" element={<CiptaPembatalanForm />} />
          <Route path="senarai-pembatalan/:id" element={<SenaraiPembatalanForm />} />
      </Route>
      <Route path="sekatan_liabiliti" element={<PenguatkuasaanInternalSekatanLiabiliti />} />
      <Route path="pengurusan_fee" element={<h2>Pengurusan Fee</h2>} />
      <Route path="daftar_panduan_pengurusan_pertubuhan" element={<h2>Daftar Panduan Pengurusan Pertubuhan</h2>} />
      <Route path="pengurusan_notis" element={<h2>Pengurusan Notis</h2>} />
      <Route path="red_flag" element={<h2>Red Flag</h2>} />
      <Route path="daftar_nama_larangan" element={<h2>Daftar Nama Larangan</h2>} />
      <Route path="pemeriksaan" element={<h2>Pemeriksaan</h2>} />
      <Route path="pendakwaan" element={<h2>Pendakwaan</h2>} />
    </Route>
    <Route element={<LayoutEnforcementDetails />}>
      <Route path="sekatan_liabiliti/create" element={<PenguatkuasaanInternalSekatanLiabilitiCreate />} />
      <Route path="sekatan_liabiliti/create/:id" element={<PenguatkuasaanInternalSekatanLiabilitiCreate002 />} />
      <Route path="sekatan_liabiliti/:id" element={<PenguatkuasaanInternalSekatanLiabilitiCreate002 mode="VIEW" />} />
      <Route path="sekatan_liabiliti/update/:id" element={<PenguatkuasaanInternalSekatanLiabilitiCreate002 mode="UPDATE" />} />
    </Route>
  </Route>
)

/**
 * @deprecated please use {@link usePenguatKuasaanRoutes} instead.
 */
export const penguatKuasaan = {
  routes: () => localStorage.getItem("portal") === PORTAL_INTERNAL && routeComponents
}

export const usePenguatKuasaanRoutes = () => {
  const userPortal = useSelector(getUserPortal)

  const routes = userPortal === parseInt(PORTAL_INTERNAL) && routeComponents

  return {
    routes
  }
}
