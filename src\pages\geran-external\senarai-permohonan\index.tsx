import { useNavigate } from "react-router-dom";
import { globalStyles } from "@/helpers";

import { Box, Typography, IconButton } from "@mui/material";

import { EditIcon } from "@/components/icons";

const SenaraiPermohonan: React.FC = () => {
  const navigate = useNavigate();
  const classes = globalStyles();

  return (
    <>
      <Box className={classes.section} mt={1}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            Persatuan
          </Typography>

          <Box className={classes.sectionBox} mb={1}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                className="label"
                sx={{ flex: 2, textAlign: "center" }}
              >
                Persatuan Penduduk Presint 11F Putrajaya
              </Typography>

              <Typography
                className="label"
                sx={{ flex: 2, textAlign: "center" }}
              >
                PPM-024-16-21022013
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  flex: 1,
                }}
              >
                <Typography
                  className={classes.statusBadge}
                  sx={{
                    border: "1px solid #9747FF",
                    margin: "0 !important",
                  }}
                >
                  Layak
                </Typography>

                <IconButton onClick={() => navigate("./senarai-permohonan/20")}>
                  <EditIcon color="#1DC1C1" />
                </IconButton>
              </Box>
            </Box>
          </Box>
          <Box className={classes.sectionBox} mb={1}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                className="label"
                sx={{ flex: 2, textAlign: "center" }}
              >
                Kelab Kebajikan dan Rekreasi Seksyen National Health
                Financing,KKM (Kelab NHF)
              </Typography>

              <Typography
                className="label"
                sx={{ flex: 2, textAlign: "center" }}
              >
                PPM-024-16-21022013
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  flex: 1,
                }}
              >
                <Typography
                  className={classes.statusBadge}
                  sx={{
                    border: "1px solid #FF0000",
                    margin: "0 !important",
                  }}
                >
                  Tidak Layak
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default SenaraiPermohonan;
