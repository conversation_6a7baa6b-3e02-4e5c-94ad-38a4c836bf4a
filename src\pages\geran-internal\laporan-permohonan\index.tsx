import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { CustomTabsPanel } from "@/components";
import LulusPenilaian from "./LulusPenilaian";
import TidakBerjayaPenilaian from "./TidakBerjayaPenilaian";

const LaporanPermohonan: React.FC = () => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const isMyLanguage = i18n.language === "my";

  const [initialTab, setInitialTab] = useState(0);

  useEffect(() => {
    if (location.state?.tab === "senarai-pembatalan") {
      setInitialTab(1);
    } else {
      setInitialTab(0);
    }
  }, [location.state]);

  const tabItems = [
    {
      label: "Lulus Penilaian",
      content: <LulusPenilaian />,
    },
    {
      label: "Tidak Berjaya Penilaian",
      content: <TidakBerjayaPenilaian />,
    },
  ];

  return <CustomTabsPanel tabs={tabItems} initialTab={initialTab} />;
};

export default LaporanPermohonan;
