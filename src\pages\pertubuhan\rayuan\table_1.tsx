import React, { useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  InputAdornment,
  SvgIcon,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { ApplicationStatusEnum, SebabRyuanEnum } from "../../../helpers/enums";
import { useTranslation } from "react-i18next";
import { useQuery } from "@/helpers";
import { FieldValues, useForm } from "react-hook-form";
import SearchIcon from "@mui/icons-material/Search";
import { ButtonPrimary, DataTable } from "@/components";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";

interface RayuanTable1Props {
  refreshTable2: () => void;
}

const RayuanTable1: React.FC<RayuanTable1Props> = ({ refreshTable2 }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [searchQuery, setSearchQuery] = useState("");

  const [searchAppeal, setSearchAppeal] = useState<any>("");

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 3,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");
  const {
    data: appealListDataResponse,
    isLoading: isLoadingAppealListData,
    refetch,
  } = useQuery({
    url: "society/appeal/getUserSocieties",
    filters: [
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
      {
        field: "searchQuery",
        value: searchQuery,
        operator: "eq",
      },
    ],
  });

  const totalList = appealListDataResponse?.data?.data?.total ?? 0;
  const rowData = appealListDataResponse?.data?.data?.data ?? [];

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      setValue("page", 1);
      setSearchQuery(value);
    }
  };

  const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();

  const handleRayuanClick = (rowData: any, refreshTable2: () => void) => {
    create(
      {
        url: `${API_URL}/society/appeal/create`,
        method: "post",
        values: {
          societyId: rowData?.societyId,
          branchId: rowData?.branchId,
          societyNonCitizenCommitteeId: rowData?.nonCitizenId,
          amendmentId: rowData?.amendmentId,
          societyNo: rowData?.societyNo,
          // identificationNo: rowData?.identificationNo,
          idSebab: rowData?.idSebab,
          // sebabLain: rowData?.sebabLain,
          // applicationStatusCode: 1,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data && rowData?.societyId) {
            refreshTable2();
            refetch();
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const columns = [
    {
      field: "societyName",
      headerName: t("organizationName"),
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "idSebab",
      headerName: t("jenisRayuan"),
      width: 300,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row?.idSebab ? (
          <Typography sx={{ fontSize: "14px" }}>
            {t(
              SebabRyuanEnum[
                (row?.idSebab as keyof typeof SebabRyuanEnum) || "0"
              ]
            )}
          </Typography>
        ) : (
          "-"
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "societyApplicationStatusCode",
      headerName: t("organizationStatus"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return t(
          ApplicationStatusEnum[
            (row?.societyApplicationStatusCode as keyof typeof ApplicationStatusEnum) ||
              "0"
          ]
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "countdown",
      headerName: t("appealPeriod"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      width: 100,
      sortable: false,
      disableColumnMenu: true,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;

        return (
          <Box>
            <ButtonPrimary
              onClick={() => handleRayuanClick(row, refreshTable2)}
              size="small"
              fullWidth={false}
              sx={{ minWidth: "100px" }}
            >
              {t("rayuan")}
            </ButtonPrimary>
          </Box>
        );
      },
    },
  ];

  return (
    <Box
      sx={{
        border: "1px solid #DADADA",
        borderRadius: "10px",
        p: { xs: 1, sm: 2, md: 3 },
      }}
    >
      <Typography variant="h6" className="title" sx={{ mb: 4 }}>
        {t("appealWaitingList")}
      </Typography>
      <TextField
        fullWidth
        variant="outlined"
        placeholder={t("namaPertubuhan")}
        sx={{
          display: "block",
          boxSizing: "border-box",
          maxWidth: 570,
          marginInline: "auto",
          height: "40px",
          background: "var(--border-grey)",
          opacity: 0.5,
          border: "1px solid var(--text-grey)",
          borderRadius: "10px",
          "& .MuiOutlinedInput-root": {
            height: "40px",
            "& fieldset": {
              border: "none",
            },
          },
        }}
        onKeyDown={onSearchKeyDown}
        onChange={(e) => setSearchAppeal(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon
                sx={{
                  color: "var(--text-grey-disabled)",
                  marginLeft: "8px",
                }}
              />
            </InputAdornment>
          ),
        }}
      />
      <DataTable
        columns={columns as any}
        rows={rowData}
        page={page}
        rowsPerPage={pageSize}
        totalCount={totalList}
        onPageChange={(newPage) => setValue("page", newPage)}
        onPageSizeChange={(newPageSize) => {
          setValue("page", 1);
          setValue("pageSize", newPageSize);
        }}
        isLoading={isLoadingAppealListData}
      />
    </Box>
  );
};

export default RayuanTable1;
