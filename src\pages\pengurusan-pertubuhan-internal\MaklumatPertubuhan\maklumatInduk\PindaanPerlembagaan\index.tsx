import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { FieldValues, useForm } from "react-hook-form";
import { useParams } from "react-router-dom";

import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import {
  DecisionOptions,
  DecisionOptionsCode,
  MeetingTypeOption,
  ROApprovalType,
} from "../../../../../helpers/enums";
import { ButtonPrimary } from "../../../../../components/button";
import { formatDateToDDMMYYYY } from "../../../../../helpers/utils";
import DisabledTextField from "../../../../../components/input/DisabledTextField";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import { useMutation } from "@/helpers";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

function PindaanInfoView() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { amendmentId, societyId } = useParams();
  const amendmentIdDecoded = atob(amendmentId || "");
  const societyIdDecoded = atob(societyId || "");
  const { t } = useTranslation();
  const navigate = useNavigate();

  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        societyName: "",
        societyNo: "",
        amendmentType: "",
        constitutionType: "",
        meetingDate: "",
        meetingType: "",
        decisionDate: "",
        noteRo: "",
        roName: "",
        paymentDate: "",
        applicationStatusCode: "",
      },
    });

  const { data: amendmentData } = useCustom({
    url: `${API_URL}/society/amendment/getAmendmentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        id: amendmentIdDecoded,
      },
    },
    queryOptions: {
      enabled: !!amendmentIdDecoded,
      onSuccess: (responseData) => {
        const res = responseData?.data?.data?.data?.[0];
        setValue("societyName", res?.societyName);
        setValue("societyNo", res?.societyNo);
        setValue("amendmentType", res?.amendmentType);
        setValue("constitutionType", res?.constitutionType);
        setValue("meetingDate", res?.meetingDate);
        setValue("meetingType", res?.meetingType);
        setValue("paymentDate", res?.paymentDate);
        setValue("applicationStatusCode", res?.applicationStatusCode);
      },
    },
  });

  const decisionOptions = DecisionOptionsCode(t);

  const { fetch: getQuery, isLoading: isLoadingQuery } = useMutation({
    url: "society/roQuery/getQuery",
    method: "post",
    onSuccess: (data: any) => {
      const queryData = data?.data?.data?.[0];
      if (queryData) {
        console.log("queryData", queryData);
        setValue("noteRo", queryData?.note);
        setValue("roName", queryData?.officerName);
        const selectedDecision = decisionOptions.find(
          (item) => item.value === 36
        );
        setValue("decision", selectedDecision?.label);
        setValue("decisionDate", queryData?.decisionDate);
      }
    },
    onSuccessNotification: () => {},
  });

  const { fetch: getROApproval, isLoading: isLoadingROApproval } = useMutation({
    url: "society/roApproval/getAll",
    method: "post",
    onSuccess: (data: any) => {
      const queryData = data?.data?.data?.[0];
      if (queryData) {
        console.log("queryData", queryData);
        setValue("noteRo", queryData?.note);
        setValue("roName", queryData?.officerName);
        const selectedDecision = decisionOptions.find(
          (item) => item.value === queryData?.decision
        );
        setValue("decision", selectedDecision?.label);
        setValue("decisionDate", queryData?.decisionDate);
      }
    },
    onSuccessNotification: () => {},
  });

  useEffect(() => {
    if (
      amendmentIdDecoded &&
      societyIdDecoded &&
      getValues("applicationStatusCode")
    ) {
      // @ts-ignore
      if (Number(getValues("applicationStatusCode")) === 36) {
        const payload = {
          societyId: societyIdDecoded,
          amendmentId: amendmentIdDecoded,
          roApprovalType: ROApprovalType.SOCIETY_AMENDMENT.code,
        };

        getQuery(payload);
      } else {
        const payload = {
          societyId: societyIdDecoded,
          amendmentId: amendmentIdDecoded,
          type: ROApprovalType.SOCIETY_AMENDMENT.code,
          decision: getValues("applicationStatusCode"),
        };

        getROApproval(payload);
      }
    }
  }, [
    amendmentIdDecoded,
    societyIdDecoded,
    getValues("applicationStatusCode"),
  ]);

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {getValues("societyName")}
              <br />
              {getValues("societyNo")}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            backgroundColor: "white",
            padding: "18px 16px",
            borderRadius: "14px",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="16px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("organizationInformation")}
            </Typography>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organization_name")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    getValues("societyName") ? getValues("societyName") : "-"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationNumberLowerCase")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={getValues("societyNo") ? getValues("societyNo") : "-"}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("constitutionType")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    getValues("constitutionType")
                      ? getValues("constitutionType")
                      : "-"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("jenisMesyuarat")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    getValues("meetingType")
                      ? getMeetingLabel(getValues("meetingType"))
                      : "-"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("meetingDate")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    getValues("meetingDate")
                      ? formatDateToDDMMYYYY(getValues("meetingDate"))
                      : "-"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={4} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("tarikhBayar")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    getValues("paymentDate")
                      ? formatDateToDDMMYYYY(getValues("paymentDate"))
                      : "-"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("resultdateif")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    getValues("decisionDate")
                      ? formatDateToDDMMYYYY(getValues("decisionDate"))
                      : "-"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("diluluskanOleh")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={getValues("roName") ? getValues("roName") : "-"}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("keputusan")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={getValues("decision")} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("remarks")} ({t("jikaAda")})
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  multiline
                  row={3}
                  value={getValues("noteRo") ? getValues("noteRo") : "-"}
                />
              </Grid>
            </Grid>
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              sx={{
                display: "block",
                backgroundColor: "var(--primary-color)",
                color: "white",
                "&:hover": { backgroundColor: "#19ADAD" },
                textTransform: "none",
                fontWeight: 400,
              }}
              onClick={() => navigate(-1)}
            >
              {t("back")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>
    </>
  );
}

export default PindaanInfoView;
