import React from "react";
import { useTranslation } from "react-i18next";
import { useCallback, useEffect, useMemo } from "react";
import {
  useWatch,
  useFormContext,
  useFieldArray,
  FieldValues,
} from "react-hook-form";
import { useDebounce } from "@/helpers";

import { Box, Typography } from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import FormFieldRow from "@/components/form-field-row";
import Label from "@/components/label/Label";
import MemberRow from "./MemberRow";
import AttendeesInput from "./AttendeesInput";

interface MemberAttendancesProps {
  disabledState: boolean;
  isEditable: boolean;
  isViewOnly: boolean;
  meetingDetail?: boolean;
}

export interface IMember {
  id?: number;
  tempId?: string;
  name: string;
  position: string;
  status?: number;
}

interface IFormContext extends FieldValues {
  totalAttendees: number;
  memberAttendances: IMember[];
}

const MemberAttendances: React.FC<MemberAttendancesProps> = React.memo(
  ({ disabledState, isEditable, isViewOnly, meetingDetail }) => {
    const { t, i18n } = useTranslation();
    const { control, setValue } = useFormContext<IFormContext>();
    const isMyLanguage = i18n.language === "my";

    const { fields, append, remove, update } = useFieldArray({
      control,
      name: "memberAttendances",
      keyName: "tempId",
    });

    const totalAttendees = useWatch({
      control,
      name: "totalAttendees",
      defaultValue: 0,
    });

    const debouncedTotalAttendees = useDebounce(totalAttendees, 500);

    const filteredMemberAttendances = useMemo(
      () => fields.filter((field: any) => field.status !== -1),
      [fields]
    );

    const deletedMembers = useMemo(
      () => fields.filter((field: any) => field.status === -1),
      [fields]
    );
    const deletedMembersCount = deletedMembers.length;

    const handleAddMember = useCallback(() => {
      const newMember = {
        name: "",
        position: "",
      };
      append(newMember);
      setValue("totalAttendees", Number(totalAttendees) + 1);
    }, [fields, append, setValue]);

    const handleRemoveMember = useCallback(
      (id: number | null, tempId: string | null) => {
        if (id) {
          const findIndex = fields.findIndex((field: any) => field.id === id);

          if (findIndex !== -1) {
            const updatedMember = { ...fields[findIndex], status: -1 };
            update(findIndex, updatedMember);
          } else {
            console.warn(`Member with id ${id} not found.`);
          }
        } else if (tempId) {
          const findIndex = fields.findIndex(
            (field: any) => field.tempId === tempId
          );

          if (findIndex !== -1) {
            remove(findIndex);
          } else {
            console.warn(`Member with tempId ${tempId} not found.`);
          }
        }

        const newTotalAttendees = Math.max(0, Number(totalAttendees) - 1);
        setValue("totalAttendees", newTotalAttendees);
      },
      [fields, update, remove, setValue, totalAttendees]
    );

    const updateMemberAttendances = useCallback(
      (newTotal: number) => {
        const currentTotal = fields.filter(
          (field: any) => field.status !== -1
        ).length;

        if (newTotal > currentTotal) {
          const newMembers = Array.from(
            { length: newTotal - currentTotal },
            (_, i) => ({
              name: "",
              position: "",
            })
          );
          append(newMembers);
        } else if (newTotal < currentTotal) {
          const updatedMembers = fields
            .map((field: any, index: number) => {
              if (index >= newTotal) {
                if (field.id) {
                  return { ...field, status: -1 };
                } else {
                  return null;
                }
              }
              return field;
            })
            .filter((field: any) => field !== null);

          setValue("memberAttendances", updatedMembers);
        }
      },
      [fields, append, setValue]
    );

    useEffect(() => {
      if (
        !isNaN(debouncedTotalAttendees) &&
        typeof debouncedTotalAttendees === "number"
      ) {
        updateMemberAttendances(totalAttendees);
      }
    }, [debouncedTotalAttendees]);

    return (
      <Box
        sx={{
          borderRadius: "10px",
          padding: "41px 25px 25px",
          border: "0.5px solid #DADADA",
          marginBottom: "15px",
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight={500}
          marginBottom="20px"
        >
          {isMyLanguage
            ? "Kehadiran Ahli Mesyuarat"
            : "Attendance of Meeting Members"}
        </Typography>

        <FormFieldRow
          label={<Label text={t("jumlahKehadiranAhliMesyuarat")} />}
          value={<AttendeesInput disabledState={disabledState} />}
        />

        <FormFieldRow
          align="flex-start"
          label={<Label text={t("kehadiranAjk")} />}
          value={
            filteredMemberAttendances.length > 0 ? (
              filteredMemberAttendances.map(
                (member: IMember, index: number) => (
                  <React.Fragment key={member.id ?? member.tempId}>
                    <MemberRow
                      member={member}
                      index={index}
                      handleRemoveMember={handleRemoveMember}
                      isViewOnly={isViewOnly}
                      isEditable={isEditable}
                      meetingDetail={meetingDetail}
                      disabledState={disabledState}
                      deletedMembersCount={deletedMembersCount}
                    />
                  </React.Fragment>
                )
              )
            ) : (
              <Typography
                sx={{
                  fontSize: "14px",
                  color: "#666666",
                  fontWeight: "400 !important",
                  "& span": {
                    color: "red",
                  },
                }}
              >
                {isMyLanguage ? "Sila tambah AJK" : "Please add a committee."}
              </Typography>
            )
          }
        />

        <Box
          sx={{
            marginLeft: "auto",
            display: "flex",
            gap: "10px",
            width: "fit-content",
          }}
        >
          <ButtonPrimary
            onClick={handleAddMember}
            sx={{
              backgroundColor: "var(--primary-color)",
              width: "100px",
              height: "32px",
              color: "white",
              "&:hover": { backgroundColor: "#19ADAD" },
              textTransform: "none",
              fontWeight: 400,
              fontSize: "8px",
            }}
            disabled={disabledState}
          >
            {t("addAjk")}
          </ButtonPrimary>
        </Box>
      </Box>
    );
  }
);

export default MemberAttendances;
