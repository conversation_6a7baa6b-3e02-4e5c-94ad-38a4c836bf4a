import { ApplicationStatus, OrganisationPositions } from "@/helpers/enums";
import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  IconButton,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import Input from "@/components/input/Input";
import { useCustomMutation, CrudFilter } from "@refinedev/core";
import { t } from "i18next";
import { useState } from "react";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useNavigate, useParams } from "react-router-dom";
import useQuery from "@/helpers/hooks/useQuery";
import { EyeIcon } from "@/components/icons";
import { AddressList, Ajk } from "../interface";
import { useTranslation } from "react-i18next";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { API_URL } from "@/api";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import DataTable, { IColumn } from "@/components/datatable";
import { getLocalStorage } from "@/helpers";

const ListSenaraiAjk = () => {
  const { id } = useParams(); // Access the dynamic `id` from the URL (e.g., "406")
  const navigate = useNavigate();
  // @ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const { t } = useTranslation();
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  // const [addressList, setAddressList] = useState<AddressList[]>([]);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const [statementComplete, setStatementComplete] = useState(false);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  const [appointedDateList, setAppointedDatesList] = useState<any>([]);
  const [savedAppointmentDate, setSavedAppointmentDate] = useState("");
  // useQuery({
  //   url: `society/admin/address/list`,
  //   onSuccess: (data) => {
  //     const list = data?.data?.data || [];
  //     setAddressList(list);
  //   },
  // });
  const addressList = getLocalStorage("address_list", []); 
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const kemaskiniAJK = () => {
    navigate("../../ajk/jawatankuasa");
  };

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const [ajkCount, setAjkCount] = useState<number>(0);
  useQuery({
    url: `society/committee/countAjk`,
    filters: [{ field: "societyId", operator: "eq", value: societyId }],
    onSuccess: (data) => {
      const ajkCount = data?.data?.data || [];
      console.log(ajkCount);
      setAjkCount(ajkCount.count);
    },
  });

  const [ajkList, setAjkList] = useState<Ajk[]>([]);
  useQuery({
    url: `society/branch/committee/listAjk`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const ajkList = data?.data?.data?.data || [];
      setAjkList(ajkList);
    },
  });

  const handleViewAjk = (ajk: Ajk) => {
    navigate("view", {
      state: {
        ajk: ajk,
      },
    });
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    reset: resetForm,
  } = useForm<FieldValues>({
    defaultValues: {
      acknowledgeAjk: false,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
      const generalInfo = data?.data?.data || [];
      setValue("acknowledgeAjk", generalInfo?.acknowledgeAjk);
    },
  });

  const { mutate: saveGeneralInfo, isLoading: isLoadingSaveBank } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = {
      acknowledgeAjk: data.acknowledgeAjk,
    };
    // console.log(payload)
    if (isDisabled || statementComplete) {
      navigate("../juruaudit", {
        state: {
          societyId: societyId,
          statementId: statementId,
          year: year,
        },
      });
    } else {
      saveGeneralInfo(
        {
          url: `${API_URL}/society/statement/societyInfo/update`,
          method: "put",
          values: {
            ...payload,
            societyId: societyId,
            statementId: statementId,
            branchId: branchDataRedux.id,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess: () => {
            handleNext();
            navigate(
              `/pertubuhan/society/${societyId}/senarai/cawangan/view/penyataTahunan/juruaudit`,
              {
                state: {
                  societyId: societyId,
                  statementId: statementId,
                  year: year,
                  branchId: branchDataRedux.id,
                },
              }
            );
          },
        }
      );
    }
  };

  // ============================

  const { data, isLoading, refetch } = useQuery({
    url: `society/statement/general/get`,
    filters: [
      {
        field: "statementId",
        value: statementId,
        operator: "eq",
      },
      {
        field: "societyId",
        value: societyId,
        operator: "eq",
      },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const date = data?.data?.data?.ajkAppointedDate;
      setSavedAppointmentDate(date);
    },
  });

  const { data: AppointedDatesList, isLoading: AppointedDatesListIsLoading } =
    useQuery({
      url: `society/statement/${statementId}/getCommitteesAppointedDates`,
      filters: [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: branchDataRedux.id },
      ],
      onSuccess: (data) => {
        const responseData = data?.data?.data?.ajkAppointedDates?.[0];
        setAppointedDatesList(responseData);
      },
    });

  const {
    refetch: fetchCommitteesList,
    isLoading: fetchCommitteesListIsLoading,
  } = useQuery({
    url: `society/statement/${statementId}/getCommitteesForStatement`,
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: societyId,
      } as CrudFilter,
      ...(savedAppointmentDate
        ? [
            {
              field: "appointedDate",
              operator: "eq",
              value: savedAppointmentDate,
            } as CrudFilter,
          ]
        : []),
    ],
    onSuccess: (data) => {
      const responseData = data?.data?.data?.committees || [];
      setAjkList(responseData);
    },
  });

  const columns: IColumn[] = [
    {
      field: "position",
      headerName: t("position"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {t(
              `${
                OrganisationPositions.find(
                  (item) => item?.value === Number(row?.designationCode)
                )?.label || ""
              }`
            )}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "name",
      headerName: t("name"),
      align: "center",
      flex: 1,
    },
    {
      field: "email",
      headerName: t("email"),
      align: "center",
      flex: 1,
    },
    {
      field: "negeri",
      headerName: t("negeri"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row; 
        return (
          <Box>
            {
              addressList.find(
                (item: any) => item.id.toString() === row.committeeStateCode
              )?.name
            }
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      align: "center",
      headerName: "",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            <IconButton onClick={() => handleViewAjk(row)}>
              <EyeIcon />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <span style={{ color: "red", fontSize: "14px" }}>
              {t("peringatan")} :
            </span>
            <ul
              style={{
                color: "#666666",
                fontSize: "11px",
                paddingInlineStart: "15px",
              }}
            >
              <li>{t("penyataTahunan_ajk_peringatan_point_1")}</li>
              <li>{t("penyataTahunan_ajk_peringatan_point_2")}</li>
              <li>{t("penyataTahunan_ajk_peringatan_point_3")}</li>
            </ul>
          </Box>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Grid container spacing={2}>
              <Grid item xs={12} md={12}>
                <Input
                  type="date"
                  required
                  label={t("ajkAppointmentList")}
                  availableDate={appointedDateList}
                  value={savedAppointmentDate}
                  onChange={(e) => {
                    setSavedAppointmentDate(e.target.value);
                    setValue("appointmentDate", e.target.value);
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              textAlign: "center",
              p: 3,
              mb: 2,
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                color: "var(--primary-color)",
                fontSize: 14,
                fontWeight: "500 !important",
              }}
            >
              {t("bilanganAhliJawatankuasaTerkini")}
            </Typography>
            <Typography
              sx={{
                color: "#666666",
                fontSize: 14,
                fontWeight: "500 !important",
              }}
            >
              {ajkCount} Orang
            </Typography>
          </Box>

          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("ajkList")}
            </Typography>
            <Stack
              direction="row"
              spacing={2}
              mt={2}
              sx={{ pl: 1, width: "100%" }}
              justifyContent="flex-end"
            >
              {isAliranTugasAccess ? (
                <ButtonPrimary
                  sx={{
                    bgcolor: "transparent",
                    color: "#666666",
                    boxShadow: "none",
                    border: "1px solid #67D1D1",
                    fontSize: "12px",
                    fontWeight: "500 !important",
                  }}
                  onClick={kemaskiniAJK}
                >
                  {t("kemaskiniAJK")}
                </ButtonPrimary>
              ) : null}
            </Stack>

            <DataTable
              columns={columns}
              isLoading={fetchCommitteesListIsLoading}
              rows={ajkList}
              page={page}
              rowsPerPage={pageSize}
              pagination={false}
              totalCount={ajkList.length || 0}
              onPageChange={(newPage) => setValue("page", newPage)}
              onPageSizeChange={(newPageSize) =>
                setValue("pageSize", newPageSize)
              }
            /> 
            
          </Box>
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Controller
              name="acknowledgeAjk"
              control={control}
              rules={{
                // The field must be true (checked) otherwise return an error message.
                validate: (value) => value === true || t("pleaseSelect"),
              }}
              render={({ field, fieldState: { error } }) => (
                <>
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        disabled={isDisabled || statementComplete}
                        checked={field.value}
                        sx={{ fontWeight: 100 }}
                      />
                    }
                    label={
                      <Typography
                        style={{
                          fontWeight: "normal",
                          fontSize: "11px",
                          color: "#333",
                          lineHeight: "1.5",
                        }}
                      >
                        Adalah saya dengan ini mengesahkan bahawa semua ahli
                        jawatankuasa bagi pertubuhan diatas tidak hilang
                        kelayakan memegang jawatan dibawah seksyen 9A akta
                        pertubuhan 1966. Saya seterusnya mengaku bahawa
                        keterangan yang diberikan diatas adalah sesungguhnya
                        benar .*
                      </Typography>
                    }
                  />
                  {error && (
                    <Typography variant="caption" color="error">
                      {error.message}
                    </Typography>
                  )}
                </>
              )}
            />
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                bgcolor: "white",
                "&:hover": { bgcolor: "white" },
                width: isMobile ? "100%" : "auto",
              }}
              onClick={handleBackActions}
            >
              {t("kembali")}
            </ButtonOutline>
            <ButtonPrimary
              type="submit"
              variant="contained"
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              // onClick={handleNextActions}
              // disabled={createFeedBackIsloading}
            >
              {t("next")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </form>
    </>
  );
};

export default ListSenaraiAjk;
