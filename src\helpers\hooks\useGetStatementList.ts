import useMutation from "./useMutation"; 
import useQuery from "./useQuery";

interface useGetStatementContributionListProps {
  id: string | number | null;
  statementId: number;
  contributionCode:number;
  enabled?: boolean;  
  onSuccess?: (data: any) => void;
  onSuccessNotification?: (data?: any) => void; 
}

export const useGetStatementContributionList = ({
  id,
  statementId,
  enabled = false,
  contributionCode,
  onSuccess,
  onSuccessNotification
}: useGetStatementContributionListProps) => {
  
return  useQuery({
    url: `society/statement/statement-contribution/list`,
    filters: [
      { field: "societyId", operator: "eq", value: id },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "contributionCode", operator: "eq", value: contributionCode },
    ],
    autoFetch: enabled, 
    onSuccess 
  });
};
     