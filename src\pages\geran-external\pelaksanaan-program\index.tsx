import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import { globalStyles, DocumentUploadType } from "@/helpers";

import { Box, Typography } from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  FileUploadController,
} from "@/components";
import FileUploader from "@/components/input/fileUpload";

const PelaksanaanProgram: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();

  const { control, handleSubmit, getValues, setValue } = useForm<FieldValues>({
    defaultValues: {},
  });

  return (
    <>
      <Box className={classes.section} mt={1} mb={1}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            Pelaksanaan Program
          </Typography>

          <Box className={classes.sectionBox} mb={1}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                className="label"
                sx={{ flex: 2, textAlign: "center" }}
              >
                Kelab Kebajikan dan Rekreasi Seksyen National Health
                Financing,KKM (Kelab NHF)
              </Typography>

              <Typography
                className="label"
                sx={{ flex: 2, textAlign: "center" }}
              >
                PPM-024-16-21022013
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  flex: 1,
                }}
              >
                <Typography
                  className={classes.statusBadge}
                  sx={{
                    border: "1px solid #9747FF",
                    margin: "0 !important",
                  }}
                >
                  Belum Hantar
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      <Box className={classes.section} mb={1}>
        <Box className={classes.sectionBox}>
          <Typography
            sx={{
              color: "#FF0000",
              fontWeight: "500",
              fontSize: "12px !important",
              "& span": { color: "#666666", fontWeight: "400" },
            }}
          >
            NOTA
            <span>
              : Mohon lampirkan laporan bersama resit perbelanjaan. Contoh
              Pelaporan boleh dilihat di SINI
            </span>
          </Typography>
        </Box>
      </Box>

      <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={1}>
            Aktivit Program
          </Typography>

          <FormFieldRow
            label={<Label text="Tajuk aktiviti program" />}
            value={<TextFieldController control={control} name="test" />}
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text="Diskripi program" />}
            value={
              <TextFieldController
                control={control}
                name="test"
                multiline
                rows={10}
              />
            }
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text="Mauat naik gambar aktiviti" />}
            value={
              <FileUploadController
                control={control}
                name="documentId"
                // onFileSelect={(file) => setSelectedFile(file)}
                accept=".doc,.docx,.txt,.pdf"
                allowedTypes={[
                  "application/pdf",
                  "application/msword",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "text/plain",
                ]}
                required
              />
            }
          />
        </Box>
      </Box>

      <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            Lampiran perlaksanaan program
          </Typography>

          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
            }}
          >
            <FileUploader
              type={DocumentUploadType.CITIZEN_COMMITTEE}
              societyId={1234}
              validTypes={[
                "application/pdf",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword",
                "text/plain",
              ]}
              sxContainer={{
                width: "50%",
              }}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default PelaksanaanProgram;
