import { useState, useEffect, useRef, useMemo } from "react";
import {
  Controller,
  FieldV<PERSON>ues,
  SubmitHandler,
  FormProvider,
  useFormContext,
} from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { useNotification, CreateResponse } from "@refinedev/core";
import { useTranslation } from "react-i18next";
import { useForm } from "@refinedev/react-hook-form";
import { useSecretaryReformContext } from "../../Provider";
import {
  useQuery,
  useMutation,
  useUploadPresignedUrl,
  filterEmptyValuesOnObject,
  getLocalStorage,
  MALAYSIA,
  MeetingTypeOption,
  DocumentUploadType,
  omitKeysFromObject,
  useGetDocument,
  DOCUMENT_MAX_FILE_SIZE,
  downloadFile,
} from "@/helpers";
import { yupResolver } from "@hookform/resolvers/yup";

import { Box, Typography, IconButton, Grid, Checkbox } from "@mui/material";
import {
  ButtonPrimary,
  ButtonOutline,
  Label,
  FormFieldRow,
  TextFieldController,
  SelectFieldController,
  DatePickerController,
  TimePickerController,
  DialogConfirmation,
  AWSLocationSearchMap,
  LocationSelectedParams,
} from "@/components";

import { UploadIcon, EyeIcon } from "@/components/icons";

import {
  IApiResponse,
  IMeetingDetail,
  IAddressNode,
  IMeetingInfo,
} from "@/types";

import { useMeetingSchema } from "@/schemas";

interface IMember {
  id?: number;
  tempId?: string;
  name: string;
  position: string;
}

const meetingTypeOptions = MeetingTypeOption.filter(
  (meeting) => ![1, 5].includes(meeting.value)
);

const MeetingForm: React.FC = () => {
  const navigate = useNavigate();
  const { societyId, secretaryId } = useParams();
  const { t, i18n } = useTranslation();
  const { open } = useNotification();
  const inputFileRef = useRef<HTMLInputElement>(null);
  const isMyLanguage = i18n.language === "my";

  const {
    meetingData,
    societyData,
    isEditable,
    isViewOnly,
    isFeedback,
    handleBack,
    handleSecretaryReformStatus,
    isSecretaryReformSuccess,
  } = useSecretaryReformContext();
  const {
    getValues: getSecretaryFormValues,
    setValue: setSecretaryFormValue,
    control: secretaryFormControl,
    trigger,
    watch: watchSecretaryFormValue,
  } = useFormContext();

  const id = watchSecretaryFormValue("id");

  const disabledState = isViewOnly || isFeedback;
  const requiredState = !disabledState;

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState("");
  const [currentDocId, setCurrentDocId] = useState<number | null>(null);

  const [duration, setDuration] = useState("");
  const [isConfirmOldSecretary, setIsConfirmOldSecretary] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { upload: uploadFile, isLoading: isUploadingFile } =
    useUploadPresignedUrl({
      showSuccessNotification: false,
      onSuccessUpload: () => {
        if (currentDocId) {
          deleteDocument();
        } else {
          navigate("/pertubuhan/pembaharuan-setiausaha");
        }
      },
    });

  const { getDocument } = useGetDocument({
    onSuccess: (data) => {
      setFilePreview(data?.url ?? "");
      setCurrentDocId(data?.id ?? null);
    },
  });

  const { refetch: downloadPDFTemplate } = useQuery<
    IApiResponse<{ meetingMinutes: string }>
  >({
    url: "society/pdf/getPDFTemplate?templateCode=MEETING_MINUTES_PDF",
    autoFetch: false,
    onSuccess: (data) => {
      const url = data?.data?.data?.meetingMinutes;

      if (url) window.open(url, "_blank", "noreferrer,noopener");
    },
  });

  const { fetch: deleteDocument, isLoading: isDeletingDocument } = useMutation({
    url: `society/document/deleteDocument?id=${currentDocId}`,
    method: "put",
    onSuccess: (data) => {
      navigate("/pertubuhan/pembaharuan-setiausaha");
    },
  });

  const { fetch: downloadMeetingMinute } = useMutation({
    url: "society/pdf/meetingMinutes",
    onSuccess: (data) => {
      let base64String = data?.data?.data?.byte ?? "";

      if (base64String.startsWith("JVB"))
        base64String = `data:application/pdf;base64,${base64String}`;

      downloadFile({
        data: base64String,
        name: `minit-mesyuarat.pdf`,
      });
    },
  });

  const { fetch: createMeeting, isLoading: isCreateMeeting } = useMutation<
    IApiResponse<number>
  >({
    url: "society/meeting/create",
    method: "post",
    onSuccess: (data) => handleMeetingSuccess(data),
  });

  const { fetch: updateMeeting, isLoading: isUpdateMeeting } = useMutation<
    IApiResponse<number>
  >({
    url: "society/meeting/update",
    method: "put",
    onSuccess: (data) => handleMeetingSuccess(data),
  });

  const { fetch: createSecretary, isLoading: isCreatingSecretary } =
    useMutation<IApiResponse<{ id: number }>>({
      url: "society/secretary/principal/create",
      method: "post",
      onSuccess: () => {
        handleSecretaryReformStatus(true);
        setTimeout(() => navigate("../.."), 1000);
      },
    });

  const { fetch: updateSecretary, isLoading: isUpdatingSecretary } =
    useMutation({
      url: "society/secretary/principal",
      method: "put",
      onSuccess: () => handleSecretaryReformStatus(true),
    });

  const handleMeetingSuccess = (data: CreateResponse<IApiResponse<number>>) => {
    const successCode = [200, 201];

    if (!successCode.includes(data?.data?.code))
      return open?.({
        type: "error",
        message: t("error"),
        description: "Failed to update meeting",
      });

    const secretaryFormValues = getSecretaryFormValues();
    const meetingId = meetingData?.id ?? data?.data?.data;
    const keysToSkip = [
      "workTelNoCode",
      "hpNoCode",
      "homeTelNoCode",
      "isUserApplicant",
    ];
    const filteredSecretaryValues = omitKeysFromObject(
      secretaryFormValues,
      keysToSkip
    );

    const payload = filterEmptyValuesOnObject({
      ...filteredSecretaryValues,
      meetingId,
      workTelNo: `${secretaryFormValues.workTelNoCode} ${secretaryFormValues.workTelNo}`,
      homeTelNo: `${secretaryFormValues.homeTelNoCode} ${secretaryFormValues.homeTelNo}`,
      hpNo: `${secretaryFormValues.hpNoCode} ${secretaryFormValues.hpNo}`,
      applicationStatusCode: !id ? 2 : null,
    });

    if (secretaryId) {
      updateSecretary(payload);
    } else {
      createSecretary(payload);
    }

    if (selectedFile) {
      const params = {
        type: DocumentUploadType.MEETING,
        societyId: Number(societyId),
        societyNo: societyData?.societyNo,
        meetingId,
      };
      uploadFile({ params, file: selectedFile });
    }
  };

  const defaultFormValues = {
    societyId: Number(societyId),
    societyNo: societyData?.societyNo,
    branchId: "",
    branchNo: "",
    meetingType: "",
    meetingPlace: "",
    meetingMethod: "",
    meetingPurpose: "",
    platformType: "",
    meetingDate: "",
    meetingTime: "",
    GISInformation: "2.745564, 101.707021",
    meetingAddress: "",
    state: "",
    district: "",
    city: "",
    postcode: "",
    totalAttendees: 0,
    meetingContent: "tidak",
    openingRemarks: "",
    mattersDiscussed: "",
    otherMatters: "",
    closing: "",
    providedBy: "",
    confirmBy: "",
    meetingMinute: "",
    status: 1,
    memberAttendances: [],
  };

  const schema = useMeetingSchema({
    meetingDate: requiredState,
    meetingType: requiredState,
    meetingMethod: requiredState,
    meetingTime: requiredState,
    meetingTimeTo: requiredState,
    meetingMinute: requiredState,
    meetingPurpose: requiredState,
    GISInformation: requiredState,
  });

  const methods = useForm<FieldValues>({
    defaultValues: defaultFormValues,
    resolver: yupResolver(schema),
  });

  const {
    control,
    setValue,
    getValues,
    watch,
    handleSubmit,
    reset: resetForm,
  } = methods;

  const meetingContent = watch("meetingContent");
  const meetingMethod = watch("meetingMethod");
  const meetingTime = watch("meetingTime");
  const meetingTimeTo = watch("meetingTimeTo");

  const meetingList: IMeetingInfo[] = getLocalStorage("meeting_list", []);
  const addressList: IAddressNode[] = getLocalStorage("address_list", []);

  const meetingMethodOptions = useMemo(
    () =>
      meetingList
        ?.filter((item: any) => item.pid === 2)
        .map((item: any) => ({
          value: String(item.id),
          label: item.nameBm,
        })),
    [meetingList]
  );
  const meetingPlatformOptions = useMemo(
    () =>
      meetingList
        ?.filter((item: any) => item.pid === 3)
        .map((item: any) => ({
          value: String(item.id),
          label: item.nameBm,
        })),
    [meetingList]
  );
  const cityOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === MALAYSIA)
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList]
  );
  const districtOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === Number(watch("state")))
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("state")]
  );

  const handleFileChange = (file: File | undefined) => {
    if (file) {
      const validTypes = [
        "image/svg+xml",
        "image/png",
        "image/jpeg",
        "image/gif",
        "application/pdf",
      ];
      if (validTypes.includes(file.type)) {
        const previewUrl = URL.createObjectURL(file);

        setSelectedFile(file);
        setFilePreview(previewUrl);
      } else {
        alert(
          "Invalid file type. Please upload an SVG, PNG, JPG, PDF, or GIF file."
        );
      }
    }
  };

  const setMeetingDetail = (data: IMeetingDetail) => {
    const keysToSkip = new Set([
      "createdBy",
      "createdDate",
      "modifiedBy",
      "modifiedDate",
      "id",
      "societyId",
      "societyNo",
      "branchId",
      "branchNo",
      "meetingMemberAttendances",
      "meetingTime",
      "meetingTimeTo",
      "meetingType",
    ]);

    const formattedTime = (time: string | undefined): string => {
      return time ? time.slice(0, 5) : "";
    };

    const findMeetingType = MeetingTypeOption.find(
      (meeting) => meeting.label === data.meetingType
    );

    const meetingType =
      typeof data.meetingType === "string"
        ? findMeetingType?.value ?? data.meetingType
        : data.meetingType;

    Object.entries(data).forEach(([key, value]) => {
      if (!keysToSkip.has(key)) {
        setValue(key as keyof FieldValues, value);
      }
    });

    if (data.meetingTime) {
      setValue("meetingTime", formattedTime(data.meetingTime));
    }
    if (data.meetingTimeTo) {
      setValue("meetingTimeTo", formattedTime(data.meetingTimeTo));
    }
    if (data.meetingType) {
      setValue("meetingType", Number(meetingType));
    }
    if (data.meetingMemberAttendances) {
      setValue("memberAttendances", data.meetingMemberAttendances);
    }
  };

  const resetFormExceptMeetingDate = () => {
    const currentMeetingDate = watch("meetingDate");

    resetForm({
      ...defaultFormValues,
      meetingDate: currentMeetingDate,
    });
  };

  const handleJanaMinit = async () => {
    const {
      societyId,
      meetingPlace,
      meetingAddress,
      meetingType,
      meetingPurpose,
      meetingDate,
      meetingTime,
      meetingTimeTo,
      state,
      district,
      postcode,
      platformType,
      memberAttendances,
      openingRemarks,
      mattersDiscussed,
      otherMatters,
      closing,
      providedBy,
      confirmBy,
    } = getValues();

    // Validate mandatory fields
    const mandatoryFields = [
      { field: meetingType, name: t("meetingType") },
      { field: meetingMethod, name: t("meetingMethod") },
      { field: meetingPurpose, name: t("tujuanMesyuarat") },
      { field: meetingDate, name: t("tarikhMesyuarat") },
      { field: meetingTime, name: t("startDate") },
      { field: meetingTimeTo, name: t("endDate") },
    ];

    // Add conditional mandatory fields based on meetingMethod
    if (["8", "9"].includes(meetingMethod)) {
      mandatoryFields.push(
        { field: meetingPlace, name: t("tempatMesyuarat") },
        { field: meetingAddress, name: t("alamatTempatMesyuarat") },
        { field: state, name: t("state") },
        { field: district, name: t("district") },
        { field: postcode, name: t("poskod") }
      );
    }

    // Add conditional mandatory fields based on meetingMethod
    if (["9", "10"].includes(meetingMethod)) {
      mandatoryFields.push({
        field: platformType,
        name: t("platformType"),
      });
    }

    // Check for empty mandatory fields
    const emptyFields = mandatoryFields.filter((field) => !field.field);

    if (emptyFields.length > 0) {
      alert(
        t("mandatoryFieldsAlert") +
          " : \n" +
          emptyFields.map((field) => field.name).join(", ")
      );
      return;
    }

    const payload = {
      societyId,
      meetingTitle: "",
      meetingDescription: "",
      meetingDate: meetingDate,
      meetingTimeFrom: `${meetingTime}:00`,
      meetingTimeTo: `${meetingTimeTo}:00`,
      meetingLocation: meetingPlace,
      committees: memberAttendances,
      openingRemarks: openingRemarks,
      mattersDiscussed: mattersDiscussed,
      othersDiscussionRemarks: otherMatters,
      closingRemarks: closing,
      preparedBy: providedBy,
      approvedBy: confirmBy,
    };

    downloadMeetingMinute(payload);
  };

  const onSubmit: SubmitHandler<FieldValues> = async (data) => {
    const isSecretaryFormValid = await trigger();

    if (!isSecretaryFormValid) return;

    setIsDialogOpen(true);
  };

  const handleLocationSelected = ({
    fullAddress,
    stateId,
    districtId,
    city,
    postcode,
    geometry,
  }: LocationSelectedParams) => {
    setValue("city", city);
    setValue("postcode", postcode);
    setValue("meetingAddress", fullAddress);
    setValue("state", stateId.toString());
    setValue("district", districtId.toString());
    setValue("GISInformation", `${geometry[0]}, ${geometry[1]}`);
  };

  const handleFormSubmit = () => {
    const meetingFormValues = getValues();
    const secretaryFormValues = getSecretaryFormValues();

    const sanitizedData = {
      ...meetingFormValues,
      meetingTime: `${meetingFormValues.meetingTime}:00`,
      meetingTimeTo: `${meetingFormValues.meetingTimeTo}:00`,
      memberAttendances: meetingFormValues.memberAttendances.map(
        (member: IMember) => {
          const { tempId, ...rest } = member;
          return rest;
        }
      ),
    };

    const payload = filterEmptyValuesOnObject(sanitizedData);

    if (isEditable && secretaryFormValues?.meetingId) {
      updateMeeting({ ...payload, id: secretaryFormValues.meetingId });
      return;
    }

    createMeeting(payload);
  };

  useEffect(() => {
    if (meetingData) {
      setMeetingDetail(meetingData);
    } else {
      resetFormExceptMeetingDate();
    }
  }, [meetingData]);

  useEffect(() => {
    if (meetingData && societyData) {
      getDocument([
        {
          field: "societyId",
          operator: "eq",
          value: societyData.id,
        },
        {
          field: "meetingId",
          operator: "eq",
          value: meetingData.id,
        },
        {
          field: "type",
          operator: "eq",
          value: DocumentUploadType.MEETING,
        },
      ]);
    }
  }, [meetingData, societyData]);

  useEffect(() => {
    if (meetingTime && meetingTimeTo) {
      const start = new Date(`1970-01-01T${meetingTime}:00`);
      const end = new Date(`1970-01-01T${meetingTimeTo}:00`);

      if (end >= start) {
        const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
        setDuration(`${diffMinutes} ${t("minute")}`);
      } else {
        setDuration(t("validation.invalidTimeDuration"));
      }
    } else {
      setDuration("");
    }
  }, [meetingTime, meetingTimeTo]);

  return (
    <FormProvider {...methods}>
      <form noValidate onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            backgroundColor: "white",
            padding: "18px 16px",
            borderRadius: "14px",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight={500}
              marginBottom="20px"
            >
              {isMyLanguage
                ? "Mesyuarat Pelantikan Setiausaha Baru"
                : "New Secretary Appointment Meeting"}
            </Typography>

            <FormFieldRow
              label={<Label text={t("meetingDate")} required />}
              value={
                <DatePickerController
                  name="meetingDate"
                  control={control}
                  onChange={(date) =>
                    setSecretaryFormValue("meetingDate", date)
                  }
                  disabled={disabledState}
                  rules={{
                    required: requiredState ? t("fieldRequired") : false,
                  }}
                  required={requiredState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("meetingType")} required />}
              value={
                <SelectFieldController
                  name="meetingType"
                  control={control}
                  options={meetingTypeOptions}
                  onChange={(event) => {
                    const meetingType = event.target.value;

                    setSecretaryFormValue("meetingType", meetingType);
                  }}
                  placeholder={t("selectPlaceholder")}
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("kaedahMesyuarat")} required />}
              value={
                <SelectFieldController
                  name="meetingMethod"
                  control={control}
                  options={meetingMethodOptions}
                  placeholder={t("selectPlaceholder")}
                  disabled={disabledState}
                />
              }
            />

            {["9", "10"].includes(meetingMethod) && ( // hybrid or tailan meeting method only
              <FormFieldRow
                label={<Label text={t("jenisPlatform")} required />}
                value={
                  <SelectFieldController
                    name="platformType"
                    control={control}
                    options={meetingPlatformOptions}
                    placeholder={t("selectPlaceholder")}
                    disabled={disabledState}
                  />
                }
              />
            )}

            <FormFieldRow
              label={<Label text={t("tujuanMesyuarat")} required />}
              value={
                <TextFieldController
                  name="meetingPurpose"
                  control={control}
                  disabled={disabledState}
                  rules={{
                    required: requiredState ? t("fieldRequired") : false,
                  }}
                  required={requiredState}
                />
              }
            />

            <FormFieldRow
              align="flex-start"
              label={<Label text={t("time")} required />}
              value={
                <Box display="flex" gap={2}>
                  <TimePickerController
                    name="meetingTime"
                    control={control}
                    sx={{
                      width: "140px",
                    }}
                    disabled={disabledState}
                  />

                  <TimePickerController
                    name="meetingTimeTo"
                    control={control}
                    sx={{
                      width: "140px",
                    }}
                    disabled={disabledState}
                  />

                  <Typography
                    fontSize="12px"
                    lineHeight="37px"
                    fontWeight="400 !important"
                  >
                    {duration && duration}
                  </Typography>
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("meetingPlace")} required />}
              value={
                <TextFieldController
                  name="meetingPlace"
                  control={control}
                  disabled={disabledState}
                  rules={{
                    required: t("fieldRequired"),
                  }}
                  required
                />
              }
            />

            <FormFieldRow
              align="flex-start"
              label={<Label text={t("locationMap")} required />}
              value={
                <AWSLocationSearchMap
                  zoom={20}
                  onLocationSelected={handleLocationSelected}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("sebabTukarSetiausaha")} required />}
              value={
                <TextFieldController
                  name="reasonOfChange"
                  control={secretaryFormControl}
                  disabled={disabledState}
                  rules={{
                    required: t("fieldRequired"),
                  }}
                  required
                />
              }
            />
          </Box>

          {["8", "10"].includes(meetingMethod) && ( // bersemuka and hibrid meeting method only
            <Box
              sx={{
                borderRadius: "10px",
                padding: "41px 25px 25px",
                border: "0.5px solid #DADADA",
                marginBottom: "15px",
              }}
            >
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
                marginBottom="20px"
              >
                {t("alamatTempatMesyuarat")}
              </Typography>

              <FormFieldRow
                label={<Label text={t("meetingPlaceAddress")} required />}
                value={
                  <TextFieldController
                    name="meetingAddress"
                    control={control}
                    disabled={disabledState}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("state")} required />}
                value={
                  <SelectFieldController
                    name="state"
                    control={control}
                    options={cityOptions}
                    placeholder={t("selectPlaceholder")}
                    onChange={() => setValue("district", "")}
                    disabled={disabledState}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("district")} required />}
                value={
                  <SelectFieldController
                    name="district"
                    control={control}
                    options={districtOptions}
                    placeholder={t("selectPlaceholder")}
                    disabled={!watch("state") || disabledState}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("city")} />}
                value={
                  <TextFieldController
                    name="city"
                    control={control}
                    disabled={disabledState}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("postcode")} required />}
                value={
                  <TextFieldController
                    name="postcode"
                    inputProps={{ maxLength: 5 }}
                    maxRows={5}
                    rules={{
                      required: t("fieldRequired"),
                      validate: (value: string) => {
                        if (!/^\d{5}$/.test(value)) {
                          return t("residencePostcode");
                        }
                        return true;
                      },
                    }}
                    control={control}
                    disabled={disabledState}
                  />
                }
              />
            </Box>
          )}

          {/* <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "15px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight={500}
              marginBottom="20px"
            >
              {t("maklumatMesyuarat")}
            </Typography>

            <FormFieldRow
              label={<Label text={t("fillMeetingInformation")} />}
              value={
                <SelectFieldController
                  name="meetingContent"
                  control={control}
                  options={meetingContentOptions}
                  placeholder={t("selectPlaceholder")}
                  disabled={disabledState}
                />
              }
            />
          </Box> */}

          {/* {meetingContent === "ya" && (
            <MemberAttendances
              disabledState={disabledState}
              isEditable={isEditable}
              isViewOnly={isViewOnly}
              meetingDetail={!!meetingData}
            />
          )} */}

          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "15px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight={500}
              marginBottom="20px"
            >
              Minit Mesyuarat
            </Typography>

            <FormFieldRow
              label={<Label text={t("minitMesyuarat")} required />}
              value={
                <>
                  <Controller
                    name="meetingMinute"
                    control={control}
                    rules={{
                      required: requiredState ? t("fieldRequired") : false,
                    }}
                    render={({ field, fieldState: { error } }) => (
                      <>
                        <Box
                          sx={{
                            border: error
                              ? "1px solid red"
                              : "1px solid #DADADA",
                            borderRadius: "8px",
                            p: 3,
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            gap: 1,
                            backgroundColor: "#FFFFFF",
                            cursor:
                              !meetingData || isEditable ? "pointer" : "normal",
                            "&:hover": {
                              backgroundColor: "#F9FAFB",
                            },
                          }}
                          onClick={(e) => {
                            e.stopPropagation();

                            if (
                              inputFileRef.current &&
                              (!meetingData || isEditable)
                            )
                              inputFileRef.current.click();
                          }}
                        >
                          <Box
                            sx={{
                              width: "100%",
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            {selectedFile || watch("meetingMinute") ? (
                              <Box
                                sx={{
                                  width: "100%",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "space-between",
                                }}
                              >
                                <Box>
                                  <Typography
                                    sx={{
                                      color: "#222222",
                                      fontSize: "14px",
                                      textAlign: "center",
                                    }}
                                  >
                                    {selectedFile?.name ||
                                      watch("meetingMinute")}
                                  </Typography>
                                </Box>

                                <IconButton
                                  onClick={() =>
                                    window.open(filePreview, "_blank")
                                  }
                                  sx={{
                                    padding: 0,
                                  }}
                                >
                                  <EyeIcon color="#666666" />
                                </IconButton>
                              </Box>
                            ) : (
                              <>
                                <UploadIcon />
                                <Typography
                                  sx={{
                                    color: "var(--primary-color)",
                                    fontSize: "14px",
                                    fontWeight: 500,
                                    textAlign: "center",
                                  }}
                                >
                                  Muat naik
                                </Typography>
                                <Typography
                                  sx={{
                                    color: "#667085",
                                    fontSize: "12px",
                                    textAlign: "center",
                                    fontWeight: "400 !important",
                                  }}
                                >
                                  PDF (max 25 MB)
                                </Typography>
                              </>
                            )}
                          </Box>
                          <input
                            ref={inputFileRef}
                            type="file"
                            hidden
                            onChange={(e) => {
                              const file = e.target?.files?.[0];

                              if (file && file.size > DOCUMENT_MAX_FILE_SIZE)
                                return alert(t("validation.documentSize"));

                              field.onChange(file?.name);
                              handleFileChange(file);
                            }}
                            accept=".pdf"
                          />
                        </Box>
                        {error && (
                          <span style={{ color: "red", fontSize: "12px" }}>
                            {error.message}
                          </span>
                        )}
                      </>
                    )}
                  />

                  <Typography
                    sx={{
                      mt: 1,
                      color: "#147C7C",
                      textDecoration: "underline",
                      cursor: "pointer",
                    }}
                    onClick={() => downloadPDFTemplate()}
                  >
                    {t("downloadTemplate")}
                  </Typography>
                </>
              }
            />

            {meetingContent === "ya" && (
              <>
                <FormFieldRow
                  align="flex-start"
                  label={<Label text={t("ucapanAluanPengerusi")} />}
                  value={
                    <TextFieldController
                      name="openingRemarks"
                      control={control}
                      disabled={disabledState}
                      multiline
                      sx={{
                        "& .MuiInputBase-input": {
                          fontSize: "14px",
                          height: "90px !important",
                        },
                      }}
                    />
                  }
                />

                <FormFieldRow
                  align="flex-start"
                  label={<Label text={t("perkaraPerkara")} />}
                  value={
                    <TextFieldController
                      name="mattersDiscussed"
                      control={control}
                      disabled={disabledState}
                      multiline
                      sx={{
                        "& .MuiInputBase-input": {
                          fontSize: "14px",
                          height: "90px !important",
                        },
                      }}
                    />
                  }
                />

                <FormFieldRow
                  align="flex-start"
                  label={<Label text={t("penutup")} />}
                  value={
                    <TextFieldController
                      name="closing"
                      control={control}
                      disabled={disabledState}
                      multiline
                      sx={{
                        "& .MuiInputBase-input": {
                          fontSize: "14px",
                          height: "90px !important",
                        },
                      }}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("disediakanOleh")} />}
                  value={
                    <TextFieldController
                      name="providedBy"
                      control={control}
                      disabled={disabledState}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("disahkanOleh")} />}
                  value={
                    <TextFieldController
                      name="confirmBy"
                      control={control}
                      disabled={disabledState}
                    />
                  }
                />
              </>
            )}
          </Box>

          {/* <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "15px",
            }}
          >
            <FormFieldRow
              label={<Label text={t("name")} required />}
              value={
                <TextFieldController
                  name="oldSecretaryName"
                  control={secretaryFormControl}
                  disabled={disabledState}
                  rules={{
                    required: t("fieldRequired"),
                  }}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("noKadPengenalan")} required />}
              value={
                <TextFieldController
                  name="oldSecretaryIdentificationNumber"
                  control={secretaryFormControl}
                  disabled={disabledState}
                  rules={{
                    required: t("fieldRequired"),
                  }}
                  required
                />
              }
            />
          </Box> */}

          {!disabledState && (
            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                marginTop: 1,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={2} pt={2}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "flex-start",
                    }}
                  >
                    <Checkbox
                      sx={{ p: 0, mr: 2 }}
                      onChange={(e) =>
                        setIsConfirmOldSecretary(e.target.checked)
                      }
                      checkedIcon={
                        <Box
                          sx={{
                            width: "12px",
                            height: "12px",
                            border: "0.5px solid var(--primary-color)",
                            background: "var(--primary-color)",
                          }}
                        />
                      }
                    />
                    <Typography className="label">
                      {t("confirmOldSecretary")}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Box>
                </Grid>
              </Box>
            </Box>
          )}
        </Box>

        {/* {!isFeedback && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
              gap: 1,
              mt: 3,
              mb: 3,
            }}
          >
            <NavButton onClick={handleBack} disabled={isSecretaryReformSuccess}>
              {"Previous"}
            </NavButton>

            <PageButton
              onClick={handleBack}
              disabled={isSecretaryReformSuccess}
            >
              1
            </PageButton>
            <PageButton active>2</PageButton>
          </Box>
        )} */}

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            mt: 3,
            gap: 1,
            marginRight: 4,
          }}
        >
          {meetingContent === "ya" && (
            <>
              <ButtonOutline
                sx={{
                  minWidth: "120px",
                  textTransform: "none",
                  fontWeight: "bold",
                  borderColor: "#DADADA",
                }}
                onClick={handleJanaMinit}
              >
                {t("janaMinit")}
              </ButtonOutline>
            </>
          )}
          <Box
            sx={{
              width: "fit-content",
              marginTop: "24px",
              marginLeft: "auto",
              display: "flex",
              gap: "11px",
            }}
          >
            <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>
            <ButtonPrimary type="submit" disabled={!isConfirmOldSecretary}>
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </Box>
      </form>

      <DialogConfirmation
        open={isDialogOpen}
        isSuccess={isSecretaryReformSuccess && !isUploadingFile}
        isMutating={
          isCreateMeeting ||
          isUpdateMeeting ||
          isCreatingSecretary ||
          isUpdatingSecretary ||
          isUploadingFile ||
          isDeletingDocument
        }
        onClose={() => setIsDialogOpen(false)}
        onAction={handleFormSubmit}
      />
    </FormProvider>
  );
};

export default MeetingForm;
