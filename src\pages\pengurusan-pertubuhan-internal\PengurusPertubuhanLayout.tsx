import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import useQuery from "../../helpers/hooks/useQuery";
import { NavigateBefore } from "@mui/icons-material";
import { Grid, Box, Button, Typography, IconButton } from "@mui/material";
import Sidebar from "../../components/layout/Sidebar";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { getLocalStorage, setLocalStorage } from "../../helpers/utils";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import { getNavItem, MENU_INTERNAL, NavItem } from "@/helpers/menuConfig";
import AuthHelper from "@/helpers/authHelper";

type LayoutProps = {
  children?: React.ReactNode;
};

export const ********************************: React.FC<LayoutProps> = ({
  children,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  function goTo(path: string) {
    navigate(path);
  }

  const { refetch: fetchCategoryList } = useQuery({
    url: "society/admin/category/list",
    autoFetch: false,
    onSuccess: (data) => {
      const categoryList = data?.data?.data;

      if (categoryList?.length > 0) {
        setLocalStorage("category_list", categoryList);
      } else {
        setLocalStorage("category_list", null);
      }
    },
  });

  // menu permission
  const menus = getNavItem(MENU_INTERNAL(), "pertubuhan");
  const [allowedMenuItems, setAllowedMenuItems] = useState<NavItem[]>(menus);

  useEffect(() => {
    const finalMenuItems = allowedMenuItems.filter((item) => {
      if (item.permissions.length == 0) {
        // If the permissions (length = 0), it means no permission is required.
        return true;
      } else {
        return AuthHelper.hasAuthority(item.permissions);
      }
    });

    if (JSON.stringify(finalMenuItems) !== JSON.stringify(allowedMenuItems)) {
      setAllowedMenuItems(finalMenuItems);
    }
  }, [allowedMenuItems]);
  // menu permission

  useEffect(() => {
    const category_list = getLocalStorage("category_list", null);

    if (!category_list) fetchCategoryList();
  }, []);

  const hasPertubuhanPermission = AuthHelper.hasPageAccess(
    PermissionNames.PERTUBUHAN.label,
    pageAccessEnum.Read
  );

  useEffect(() => {
    if (!hasPertubuhanPermission) {
      navigate("/internal-user");
    }
  }, [hasPertubuhanPermission, navigate]);

  const path = location.pathname;
  const segments = path.split("/").filter(Boolean);

  const isBeforeLast =
    segments.includes("keputusan-pertubuhan") &&
    segments.indexOf("keputusan-pertubuhan") === segments.length - 2;

  return (
    <Box
      sx={{
        "& .MuiTypography-root": {
          fontFamily: "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
          fontWeight: "700",
        },
        transition: "max-height 0.3s ease",
      }}
    >
      <Box
        onClick={() =>
          isBeforeLast ? navigate("/internal-user") : navigate(-1)
        }
        sx={{
          display: "flex",
          alignItems: "center",
          mb: 3,
          gap: 2,
          cursor: "pointer",
        }}
      >
        <IconButton size="small" sx={{ color: "#666666", p: 0 }}>
          <NavigateBefore />
        </IconButton>

        <Typography
          sx={{ color: "#666666", fontSize: 18, fontWeight: "400 !important" }}
        >
          {location.pathname.includes("cawangan")
            ? t("cawangan")
            : location.pathname.includes("rayuan")
            ? t("rayuan")
            : location.pathname.includes("kuiri")
            ? t("kuiri")
            : t("pertubuhan")}
        </Typography>
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={2}>
          <Sidebar
            sx={{
              // minWidth: "190px",
              padding: "37px 26px",
              height: "fit-content",
              display: "flex",
              flexDirection: "column",
              gap: "10px",
              textAlign: "center",
            }}
          >
            {allowedMenuItems.map((data: any, index: number) => {
              const { label, path, children } = data;

              const isActive =
                location.pathname.startsWith(path) ||
                (children &&
                  children.some((child: any) =>
                    location.pathname.startsWith(child.path)
                  ));

              return (
                <Button
                  key={label + index}
                  sx={{
                    color: isActive ? "#fff" : "#666666",
                    background: isActive
                      ? "var(--primary-color)"
                      : "transparent",
                    "&:hover": {
                      background: "var(--primary-color)",
                    },
                    borderRadius: "20px",
                    textTransform: "none",
                    lineHeight: "20px",
                    py: 1.5,
                  }}
                  onClick={() => {
                    if (path == "/pengurus-pertubuhan/keputusan-pertubuhan") {
                      goTo(
                        "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk"
                      );
                    } else {
                      goTo(path);
                    }
                  }}
                >
                  {label}
                </Button>
              );
            })}
          </Sidebar>
        </Grid>
        <Grid item xs={10}>
          <Box sx={{ flexGrow: 1 }}>
            <Outlet />
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ********************************;
