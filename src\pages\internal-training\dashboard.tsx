import React, {useState} from "react";
import {Box, IconButton, Typography} from "@mui/material";
import TrainingModule from "@/pages/training/trainingModule";
import CertificateModule from "@/pages/training/certificateModule";
import {useTranslation} from "react-i18next";
import CustomDataGrid from "@/components/datagrid";
import {GridColDef} from "@mui/x-data-grid";
import {Switch} from "@/components/switch";
import InternalTrainingHeader from "@/pages/internal-training/trainingHeader";
import InternalTrainingDashboardSidebar from "@/pages/internal-training/dashboardSidebar";
import {EditIcon, EyeIcon, TrashIcon} from "@/components/icons";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import dayjs from "dayjs";
import {useNavigate} from "react-router-dom";
import ConfirmationDialog from "@/pages/internal-training/confirmDialog";
import {labelStyle} from "@/pages/internal-training/trainingConstant";
import DataTable from "../../components/datatable";
import {FieldValues, useForm} from "react-hook-form";
import useQuery from "../../helpers/hooks/useQuery";
import {formatDate} from "@/helpers";


const InternalTrainingDashboard: React.FC = () => {
  const {t, i18n} = useTranslation();
  const [page, setPage] = useState("training");
  const [filter, setFilter] = useState("all");
  const navigate = useNavigate();

  const [openModal, setOpenModal] = useState(false);
  const [deleteId, setDeleteId] = useState(0);

  const [refetchData, setRefetchData] = useState(false);

  const tabs = [
    {label: "Semua", filter: "all"},
    {label: "Aktif", filter: "active"},
    {label: "Draf", filter: "draft"},
    {label: "Asas", filter: "basic"},
    {label: "Sederhana", filter: "medium"},
    {label: "Mahir", filter: "hard"},
  ];

  const handleFilter = (val: any) => {
    //setPage(path);
    setFilter(val)
  }

  const handleSwitchOnChange = (id: number, val: boolean) => {
    Change(id, val ? 1 : 2);
  }

  const handleDelete = () => {
    if(deleteId != 0){
      Delete(deleteId);
    }
    setDeleteId(0)
  }


  const columns: GridColDef[] = [
    {
      field: "title",
      headerName: t("titleFeedback"),
      flex: 1,
      renderCell: ({row}) => {
        return  <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor:"pointer"
          }}
          onClick={(e) => navigate(`update/${row.id}`)}
        >
          {row?.title}
        </Typography>;
      },
    },
    {
      field: "dateCreated",
      headerName: "Tarikh Penerbitan",
      flex: 1,
      renderCell: ({row}) => {
        const formattedDate = formatDate(row?.createdDate)
        return formattedDate;
      },
    },
    {
      field: "author",
      headerName: "Pengarang",
      flex: 1,
      renderCell: ({row}) => {
        return row?.user?.name ?? "";
      },
    },
    {
      field: "type",
      headerName: "Jenis Aktiviti",
      flex: 1,
      renderCell: ({row}) => {
        return "";
      },
    },
    {
      field: "active",
      headerName: "Aktivasi",
      flex: 1,
      renderCell: ({row}) => {
        return (<Switch
          checked={row.status==1}
          onChange={(e) => handleSwitchOnChange(row.id, e.target.checked)}
        />)
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({row}) => {
        return row?.status==1 ? "PUBLISHED" : "HIDDEN";
      },
    },
    {
      field: "action",
      headerName: t("action"),
      flex: 1,
      renderCell: ({row}) => {
        return  (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              columnGap: "0.5rem",
            }}
          >
            <IconButton
              style={{
                minWidth: "2rem",
                minHeight: "2rem",
              }}
              color="primary"
              onClick={() => {
                navigate(`update/${row.id}`)
              }}
            >
              <EditIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>

            <IconButton
              style={{
                minHeight: "2rem",
                minWidth: "2rem",
              }}
              color="error"
              onClick={() => confirmDeleteBranch(row.id)}
            >
              <TrashIcon />
            </IconButton>
            <IconButton
              style={{
                minHeight: "2rem",
                minWidth: "2rem",
              }}
              //color="error"
              //onClick={() => confirmDeleteBranch(row)}
            >
              <EyeIcon />
            </IconButton>
          </Box>
        );
      },
    },
  ]

  const confirmDeleteBranch = (id: number) => {
    setDeleteId(id);
    setOpenModal(true);
  }

  const {mutate: deleteTraining, isLoading: isLoadingDelete} = useCustomMutation();
  const Delete = (id: number): void => {
    deleteTraining(
      {
        url: `${API_URL}/society/admin/training/courses/${id}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            setRefetchData(true);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {mutate: changeTraining, isLoading: isLoadingChange} = useCustomMutation();
  const Change = (id: number, status: number): void => {
    changeTraining(
      {
        url: `${API_URL}/society/admin/training/courses/${id}/status`,
        method: "put",
        values: {
          status: status
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            setRefetchData(true);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <Box sx={{
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-evenly",
        gap:1
      }}>
      <Box sx={{width: "85%"}}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          backgroundColor: "white",
          paddingBottom: "8px",
          justifyContent: "space-evenly",
          borderRadius: "10px",
          px: 2,
          py: 1,
          mb: 1,
        }}
      >
        {tabs.map((tab, index) => {
          // Tentukan apakah tab saat ini aktif berdasarkan URL
          const isActive = filter === tab.filter;
          return (
            <React.Fragment key={index}>
              <Box
                sx={{
                  flex: 1,
                  backgroundColor: isActive ? "#0CA6A6" : "#FFFFFF",
                  p: 1,
                  //mx:1,
                  borderRadius: "5px",
                }}>
                <Box
                  key={index}
                  onClick={() => {
                    //handleNavigation(tab.path)
                    handleFilter(tab.filter)
                  }}
                  sx={{
                    cursor: "pointer",
                    color: isActive ? "#FFFFFF" : "#666666",
                    transition: "color 0.3s, border-bottom 0.3s",
                  }}
                >
                  <Typography sx={{fontWeight: "400 !important", textAlign: "center", fontSize: "14px"}}>
                    {tab.label}
                  </Typography>
                </Box>
              </Box>
            </React.Fragment>
          );
        })}
      </Box>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography
            sx={{
              color: "#1DC1C1",
              pt: 3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            Carian Latihan
          </Typography>
          <Box>
            <CustomDataGrid
              url={`${API_URL}/society/admin/training/courses`}
              columns={columns}
              noResultMessage={t("noData")}
              isFiltered
              type={1}
              setRefetchData={setRefetchData}
              refetchData={refetchData}
              //onDataReceived={(data) => setTotal(data?.data?.total || 0)}
              //filters={[{ field: "filter", operator: "eq", value: filter }]}
            />
          </Box>
        </Box>
      </Box>
      </Box>
        <InternalTrainingDashboardSidebar />
      </Box>
      <ConfirmationDialog text={"Adakah anda pasti untuk memadam latihan ini"} handleSave={handleDelete}
                          labelStyle={labelStyle} openModal={openModal} setOpenModal={setOpenModal}/>
    </>
  );
}

export default InternalTrainingDashboard;
