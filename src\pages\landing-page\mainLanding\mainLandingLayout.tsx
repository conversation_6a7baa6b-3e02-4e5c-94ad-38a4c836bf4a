import { FC, ReactNode, useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { Box } from "@mui/material";
import Navbar from "../../../components/navbar";
import Footer from "./footer";
import CarianPertubuhanLanding from "./carian-pertubuhan-landing";
import ScrollRestoration from "@/services/ScrollRestoration";

interface LayoutProps {
  children: ReactNode;
}

const MainLandingLayout: FC<LayoutProps> = ({ children }) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const location = useLocation();

  const [searchTerm, setSearchTerm] = useState<string>("");
  const [visible, setVisible] = useState<boolean>(false);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setVisible(true);
  };

  const handleCloseSearch = () => {
    setVisible(false);
    setTimeout(() => setSearchTerm(""), 300);
  };

  useEffect(() => {
    if (visible) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [visible]);

  return (
    <>
      <ScrollRestoration />
      <Box
        className="main-layout-content"
        ref={scrollRef}
        sx={{
          display: "grid",
          flexDirection: "column",
          height: "100vh",
          overflowY: "auto",
        }}
      >
        <Box sx={{ display: "grid", flexDirection: "column" }}>
          <Navbar onSearch={handleSearch} />

          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              zIndex: 11,
              transform: visible ? "translateY(0%)" : "translateY(-100%)",
              transition: "transform 300ms ease-in-out",
              pointerEvents: visible ? "auto" : "none",
              overflowY: "auto",
            }}
          >
            {searchTerm && (
              <CarianPertubuhanLanding
                searchTerm={searchTerm}
                onClose={handleCloseSearch}
              />
            )}
          </Box>
          {!visible ? (
            <>
              {children}{" "}
              <Box
                sx={{
                  backgroundColor: "var(--primary-color)",
                  padding: "48px 60px 48px 60px",
                  "@media (max-width: 800px)": {
                    padding: "60px 30px 60px 30px",
                  },
                  "@media (max-width: 450px)": {
                    padding: "60px 15px 60px 15px",
                  },
                }}
              >
                <Footer />
              </Box>
            </>
          ) : null}
        </Box>
      </Box>
    </>
  );
};

export default MainLandingLayout;
