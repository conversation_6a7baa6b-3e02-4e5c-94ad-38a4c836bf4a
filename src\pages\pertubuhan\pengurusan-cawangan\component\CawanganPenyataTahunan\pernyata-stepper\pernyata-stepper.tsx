import React from "react";
import {
  Stepper,
  Step,
  StepLabel,
  useMediaQuery,
  Theme,
  Box,
  StepConnector,
  Typography,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CheckIcon from "@mui/icons-material/Check";

const steps = [
  "generalInformation",
  "annualMeetingInformation",
  "committeeAndAuditorInformation",
  "incomeStatement",
  "assetsAndLiabilities",
  "contributionsFromAbroad",
];

interface PernyataStepperProps {
  activeStep: number;
}

const DashedStepConnector = () => (
  <StepConnector
    sx={{
      "&.Mui-active": {
        "& .MuiStepConnector-line": {
          borderColor: "#CDE4E4",
        },
      },
      "&.Mui-completed": {
        "& .MuiStepConnector-line": {
          borderColor: "#CDE4E4",
        },
      },
      "& .MuiStepConnector-line": {
        borderColor: "primary.secondary",
        borderStyle: "dashed", // Changed to dashed
        borderWidth: "1px",
      },
    }}
  />
);

export const PernyataStepper: React.FC<PernyataStepperProps> = ({
  activeStep,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const handleStepClick = (step: number) => {
    switch (step) {
      case 0:
        navigate("../create");
        break;
      case 1:
        navigate("../agung");
        break;
      case 2:
        navigate("../ajk");
        break;
      case 3:
        navigate("../pendapatan");
        break;
      case 4:
        navigate("../aset");
        break;
      case 5:
        navigate("../sumbangan");
        break;
      default:
        navigate("../create");
    }
  };

  return (
    <Stepper
      sx={{ mb: 3, mt: 7 }}
      activeStep={activeStep}
      alternativeLabel={!isMobile}
      orientation={isMobile ? "vertical" : "horizontal"}
      {...(!isMobile ? { connector: <DashedStepConnector /> } : {})}
    >
      {steps.map((stepKey, index) => (
        <Step key={stepKey} onClick={() => handleStepClick(index)}>
          <StepLabel
            icon={
              <Box
                sx={{
                  width: "30px",
                  height: "30px",
                  border:
                    index !== activeStep
                      ? "3px solid #6CA7CA"
                      : "3px solid #31A127",
                  borderRadius: "50%",
                  textAlign: "center",
                  position: "relative",
                  backgroundColor:
                    index < activeStep
                      ? "#D3E3FD"
                      : index === activeStep
                      ? "#D3E3FD"
                      : "",
                  color: "#402DFF",
                  fontFamily: "'Poppins', sans-serif",
                }}
              >
                <>
                  <Box
                    sx={{
                      width: "40px",
                      height: "40px",
                      border:
                        index !== activeStep
                          ? "3px solid #6CA7CA"
                          : "3px solid #74CA6C", // Adjust border thickness and color
                      borderRadius: "50%",
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                      backgroundColor: "transparent", // Ensure background is transparent
                    }}
                  ></Box>
                  <HalfCircleBorderBox />
                </>
                {index < activeStep ? <CheckIcon /> : index + 1}
              </Box>
            }
            sx={{
              cursor: "pointer",
              position: "relative",
            }}
          >
            <Typography
              sx={{
                marginTop: !isMobile ? "60px" : "",
                fontFamily: "'Poppins', sans-serif",
              }}
            >
              {t(stepKey)}
            </Typography>
          </StepLabel>
        </Step>
      ))}
    </Stepper>
  );
};

const HalfCircleBorderBox = () => {
  return (
    <Box sx={{ position: "relative", top: "60%" }}>
      <Box
        sx={{
          width: "50px",
          height: "50px",
          border: "2px solid #CDE4E4",
          borderRadius: "50%",
          borderBottomColor: "transparent",
          borderLeftColor: "transparent",
          position: "absolute",
          top: "0%",
          left: "50%",
          transform: "translate(-50%, -50%) rotate(134deg)", // Gabungkan translate dan rotate
          backgroundColor: "transparent",
        }}
      />
      <Box
        sx={{
          width: "1px",
          height: "15px",
          backgroundColor: "#6CA7CA",
          position: "absolute",
          top: "24px",
          left: "50%",
        }}
      />
      <Box
        sx={{
          width: "15px",
          height: "15px",
          border: "1px solid #6CA7CA", // Adjust border thickness and color
          borderRadius: "50%",
          position: "absolute",
          top: "45px",
          left: "50%",
          transform: "translate(-50%, -50%)",
          backgroundColor: "transparent",
        }}
      />
    </Box>
  );
};
