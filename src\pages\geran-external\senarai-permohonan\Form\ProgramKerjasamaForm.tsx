import React from "react";
import { useTranslation } from "react-i18next";
import { useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, Typography } from "@mui/material";
import { ToggleButtonController, TextFieldController } from "@/components";

const perogramOptions = [
  {
    value: "1",
    label: "Kementerian Kesihatan Malaysia (KKM)",
  },
  {
    value: "2",
    label: "Polis Diraja Malaysia (PDRM)",
  },
  {
    value: "3",
    label: "Jabatan Bomba dan Penyelamat Malaysia (JBPM)",
  },
  {
    value: "4",
    label: "Agensi antidadah Kebangsaan (AADK)",
  },
  {
    value: "5",
    label: "Jabatan Sukarelawan Malaysia (RELA)",
  },
  {
    value: "6",
    label: "Angkatan Pertahanan Awam Malaysia (APM)",
  },
  {
    value: "other",
    label: "Lain-Lain Jabatan/Agensi",
  },
];

const Form = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const { control, watch } = useFormContext();

  return (
    <Box className={classes.sectionBox} mb={2}>
      <Typography className="title" mb={2}>
        Program dengan kerjasama
      </Typography>

      <ToggleButtonController
        name="program"
        control={control}
        options={perogramOptions}
        sx={{
          gap: 4,
        }}
      />

      {watch("program") === "other" && (
        <TextFieldController
          name="otherReason"
          control={control}
          multiline
          rows={1}
        />
      )}
    </Box>
  );
};

const ProgramKerjasamaForm = React.memo(Form);
export default ProgramKerjasamaForm;
