import { API_URL } from "@/api";
import { NotificationColors, useQuery } from "@/helpers";
import { ChevronLeftRounded, MoreHoriz } from "@mui/icons-material";
import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  Grid,
  IconButton,
  Popover,
  Typography,
} from "@mui/material";
import { useCustomMutation } from "@refinedev/core";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useEffect, useRef, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";

export type Notification = {
  content: string;
  createdDate: string;
  notificationTrackerId: number;
  notificationType: number;
  recipientIdentificationNo: string;
  recipientUsername: string;
  seen: boolean;
  seenAt: string | null;
  subject: string;
  tags: string;
};

export const MainNotificationPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<Map<number, Notification>>(
    new Map()
  );
  const [hasMore, setHasMore] = useState(true);
  const [groupedNotifications, setGroupedNotifications] = useState<
    Record<string, Notification[]>
  >({});
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);

  dayjs.extend(relativeTime);

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data: notificationData,
    isLoading: isLoadingNotificationData,
    refetch,
  } = useQuery({
    url: "notification/getAll/me",
    filters: [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: page, operator: "eq" },
    ],
  });

  useEffect(() => {
    if (localStorage.getItem("portal")) {
      refetch();
    }
  }, [localStorage.getItem("portal")]);

  const groupNotifications = (
    notifications: Notification[]
  ): Record<string, Notification[]> => {
    return notifications.reduce<Record<string, Notification[]>>((acc, noti) => {
      const createdDate = dayjs(noti?.createdDate, "DD-MM-YYYY HH:mm:ss");
      const today = dayjs().startOf("day");
      const yesterday = dayjs().subtract(1, "day").startOf("day");
      const sevenDaysAgo = dayjs().subtract(7, "day").startOf("day");
      const oneMonthAgo = dayjs().subtract(1, "month").startOf("day");

      if (createdDate.isSame(today, "day")) {
        acc["hariIni"] = [...(acc["hariIni"] || []), noti];
      } else if (createdDate.isSame(yesterday, "day")) {
        acc["semalam"] = [...(acc["semalam"] || []), noti];
      } else if (createdDate.isAfter(sevenDaysAgo)) {
        acc["7HariTerakhir"] = [...(acc["7HariTerakhir"] || []), noti];
      } else if (createdDate.isAfter(oneMonthAgo)) {
        acc["sebulanTerakhir"] = [...(acc["sebulanTerakhir"] || []), noti];
      } else {
        acc["sebelumnya"] = [...(acc["sebelumnya"] || []), noti];
      }

      return acc;
    }, {});
  };

  useEffect(() => {
    if (notificationData?.data?.data?.data) {
      const newNotifications: Notification[] = notificationData.data.data.data;
      const total: number = notificationData.data.data.total;

      setNotifications((prev) => {
        const updated = new Map(prev);
        newNotifications.forEach((noti) =>
          updated.set(noti?.notificationTrackerId, noti)
        );
        return new Map(updated);
      });

      setHasMore(newNotifications.length + notifications.size < total);
    }
  }, [notificationData]);
  const boxRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    setGroupedNotifications(
      groupNotifications(Array.from(notifications.values()))
    );
  }, [notifications]);

  const loadMore = () => {
    if (!isLoadingNotificationData && hasMore) {
      setValue("page", page + 1);
    }
  };

  const location = useLocation();
  useEffect(() => {
    setTimeout(() => {
      refetch();
    }, 300);
  }, [location.pathname]);

  useEffect(() => {
    if (page > 1) {
      refetch();
    }
  }, [page]);

  useEffect(() => {
    const container = boxRef.current;
    if (container) {
      const handleScroll = () => {
        if (
          container.scrollTop + container.clientHeight >=
          container.scrollHeight - 10
        ) {
          loadMore();
        }
      };

      container.addEventListener("scroll", handleScroll);
      return () => container.removeEventListener("scroll", handleScroll);
    }
  }, [isLoadingNotificationData, hasMore, boxRef]);
  const getAvatarColor = (letter: string) => {
    const index =
      letter.toUpperCase().charCodeAt(0) % NotificationColors.length;
    return NotificationColors[index];
  };

  const handleMarkAsRead = () => {
    const notificationTrackerIds = Array.from(notifications.values()).map(
      (noti) => noti?.notificationTrackerId
    );
    MarkLoadedAsRead(notificationTrackerIds);
  };

  const { mutate: markLoadedAsRead, isLoading: isLoadingMarkLoadedAsRead } =
    useCustomMutation();

  const MarkLoadedAsRead: (filteredData: any) => void = (filteredData) => {
    markLoadedAsRead(
      {
        url: `${API_URL}/notification/markAsSeen`,
        method: "put",
        values: {
          notificationTrackerIds: filteredData,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setMenuAnchorEl(null);
          setValue("page", 1);
          setNotifications(new Map());
          setGroupedNotifications({});
          refetch();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <Box sx={{ display: "grid", gap: 4 }}>
      <Typography
        sx={{
          display: "flex",
          alignItems: "center",
          color: "#666666",
          gap: 2,
          fontSize: 18,
          fontWeight: "400 !important",
          "&:hover": { cursor: "pointer" },
        }}
        onClick={() => navigate(-1)}
      >
        <ChevronLeftRounded />
        {t("notifikasi")}
      </Typography>
      <Grid container>
        <Grid item xs={10}>
          <Box
            sx={{
              backgroundColor: "white",
              borderRadius: "15px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              display: "grid",
              overflow: "hidden",
            }}
          >
            <Box>
              {isLoadingNotificationData && notifications.size === 0 ? (
                <Box
                  sx={{
                    display: "grid",
                    gap: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    textAlign: "center",
                    px: 3,
                    py: 2,
                  }}
                >
                  <CircularProgress
                    size={24}
                    sx={{ display: "block", mx: 3, mb: 2 }}
                  />
                </Box>
              ) : notifications.size === 0 ? (
                <Box
                  sx={{
                    display: "grid",
                    gap: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    textAlign: "center",
                    px: 3,
                    py: 2,
                  }}
                >
                  <Typography className="label-dashboard">
                    {t("noMoreNotification")}
                  </Typography>
                  <Typography className="sub-title-notification">
                    {t("noMoreNotification2")}
                  </Typography>
                </Box>
              ) : (
                <>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mx: 3,
                      my: 3,
                      gap: 2,
                    }}
                  >
                    <Typography variant="h6" className="mainTitle">
                      {t("notifikasi")}
                    </Typography>
                    <IconButton
                      className="mainTitle"
                      onClick={(e) => setMenuAnchorEl(e.currentTarget)}
                    >
                      <MoreHoriz />
                    </IconButton>
                  </Box>
                  <Box ref={boxRef} sx={{ overflowY: "auto", maxHeight: 630 }}>
                    {Object.entries(groupedNotifications).map(
                      ([label, notis]) => (
                        <Box key={label}>
                          <Typography
                            className="title-no-height"
                            sx={{ fontWeight: "bold", mx: 3, my: 2 }}
                          >
                            {t(`${label}`)}
                          </Typography>
                          <Box sx={{ display: "grid" }}>
                            {notis.map((noti: any) => {
                              const createdDate = dayjs(
                                noti?.createdDate,
                                "DD-MM-YYYY HH:mm:ss"
                              );
                              const diffMinutes = dayjs().diff(
                                createdDate,
                                "minute"
                              );
                              const timeAgo =
                                diffMinutes < 60
                                  ? `${diffMinutes}m`
                                  : diffMinutes < 1440
                                  ? `${Math.floor(diffMinutes / 60)}j`
                                  : `${Math.floor(diffMinutes / 1440)}h`;

                              return (
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 2,
                                    p: 0,
                                    m: 0,
                                    minHeight: 72,
                                    backgroundColor:
                                      noti?.seen === false
                                        ? "var(--primary-color)40"
                                        : "transparent",
                                    px: 3,
                                    py: 2,
                                  }}
                                  key={noti?.notificationTrackerId}
                                >
                                  <Avatar
                                    sx={{
                                      bgcolor: getAvatarColor(
                                        noti?.subject.charAt(0)
                                      ),
                                      width: 55,
                                      height: 55,
                                      fontSize: 18,
                                    }}
                                  >
                                    {noti?.subject.charAt(0).toUpperCase()}
                                  </Avatar>
                                  <Box sx={{ flex: 1 }}>
                                    <Box
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "space-between",
                                      }}
                                    >
                                      <Typography
                                        variant="body1"
                                        className="label-dashboard"
                                        sx={{
                                          display: "-webkit-box",
                                          WebkitBoxOrient: "vertical",
                                          WebkitLineClamp: 1,
                                          overflow: "hidden",
                                        }}
                                      >
                                        {noti?.subject}
                                      </Typography>
                                      <Typography
                                        variant="caption"
                                        sx={{ color: "gray" }}
                                      >
                                        {timeAgo}
                                      </Typography>
                                    </Box>

                                    <Typography
                                      variant="body2"
                                      className="label-login"
                                      sx={{
                                        display: "-webkit-box",
                                        WebkitBoxOrient: "vertical",
                                        WebkitLineClamp: 2,
                                        overflow: "hidden",
                                        pr: 3,
                                      }}
                                      dangerouslySetInnerHTML={{
                                        __html: noti?.content.replace(
                                          /\r\n/g,
                                          " "
                                        ),
                                      }}
                                    />
                                  </Box>
                                </Box>
                              );
                            })}
                          </Box>
                        </Box>
                      )
                    )}
                    {isLoadingNotificationData && (
                      <Box
                        sx={{
                          display: "grid",
                          gap: 1,
                          justifyContent: "center",
                          alignItems: "center",
                          textAlign: "center",
                          px: 3,
                          py: 2,
                        }}
                      >
                        <CircularProgress
                          size={24}
                          sx={{ display: "block", mx: 3, mb: 2 }}
                        />
                      </Box>
                    )}
                    {!hasMore && (
                      <Box
                        sx={{
                          display: "grid",
                          gap: 1,
                          justifyContent: "center",
                          alignItems: "center",
                          textAlign: "center",
                          px: 3,
                          py: 2,
                        }}
                      >
                        <Typography className="label-dashboard">
                          {t("noMoreNotification")}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </>
              )}
            </Box>
          </Box>
        </Grid>
        <Grid item xs={2}></Grid>
      </Grid>
      <Popover
        open={Boolean(menuAnchorEl)}
        anchorEl={menuAnchorEl}
        onClose={() => setMenuAnchorEl(null)}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        sx={{ mt: 1 }}
      >
        <Typography
          className="label"
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: 300,
          }}
        >
          <Button
            fullWidth
            sx={{ textTransform: "none", py: 2 }}
            onClick={() => handleMarkAsRead()}
          >
            {t("markAsRead")}
          </Button>
        </Typography>
      </Popover>
    </Box>
  );
};
