import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { CustomTabsPanel } from "@/components";
import GarisPanduan from "./garis-panduan";
import PelaksanaanProgram from "./pelaksanaan-program";
import SenaraiPermohonan from "./senarai-permohonan";

const GeranExternalMain: React.FC = () => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const isMyLanguage = i18n.language === "my";

  const [initialTab, setInitialTab] = useState(0);

  useEffect(() => {
    if (location.state?.tab === "senarai-pembatalan") {
      setInitialTab(1);
    } else {
      setInitialTab(0);
    }
  }, [location.state]);

  const tabItems = [
    {
      label: isMyLanguage ? "Senarai Permohonan" : "Create Cancellation",
      content: <SenaraiPermohonan />,
    },
    {
      label: isMyLanguage ? "Pelaksanaan Program" : "Cancellation List",
      content: <PelaksanaanProgram />,
    },
    {
      label: isMyLanguage ? "Garis Panduan" : "Cancellation List",
      content: <GarisPanduan />,
    },
  ];

  return <CustomTabsPanel tabs={tabItems} initialTab={initialTab} />;
};

export default GeranExternalMain;
