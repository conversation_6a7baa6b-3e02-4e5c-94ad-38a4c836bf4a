import { useTranslation } from "react-i18next";
import { useForm, FieldValues, FormProvider } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box } from "@mui/material";
import { SocietyBanner, ButtonPrimary } from "@/components";

import MaklumatPertubhanDetail from "./MaklumatPertubuhanDetail";
import MaklumatPerbankanForm from "./MaklumatPerbankanForm";
import PerbelanjaanAForm from "./PerbelanjanAForm";
import PerbelanjaanBForm from "./PerbelanjaanBForm";
import PenerimaGeranForm from "./PenerimaGeranForm";
import ProgramKerjasamaForm from "./ProgramKerjasamaForm";
import ButiranProgramForm from "./ButiranProgramForm";
import PerbelanjaanProgramForm from "./PerbelanjaanProgramForm";
import AkuanForm from "./AkuanForm";

const SenaraiPermohonanForm: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();

  const methods = useForm<FieldValues>({
    defaultValues: {
      is_active: false,
      perbelanjaan: [
        { perkara: "", bilangan: "", anggaran_harga: "", jumlah: "" },
      ],
      perbelanjaanB: "",
      program: "",
    },
  });
  const { control, handleSubmit, getValues, setValue, watch } = methods;

  return (
    <FormProvider {...methods}>
      <Box className={classes.section} mb={1}>
        <SocietyBanner
          societyName="Persatuan Penduduk Presint 11F Putrajaya"
          societyNo="PPM-024-16-********"
        />
      </Box>

      <Box className={classes.section} mb={2}>
        <MaklumatPertubhanDetail />

        <MaklumatPerbankanForm />

        <PenerimaGeranForm />
      </Box>

      <Box className={classes.section} mb={2}>
        <PerbelanjaanAForm />
      </Box>

      <Box className={classes.section} mb={2}>
        <PerbelanjaanBForm />

        <ProgramKerjasamaForm />

        <ButiranProgramForm />

        <PerbelanjaanProgramForm />
      </Box>

      <Box className={classes.section}>
        <AkuanForm />

        <ButtonPrimary
          type="submit"
          sx={{ marginLeft: "auto" }}
          className={classes.btnSubmit}
          // disabled={isCreatingSocietyCancellation}
        >
          {/* {isCreatingSocietyCancellation && <CircularProgress size={15} />} */}
          {t("hantar")}
        </ButtonPrimary>
      </Box>
    </FormProvider>
  );
};

export default SenaraiPermohonanForm;
