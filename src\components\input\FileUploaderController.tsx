import { useRef, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Controller, Control, FieldValues } from "react-hook-form";
import { truncateFilename } from "@/helpers";

import { Box, IconButton, Typography, FormHelperText } from "@mui/material";
import { EyeIcon, UploadIcon } from "@/components/icons";

interface FileUploadControllerProps {
  name: string;
  control: Control<FieldValues>;
  required?: boolean;
  accept?: string;
  allowedTypes?: string[];
  maxFileSize?: number;
  helperTextPlacement?: "INSIDE" | "OUTSIDE";
  filePreview?: string | null;
  disabled?: boolean;
  onFileSelect?: (file: File | null) => void;
}

const DEFAULT_ACCEPT = ".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.svg";
const DEFAULT_ALLOWED_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "text/plain",
  "image/png",
  "image/jpeg",
  "image/gif",
  "image/svg+xml",
];

const formatFileSize = (bytes: number): string =>
  `${(bytes / 1024 / 1024).toFixed(1)}MB`;

const getFileTypesLabel = (accept: string) => {
  const extensions = accept
    .split(",")
    .map((ext) => ext.trim().replace(".", "").toUpperCase());
  return extensions.join(", ");
};

export const FileUploadController: React.FC<FileUploadControllerProps> = ({
  name,
  control,
  required = false,
  accept = DEFAULT_ACCEPT,
  allowedTypes = DEFAULT_ALLOWED_TYPES,
  maxFileSize = 25 * 1024 * 1024,
  helperTextPlacement = "INSIDE",
  filePreview: externalFilePreview,
  disabled = false,
  onFileSelect,
}) => {
  const { t } = useTranslation();
  const inputFileRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [internalFilePreview, setInternalFilePreview] = useState<string | null>(
    null
  );

  const activeFilePreview =
    selectedFile && internalFilePreview
      ? internalFilePreview
      : externalFilePreview;

  useEffect(() => {
    if (!externalFilePreview) {
      setInternalFilePreview(null);
      setSelectedFile(null);
    }
  }, [externalFilePreview]);

  const handleFileChange = (
    file: File | undefined | null,
    fieldOnChange: (value: string | null) => void
  ) => {
    if (!file) return;

    if (file.size > maxFileSize) {
      alert(t("validation.documentSize", { maxSize: maxFileSize }));
      return;
    }

    if (!allowedTypes.includes(file.type)) {
      alert(t("validation.invalidFileType"));
      return;
    }

    const previewUrl = URL.createObjectURL(file);
    setSelectedFile(file);
    setInternalFilePreview(previewUrl);
    fieldOnChange(file.name);
    onFileSelect?.(file);
  };

  const description = `${getFileTypesLabel(accept)} (max. ${formatFileSize(
    maxFileSize
  )})`;

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: required ? t("fieldRequired") : false,
      }}
      render={({ field, fieldState: { error } }) => (
        <>
          <Box
            sx={{
              border: error ? "1px solid red" : "1px solid #DADADA",
              borderRadius: "8px",
              p: 3,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 1,
              backgroundColor: "#FFFFFF",
              cursor: disabled ? "normal" : "pointer",
              "&:hover": {
                backgroundColor: "#F9FAFB",
              },
            }}
            onClick={() => !disabled && inputFileRef.current?.click()}
          >
            <Box
              sx={{
                width: "100%",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: 1,
              }}
            >
              {selectedFile || field.value ? (
                <Box
                  sx={{
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography sx={{ color: "#222", fontSize: "14px" }}>
                    {truncateFilename(selectedFile?.name ?? field.value, 30)}
                  </Typography>

                  {activeFilePreview && (
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(activeFilePreview, "_blank");
                      }}
                      sx={{ padding: 0 }}
                    >
                      <EyeIcon color="#666666" />
                    </IconButton>
                  )}
                </Box>
              ) : (
                <>
                  <UploadIcon />
                  <Typography
                    sx={{
                      color: "var(--secondary-color)",
                      fontSize: "14px",
                      fontWeight: "500 !important",
                      textAlign: "center",
                    }}
                  >
                    {t("muatNaik")}
                  </Typography>
                  <Typography
                    sx={{
                      color: "#667085",
                      fontSize: "12px",
                      fontWeight: "500 !important",
                      textAlign: "center",
                    }}
                  >
                    {description}
                  </Typography>
                </>
              )}
            </Box>

            <input
              ref={inputFileRef}
              type="file"
              hidden
              accept={accept}
              onChange={(e) =>
                handleFileChange(e.target?.files?.[0], field.onChange)
              }
              disabled={disabled}
            />
          </Box>

          {error && helperTextPlacement === "INSIDE" && (
            <FormHelperText sx={{ color: "red", fontSize: "12px" }}>
              {error.message}
            </FormHelperText>
          )}
        </>
      )}
    />
  );
};
