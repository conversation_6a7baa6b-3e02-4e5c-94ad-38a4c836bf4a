import { useTranslation } from "react-i18next";
import dayjs from "../dayjs";

export function useFormatDateLocalization() {
  const { i18n } = useTranslation();

  const formatDate = (date: dayjs.Dayjs | Date, format: string) => {
    if (dayjs(date).isValid()) {
      const stringLanguage = i18n.language as string;
      return dayjs(date)
        .locale(['bm', 'my'].includes(stringLanguage) ? 'ms' : stringLanguage)
        .format(format);
    }
    return "-";
  };

  return { formatDate };
}
