export const toCapitalCase = (value: string | null) => {
  if (!value) {
    return ''
  }
  return value.replace(/\b\w/g, c => c.toUpperCase());
}

export const toCapitalFirstLetter = (value: string | null) => {
  if (!value) {
    return ''
  }
  return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
}

export interface CountWordsOptions {
  /**
   * @default true
   */
  trim?: boolean;
}

export function countWords<
  Options extends CountWordsOptions = CountWordsOptions
>(str: string, options?: Options) {
  const trim = options?.trim ?? true;
  return (trim ? str.trim() : str)?.split(/\s+/)?.filter(Boolean)?.length ?? 0
}
