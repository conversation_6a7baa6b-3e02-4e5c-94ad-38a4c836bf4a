import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";

import { Box, Grid, styled, Typography, useTheme } from "@mui/material";
import { NavLink, useOutlet } from "react-router-dom";
import { FC, useCallback, useLayoutEffect, useRef } from "react";

import IconAduanSiasatan from "@/assets/svg/icon-aduan-siasatan.svg?react";
import IconPembatalan from "@/assets/svg/icon-pembatalan.svg?react";
import IconSekatanLiabiliti from "@/assets/svg/icon-sekatan-liabiliti.svg?react";
import IconPengurusanFee from "@/assets/svg/icon-pengurusan-fee.svg?react";
import IconPengurusanNotis from "@/assets/svg/icon-pengurusan-notis.svg?react";

interface NavIconProps {
  icon: FC;

  /**
   * @default false
   */
  active?: boolean;
}

const generateNavIcon =
  <Props extends NavIconProps = NavIconProps>({ icon }: Props) =>
  (isActive: boolean) => {
    const StyledIcon = styled(icon)(
      ({ theme }) =>
        isActive && {
          path: {
            stroke: "white",
            fill: theme.palette.primary.main,
          },
        }
    );
    return <StyledIcon />;
  };

const GeranInternalNavigation = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const location = useLocation();
  const outlet = useOutlet();
  const gridRef = useRef<(HTMLDivElement | null)[]>([]);

  const showNavigation = location.pathname.split("/").length <= 3;

  const primary = theme.palette.primary.main;
  const navigations = [
    {
      label: "Carian Pemohonan Pertubuhan",
      destinationPath: "/geran-internal/carian-permohonan",
      icon: generateNavIcon({ icon: IconAduanSiasatan }),
    },
    {
      label: "Laporan Pelaksanaan Geran",
      destinationPath: "/geran-internal/laporan-pelaksanaan",
      icon: generateNavIcon({ icon: IconPengurusanFee }),
    },
    {
      label: "Laporan Permohonan",
      destinationPath: "/geran-internal/laporan-permohonan",
      icon: generateNavIcon({ icon: IconPembatalan }),
    },
    {
      label: "Semakan Permohonan",
      destinationPath: "/geran-internal/semakan-permohonan",
      icon: generateNavIcon({ icon: IconPengurusanNotis }),
    },
    {
      label: "Pembayaran",
      destinationPath: "/geran-internal/pembayaran",
      icon: generateNavIcon({ icon: IconSekatanLiabiliti }),
    },
    {
      label: "Permohonan Lanjutan",
      destinationPath: "/geran-internal/permohonan-lanjutan",
      icon: generateNavIcon({ icon: IconPengurusanFee }),
    },
    {
      label: "Pengurusan Borang",
      destinationPath: "/geran-internal/pengurusan-borang",
      icon: generateNavIcon({ icon: IconPengurusanFee }),
    },
    {
      label: "Pentadbiran",
      destinationPath: "/geran-internal/pentadbiran",
      icon: generateNavIcon({ icon: IconPengurusanFee }),
    },
  ];

  const setGridRef = useCallback((el: HTMLDivElement, index: number) => {
    gridRef.current[index] = el;
  }, []);

  useLayoutEffect(() => {
    const itemsHeight = gridRef.current
      .filter((el): el is HTMLDivElement => el !== null && el !== undefined)
      .map((el) => el.offsetHeight);
    const maxHeight = Math.max(...itemsHeight);
    for (const el of gridRef.current) {
      if (el) {
        el.style.height = `${maxHeight}px`;
      }
    }
  }, []);

  return (
    <Grid container spacing={2}>
      <Grid item md={12}>
        <Grid container>
          <Grid item md={12}>
            {showNavigation && (
              <Box
                sx={{
                  backgroundColor: "white",
                  borderRadius: "1rem",
                  padding: 2,
                  boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)",
                }}
              >
                <Typography
                  color={primary}
                  sx={{
                    fontSize: 14,
                    fontWeight: "medium",
                    marginBottom: "1.5rem",
                  }}
                >
                  Geran Pertubuhan Prihatin Komuniti (GPPK)
                </Typography>
                <Grid container spacing={1}>
                  {navigations.map((navItem, index) => (
                    <Grid key={`enforcement-nav-item-${index}`} item sm={2}>
                      <NavLink
                        style={{ textDecoration: "none", height: "100%" }}
                        to={navItem.destinationPath}
                        end={index === 0}
                      >
                        {({ isActive }) => {
                          return (
                            <Box
                              ref={(el) =>
                                setGridRef(el as HTMLDivElement, index)
                              }
                              sx={{
                                padding: "0.75rem",
                                paddingTop: "0.5rem !important",
                                borderRadius: "0.5rem",
                                border: `1px solid ${primary}`,
                                backgroundColor: isActive ? primary : "white",
                                position: "relative",
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "space-between",
                                alignItems: "flex-start",
                                height: "100%",
                                minHeight: "60px",
                                paddingBottom: 0,
                                ...(isActive && {
                                  boxShadow:
                                    "4px 6px 12px 0 rgba(102, 102, 102, 0.3)",
                                }),
                              }}
                            >
                              <Typography
                                fontSize={12}
                                color={isActive ? "white" : primary}
                              >
                                {navItem.label}
                              </Typography>
                              {navItem.icon && (
                                <div
                                  style={{
                                    display: "flex",
                                    width: "100%",
                                    alignItems: "flex-end",
                                    justifyContent: "flex-end",
                                    position: "relative",
                                  }}
                                >
                                  {navItem.icon(isActive)}
                                </div>
                              )}
                            </Box>
                          );
                        }}
                      </NavLink>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default GeranInternalNavigation;
