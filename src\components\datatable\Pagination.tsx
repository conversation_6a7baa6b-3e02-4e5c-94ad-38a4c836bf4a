import { Box, TablePagination, TablePaginationProps } from "@mui/material"
import { CSSProperties } from "react"
import CustomPagination from "../pagination/CustomPagination"

export type DataTablePaginationProps
  = TablePaginationProps & {
  type?: "STANDARD" | "CUSTOM"
  position?: "LEFT" | "CENTER" | "RIGHT"
}

export const DataTablePagination = <
  PropType extends DataTablePaginationProps = DataTablePaginationProps
>({
  type = "STANDARD",
  position = "RIGHT",
  ...otherProps
}: PropType) => {
  const paginationStandardAlignmentValues: Record<"LEFT" | "CENTER" | "RIGHT", CSSProperties["justifyContent"]> = {
    LEFT: "flex-start",
    CENTER: "center",
    RIGHT: "flex-end"
  }

  const paginationStandardAlignment = paginationStandardAlignmentValues[position]

  return (
    <>
      {type === "STANDARD" && (
        <Box
          sx={{ display: "flex", justifyContent: paginationStandardAlignment, mt: 2 }}
        >
          <TablePagination
            // rowsPerPageOptions={otherProps?.rowsPerPageOptions ?? [5, 10, 25, 50]}
            {...otherProps}
            component="div"
          />
        </Box>
      )}

      {type === "CUSTOM" && (
        <CustomPagination
          {...otherProps}
          page={otherProps.page + 1}
          onPageChange={(newPage) => otherProps.onPageChange(null, newPage)}
          // @ts-expect-error
          position={(position ?? "RIGHT")?.toLowerCase()}
        />
      )}
    </>
  )
}
