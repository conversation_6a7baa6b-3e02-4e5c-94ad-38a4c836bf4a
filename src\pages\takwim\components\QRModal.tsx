import {
  Dialog,
  DialogContent,
  Typography,
  Box,
  IconButton
} from '@mui/material';
import { QRCodeSVG } from 'qrcode.react';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import React, { useRef } from 'react';

interface QRModalProps {
  open: boolean;
  onClose: () => void;
  type: 'kedatangan' | 'maklumbalas';
  title: string;
  eventNo: string;
}

const QRModal = ({ open, onClose, type, title, eventNo }: QRModalProps) => {
  const baseUrl = window.location.origin;
  const qrValue =
    type === 'kedatangan'
      ? `${baseUrl}/takwim/update-attendance?eventNo=${eventNo}`
      : `${baseUrl}/takwim/activity/${eventNo}?tab=feedback`;

  const svgRef = useRef<SVGSVGElement | null>(null);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(qrValue);
  };

  const handleDownload = () => {
    const svg = svgRef.current;
    if (!svg) return;

    const svgData = new XMLSerializer().serializeToString(svg);
    const canvas = document.createElement('canvas');
    const img = new Image();
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(svgBlob);

    img.onload = () => {
      canvas.width = svg.clientWidth || 200;
      canvas.height = svg.clientHeight || 200;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(img, 0, 0);
        URL.revokeObjectURL(url);
        const pngUrl = canvas.toDataURL('image/png');
        const downloadLink = document.createElement('a');
        downloadLink.href = pngUrl;
        downloadLink.download = `qr-${type}.png`;
        downloadLink.click();
      }
    };

    img.src = url;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          borderRadius: '16px',
          maxWidth: '400px',
          width: '100%',
        },
      }}
    >
      <DialogContent sx={{ textAlign: 'center', p: 4 }}>
        <Typography variant="h6" sx={{ mb: 1 }}>
          Scan untuk kehadiran
        </Typography>
        <Typography variant="body1" sx={{ mb: 3, color: '#666666' }}>
          {title}
        </Typography>

        <Box sx={{ mb: 3 }}>
          <QRCodeSVG
            value={qrValue}
            size={200}
            level="H"
            ref={svgRef}
          />
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            color: '#666666',
            mb: 2,
          }}
        >
          <Typography variant="body2" sx={{ color: '#666666' }}>
            {qrValue}
          </Typography>
          <IconButton size="small" onClick={handleCopyLink}>
            <ContentCopyIcon fontSize="small" />
          </IconButton>
        </Box>

        <IconButton
          onClick={handleDownload}
          sx={{
            backgroundColor: '#B2EBEB',
            '&:hover': { backgroundColor: '#99E6E6' },
          }}
        >
          <DownloadIcon sx={{ color: '#319795' }} />
        </IconButton>
      </DialogContent>
    </Dialog>
  );
};

export default QRModal;
