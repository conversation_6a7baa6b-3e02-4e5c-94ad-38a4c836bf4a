import { IEventOrganiser } from "@/types/event";
import { Avatar, AvatarGroup, Box, Button, Typography } from "@mui/material";

interface AvatarGroupProps {
  organizer?: IEventOrganiser;
  picPhoneNumber?: string|null;
  participants: Array<{
    name: string;
    avatar: string;
  }>;
}

const handleContact = (phoneNumber?: string|null) => {
  console.log("calling....", phoneNumber);

  if (phoneNumber) {
    window.location.href = `tel:${phoneNumber}`;
  }
};


const CustomAvatarGroup: React.FC<AvatarGroupProps> = ({
  organizer,
  picPhoneNumber,
  participants,


}) => {
  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
      {organizer && (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={"https://www.ros.gov.my/assets/img/jppm.jpg"}
            sx={{
              width: 70,
              height: 70,
              border: "2px solid white",
              boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
            }}
          />
          <Box>
            <Typography
              variant="h6"
              sx={{
                color: "#333",
                fontWeight: 500,
              }}
            >
              JPPM
            </Typography>
            <Button
              // variant="button"
              onClick={() => handleContact(picPhoneNumber)}
              disabled={!picPhoneNumber}
              // color=""
              variant="outlined"
              sx={{
                textTransform: "none",
                color: "#3483F9",
                fontSize: "7px",
                border: "1px solid #3483F9",
                p: 0.5,
                borderRadius: "5px",
                cursor: "pointer",
              }}
            >
              Hubungi
            </Button>
          </Box>
        </Box>
      )}

      <AvatarGroup
        max={8}
        total={participants.length}
        sx={{
          "& .MuiAvatar-root": {
            width: 32,
            height: 32,
            fontSize: "0.875rem",
            border: "2px solid white",
            boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
            backgroundColor: "#0CA6A6",
          },
          "& .MuiAvatarGroup-avatar": {
            "&:first-child": {
              borderColor: "#0CA6A6",
              backgroundColor: "#FFFFFF", // Different color for +remaining
              color: "#0CA6A6", // Text color for the number
              fontWeight: 600,
            },
          },
        }}
      >
        {participants.map((participant, index) => (
          <Avatar
            key={index}
            alt={participant.name}
            src={participant.avatar ? participant.avatar : participant.name}
          />
        ))}
      </AvatarGroup>
    </Box>
  );
};

export default CustomAvatarGroup;
