import { Box, Dialog, DialogContent, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { CheckedIcon } from "@/components/input/customRadio";
import { t } from "i18next";
import { ApplicationStatusList, ROApprovalType, useMutation } from "@/helpers";
import { useLocation } from "react-router-dom";
import { ButtonOutline } from "@/components";
import Input from "@/components/input/Input";
import { usePertubuhanContext } from "./PertubuhanProvider";

function KeputusanPertubuhan() {
  const [dialogSejarahKuiriSaveOpen, setDialogSejarahKuiriSaveOpen] =
    useState(false);
  const location = useLocation();
  const { societyId } = location.state || {};
  const handleQueryOpen = async () => {
    setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen);
  };
  const { societyDetailData } = usePertubuhanContext();

  const [ROQuery, setROQuery] = useState<any>(null);
  const [ROApproval, setROApproval] = useState<any>(null);

  const { fetch: getQuery, isLoading: isLoadingQuery } = useMutation({
    url: "society/roQuery/getQuery",
    method: "post",
    onSuccess: (data) => {
      console.log("roQuery/getQuery", data);
      const queryData = data?.data?.data;
      if (queryData?.length > 0) {
        setROQuery(queryData);
      }
    },
    errorNotification: () => {
      return {
        message: t("error"),
        type: "error",
      };
    },
    onSuccessNotification: () => {},
    onErrorNotification() {},
  });

  const { fetch: getRoApproval, isLoading: isLoadingRoApproval } = useMutation({
    url: "society/roApproval/getAll",
    method: "post",
    onSuccess: (data) => {
      console.log("roApproval/getAll", data);
      const queryData = data?.data?.data;
      if (queryData?.length > 0) {
        setROApproval(queryData);
      }
    },
    errorNotification: () => {
      return {
        message: t("error"),
        type: "error",
      };
    },
    onSuccessNotification: () => {},
    onErrorNotification() {},
  });

  useEffect(() => {
    if (societyId) {
      const payload = {
        societyId: societyId,
        roApprovalType: ROApprovalType.SOCIETY_REGISTRATION.code,
      };
      const roApprovalPayload = {
        societyId: societyId,
        type: ROApprovalType.SOCIETY_REGISTRATION.code,
      };

      getRoApproval(roApprovalPayload);
      getQuery(payload);
    }
  }, [societyId]);

  const [translatedList, setTranslatedList] = useState<
    { value: number | string; label: string }[]
  >([]);
  useEffect(() => {
    const newList = ApplicationStatusList.map((item) => ({
      value: item.id,
      label: t(item.value),
    }));
    setTranslatedList(newList);
  }, [t]);

  return (
    <>
      <Box sx={{ display: "grid", gap: 1 }}>
        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "15px",
            p: 3,
            border: "0.5px solid #dfdfdf",
            display: "grid",
            gap: 1,
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography color={"primary"}>{t("kuiri")}</Typography>
            <ButtonOutline
              sx={{
                width: "20%",
                gap: 1,
              }}
              disabled={!(ROQuery?.length > 0)}
              onClick={() => handleQueryOpen()}
            >
              <img height={16} width={15} src="/addDocument.png" />
              {t("queryHistory")}
            </ButtonOutline>
          </Box>

          <Input
            label={t("catatanKuiri")}
            name="notesQuery"
            value={
              ROQuery?.[0]?.notesQuery
                ? ROQuery?.[0]?.notesQuery
                : ROQuery?.[0]?.note
                ? ROQuery?.[0]?.note
                : null
            }
            disabled
            multiline
            rows={4}
          />
        </Box>
        <Box
          sx={{
            p: 3,
            backgroundColor: "white",
            borderRadius: "15px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Input
            label={t("statusPertubuhan")}
            name="applicationStatusCode"
            type="select"
            disabled
            options={translatedList}
            value={societyDetailData?.applicationStatusCode}
          />

          <Input
            label={t("remarks")}
            name="decisionNotes"
            disabled
            value={ROApproval?.[0]?.note ? ROApproval[0].note : null}
            multiline
            rows={4}
          />
        </Box>
      </Box>
      <Dialog
        open={dialogSejarahKuiriSaveOpen}
        onClose={() => setDialogSejarahKuiriSaveOpen(false)}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 4 }}>
          <Box
            sx={{
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                pb: 4,
                color: "var(--primary-color)",
              }}
              gap={2}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen)
                }
              />
              <Typography sx={{ fontWeight: "4001important" }}>
                {t("queryHistory")}
              </Typography>
            </Box>
            {/* @ts-ignore */}
            {ROQuery?.length ? (
              // @ts-ignore
              ROQuery?.map((item, index) => (
                <Box sx={{ display: "flex", mb: 4 }} key={item.id}>
                  <Box sx={{ mr: 2 }}>
                    <Box
                      sx={{
                        width: 35,
                        height: 35,
                        borderRadius: "50%",
                        border: `1px solid ${
                          item.finished ? "var(--primary-color)" : "#FF0000"
                        }`,
                        backgroundColor: item.finished
                          ? "var(--primary-color)80"
                          : "#FF000080",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {item.finished ? <CheckedIcon /> : null}
                    </Box>
                    {/* @ts-ignore */}
                    {index !== ROQuery?.length - 1 && !item?.finished && (
                      <Box
                        sx={{
                          width: 2,
                          height: "100%",
                          backgroundColor: "#DADADA",
                          ml: 2,
                        }}
                      />
                    )}
                  </Box>
                  <Box sx={{ width: "100%" }}>
                    <Box sx={{ display: "flex", gap: 3 }}>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #5088FF",
                          background: "#5088FF80",
                          borderRadius: "9px",
                          color: "#fff",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Kuiri #{index + 1}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        {item?.createdDate}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Pegawai: {item?.queryReceiver}
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        p: 3,
                        border: "1px solid #D9D9D9",
                        borderRadius: "14px",
                        width: "100%",
                        mt: 2,
                        minHeight: "150px",
                        position: "relative",
                      }}
                    >
                      <Typography sx={{ mb: 3, color: "#666666" }}>
                        {item?.notesQuery || item?.note}
                      </Typography>
                      <Box
                        sx={{
                          fontFamily: '"Poppins", sans-serif',
                          backgroundColor: item.finished
                            ? "var(--primary-color)"
                            : "#FF000080",
                          border: `1px solid ${
                            item.finished ? "var(--primary-color)" : "#FF0000"
                          }`,
                          padding: "6px 20px",
                          borderRadius: "18px",
                          color: "#fff",
                          fontSize: "14px",
                          fontWeight: "400",
                          position: "absolute",
                          bottom: "20px",
                          right: "20px",
                        }}
                      >
                        {item.finished ? t("completed") : t("belumselesai")}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              ))
            ) : (
              <Typography className="label">{t("noData")}</Typography>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
}
export default KeputusanPertubuhan;
