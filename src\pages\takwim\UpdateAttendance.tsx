import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Box, Typography, CircularProgress, Paper } from "@mui/material";
import { useNotification, useGetIdentity } from "@refinedev/core";
import { eventService } from "@/services/eventService";
import { ApiResponse, IEvent } from "@/types/event";
import {
  AttendanceUpdateRequest,
  AttendanceUpdateResponse,
  IEventAttendee,
} from "@/types/eventAttendee";
import { IUser } from "@/types/user";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import { t } from "i18next";
import AuthHelper from "@/helpers/authHelper";

const UpdateAttendance = () => {
  const [searchParams] = useSearchParams();
  const eventNo = searchParams.get("eventNo");
  const navigate = useNavigate();
  const { data: user } = useGetIdentity<IUser>();
  const { open: openNotification } = useNotification();
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [eventDetails, setEventDetails] =
    useState<AttendanceUpdateResponse | null>(null);
  const [responseMsg, setResponseMsg] = useState<string>();
  const [isWlakinParticipant, setIswalkinParticipant] =
    useState<boolean>(false);

  // Check authentication on mount
  // useEffect(() => {
    // Check if user is authenticated
    // const isAuthenticated = AuthHelper.getToken();

    // if (!isAuthenticated) {
    //   // Get current path and encode it for the query parameter
    //   const currentPath = window.location.pathname + window.location.search;
    //   const encodedPath = encodeURIComponent(currentPath);

    //   // Redirect to login with the 'to' parameter
    //   navigate(`/login?to=${encodedPath}`);
    //   return;
    // }
  // }, [navigate]);

  useEffect(() => {
    if (user) {
      updateAttendanceService(eventNo, user?.identificationNo);
    }
  }, [eventNo, user]);

  const updateAttendanceService = async (
    eventNo: string | null,
    identificationNo: string
  ) => {
    if (!eventNo) {
      setError("Event NO not found");
      setLoading(false);
      return;
    }

    try {
      // First get event details

      // Then update attendance
      const attendeeData: AttendanceUpdateRequest = {
        eventNo: eventNo,
        identificationNo: identificationNo,
        present: true,
      };
      const response: ApiResponse<AttendanceUpdateResponse> =
        await eventService.updateEventAttendeePresent(attendeeData);
      setEventDetails(response.data);

      if (response.code === 200 || response.code === 202) {
        setSuccess(true);
        setResponseMsg(response.msg);
        openNotification?.({
          message: response.msg ?? "Attendance updated successfully",
          type: "success",
        });
      } else {
        setResponseMsg(response.msg);
        if (response.code === 201) {
          setIswalkinParticipant(true);
        }
        throw new Error(response.msg || "Failed to update attendance");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
      openNotification?.({
        message:
          err instanceof Error ? err.message : "Failed to update attendance",
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Show loading while checking authentication
  if (!user) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
          backgroundColor: "#f5f5f5",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        // minHeight: "100vh",
        backgroundColor: "#f5f5f5",
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          maxWidth: 400,
          width: "100%",
          textAlign: "center",
        }}
      >
        {loading ? (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <CircularProgress />
            <Typography>{t("updatingAttendance")}</Typography>
          </Box>
        ) : success ? (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <CheckCircleIcon sx={{ fontSize: 60, color: "success.main" }} />
            <Typography variant="h6">{responseMsg}</Typography>
            {eventDetails && (
              <Typography variant="body2" color="textSecondary">
                Acara: {eventDetails.eventName}
              </Typography>
            )}
            <Typography
              variant="body2"
              sx={{ cursor: "pointer", color: "primary.main", mt: 2 }}
              onClick={() => navigate(`/takwim/activity/${eventNo}`)}
            >
              {t("Kembali ke Butiran Acara")}
            </Typography>
          </Box>
        ) : (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <ErrorIcon sx={{ fontSize: 60, color: "error.main" }} />
            <Typography variant="h6" color="error">
              {error || t("attendanceUpdateFailed")}
              {isWlakinParticipant ? (
                <Box>
                  <Typography variant="h6">Sila klik pada pautan ini untuk sertai acara. </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      cursor: "pointer",
                      color: "primary.main",
                      mt: 2,
                      textDecoration: "underline",
                    }}
                    onClick={() => navigate(`/takwim/activity/${eventNo}?isWalkinParticipant=true`)}
                  >
                    {t("Sertai Acara")}
                  </Typography>
                </Box>
              ) : (
                <Typography
                  variant="body2"
                  sx={{ cursor: "pointer", color: "primary.main", mt: 2 }}
                  onClick={() => navigate(`/takwim/activity/${eventNo}`)}
                >
                  {t("Kembali ke Butiran Acara")}
                </Typography>
              )}
            </Typography>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default UpdateAttendance;




