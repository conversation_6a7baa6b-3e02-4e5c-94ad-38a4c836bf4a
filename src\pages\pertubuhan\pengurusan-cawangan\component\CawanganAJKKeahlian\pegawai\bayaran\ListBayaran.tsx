import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import InputLabel from "@mui/material/InputLabel";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { Select, Option } from "@/components/input";
// import { OrganizationStepper } from "../../organization-stepper";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { useLocation, useNavigate, useParams } from "react-router-dom";
// import { removeFromStorage } from "../perlembagaan/removeFasal";
import { API_URL } from "@/api";
import { ApplicationStatus } from "@/helpers/enums";
// import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";

export const ListBayaran = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const navigate = useNavigate();
  const [isChecked, setIsChecked] = useState(false);

  const { id: societyId } = useParams();
  const location = useLocation();
  const publicOfficerId = location.state?.publicOfficerId;
  const propertyOfficerId = location.state?.propertyOfficerId;
  const publicOfficerName = location.state?.publicOfficerName;
  const createdDate = location.state?.createdDate;
  const type = location.state?.type;

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const safeParse = (str: string | null) => {
    try {
      return str ? JSON.parse(str) : {};
    } catch (e) {
      return {};
    }
  };

  const meetingCreateRequest = localStorage.getItem("meetingCreateRequest");
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const committeeCreateRequests = localStorage.getItem(
    "committeeCreateRequests"
  );
  const nonCitizenCommitteeCreateRequests = localStorage.getItem(
    "nonCitizenCommitteeCreateRequests"
  );
  const documentCreateRequest = localStorage.getItem("documentCreateRequest");

  const { mutate: createOrganization, isLoading: isCreating } = useCreate();
  const { mutate: editOfficer } = useCustomMutation();
  const { mutate: makePayment } = useCustomMutation();

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const editSociety = () => {
    //console.log("decodedId",decodedId);
    if (societyId) {
      const data = {
        applicationStatusCode:
          paymentMethod === "kaunter"
            ? ApplicationStatus.MENUNGGU_BAYARAN_KAUNTER
            : ApplicationStatus.MENUNGGU_BAYARAN_ONLINE,
      };
      const paymentData = {
        societyId: societyId,
        branchId: branchDataRedux.id,
        ...(propertyOfficerId && { propertyOfficerId }),
        ...(publicOfficerId && { publicOfficerId }),
        amount: 10,
        paymentMethod: "K", //K for KAUNTER, O FOR ONLINE
        paymentType: publicOfficerId
          ? "Pendaftaran Pegawai Awam (Pembayaran KAUNTER)"
          : "Pendaftaran Pegawai Harta (Pembayaran KAUNTER)",
        email: "",
      };
      editOfficer(
        {
          method: "put",
          url: publicOfficerId
            ? `${API_URL}/society/public_officer/${publicOfficerId}`
            : `${API_URL}/society/property_officer/${propertyOfficerId}`,
          values: data,
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: () => {
            return {
              message: t(
                `${
                  publicOfficerId ? "public" : "property"
                }OfficerCreatedSuccessfully`
              ),
              type: "success",
            };
          },
          errorNotification: (data) => {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onSuccess(data, variables, context) {
            if (paymentMethod === "kaunter") {
              KaunterPayment(paymentData);
            } 
          },
        }
      ); 
    }
  };

  function KaunterPayment(paymentData: any) {
    makePayment(
      {
        method: "post",
        url: `${API_URL}/society/payment/makePayment`,
        values: paymentData,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          navigate(`kaunter?id=${societyId}`, {
            state: {
              createdDate,
              societyId: societyId,
              ...(propertyOfficerId && { propertyOfficerId }),
              ...(publicOfficerId && { publicOfficerId }),
              ...(publicOfficerName && { publicOfficerName }),
            },
          });
        },
      }
    );
  }

  const { data: paymentStatus, isLoading } = useCustom({
    url: `${API_URL}/society/admin/integration/payment/status`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const onlinePaymentEnabled: boolean =
    paymentStatus?.data?.data?.payment?.toLowerCase() == "enabled";
  const alert: boolean = paymentStatus?.data?.data?.alert;

  const handleSave = () => {
    const data = {
      meetingCreateRequest: safeParse(meetingCreateRequest),
      constitutionContentRegisterRequest: [
        {
          ...societyDataRedux,
          constitutionValues: [],
        },
      ],
      committeeCreateRequests: [],
      nonCitizenCommitteeCreateRequests: [],
      documentCreateRequest: safeParse(documentCreateRequest),
    };

    createOrganization(
      {
        resource: "society/organization/register",
        values: data,
        meta: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          handleCloseDialog();
          setDialogAlertSuccessSaveOpen(true);

          localStorage.removeItem("meetingCreateRequest");
          localStorage.removeItem("committeeCreateRequests");
          localStorage.removeItem("nonCitizenCommitteeCreateRequests");
          localStorage.removeItem("documentCreateRequest");
          localStorage.removeItem("organizationGoals");

          // removeFromStorage();
        },
      }
    );
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <Box sx={{ display: "flex" }}>
      <Box sx={{ width: "100%" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
              width: "100%",
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: "500 !important",
                  }}
                >
                  {t("payment")}
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontFamily: "Poppins",
                  fontSize: 14,
                  fontWeight: "400 !important",
                  lineHeight: "21px",
                  textAlign: "left",
                  textUnderlinePosition: "from-font",
                  textDecorationSkipInk: "none",
                  mb: 2,
                }}
              >
                {t("agreementText")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1,
                }}
              >
                <Checkbox
                  id="akuan-setuju-terima"
                  checked={isChecked}
                  onChange={handleCheckboxChange}
                  sx={{
                    color: "#00A7A7",
                    "&.Mui-checked": {
                      color: "#00A7A7",
                    },
                    padding: "0",
                  }}
                />
                <InputLabel
                  htmlFor="akuan-setuju-terima"
                  required
                  sx={{
                    color: "#333333",
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: 1.4,
                  }}
                >
                  {t("agreementAcceptance")}
                </InputLabel>
              </Box>
            </Box>

            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 3,
              }}
            >
              <Box sx={{ mb: 2 }}>
                <Box sx={{ mb: 1 }}>
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      color: "#00A7A7",
                      fontSize: 16,
                      fontWeight: "500 !important",
                    }}
                  >
                    {t("paymentMethod")}
                  </Typography>
                </Box>
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                >
                  <Grid item xs={4}>
                    {/* <Typography>1</Typography> */}
                  </Grid>
                  <Grid item xs={8}>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        color: "#FF0000",
                        marginLeft: "15px",
                      }}
                    >
                      {alert}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <InputLabel
                      htmlFor="cara-pembayaran"
                      required
                      sx={{
                        color: "#333333",
                        fontSize: 14,
                        fontWeight: 400,
                        minWidth: "150px",
                      }}
                    >
                      {t("paymentMethod")}
                    </InputLabel>
                  </Grid>
                  <Grid item xs={8}>
                    <Select
                      value={paymentMethod}
                      onChange={handleChange}
                      id="cara-pembayaran"
                      t={t}
                      sx={{
                        width: "100%",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E5E5",
                          borderRadius: 1,
                        },
                      }}
                    >
                      <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                      {onlinePaymentEnabled ? (
                        <Option value="online">{t("pembayaranOnline")}</Option>
                      ) : (
                        <Option value="online" disabled>
                          {t("pembayaranOnline")}
                        </Option>
                      )}
                    </Select>
                  </Grid>
                </Grid>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontSize: 12,
                  marginTop: 10,
                  textAlign: "center",
                }}
              >
                {t("paymentNote")}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <ButtonPrimary
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
                disabled={!isChecked || !paymentMethod}
                onClick={handleSubmit}
              >
                {t("hantar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          PaperProps={{
            style: {
              borderRadius: "8px",
              width: "400px",
              padding: "24px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          }}
        >
          <DialogContent sx={{ textAlign: "center", p: 0, mb: 3 }}>
            <Typography
              sx={{
                fontSize: 16,
                color: "#333333",
                fontWeight: 500,
              }}
            >
              {t("confirmSubmitApplication")}
            </Typography>
          </DialogContent>

          <DialogActions
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 1,
              p: 0,
            }}
          >
            <ButtonPrimary
              onClick={() => {
                handleCloseDialog();
                editSociety();
                if (paymentMethod === "online") {
                  const state = {
                    createdDate,
                    societyId: societyId,
                    ...(propertyOfficerId && { propertyOfficerId }),
                    ...(publicOfficerId && { publicOfficerId }),
                    ...(publicOfficerName && { publicOfficerName }),
                  };
                  navigate("online?id=" + societyId, {
                    state: state,
                  });
                }
              }}
              sx={{
                width: "79px",
                height: "24px",
                padding: "11px 16px",
                borderRadius: "10px",
                backgroundColor: "#147C7C",
                textTransform: "none",
                fontSize: "14px",
                "&:hover": {
                  backgroundColor: "#116666",
                },
              }}
            >
              {t("hantar")}
            </ButtonPrimary>

            <Typography
              onClick={handleCloseDialog}
              sx={{
                fontFamily: "Poppins",
                fontSize: "12px",
                fontWeight: 400,
                lineHeight: "12px",
                textAlign: "center",
                textDecoration: "underline",
                textDecorationStyle: "solid",
                textUnderlinePosition: "from-font",
                textDecorationSkipInk: "none",
                color: "#DADADA",
                cursor: "pointer",
              }}
            >
              {t("kembali")}
            </Typography>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        {/* <OrganizationStepper activeStep={activeStep} /> */}

        {/* <InfoQACard /> */}
      </Box>
    </Box>
  );
};

export default ListBayaran;
