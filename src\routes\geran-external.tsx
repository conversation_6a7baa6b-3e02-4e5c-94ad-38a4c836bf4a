/* eslint-disable react-refresh/only-export-components */
import { Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import { useSelector } from "react-redux";
import { getUserPortal } from "@/redux/userReducer";
import { PORTAL_EXTERNAL } from "@/helpers";

import { PageLoader } from "@/components";
const GeranExternalLayout = lazy(() => import("@/pages/geran-external/Layout"));
const GeranExternalMain = lazy(() => import("@/pages/geran-external/Main"));
const SenaraiPermohonanForm = lazy(
  () => import("@/pages/geran-external/senarai-permohonan/Form")
);

const routeComponents = (
  <Route
    path="geran-external"
    element={
      <Suspense fallback={<PageLoader />}>
        <GeranExternalLayout />
      </Suspense>
    }
  >
    <Route index element={<GeranExternalMain />} />
    <Route path="senarai-permohonan/:id" element={<SenaraiPermohonanForm />} />
  </Route>
);

/**
 * @deprecated please use {@link useGeranExternalRoutes} instead
 */
export const geranExternal = {
  routes:
    localStorage.getItem("portal") === PORTAL_EXTERNAL &&
    import.meta.env.VITE_APP_ENV !== "production" ? (
      <></>
    ) : null,
};

export const useGeranExternalRoutes = () => {
  const userPortal = useSelector(getUserPortal);
  const routes =
    userPortal === parseInt(PORTAL_EXTERNAL) &&
    import.meta.env.VITE_APP_ENV !== "production" &&
    routeComponents;

  return {
    routes,
  };
};
