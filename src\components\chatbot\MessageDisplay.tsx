import { Ava<PERSON>, <PERSON>, Typo<PERSON>, <PERSON>, Icon<PERSON>utton, Toolt<PERSON> } from "@mui/material";
import { GroupedChatMessage } from "./types";
import { memo, useCallback, useEffect, useState } from "react";
import { VolumeUp, VolumeOff } from "@mui/icons-material";
import { useLocation } from "react-router-dom";
import { useSpeech } from "@/contexts/chatbot/SpeechContext";

/**
 * Formats a timestamp string to a readable time format
 * @param timestamp ISO timestamp string
 * @returns Formatted time string (e.g., "14:30")
 */
const formatMessageTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false });
  } catch (e) {
    // console.error("Error formatting timestamp:", e);
    return "";
  }
};

/**
 * Converts markdown-style links to JSX
 * @param text Text containing markdown links
 * @returns Array of text and link elements
 */
const parseLinks = (text: string) => {
  const parts = text.split(/(\[.*?\]\(.*?\))/);
  return parts.map((part, index) => {
    const linkMatch = part.match(/\[(.*?)\]\((.*?)\)/);
    if (linkMatch) {
      const [, label, url] = linkMatch;
      return (
        <Link
          key={index}
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          sx={{
            wordBreak: "break-all",
            overflowWrap: "break-word",
            "&:hover": {
              opacity: 0.8,
            }
          }}
        >
          {label}
        </Link>
      );
    }
    return part;
  });
};

/**
 * Formats a date string to the specified locale format
 * @param dateString ISO date string
 * @param locale The locale to format the date in ('en' or 'ms')
 * @returns Formatted date string (e.g., "Ahad, 18 Mei 2025" or "Sunday, May 18, 2025")
 */
const formatGroupDate = (dateString: string, locale: string = 'en'): string => {
  try {
    const date = new Date(dateString);
    const localeCode = locale === 'ms' ? 'ms-MY' : 'en-US';
    return date.toLocaleDateString(localeCode, {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  } catch (e) {
    // console.error("Error formatting group date:", e);
    return dateString;
  }
};

/**
 * Normalize text to handle variations of "Rosie"
 * @param text The text to normalize
 * @returns Normalized text
 */
const normalizeRosieName = (text: string): string => {
  // Define variations of Rosie that should be normalized
  const rosieVariations = [
    /r[ou]s[iey]+/i,  // Matches: Rosie, Rusi, Rosi, Russi, Rossi
    /k[ei]r[ou]s[iey]+/i,  // Matches: Kerusi, Kerosi
    /r[ou]s[iey]+/gi,  // Global match for multiple occurrences
    /k[ei]r[ou]s[iey]+/gi,  // Global match for multiple occurrences
  ];

  // Replace any variation with "Rosie"
  let normalizedText = text;
  rosieVariations.forEach(variation => {
    normalizedText = normalizedText.replace(variation, 'Rosie');
  });

  // Handle case where the name is at the start of a sentence
  normalizedText = normalizedText.replace(/^rosie/i, 'Rosie');
  
  // Handle case where the name is after a space
  normalizedText = normalizedText.replace(/\srosie\b/gi, ' Rosie');

  return normalizedText;
};

interface MessageDisplayProps {
  messages: GroupedChatMessage[];
  currentBotMessage: string | null;
  displayedBotText: string;
  loading: boolean;
  error: string;
  userProfilePicture?: string;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  locale?: string;
  disableSpeech?: boolean;
  isWidget?: boolean;
}

/**
 * Component for displaying chat messages
 */
export const MessageDisplay = memo(({
  messages,
  currentBotMessage,
  displayedBotText,
  loading,
  error,
  userProfilePicture,
  messagesEndRef,
  locale = 'en',
  disableSpeech = false,
  isWidget = false,
}: MessageDisplayProps) => {
  const location = useLocation();
  
  // Get text-to-speech functionality and UI control from the speech context
  const { textToSpeech, speak, stopSpeaking, showSpeechControls } = useSpeech();

  // Check if we're on the chatbot page
  const isOnChatbotPage = location.pathname === "/chatbot" || location.pathname === "/chatbot-full";

  // Track if a message is currently being spoken
  const [speakingMessageId, setSpeakingMessageId] = useState<string | null>(null);

  /**
   * Handles speaking a message with proper replay functionality
   */
  const handleSpeak = useCallback(async (text: string, messageId: string) => {
    // console.log('MessageDisplay: handleSpeak called for message:', messageId);
    
    // If the same message is currently speaking, stop it and replay
    if (speakingMessageId === messageId) {
      // console.log('MessageDisplay: Same message is speaking, stopping and replaying');
      stopSpeaking();
      setSpeakingMessageId(null);
      
      // Wait a bit for the stop to complete, then replay
      setTimeout(() => {
        const uniqueId = messageId || `msg_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`;
        const isOnSpeechInputPage = window.location.pathname === "/speech-input";
        speak(text, uniqueId, isOnSpeechInputPage);
        setSpeakingMessageId(uniqueId);
        // console.log('MessageDisplay: Replaying message with ID:', uniqueId);
      }, 200);
    } else {
      // Different message or no message currently speaking
      // console.log('MessageDisplay: Starting new speech for message:', messageId);
      
      // Use the messageId if available, otherwise use the timestamp
      const uniqueId = messageId || `msg_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`;

      // Check if we're on the speech input page
      const isOnSpeechInputPage = window.location.pathname === "/speech-input";

      // Only speak on the chatbot page, not on the speech input page
      speak(text, uniqueId, isOnSpeechInputPage);
      setSpeakingMessageId(uniqueId);

      // console.log('MessageDisplay: Speaking message with ID:', uniqueId);
    }
  }, [speak, stopSpeaking, speakingMessageId]);

  // Update speaking state when textToSpeech state changes
  useEffect(() => {
    if (!textToSpeech.isSpeaking) {
      setSpeakingMessageId(null);
    }
  }, [textToSpeech.isSpeaking]);

  // Group messages by date
  const messagesByDate = Array.from(
    messages.reduce((map, msg) => {
      // Make sure the message has a valid date
      const date = msg.date ? formatGroupDate(msg.date, locale) : formatGroupDate(new Date().toDateString(), locale);
      if (!map.has(date)) map.set(date, []);
      map.get(date)!.push(msg);
      return map;
    }, new Map<string, GroupedChatMessage[]>())
  );

  // Log the number of messages and groups
  // console.log(`MessageDisplay: Displaying ${messages.length} messages in ${messagesByDate.length} date groups`);

  // Log message details for debugging
  useEffect(() => {
    if (messages.length > 0) {
      // console.log(`MessageDisplay: First message: ${messages[0].sender} - "${messages[0].text.substring(0, 30)}..."`);
      // console.log(`MessageDisplay: Last message: ${messages[messages.length-1].sender} - "${messages[messages.length-1].text.substring(0, 30)}..."`);
    }
  }, [messages]);

  return (
    <Box sx={{ 
      flex: 1, 
      p: 2, 
      overflowY: "auto",
      overflowX: "hidden",
      width: "100%",
      maxWidth: "100%",
      minWidth: 0, // Important for flex child to prevent overflow
    }}>
      {/* Messages grouped by date */}
      {messagesByDate.map(([date, msgs], groupIdx) => (
        <Box key={date + groupIdx} sx={{ width: "100%", maxWidth: "100%" }}>
          <Typography
            variant="caption"
            sx={{ display: "block", textAlign: "center", color: "gray", mt: 2, mb: 1 }}
          >
            {date} ({msgs.length} messages)
          </Typography>
          {msgs.map((msg, idx) => (
            <Box
              key={groupIdx + "-" + idx}
              sx={{
                display: "flex",
                flexDirection: msg.sender === "user" ? "row-reverse" : "row",
                alignItems: "flex-end",
                mb: 1.5,
                width: "100%",
                maxWidth: "100%",
              }}
            >
              <Avatar
                src={msg.sender === "user" ? userProfilePicture : "/rosie-bot.png"}
                alt={msg.sender === "user" ? "User Avatar" : "Bot Avatar"}
                variant={msg.sender === "user" ? "circular" : "square"}
                sx={{
                  width: 48,
                  height: 48,
                  ml: msg.sender === "user" ? 1 : 0,
                  mr: msg.sender === "bot" ? 1 : 0,
                  flexShrink: 0,
                }}
              />
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: 3,
                  bgcolor: msg.sender === "user" ? "primary.main" : "grey.100",
                  color: msg.sender === "user" ? "white" : "text.primary",
                  maxWidth: "80%",
                  boxShadow: 3,
                  position: "relative",
                  overflowWrap: "break-word",
                  wordWrap: "break-word",
                  wordBreak: "break-word",
                  hyphens: "auto",
                  display: "flex",
                  flexDirection: "column",
                  overflow: "hidden",
                  minWidth: 0, // Important for flex child to prevent overflow
                  paddingBottom: "14px", // Ensure timestamp never overlaps text
                }}
              >
                <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                  <Typography
                    variant="body2"
                    sx={{
                      whiteSpace: "pre-wrap",
                      paddingBottom: "14px",
                      overflowX: "hidden",
                      overflowWrap: "break-word",
                      wordWrap: "break-word",
                      wordBreak: "break-word",
                      flexGrow: 1,
                      minWidth: 0, // Important for flex child to prevent overflow
                      "& a": {
                        color: msg.sender === "user" ? "inherit" : "primary.main",
                        textDecoration: "underline",
                        wordBreak: "break-all",
                        overflowWrap: "break-word",
                      },
                    }}
                  >
                    {parseLinks(
                        msg.sender === "user"
                          ? normalizeRosieName(msg.text)
                          : msg.text
                      )}
                  </Typography>

                  {/* Text-to-speech button for bot messages */}
                  {msg.sender === "bot" &&
                   textToSpeech.isSupported &&
                   showSpeechControls &&
                   isOnChatbotPage &&
                   !disableSpeech &&
                   !isWidget &&
                   msg.text &&
                   msg.text.trim().length > 0 && (
                    <Tooltip
                      title={
                        speakingMessageId === (msg.messageId || msg.timestamp)
                          ? (locale === "ms" ? "Berhenti berbual" : "Stop speaking")
                          : (locale === "ms" ? "Bacakan mesej" : "Speak message")
                      }
                    >
                      <IconButton
                        size="small"
                        onClick={() => handleSpeak(msg.text, msg.messageId || msg.timestamp)}
                        sx={{
                          ml: 1,
                          mt: -0.5,
                          color: speakingMessageId === (msg.messageId || msg.timestamp) ? "error.main" : "text.secondary",
                          opacity: msg.spoken && speakingMessageId !== (msg.messageId || msg.timestamp) ? 0.5 : 1,
                          flexShrink: 0,
                          "&:hover": {
                            backgroundColor: "rgba(0, 0, 0, 0.04)",
                          },
                        }}
                      >
                        {speakingMessageId === (msg.messageId || msg.timestamp) ? <VolumeOff /> : <VolumeUp />}
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>

                <Typography
                  variant="caption"
                  sx={{
                    display: "block",
                    textAlign: "right",
                    color: msg.sender === "user" ? "rgba(255,255,255,0.7)" : "text.secondary",
                    mt: 0.5,
                    fontSize: "0.7rem",
                    position: "absolute",
                    right: 8,
                    bottom: 4,
                  }}
                >
                  {formatMessageTime(msg.timestamp)}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      ))}

      {/* Animated Bot Bubble */}
      {currentBotMessage !== null && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "flex-end",
            mb: 1.5,
            width: "100%",
            maxWidth: "100%",
          }}
        >
          <Avatar
            src="/rosie-bot.png"
            alt="Bot Avatar"
            variant="square"
            sx={{
              width: 48,
              height: 48,
              mr: 1,
              flexShrink: 0
            }}
          />
          <Box
            sx={{
              p: 1.5,
              borderRadius: 3,
              bgcolor: "grey.100",
              color: "text.primary",
              maxWidth: "80%",
              boxShadow: 3,
              position: "relative",
              overflowWrap: "break-word",
              wordWrap: "break-word",
              wordBreak: "break-word",
              hyphens: "auto",
              display: "flex",
              flexDirection: "column",
              overflow: "hidden",
              minWidth: 0, // Important for flex child to prevent overflow
            }}
          >
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
              <Typography
                variant="body2"
                sx={{
                  whiteSpace: "pre-wrap",
                  paddingBottom: "14px",
                  overflowX: "hidden",
                  overflowWrap: "break-word",
                  wordWrap: "break-word",
                  wordBreak: "break-word",
                  flexGrow: 1,
                  minWidth: 0, // Important for flex child to prevent overflow
                  "& a": {
                    color: "primary.main",
                    textDecoration: "underline",
                    wordBreak: "break-all",
                    overflowWrap: "break-word",
                  },
                }}
              >
                {parseLinks(displayedBotText)}
                <span className="blinking-cursor">|</span>
              </Typography>

              {/* No text-to-speech button during typing animation */}
            </Box>

            <Typography
              variant="caption"
              sx={{
                display: "block",
                textAlign: "right",
                color: "text.secondary",
                mt: 0.5,
                fontSize: "0.7rem",
                position: "absolute",
                right: 8,
                bottom: 4,
              }}
            >
              {formatMessageTime(new Date().toISOString())}
            </Typography>
          </Box>
        </Box>
      )}

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
          <Box sx={{ display: "flex", gap: 0.5 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                bgcolor: "grey.500",
                borderRadius: "50%",
                animation: "bounce 1s infinite ease-in-out",
              }}
            />
            <Box
              sx={{
                width: 8,
                height: 8,
                bgcolor: "grey.500",
                borderRadius: "50%",
                animation: "bounce 1s 0.2s infinite ease-in-out",
              }}
            />
            <Box
              sx={{
                width: 8,
                height: 8,
                bgcolor: "grey.500",
                borderRadius: "50%",
                animation: "bounce 1s 0.4s infinite ease-in-out",
              }}
            />
          </Box>
        </Box>
      )}

      {/* Error message */}
      {error && (
        <Box sx={{ mt: 2, textAlign: "center" }}>
          <Typography variant="body2" color="error">
            {error}
          </Typography>
        </Box>
      )}

      {/* Ref for scrolling to bottom */}
      <div ref={messagesEndRef} />
    </Box>
  );
});

MessageDisplay.displayName = "MessageDisplay";
