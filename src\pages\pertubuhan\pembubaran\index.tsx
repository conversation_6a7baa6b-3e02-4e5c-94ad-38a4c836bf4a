import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import LandingP<PERSON>bubaran from "./LandingPembubaran";
import DialogNotice from "./DialogNotice";

const Pembubaran: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useLocation()
  const [isDialogOpen, setIsDialogOpen] = useState(state?.isAdded ? false : true);

  const handleDialogClose = () => navigate('../maklumat');
  const handleDialogConfirm = () => setIsDialogOpen(false);

  return (
    <>
      <LandingPembubaran />

      <DialogNotice
        open={isDialogOpen}
        onClose={handleDialogClose}
        handleConfirm={handleDialogConfirm}
      />
    </>
  );
};

export default Pembubaran;
