import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState = {
  step: 1,
  totalSteps: 2,
  isSuccess: false,
};

export const secretaryBranchReformSlice = createSlice({
  name: "secretaryBranchRef<PERSON>",
  initialState,
  reducers: {
    setStep: (state, action: PayloadAction<number>) => {
      state.step = action.payload;
    },
    handleNext: (state) => {
      if (state.step < state.totalSteps) {
        state.step += 1;
      }
    },

    handlePrev: (state) => {
      if (state.step > 0) {
        state.step -= 1;
      }
    },
    setIsSecretaryReformSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccess = action.payload;
    },
    reset: () => initialState,
  },
});

export const {
  setStep,
  handleNext,
  handlePrev,
  setIsSecretaryReformSuccess,
  reset,
} = secretaryBranchReformSlice.actions;

export default secretaryBranchReformSlice.reducer;
