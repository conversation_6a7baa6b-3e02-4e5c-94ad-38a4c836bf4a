import { Navigate, Outlet, Route, Routes } from "react-router-dom";
import { Container } from "./Container";
import { Authenticated } from "@refinedev/core";
import { CatchAllNavigate } from "@refinedev/react-router-v6";
import AuthenticatedLayout from "./layouts/AuthenticatedLayout";
import { ComingSoon } from "./components/header/comingSoonAuth";
import { chatbotRoute } from "./routes/chatbot";
import { FeedbackRoute } from "./routes/feedback";
import { auth } from "./routes/auth";
import { landingPage } from "./routes/landingPage";
import { blogPosts } from "./routes/blogPosts";
import { categories } from "./routes/categories";
import { pertubuhan } from "./routes/pertubuhan";
import { notifikasi } from "./routes/notifications";
import { profile } from "./routes/profile";
import { useGeranExternalRoutes } from "./routes/geran-external";
import { useGeranInternalRoutes } from "./routes/geran-internal";
import { carian } from "./routes/carian";
import { pewartaan } from "./routes/pewartaan";
import { latihan } from "./routes/latihan";
import { mejaBantuan } from "./routes/pertubuhan/meja-bantuan";
import { pembayaran } from "./routes/pertubuhan/pembayaran";
import { dashboard } from "./routes/dashboard";
import { pengurus_pertubuhan_internal } from "./routes/pengurus-pertubuhan-internal";
import { pengurus_pertubuhan_external } from "./routes/pengurus-pertubuhan-external";
import { usePenguatKuasaanRoutes } from "./routes/penguatkuasaan";
import { takwimAuth } from "./routes/takwim";
import ForbiddenPage from "./pages/forbidden";

export const AppRoutes = () => {
  const geranExternal = useGeranExternalRoutes();
  const geranInternal = useGeranInternalRoutes();
  const penguatKuasaan = usePenguatKuasaanRoutes();

  return (
    <Routes>
      <Route element={<Container />}>
        <Route
          element={
            <Authenticated
              key="authenticated-inner"
              fallback={<CatchAllNavigate to="/login" />}
              v3LegacyAuthProviderCompatible
            >
              <AuthenticatedLayout>
                <Outlet />
              </AuthenticatedLayout>
            </Authenticated>
          }
        >
          <Route index element={<Navigate to="/pertubuhan" />} />
          {blogPosts.routes}
          {categories.routes}
          {pertubuhan.routes}
          {notifikasi.routes}
          {profile.routes}
          {geranExternal.routes}
          {geranInternal.routes}
          {carian.routes}
          {pewartaan.routes}
          {latihan.routes}
          {mejaBantuan.routes}
          {pembayaran.routes}
          {dashboard.routes}
          {pengurus_pertubuhan_internal.routes}
          {pengurus_pertubuhan_external.routes}
          {penguatKuasaan.routes}
          {takwimAuth.routes}
        </Route>
        {chatbotRoute.routes}
        {FeedbackRoute.routes}
        {auth.routes}
        {landingPage.routes}
        <Route path="coming_soon" element={<ComingSoon />} />
        <Route path="forbidden" element={<ForbiddenPage />} />
      </Route>
    </Routes>
  );
};
