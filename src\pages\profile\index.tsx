import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  CircularProgress,
} from "@mui/material";
import { ButtonPrimary } from "../../components/button";
import Input from "../../components/input/Input";
import { useNavigate } from "react-router-dom";
import { API_URL } from "../../api";
import { makeStyles } from "@mui/styles";
import { useCreate } from "@refinedev/core";
import { IdTypes, ListGelaran } from "../../helpers/enums";
import { useQuery } from "@/helpers";

const useStyles = makeStyles((theme) => ({
  input: {
    display: "none",
  },
}));

const Profile: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  //const [gelaran, setGelaran] = useState("");

  const profileData = {
    name: "",
    email: "",
    identificationType: "",
    identificationNo: "",
    citizenshipTitle: "",
  };

  const { mutate: updateProfile, isLoading: isUpdating } = useCreate();

  const [profile, setProfile] = useState(profileData);
  const [roles, setRoles] = useState([]);
  const [profilePicture, setProfilePicture] = useState("");
  const [mobilePhone, setMobilePhone] = useState("");
  // const [housePhone, setHousePhone] = useState("");
  //const [citizenshipTitle, setCitizenshipTitle] = useState("");
  const [titleCode, setTitleCode] = useState("");
  const [idType, setIdType] = useState("");

  const { data: ProfileData, isLoading: isLoadingProfile } = useQuery({
    url: `user/profile/getCurrentProfile`,
    onSuccess: (data) => {
      const profileData = data?.data?.data;
      setProfile(profileData);
      setMobilePhone(profileData.mobilePhone);
      // setHousePhone(profileData.housePhone);
      setTitleCode(profileData.titleCode);
      setProfilePicture(profileData.profilePicture);
    },
  });

  const { data: Roles, isLoading: isLoadingRoles } = useQuery({
    url: `user/profile/getUserInternalDetails`,
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        setRoles(data?.data?.data?.userRole);
      }
    },
  });

  const handleSave = () => {
    const data = {
      // housePhone: housePhone,
      mobilePhone: mobilePhone,
      titleCode: titleCode,
    };

    updateProfile({
      resource: "user/profile/updateProfile",
      values: data,
      meta: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      successNotification: (data) => {
        return {
          message: data?.data?.msg,
          type: "success",
        };
      },
      errorNotification: (data) => {
        return {
          message: data?.data?.msg,
          type: "error",
        };
      },
    });
  };

  const disabledInnerStyle = {
    background: "#ffffff",
    "-webkit-text-fill-color": "#000000 !important",
  };
  const disabledStyle = { "& .Mui-disabled": disabledInnerStyle };

  const handleFileChange = async (e: any) => {
    //let temp = e.target.value;
    //temp = temp.replace("C:\fakepath\"","");
    //let doc = document.getElementById("profile-image-input");

    const multipartFormData = new FormData();
    const files = e.target.files;
    const file = files[0];
    const arrayBuffer = await file.arrayBuffer();
    const blob = new Blob([new Uint8Array(arrayBuffer)], { type: file.type });
    multipartFormData.append("file", file, file.name);
    const response = await fetch(`${API_URL}/user/profile/profilePicture`, {
      method: "post",
      headers: {
        //'Content-Type': 'multipart/form-data',
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: multipartFormData,
    });
    const data = await response.json();
    if (data.status === "SUCCESS") {
      console.log(data);
      setProfilePicture(data.data);
    }
  };

  const classes = useStyles();

  const [translatedList, setTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setTranslatedList(newList);
  }, [t]);

  useEffect(() => {
    if (profile.identificationType && translatedList.length > 0) {
      const foundItem = translatedList.find(
        (i) => i.value == profile.identificationType.toString()
      );
      setIdType(foundItem?.value || "");
    }
  }, [profile.identificationType, translatedList]);
  const getIdTypeLabel = (data: any) => {
    const foundItem = translatedList.find((i) => i.value == data);
    return foundItem ? foundItem.label : "Not Found";
  };

  console.log("roles", roles);
  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: "20px", mb: 3 }}>
            <CardContent
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                height: "100%",
                position: "relative",
              }}
            >
              <input
                accept="image/*"
                className={classes.input}
                id="profile-image-input"
                type="file"
                onChange={handleFileChange}
              />
              <Box
                sx={{
                  width: "50px",
                  height: "50px",
                  borderRadius: "50%",
                  backgroundColor: "#F4F4F4",
                  position: "absolute",
                  zIndex: "10",
                  bottom: "75px",
                  right: "100px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  cursor: "pointer",
                }}
                onClick={() => {
                  const inputElement = document.getElementById(
                    "profile-image-input"
                  );
                  if (inputElement) inputElement.click();
                }}
              >
                <img src="/plus.png" alt="plus" />
              </Box>
              <label htmlFor="profile-image-input">
                <Avatar
                  src={profilePicture}
                  sx={{ width: 200, height: 200, mb: 2, mt: 3 }}
                />
              </label>
              <Typography
                variant="h5"
                component="h2"
                gutterBottom
                sx={{ color: "#666666", fontWeight: "medium" }}
              >
                {profile.name}
              </Typography>
            </CardContent>
          </Card>
          <Card sx={{ borderRadius: "20px" }}>
            <CardContent
              sx={{
                p: 3,
              }}
            >
              <Typography
                gutterBottom
                sx={{ color: "var(--primary-color)", fontWeight: "500" }}
              >
                {t("profile")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography
                    sx={{ color: "#666666" }}
                    variant="body1"
                    gutterBottom
                  >
                    {t("name")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Typography
                    sx={{ color: "#666666" }}
                    variant="body1"
                    gutterBottom
                  >
                    : {profile.name}
                  </Typography>
                </Grid>
              </Grid>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography
                    sx={{ color: "#666666" }}
                    variant="body1"
                    gutterBottom
                  >
                    {t("email")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Typography
                    sx={{ color: "#666666" }}
                    variant="body1"
                    gutterBottom
                  >
                    : {profile.email}
                  </Typography>
                </Grid>
              </Grid>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography
                    sx={{ color: "#666666" }}
                    variant="body1"
                    gutterBottom
                  >
                    {t("noTelephone")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Typography
                    sx={{ color: "#666666" }}
                    variant="body1"
                    gutterBottom
                  >
                    : {mobilePhone}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={8}>
          <Typography
            variant="h6"
            component="h3"
            gutterBottom
            sx={{ color: "#666666", fontWeight: "medium" }}
          >
            {t("userProfile")}
          </Typography>

          <Card sx={{ borderRadius: "20px" }}>
            <CardContent
              sx={{ height: "100%", display: "flex", flexDirection: "column" }}
            >
              <Box
                sx={{
                  p: 3,
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Input
                    //textColor="#666666"
                    value={titleCode}
                    onChange={(e) => setTitleCode(e.target.value)}
                    required
                    label={t("gelaran")}
                    options={ListGelaran}
                    type="select"
                  />
                  <Input
                    //textColor="#666666"
                    label={t("fullName")}
                    placeholder=""
                    disabled={true}
                    customClass={disabledStyle}
                    value={profile.name}
                  />
                  <Input
                    //textColor="#666666"
                    label={t("citizenship")}
                    placeholder=""
                    disabled={true}
                    customClass={disabledStyle}
                    value={
                      idType === "1" || idType === "2" || idType === "3"
                        ? "Warganegara"
                        : "Bukan Warganegara"
                    }
                  />
                  <Input
                    //textColor="#666666"
                    label={t("identification")}
                    placeholder=""
                    disabled={true}
                    customClass={disabledStyle}
                    // options={translatedList}
                    // type="select"
                    value={getIdTypeLabel(
                      profile.identificationType.toString()
                    )}
                  />
                  <Input
                    //textColor="#666666"
                    label={t("idNumberPlaceholder")}
                    placeholder=""
                    disabled={true}
                    customClass={disabledStyle}
                    value={profile.identificationNo}
                  />
                  {roles?.length > 0 ? (
                    <Grid container spacing={2} alignItems={"flex-start"}>
                      <Grid item xs={12} sm={4}>
                        <Typography
                          variant="body1"
                          sx={{
                            color: "#666666",
                            fontWeight: "400 !important",
                            fontSize: "14px",
                          }}
                        >
                          {t("peranan")}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={8}>
                        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                          {roles.map((role, index) => (
                            <Chip key={index} label={role} />
                          ))}
                        </Box>
                      </Grid>
                    </Grid>
                  ) : isLoadingRoles ? (
                    <CircularProgress />
                  ) : null}
                </Grid>
              </Box>

              <Box
                sx={{
                  mt: 3,
                  p: 3,
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Typography
                    gutterBottom
                    sx={{
                      color: "var(--primary-color)",
                      fontWeight: "500",
                      mb: 3,
                    }}
                  >
                    {t("contactInfo")}
                  </Typography>

                  <Input
                    //textColor="#666666"
                    required
                    label={t("noTelefonBimbit")}
                    value={mobilePhone}
                    onChange={(e) => setMobilePhone(e.target.value)}
                    placeholder=""
                  />
                  {/* <Input
                    //textColor="#666666"
                    label={t("homeNumber")}
                    value={housePhone}
                    onChange={(e) => setHousePhone(e.target.value)}
                    placeholder=""
                  /> */}
                  <Input
                    //textColor="#666666"
                    label={t("email")}
                    placeholder=""
                    disabled={true}
                    customClass={disabledStyle}
                    value={profile.email}
                  />
                </Grid>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  mt: "auto",
                  pt: 2,
                }}
              >
                <ButtonPrimary onClick={handleSave}>
                  {t("update")}
                </ButtonPrimary>
              </Box>
            </CardContent>
          </Card>

          <Card
            sx={{ borderRadius: "20px", mt: 3 }}
            onClick={() => navigate("password")}
          >
            <CardContent
              sx={{
                py: 2,
                pl: 6,
                cursor: "pointer",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                variant="h6"
                component="h3"
                gutterBottom
                sx={{
                  color: "#666666",
                  fontWeight: "medium",
                  m: 0,
                  textAlign: "center",
                  flexGrow: 1,
                }}
              >
                {t("changePassword")}
              </Typography>
              <img src="/arrow-right.png" alt="arrow" width={50} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Profile;
