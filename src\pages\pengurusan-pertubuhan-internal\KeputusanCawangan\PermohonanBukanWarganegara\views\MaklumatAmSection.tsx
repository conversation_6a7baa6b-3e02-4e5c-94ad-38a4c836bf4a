import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Typography, Box, Grid } from "@mui/material";
import {
  CitizenshipStatus,
  DocumentUploadType,
  DurationOptions,
  getLocalStorage,
  IdTypes,
  OrganisationPositions,
  useQuery,
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { useEffect, useState } from "react";
import Input from "@/components/input/Input";

interface FormValues {
  name: any;
  citizenshipStatus: any;
  identificationType: any;
  identificationNo: any;
  applicantCountryCode: any;
  gender: any;
  visaNo: number | any;
  visaExpirationDate: any;
  permitNo: any;
  permitExpirationDate: any;
  tujuanDMalaysia: any;
  tempohDMalaysia: any;
  stayDurationDigit: number | any;
  stayDurationUnit: any;
  designationCode: any;
  summary: any;
  societyNo: string | null;
  societyName: null;
}
const MaklumatAmSection = ({ info, societyInfo }: any) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const [formValues, setFormValues] = useState<FormValues>({
    name: "",
    societyNo: null,
    societyName: null,
    citizenshipStatus: 2,
    identificationType: "",
    identificationNo: null,
    visaNo: "",
    visaExpirationDate: "",
    permitNo: "",
    permitExpirationDate: "",
    tujuanDMalaysia: "",
    tempohDMalaysia: "",
    stayDurationDigit: null,
    stayDurationUnit: "",
    designationCode: "",
    summary: "",
    applicantCountryCode: null,
    gender: null,
  });

  const { id: memberId } = useParams();

  const [durationOptionsTranslated, setDurationOptionsTranslated] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newDurationList = DurationOptions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setDurationOptionsTranslated(newDurationList);
  }, [t]);

  useEffect(() => {
    if (info) {
      setFormValues(info);
    }
  }, [info]);

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  const addressList = getLocalStorage("address_list", null);
  useEffect(() => {
    const newOList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setIdTypeTranslatedList(newOList);
  }, [t]);
  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  console.log("info", info);

  return (
    <Box
      sx={{
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("pertubuhan")}
        </Typography>

        <Input
          value={
            societyInfo?.societyNo
              ? societyInfo?.societyNo
              : societyInfo?.applicationNo
          }
          name="societyNo"
          disabled
          required
          label={t("organizationNumber2")}
        />
        <Input
          value={societyInfo?.societyName}
          name="societyName"
          disabled
          required
          label={t("organization_name")}
        />
      </Box>

      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("nonCitizenSecretaryInformation")}
        </Typography>
        <Input
          disabled
          value={formValues.name ? formValues.name : ""}
          name="name"
          label={t("fullNameCapitalizedOnlyFirstLetter")}
        />
        <Input
          disabled
          value={
            formValues.citizenshipStatus
              ? Number(formValues.citizenshipStatus)
              : 2
          }
          name="citizenshipStatus"
          label={t("citizenship")}
          type="select"
          options={CitizenshipStatus.map((item) => ({
            ...item,
            label: t(item.label),
          }))}
          required
        />

        <Input
          disabled
          value={
            formValues.identificationType ? formValues.identificationType : ""
          }
          name="identificationType"
          required
          label={t("idTypeCapitalizedOnlyFirstLetter")}
          options={idTypeTranslatedList}
          type="select"
        />
        <Input
          disabled
          value={formValues.identificationNo ? formValues.identificationNo : ""}
          name="identificationNo"
          required
          label={t("idNumberCapitalizedOnlyFirstLetter")}
        />
        <Input
          disabled
          value={Number(formValues.applicantCountryCode) ?? ""}
          name="applicantCountryCode"
          required
          label={t("originCountry")}
          options={CountryData}
          type="select"
        />
        <Input
          disabled
          value={formValues.visaNo ? formValues.visaNo : ""}
          name="visaNo"
          label={t("nomborVisa")}
        />
        <Input
          disabled
          value={
            formValues.visaExpirationDate ? formValues.visaExpirationDate : ""
          }
          name="visaExpirationDate"
          type="date"
          label={t("visaExpiryDate")}
        />
        <Input
          disabled
          value={formValues.permitNo ? formValues.permitNo : ""}
          name="permitNo"
          label={t("nomborPermit")}
        />
        <Input
          disabled
          value={
            formValues.permitExpirationDate
              ? formValues.permitExpirationDate
              : ""
          }
          name="permitExpirationDate"
          type="date"
          label={t("permitExpiryDate")}
        />
        <Input
          value={formValues?.tujuanDMalaysia ?? ""}
          name="tujuanDMalaysia"
          required
          label={t("tujuanDiMalaysia")}
          disabled
        />
        {Number(formValues.identificationType) !== 4 && (
          <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="body1"
                sx={{
                  color: "#666666",
                  fontWeight: "400 !important",
                  fontSize: "14px",
                }}
              >
                {t("tempohDiMalaysia")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationDigit ?? ""}
                    name="stayDurationDigit"
                    required
                    type="text"
                    inputMode="numeric"
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d*$/.test(value)) {
                        setFormValues({
                          ...formValues,
                          stayDurationDigit: parseInt(value) || null,
                        });
                      }
                    }}
                    disabled
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationUnit ?? ""}
                    name="stayDurationUnit"
                    required
                    type="select"
                    options={durationOptionsTranslated}
                    disabled
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        )}
        <Input
          disabled
          value={Number(formValues?.designationCode) ?? ""}
          name="designationCode"
          required
          options={positionsTranslatedList}
          label={t("position")}
          type="select"
        />
        <Input
          disabled
          value={formValues.summary ? formValues.summary : ""}
          name="summary"
          multiline
          rows={4}
          required
          label={t("importanceOfPosition2")}
        />
      </Box>
      {info?.branchId ? (
        <FileUploader
          title="ajkEligibilityCheck"
          type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
          societyId={Number(societyInfo?.id)}
          branchId={info?.branchId ? Number(info?.branchId) : null}
          icNo={formValues?.identificationNo}
          validTypes={[
            "text/plain",
            "application/rtf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "application/vnd.oasis.opendocument.text",
            "application/pdf",
          ]}
          disabled={true}
        />
      ) : null}
    </Box>
  );
};

export default MaklumatAmSection;
