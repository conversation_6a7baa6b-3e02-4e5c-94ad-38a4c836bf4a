import { Box, IconButton, Typography } from "@mui/material";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import dayjs from "dayjs";
import 'dayjs/locale/ms';

interface MonthYearPaginationProps {
  selectedDate: dayjs.Dayjs;
  onDateChange: (newDate: dayjs.Dayjs) => void;
}

const MonthYearPagination: React.FC<MonthYearPaginationProps> = ({
  selectedDate,
  onDateChange,
}) => {
  const handlePrevMonth = () => {
    // Ensure we maintain start of month when changing months
    const newDate = selectedDate.subtract(1, 'month').startOf('month');
    onDateChange(newDate);
  };

  const handleNextMonth = () => {
    // Ensure we maintain start of month when changing months
    const newDate = selectedDate.add(1, 'month').startOf('month');
    onDateChange(newDate);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        bgcolor: '#fff',
        borderRadius: '20px',
        padding: '4px 12px',
        border: '1px solid #E0E0E0',
        width: 'fit-content'
      }}
    >
      <Typography sx={{
        fontSize: '14px',
        fontWeight: 500,
        color: '#333',
        textTransform: 'capitalize',
        minWidth: '80px'
      }}>
        {selectedDate.locale('ms').format('MMM YYYY')}
      </Typography>
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        <IconButton
          size="small"
          onClick={handlePrevMonth}
          sx={{
            bgcolor: '#F5F5F5',
            '&:hover': { bgcolor: '#EEEEEE' },
            padding: '4px'
          }}
        >
          <ChevronLeft sx={{ fontSize: 20 }} />
        </IconButton>
        <IconButton
          size="small"
          onClick={handleNextMonth}
          sx={{
            bgcolor: '#F5F5F5',
            '&:hover': { bgcolor: '#EEEEEE' },
            padding: '4px'
          }}
        >
          <ChevronRight sx={{ fontSize: 20 }} />
        </IconButton>
      </Box>
    </Box>
  );
};

export default MonthYearPagination;

