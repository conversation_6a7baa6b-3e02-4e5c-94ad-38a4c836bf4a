import React, {useState} from "react";
import DialogContent from "@mui/material/DialogContent";
import {Box, IconButton, SxProps, Theme, Typography} from "@mui/material";
import {Close as CloseIcon} from "@mui/icons-material";
import {ButtonOutline, ButtonPrimary} from "@/components";
import Dialog from "@mui/material/Dialog";
import {useTranslation} from "react-i18next";

interface ConfirmationDialogProps {
  text: string,
  handleSave: (e: React.MouseEvent<HTMLButtonElement>) => void,
  labelStyle: SxProps<Theme>,
  openModal: boolean,
  setOpenModal: (value: boolean) => void,
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({text, handleSave, labelStyle, openModal=false, setOpenModal}) => {

  const {t, i18n} = useTranslation();
  const handleCloseModal = () => {
    setOpenModal(false);
  };

  return (<>
    <Dialog
      open={openModal}
      onClose={handleCloseModal}
      maxWidth="xs"
      //fullWidth
      //sx={{p:0}}
    >
      <DialogContent dividers={false} sx={{position: "relative", p: 5,}}>
        <IconButton
          aria-label="close"
          onClick={handleCloseModal}
          sx={{
            position: "absolute",
            right: 8,
            top: 1,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon/>
        </IconButton>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            gap: 2,
          }}
        > <Typography sx={labelStyle}>
          {text}
        </Typography>
          <ButtonPrimary
            variant="contained"
            sx={{
              width: "auto",
            }}
            onClick={handleSave}
            //disabled={true}
          >
            {t("yes")}
          </ButtonPrimary>
          <ButtonOutline
            sx={{
              bgcolor: "white",
              "&:hover": {bgcolor: "white"},
              width: "auto",
            }}
            onClick={handleCloseModal}
          >
            {t("no")}
          </ButtonOutline>
        </Box>
      </DialogContent>
    </Dialog>
  </>);
}

export default ConfirmationDialog;
