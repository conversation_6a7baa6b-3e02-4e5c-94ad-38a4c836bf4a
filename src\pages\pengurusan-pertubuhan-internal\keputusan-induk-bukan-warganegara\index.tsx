import { SyntheticEvent, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import {
  Box,
  CircularProgress,
  Grid,
  Typography,
  useMediaQuery,
  Theme,
} from "@mui/material";
import MaklumatPermohonanSection from "./views/MaklumatPermohonanSection";
import AccordionComp from "../View/Accordion";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import { FieldValues, useForm } from "react-hook-form";
import {
  ApplicationStatusList,
  filterEmptyValuesOnObject,
  ROApprovalType,
} from "@/helpers";
import useMutation from "@/helpers/hooks/useMutation";
import SelectFieldController from "@/components/input/select/SelectFieldController";
import TextFieldController from "@/components/input/TextFieldController";
import { AppDispatch } from "@/redux/store";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import useQuery from "@/helpers/hooks/useQuery";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";
import { API_URL } from "@/api";
import { useBack, useCustom, useCustomMutation } from "@refinedev/core";
import { DialogConfirmation } from "@/components";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

const subTitleStyle = {
  color: "#0CA6A6",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

function KeputusanIndukBukanWargaNegara() {
  const { t } = useTranslation();
  const { id } = useParams();
  const back = useBack();

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const dispatch: AppDispatch = useDispatch();
  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  const {
    data: committeeResponse,
    isLoading: CommitteeDataLoading,
    refetch: fetchCommitteeData,

  } = useQuery({
    url: `society/nonCitizenCommittee/${id}`,
    onSuccess: (data) => {
      const committeeData = data?.data?.data ?? null
      setValueRoAction("ro", committeeData?.ro ? parseInt(committeeData.ro) : null);
      setValueRoAction("noteRo", committeeData?.noteRo ?? null);
    }
  });

  const committeeData = committeeResponse?.data?.data ?? null

  useEffect(() => {
    if (committeeData?.societyId) {
      dispatch(
        fetchSocietyByIdData({
          id: committeeData?.societyId,
        })
      );
    }
  }, [committeeData]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const sectionItems = [
    {
      subTitle: t("maklumatPermohonan"),
      component: (
        <MaklumatPermohonanSection
          societyDataById={societyDataById}
          NonCitizenCommitteeData={committeeData}
        />
      ),
    },
  ];

  const decisionOptions = [
    {
      value: 3,
      label: t("lulus"),
    },
    {
      value: 4,
      label: t("tolak"),
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const payload = getValues();
    if (committeeData?.societyId) {
      payload.societyId = committeeData?.societyId;
      payload.roApprovalType = ROApprovalType.SOCIETY_NON_CITIZEN.code;
      payload.societyNonCitizenId = id;
      payload.rejectReason =
        payload.applicationStatusCode === 4 ? payload?.note : null;
      payload.note = payload.applicationStatusCode === 3 ? payload?.note : null;
      const filterPayload = filterEmptyValuesOnObject(payload);
      updateDecision(filterPayload);
    }
  };

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        applicationStatusCode: "",
        rejectReason: "",
        note: "",
        roApprovalType: "SOCIETY_NON_CITIZEN",
      },
    });

  const { fetch: updateDecision, isLoading: isLoadingDecisionUpdate } =
    useMutation({
      url: "society/roDecision/updateApprovalStatus",
      method: "patch",
      onSuccess: (data) => {
        setIsSuccess(true);
      },
    });

  const onSubmit = () => setIsDialogOpen(true);

  const methodsRoAction = useForm<{ ro: number | null; noteRo: string }>();

  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
    formState: formStateUpdateRO,
  } = methodsRoAction;

  const { data: roListData, isLoading: isRoListLoading } = useCustom({
    url: `${API_URL}/society/user/getRoList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: committeeData?.societyId,
      },
    },
    queryOptions: {
      enabled: !!committeeData?.societyId,
    },
  });

  const roList = roListData?.data?.data ?? [];

  const roListOptions =
    roList.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const { mutateAsync: updateRo, isLoading: isLoadingUpdateRO } = useCustomMutation();

  const onSubmitRoAction = async (data: FieldValues) => {
    await updateRo(
      {
        url: `${API_URL}/society/roDecision/updateRo`,
        method: "patch",
        values: {
          roId: data.ro,
          societyNonCitizenId: id,
          noteRo: data.noteRo,
          amendmentId: data.amendmentId,
          roApprovalType: ROApprovalType.SOCIETY_NON_CITIZEN.code,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: data?.data.status === "ERROR" ? "error" : "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
    await fetchCommitteeData();
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
              display: "grid",
              gap: 1,
            }}
          >
            <Typography sx={{ fontWeight: "400!important" }}>
              {societyDataById?.societyName}
            </Typography>
            <Typography sx={{ fontWeight: "400!important" }}>
              {societyDataById?.societyNo}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          {sectionItems.map((item, index) => {
            return (
              <AccordionComp
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </AccordionComp>
            );
          })}
        </Box>

        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form onSubmit={handleSubmitRoAction(onSubmitRoAction)}>
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("responsibleRO")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="ro"
                    required
                    // @ts-expect-error
                    control={controlRoAction}
                    options={roListOptions}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    // @ts-expect-error
                    control={controlRoAction}
                    name="noteRo"
                    multiline
                    defaultValue={getValuesRoAction("noteRo")}
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrimary
                variant="contained"
                type="submit"
                sx={{ width: isMobile ? "100%" : "auto", mb: 3 }}
                disabled={!formStateUpdateRO.isValid || formStateUpdateRO.isSubmitting || isLoadingUpdateRO}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </form>

          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography variant="h6" component="h2" sx={subTitleStyle}>
                  {t("keputusan")}
                </Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("statusPermohonan")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <SelectFieldController
                    control={control}
                    name="applicationStatusCode"
                    options={decisionOptions}
                    disabled={isFormDisabled}
                    sx={{
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    name="note"
                    multiline
                    limitWord={100}
                    disabled={isFormDisabled}
                    required={watch("applicationStatusCode") === 36}
                    sx={{
                      minHeight: "92px",
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: "34px",
                gap: "10px",
              }}
            >
              <ButtonOutline
                onClick={back}
              >
                {t("back")}
              </ButtonOutline>

              <ButtonPrimary
                type="submit"
                disabled={isFormDisabled || CommitteeDataLoading}
              >
                {CommitteeDataLoading ? (
                  <CircularProgress
                    size="0.5rem"
                    sx={{
                      display: "block",
                    }}
                  />
                ) : (
                  t("hantar")
                )}
              </ButtonPrimary>
            </Box>
          </form>
        </Box>
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isLoadingDecisionUpdate}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        decisionLabel={
          ApplicationStatusList.find(
            (item) => item.id === getValues().applicationStatusCode
          )?.value || "-"
        }
      />
    </>
  );
}

export default KeputusanIndukBukanWargaNegara;
