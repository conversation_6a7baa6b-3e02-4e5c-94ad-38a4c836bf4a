import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";

import {
  Typography,
  Box,
  Grid,
  useMediaQuery,
  Theme,
  Select,
  MenuItem,
  TextField,
  FormControl,
} from "@mui/material";
import FormFieldRow from "../../../../components/form-field-row";
import Label from "../../../../components/label/Label";
import DisabledTextField from "../../../../components/input/DisabledTextField";
import { ButtonPrimary } from "../../../../components/button";
import { useEffect, useState } from "react";
import { capitalizeWords, ROApprovalType } from "@/helpers";
// import TextArea from "@/components/input/TextArea";
import {
  Controller,
  FormProvider,
  useForm,
  FieldValues,
} from "react-hook-form";
import { <PERSON><PERSON><PERSON><PERSON>ontroller, TextFieldController } from "@/components";

const MaklumatAmSection = () => {
  const { t } = useTranslation();
  const { amendmentId, id } = useParams();
  const [amendmentInfo, setAmendmentInfo] = useState<any>(null);
  const decodedId = atob(id || "");
  const [societyId, setSocietId] = useState("");
  const [societyName, setSocietyName] = useState("");
  const [catergory, setCatergory] = useState([]);
  const methods = useForm<FieldValues>({
    defaultValues: {
      organizationGoals: "",
      organizationLevel: "Negeri",
      organizationCategory: "",
      organizationSubCategory: "",
      hasBranch: 0,
      constitutionType: "",
      amendmentId: amendmentId,
      ro: "",
      noteRo: "",
    },
  });
  const { handleSubmit, control, setValue, getValues, watch } = methods;

  const { data } = useCustom({
    url: `${API_URL}/society/${decodedId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, shortName } = responseData?.data?.data;
        setSocietyName(societyName);
        setValue("namaRingkasPertubuhan", shortName);
      },
    },
  });

  const { data: amendmentData } = useCustom({
    url: `${API_URL}/society/amendment/getAmendmentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        id: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId,
      onSuccess(data) {
        const amendmenData = data?.data?.data?.data?.[0];
        setAmendmentInfo(amendmenData);
      },
    },
  });

  useEffect(() => {
    setSocietId(amendmentInfo?.societyId);
    setValue("organizationGoals", amendmentInfo?.goal);
    setValue("organizationLevel", amendmentInfo?.societyLevel);
    setValue(
      "organizationCategory",
      amendmentInfo?.categoryCodeJppm
        ? Number(amendmentInfo?.categoryCodeJppm)
        : "-"
    );
    setValue(
      "organizationSubCategory",
      amendmentInfo?.subCategoryCode
        ? Number(amendmentInfo?.subCategoryCode)
        : "-"
    );
    setValue("hasBranch", amendmentInfo?.hasBranch == "0" ? 0 : 1);
    setValue("constitutionType", amendmentInfo?.constitutionType);
    setValue("ro", Number(amendmentInfo?.ro));
    setValue("noteRo", amendmentInfo?.noteRo);
  }, [amendmentInfo]);

  const { data: categoryData, isLoading: isCategoryLoading } = useCustom({
    url: `${API_URL}/society/admin/category/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess(data) {
        const cat = data?.data?.data || [];
        setCatergory(cat);
      },
    },
  });

  const mainCategoriesData = catergory.filter((cat: any) => cat.level === 1);
  const subCategoriesData = catergory.filter((cat: any) => cat.level === 2);

  const mainCategories = mainCategoriesData
    .filter((cat: any) => cat.level === 1)
    .map((cat: any) => ({
      value: cat.id,
      label: cat.categoryNameEn,
    }));
  const subCategories = subCategoriesData
    .filter((cat: any) => cat.level === 2)
    .map((cat: any) => ({
      value: cat.id,
      label: cat.categoryNameEn,
    }));

  return (
    <Box
      sx={{
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <FormProvider {...methods}>
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #DADADA",
            borderRadius: "10px",
            marginBottom: 1,
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("organizationInfo")}
          </Typography>
          <FormFieldRow
            label={<Label text={t("namaPertubuhan")} />}
            value={<DisabledTextField value={societyName} />}
          />
          <FormFieldRow
            label={<Label text={t("namaRingkasPertubuhan")} />}
            value={
              <DisabledTextField
                value={
                  getValues("namaRingkasPertubuhan")
                    ? getValues("namaRingkasPertubuhan")
                    : "-"
                }
              />
            }
          />
          <FormFieldRow
            label={
              <>
                <Label text={t("organizationLevel")} />
                <span style={{ color: "red" }}>*</span>
              </>
            }
            value={<DisabledTextField value={getValues("organizationLevel")} />}
          />
          <FormFieldRow
            label={
              <>
                <Label text={t("organizationCategory")} />
                <span style={{ color: "red" }}>*</span>
              </>
            }
            value={
              <SelectFieldController
                name="organizationCategory"
                control={control}
                options={mainCategories}
                disabled={true}
              />
            }
          />
          <FormFieldRow
            label={
              <>
                <Label text={t("organizationSubCategory2")} />
                <span style={{ color: "red" }}>*</span>
              </>
            }
            value={
              <SelectFieldController
                name="organizationSubCategory"
                control={control}
                options={subCategories}
                disabled={true}
              />
            }
          />

          <FormFieldRow
            label={
              <>
                <Label text={t("branchOrganization")} />
                <span style={{ color: "red" }}>*</span>
              </>
            }
            value={
              <DisabledTextField
                value={getValues("hasBranch") == 1 ? t("yes") : t("no")}
              />
            }
          />
          <FormFieldRow
            label={<Label text={capitalizeWords(t("constitutionType"))} />}
            value={<DisabledTextField value={getValues("constitutionType")} />}
          />
        </Box>
      </FormProvider>
    </Box>
  );
};

export default MaklumatAmSection;
