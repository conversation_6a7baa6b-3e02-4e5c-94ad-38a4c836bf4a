import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const TrainingAddIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg
      ref={ref}
      width="24"
      height="24"
      viewBox="-1 -1 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{color, ...sx}}
      {...props}
    >
      <path
        d="M0 1.375C0 1.01033 0.144866 0.660591 0.402728 0.402728C0.660591 0.144866 1.01033 0 1.375 0H7.625C7.98967 0 8.33941 0.144866 8.59727 0.402728C8.85513 0.660591 9 1.01033 9 1.375V4.511C8.76617 4.36128 8.51412 4.24214 8.25 4.1565V1.375C8.25 1.03 7.97 0.75 7.625 0.75H1.375C1.03 0.75 0.75 1.03 0.75 1.375V7.625C0.75 7.97 1.03 8.25 1.375 8.25H4.1565C4.243 8.517 4.3625 8.7685 4.511 9H1.375C1.01033 9 0.660591 8.85513 0.402728 8.59727C0.144866 8.33941 0 7.98967 0 7.625V1.375ZM10 7.25C10 6.52065 9.71027 5.82118 9.19454 5.30546C8.67882 4.78973 7.97935 4.5 7.25 4.5C6.52065 4.5 5.82118 4.78973 5.30546 5.30546C4.78973 5.82118 4.5 6.52065 4.5 7.25C4.5 7.97935 4.78973 8.67882 5.30546 9.19454C5.82118 9.71027 6.52065 10 7.25 10C7.97935 10 8.67882 9.71027 9.19454 9.19454C9.71027 8.67882 10 7.97935 10 7.25ZM7.5 7.5L7.5005 8.7515C7.5005 8.8178 7.47416 8.88139 7.42728 8.92828C7.38039 8.97516 7.3168 9.0015 7.2505 9.0015C7.1842 9.0015 7.12061 8.97516 7.07372 8.92828C7.02684 8.88139 7.0005 8.8178 7.0005 8.7515V7.5H5.748C5.6817 7.5 5.61811 7.47366 5.57122 7.42678C5.52434 7.37989 5.498 7.3163 5.498 7.25C5.498 7.1837 5.52434 7.12011 5.57122 7.07322C5.61811 7.02634 5.6817 7 5.748 7H7V5.75C7 5.6837 7.02634 5.62011 7.07322 5.57322C7.12011 5.52634 7.1837 5.5 7.25 5.5C7.3163 5.5 7.37989 5.52634 7.42678 5.57322C7.47366 5.62011 7.5 5.6837 7.5 5.75V7H8.7485C8.8148 7 8.87839 7.02634 8.92528 7.07322C8.97216 7.12011 8.9985 7.1837 8.9985 7.25C8.9985 7.3163 8.97216 7.37989 8.92528 7.42678C8.87839 7.47366 8.8148 7.5 8.7485 7.5H7.5Z"
        fill="white"/>
    </svg>
  );
});
