import React from "react";
import { useTranslation } from "react-i18next";
import { useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, Typography } from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  FileUploadController,
} from "@/components";

const Form = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const { control } = useFormContext();

  return (
    <Box className={classes.sectionBox} mb={2}>
      <Typography className="title" mb={2}>
        Maklumat Perbankan
      </Typography>

      <FormFieldRow
        label={<Label text="Nama bank persatuan" required />}
        value={<TextFieldController control={control} name="test" />}
      />

      <FormFieldRow
        label={<Label text="Nombour akun bank" required />}
        value={<TextFieldController control={control} name="test" />}
      />

      <FormFieldRow
        label={<Label text="Penyata bank" required />}
        value={<FileUploadController control={control} name="test" />}
      />
    </Box>
  );
};

const MaklumatPerbankanForm = React.memo(Form);
export default MaklumatPerbankanForm;
