import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useSenaraiContext } from "../../SenaraiContext";
import { usePembubaranContext } from "../PembubaranProvider";

import { Box, Typography } from "@mui/material";
import MeetingSummary from "../components/MeetingSummary";
import FinancialForm from "../components/FinancialForm";
import AssetFormComp from "../components/asset-form";
import FeedbackForm from "../components/FeedbackForm";

const FeedbackPembubaranComp: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { pembubaranStep } = useSenaraiContext();
  const { isCanAccessFeedback, isFetchingLiquidationDetail } =
    usePembubaranContext();

  const renderForm = () => {
    switch (pembubaranStep) {
      case 0:
        return <MeetingSummary />;
      case 1:
        return <FinancialForm />;
      case 2:
        return <AssetFormComp />;
      case 3:
        return <FeedbackForm />;
      default:
        return null;
    }
  };

  useEffect(() => {
    if (isFetchingLiquidationDetail || isCanAccessFeedback === null) return;

    if (isCanAccessFeedback === false) navigate("../..");
  }, [isCanAccessFeedback, isFetchingLiquidationDetail]);

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "18px 16px",
          borderRadius: "14px",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: "10px",
            padding: "24px 35px",
            border: "0.5px solid #DADADA",
          }}
        >
          <Typography
            fontSize="12px"
            lineHeight="18px"
            color="#666666"
            fontWeight="400 !important"
          >
            <Typography
              component="span"
              color="#FF0000"
              fontWeight="500 !important"
              fontSize="12px"
              lineHeight="18px"
            >
              {t("peringatan")}
            </Typography>{" "}
            {t("liquidationWarning.main")} <br /> <br />
            {t("liquidationWarning.point1")}
            <br /> <br />
            {t("liquidationWarning.point2")}
          </Typography>
        </Box>
      </Box>

      {renderForm()}
    </>
  );
};

export default FeedbackPembubaranComp;
