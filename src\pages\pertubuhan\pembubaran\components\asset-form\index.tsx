import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSenaraiContext } from "../../../SenaraiContext";
import { usePembubaranContext } from "../../PembubaranProvider";
import useMutation from "../../../../../helpers/hooks/useMutation";

import { Box } from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import AssetList from "./AssetList";
import AssetForm from "./AssetForm";
import DialogConfirm from "../DialogConfirm";
import { useNavigate } from "react-router-dom";

const AssetFormComp: React.FC = () => {
  const { t, i18n } = useTranslation();
  const {
    handleNextPembubaran: handleNext,
    handleBackPembubaran: handleBack,
    setPembubaranSuccess,
    isPembubaranSuccess,
  } = useSenaraiContext();
  const { form, isCanAccessFeedback, isViewOnly } = usePembubaranContext();
  const { getValues } = form;
  const formValues = getValues();
  const navigate = useNavigate();

  const isMyLanguage = i18n.language === "my";

  const { fetch: updateLiquidation, isLoading: isLoadingUpdate } = useMutation({
    url: "society/liquidate/update",
    method: "put",
    onSuccess: () => {
      setPembubaranSuccess(true);
      navigate("../../pembubaran", { state: { isAdded: true } });
    },
    msgSuccess: isMyLanguage ? "Berjaya Dikemaskini" : "Successfully Updated",
  });

  const [activeComp, setActiveComp] = useState<"list" | "form">("list");
  const [activeAsset, setActiveAsset] = useState<number | null>(null);
  const [isDialogOpen, setDialogOpen] = useState(false);

  const renderComponent = () => {
    switch (activeComp) {
      case "list":
        return (
          <AssetList
            setActiveComp={setActiveComp}
            setActiveAsset={setActiveAsset}
          />
        );
      case "form":
        return (
          <AssetForm
            setActiveComp={setActiveComp}
            activeAsset={activeAsset}
            setActiveAsset={setActiveAsset}
          />
        );
      default:
        return null;
    }
  };

  const handleDialogOpen = () => setDialogOpen(true);
  const handleDialogClose = () => setDialogOpen(false);
  const handleFormSubmit = () => {
    const payload = { ...formValues };
    updateLiquidation({
      ...payload,
      applicationStatusCode: 2,
    });
  };

  return (
    <>
      {renderComponent()}

      {activeComp === "list" && (
        <Box
          sx={{
            borderRadius: "10px",
            padding: "19px 53px",
            border: "0.5px solid #DADADA",
            backgroundColor: "#fff",
          }}
        >
          <Box
            sx={{ display: "flex", gap: "11px", justifyContent: "flex-end" }}
          >
            <ButtonOutline onClick={handleBack} disabled={isPembubaranSuccess}>
              {t("back")}
            </ButtonOutline>

            {isCanAccessFeedback ? (
              <ButtonPrimary onClick={handleNext}>{t("next")}</ButtonPrimary>
            ) : !isViewOnly ? (
              <ButtonPrimary onClick={handleDialogOpen}>
                {t("hantar")}
              </ButtonPrimary>
            ) : null}
          </Box>
        </Box>
      )}

      <DialogConfirm
        open={isDialogOpen}
        isSuccess={isPembubaranSuccess}
        isMutating={isLoadingUpdate}
        onClose={handleDialogClose}
        handleFormSubmit={handleFormSubmit}
      />
    </>
  );
};

export default AssetFormComp;
