import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { capitalizeWords } from "@/helpers";
interface FasalContentEnamBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentEnamBelasCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [tempohJawatan, setTempohJawatan] = useState(t("setahun"));
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [kekerapan, setKekerapan] = useState("");
  const [tempohPelucutan, setTempohPelucutan] = useState("");
  const [tempohPelucutanWaktu, setTempohPelucutanWaktu] = useState(t("day"));
  const [pengerusi, setPengerusi] = useState(t("chairman"));
  const [jumlahPengerusi, setJumlahPengerusi] = useState<any>(1);
  const [timbalan, setTimbalan] = useState(t("timbalanPengerusi"));
  const [jumlahTimbalan, setJumlahTimbalan] = useState<any>("");
  const [naib, setNaib] = useState(t("naibPengerusi"));
  const [jumlahNaib, setJumlahNaib] = useState<any>("");
  const [setiaUsaha, setSetiaUsaha] = useState(t("branchSecretary"));
  const [jumlahSetiaUsaha, setJumlahSetiaUsaha] = useState<any>(1);
  const [penolongSetiaUsaha, setPenolongSetiaUsaha] = useState(
    t("asistantSecretary")
  );
  const [jumlahPenolongSetiaUsaha, setJumlahPenolongSetiaUsaha] =
    useState<any>("");
  const [bendahari, setBendahari] = useState(t("treasurer"));
  const [jumlahBendahari, setJumlahBendahari] = useState<any>(1);
  const [penolongBendahari, setPenolongBendahari] = useState(
    t("asistantTreasurer")
  );
  const [jumlahPenolongBendahari, setJumlahPenolongBendahari] =
    useState<any>("");
  const [ahliBiasa, setAhliBiasa] = useState(t("ordinaryCommitteeMember"));
  const [jumlahAhliBiasa, setJumlahAhliBiasa] = useState<any>("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState("");
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  /*const clauseContent = `
1. Jawatankuasa Cawangan seperti berikut yang dinamakan pegawai-pegawai Pertubuhan hendaklah dipilih ${
    tempohJawatan || "<<Tempoh Pelantikan Jawatankuasa>>"
  } sekali dalam Mesyuarat Agung ${
    pemilihanAjk || "<<Jenis Mesyuarat>>"
  } Cawangan:

i. ${jumlahPengerusi || "<<Bilangan Pengerusi>>"} orang ${
    pengerusi || "<<Jawatan Pengerusi>>"
  }

ii. ${jumlahTimbalan || "<<Bilangan Timbalan Pengerusi>>"} orang ${
    timbalan || "<<Jawatan Timbalan Pengerusi>>"
  }

iii. ${jumlahNaib || "<<Bilangan Naib Pengerusi>>"} orang ${
    naib || "<<Jawatan Naib Pengerusi>>"
  } (pilihan)

iv. ${jumlahSetiaUsaha || "<<Bilangan Setiausaha>>"} orang ${
    setiaUsaha || "<<Jawatan Setiausaha>>"
  }

v. ${jumlahPenolongSetiaUsaha || "<<Bilangan Penolong Setiausaha>>"} orang ${
    penolongSetiaUsaha || "<<Jawatan Penolong Setiausaha>>"
  }

vi. ${jumlahBendahari || "<<Bilangan Bendahari>>"} orang ${
    bendahari || "<<Jawatan Bendahari>>"
  }

vii. ${jumlahPenolongBendahari || "<<Bilangan Penolong Bendahari>>"} orang ${
    penolongBendahari || "<<Jawatan Penolong Bendahari>>"
  } (pilihan)

viii. ${jumlahAhliBiasa || "<<Bilangan Ahli Jawatankuasa Biasa>>"} orang ${
    ahliBiasa || "<<Jawatan Ahli Jawatankuasa Biasa>>"
  }

2. Semua ahli yang berkhidmat dalam Jawatankuasa Cawangan dan tiap-tiap pegawai yang menjalankan tugas-tugas eksekutif dalam cawangan hendaklah warganegara Malaysia atau bukan warganegara yang mendapat kebenaran Pendaftar Pertubuhan.

3. Nama-nama untuk jawatan-jawatan yang di atas hendaklah dicadang serta disokong dan pemilihan dijalankan dengan cara mengundi oleh ahli-ahli dalam Mesyuarat Agung ${
    pemilihanAjk || "<<Jenis Mesyuarat>>"
  } Cawangan. Semua pegawai boleh dilantik semula ${
    tempohJawatan || "<<Tempoh Pelantikan Jawatankuasa>>"
  } tahun sekali.

4. Fungsi Jawatankuasa Cawangan ialah mengelola dan mengaturkan kerja-kerja harian cawangan mengikut Perlembagaan Pertubuhan serta arahan-arahan Jawatankuasa Induk dan Mesyuarat Agung.

5. Jawatankuasa Cawangan hendaklah bermesyuarat sekurang-kurangnya ${
    kekerapan || "<<Kekerapan Mesyuarat>>"
  } kali setahun. Notis bagi tiap-tiap mesyuarat hendaklah diberikan kepada ahli Jawatankuasa Cawangan sekurang-kurangnya tujuh (7) hari sebelum tarikh mesyuarat.

6. Setiausaha Cawangan hendaklah menghantar kepada Setiausaha Agung satu (1) salinan minit tiap-tiap mesyuarat Jawatankuasa Cawangan dalam tempoh 30 hari setelah selesai mesyuarat tersebut.
`;*/

  //const clause16 = localStorage.getItem("clause16");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause16);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }

      const fieldMappings: Record<string, (value: string) => void> = {
        "Tempoh Pelantikan Jawatankuasa": setTempohJawatan,
        "Jenis Mesyuarat Agung": setPemilihanAjk,
        "Kekerapan Mesyuarat Jawatankuasa": setKekerapan,
        "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa":
          setTempohPelucutan,
        "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa":
          setTempohPelucutanWaktu,
        Pengerusi: setPengerusi,
        "Bilangan Pengerusi": setJumlahPengerusi,
        "Timbalan Pengerusi": setTimbalan,
        "Bilangan Timbalan Pengerusi": setJumlahTimbalan,
        "Naib Pengerusi": setNaib,
        "Bilangan Naib Pengerusi": setJumlahNaib,
        Setiausaha: setSetiaUsaha,
        "Bilangan Setiausaha": setJumlahSetiaUsaha,
        "Penolong Setiausaha": setPenolongSetiaUsaha,
        "Bilangan Penolong Setiausaha": setJumlahPenolongSetiaUsaha,
        Bendahari: setBendahari,
        "Bilangan Bendahari": setJumlahBendahari,
        "Penolong Bendahari": setPenolongBendahari,
        "Bilangan Penolong Bendahari": setJumlahPenolongBendahari,
        "Ahli Jawatankuasa Biasa": setAhliBiasa,
        "Bilangan Ahli Jawatankuasa Biasa": setJumlahAhliBiasa,
      };

      if (clause.constitutionValues) {
        clause.constitutionValues.forEach((item: any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }

      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan mesyuarat>>/gi,
    `<b>${kekerapan || "<<kekerapan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh membela diri pelucutan>>/gi,
    `<b>${tempohPelucutan || "<<tempoh membela diri pelucutan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<notis panggilan mesyuarat>>/gi, `<b>${notisPanggilanMesyuarat || '<<notis panggilan mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Pengerusi Cawangan>>/gi,
    `<b>${pengerusi || "<<jawatan Pengerusi Cawangan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Timbalan Pengerusi Cawangan>>/gi,
    `<b>${timbalan || "<<jawatan Timbalan Pengerusi Cawangan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan Timbalan Pengerusi Cawangan>>/gi,
    `<b>${jumlahTimbalan || "<<bilangan Timbalan Pengerusi Cawangan>>"}</b>`
  );

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Bendahari Cawangan>>/gi,
    `<b>${bendahari || "<<jawatan Bendahari Cawangan>>"}</b>`
  );

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Setiausaha Cawangan>>/gi,
    `<b>${setiaUsaha || "<<jawatan Setiausaha Cawangan>>"}</b>`
  );
  //
  if (Number(jumlahNaib) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Naib Pengerusi Cawangan>>/gi,
      `<b>${naib || "<<jawatan Naib Pengerusi Cawangan>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Naib Pengerusi Cawangan>>/gi,
      `<b>${
        Number(jumlahNaib) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahNaib} orang` || "<<bilangan Naib Pengerusi Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*ii\.\s*<<bilangan Naib Pengerusi Cawangan>> <<jawatan Naib Pengerusi Cawangan>>\s*[\r\n]?/gim,
      "    "
    );
  }
  //
  if (Number(jumlahPenolongSetiaUsaha) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Setiausaha Cawangan>>/gi,
      `<b>${
        penolongSetiaUsaha || "<<jawatan Penolong Setiausaha Cawangan>>"
      }</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. SU Cawangan>>/gi,
      `<b>${
        Number(jumlahPenolongSetiaUsaha) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongSetiaUsaha} orang` ||
            "<<bilangan pen. SU Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*iv\.\s*<<bilangan pen. SU Cawangan>> <<jawatan Penolong Setiausaha Cawangan>>\s*[\r\n]?/gim,
      "    "
    );
  }

  //
  if (Number(jumlahPenolongBendahari) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Bendahari Cawangan>>/gi,
      `<b>${penolongBendahari || "<<jawatan Penolong Bendahari Cawangan>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. bendahari Cawangan>>/gi,
      `<b>${
        Number(jumlahPenolongBendahari) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongBendahari} orang` ||
            "<<bilangan pen. bendahari Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vi\.\s*<<bilangan pen. bendahari Cawangan>>\s*(?:orang\s+)?<<jawatan Penolong Bendahari Cawangan>>\s*[\r\n]?/gim,
      "    "
    );
  }
  //

  if (Number(jumlahAhliBiasa) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Ahli Jawatankuasa Biasa Cawangan>>/gi,
      `<b>${ahliBiasa || "<<jawatan Ahli Jawatankuasa Biasa Cawangan>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan ajk Cawangan>>/gi,
      `<b>${
        Number(jumlahAhliBiasa) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahAhliBiasa} orang` || "<<bilangan ajk Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*<<bilangan ajk Cawangan>> <<jawatan Ahli Jawatankuasa Biasa Cawangan>>\s*[\r\n]?/gim,
      ""
    );
  }

  clauseContent = clauseContent.replace("<<jawatan telah diubahsuai>>", "");
  clauseContent = clauseContent.replace(
    "<<penambahan jawatan diubahsuai>>",
    ``
  );

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  clauseContent = renumberRomanList(clauseContent);

  function renumberRomanList(content: any) {
    const romanNumerals = [
      "i",
      "ii",
      "iii",
      "iv",
      "v",
      "vi",
      "vii",
      "viii",
      "ix",
      "x",
    ];
    let index = 0;

    return content.replace(
      /^(\s*)(?:i{1,3}|iv|v?i{0,3}|ix|x)\.\s/gim,
      (_: any, indent: any) => {
        const roman = romanNumerals[index++] || `${index}.`;
        return `${indent}${roman}. `;
      }
    );
  }

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!tempohJawatan) {
      errors.tempohJawatan = t("fieldRequired");
    }
    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }
    if (!kekerapan) {
      errors.kekerapan = t("fieldRequired");
    }
    // if (!tempohPelucutan) {
    //   errors.tempohPelucutan = t("fieldRequired");
    // }
    // if (!tempohPelucutanWaktu) {
    //   errors.tempohPelucutanWaktu = t("fieldRequired");
    // }
    //
    if (!jumlahPengerusi) {
      errors.jumlahPengerusi = t("fieldRequired");
    }
    if (!pengerusi) {
      errors.pengerusi = t("fieldRequired");
    }
    // if (!jumlahTimbalan) {
    //   errors.jumlahTimbalan = t("fieldRequired");
    // }
    if (!timbalan) {
      errors.timbalan = t("fieldRequired");
    }
    // if (!jumlahNaib) {
    //   errors.jumlahNaib = t("fieldRequired");
    // }
    // if (!naib) {
    //   errors.naib = t("fieldRequired");
    // }
    if (!jumlahSetiaUsaha) {
      errors.jumlahSetiaUsaha = t("fieldRequired");
    }
    if (!setiaUsaha) {
      errors.setiaUsaha = t("fieldRequired");
    }
    // if (!jumlahPenolongSetiaUsaha) {
    //   errors.jumlahPenolongSetiaUsaha = t("fieldRequired");
    // }
    // if (!penolongSetiaUsaha) {
    //   errors.penolongSetiaUsaha = t("fieldRequired");
    // }
    if (!jumlahBendahari) {
      errors.jumlahBendahari = t("fieldRequired");
    }
    if (!bendahari) {
      errors.bendahari = t("fieldRequired");
    }
    // if (!jumlahPenolongBendahari) {
    //   errors.jumlahPenolongBendahari = t("fieldRequired");
    // }
    // if (!penolongBendahari) {
    //   errors.penolongBendahari = t("fieldRequired");
    // // }
    // if (!jumlahAhliBiasa) {
    //   errors.jumlahAhliBiasa = t("fieldRequired");
    // }
    // if (!ahliBiasa) {
    //   errors.ahliBiasa = t("fieldRequired");
    // }

    return errors;
  };

  const total = [
    jumlahPengerusi,
    jumlahTimbalan,
    jumlahNaib,
    jumlahSetiaUsaha,
    jumlahPenolongSetiaUsaha,
    jumlahBendahari,
    jumlahPenolongBendahari,
    jumlahAhliBiasa,
  ].reduce((sum, val) => sum + parseInt(String(val || 0)), 0);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("positionOfAuthority")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("electionPeriod")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl error={!!formErrors.tempohJawatan} fullWidth required>
              <Select
                size="small"
                value={tempohJawatan}
                displayEmpty
                disabled
                onChange={(e) => {
                  setTempohJawatan(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohJawatan: "",
                  }));
                }}
              >
                <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                <MenuItem value={t("duaTahun")}>{t("duaTahun")}</MenuItem>
              </Select>
            </FormControl>
            {formErrors.tempohJawatan && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.tempohJawatan}
              </FormHelperText>
            )}
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("jenisMesyuaratAgung")}</Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl error={!!formErrors.pemilihanAjk} fullWidth required>
              <Select
                size="small"
                value={pemilihanAjk}
                disabled
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
              </Select>
            </FormControl>
            {formErrors.pemilihanAjk && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.pemilihanAjk}
              </FormHelperText>
            )}
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("meetingFrequency")}</Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl error={!!formErrors.kekerapan} fullWidth required>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="4"
                fullWidth
                disabled
                required
                value={kekerapan}
                error={!!formErrors.kekerapan}
                // helperText={formErrors.kekerapan}
                onChange={(e) => {
                  setKekerapan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kekerapan: "",
                  }));
                }}
                InputProps={{
                  endAdornment: (
                    <Typography sx={{ ...labelStyle, mt: 1 }}>
                      {t("times")}
                    </Typography>
                  ),
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </FormControl>
            {formErrors.kekerapan && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.kekerapan}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("ahliJawatanKuasa")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("NumberofPositions")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("JobTitle")}
            </Typography>
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              disabled
              sx={{ background: "#E8E9E8" }}
              required
              value={jumlahPengerusi}
              onChange={(e) => {
                setJumlahPengerusi(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahPengerusi: "",
                }));
              }}
              error={!!formErrors.jumlahPengerusi}
              helperText={formErrors.jumlahPengerusi}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl error={!!formErrors.pengerusi} fullWidth required>
              <Select
                size="small"
                value={pengerusi}
                displayEmpty
                onChange={(e) => {
                  setPengerusi(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pengerusi: "",
                  }));
                }}
              >
                <MenuItem value={t("branchChairman")}>
                  {t("branchChairman")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("presidenCawangan")}>
                  {t("presidenCawangan")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("thepresidentBranch")}>
                  {t("thepresidentBranch")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                {/* <MenuItem value={t("pengarahCawangan")}>
                  {t("pengarahCawangan")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem> */}
              </Select>
            </FormControl>
            {formErrors.pengerusi && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.pengerusi}
              </FormHelperText>
            )}
          </Grid>
          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahNaib}
              onChange={(e) => {
                setJumlahNaib(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahNaib: "",
                }));
              }}
              error={!!formErrors.jumlahNaib}
              helperText={formErrors.jumlahNaib}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl error={!!formErrors.naib} fullWidth required>
              <Select
                size="small"
                value={naib}
                displayEmpty
                onChange={(e) => {
                  setNaib(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    naib: "",
                  }));
                }}
              >
                <MenuItem value={t("timbalanPengerusiCawangan")}>
                  {t("timbalanPengerusiCawangan")}
                </MenuItem>
                <MenuItem value={t("timbalanPresidenCawangan")}>
                  {t("timbalanPresidenCawangan")}
                </MenuItem>
                <MenuItem
                  value={`${t("timbalanyYangDipertua")} ${t("fasalCawangan")}`}
                >
                  {t("timbalanyYangDipertua")} {t("fasalCawangan")}
                </MenuItem>
              </Select>
            </FormControl>
            {formErrors.naib && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.naib}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              disabled
              sx={{ background: "#E8E9E8" }}
              value={jumlahSetiaUsaha}
              onChange={(e) => {
                setJumlahSetiaUsaha(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahSetiaUsaha: "",
                }));
              }}
              error={!!formErrors.jumlahSetiaUsaha}
              helperText={formErrors.jumlahSetiaUsaha}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl error={!!formErrors.setiaUsaha} fullWidth required>
              <Select
                size="small"
                value={setiaUsaha}
                displayEmpty
                onChange={(e) => {
                  setSetiaUsaha(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    setiaUsaha: "",
                  }));
                }}
              >
                {/* <MenuItem value={t("secretary")}>
                  {t("secretary")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem> */}
                <MenuItem value={t("branchSecretary")}>
                  {t("branchSecretary")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
              </Select>
            </FormControl>
            {formErrors.setiaUsaha && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.setiaUsaha}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahPenolongSetiaUsaha}
              onChange={(e) => {
                setJumlahPenolongSetiaUsaha(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahPenolongSetiaUsaha: "",
                }));
              }}
              error={!!formErrors.jumlahPenolongSetiaUsaha}
              helperText={formErrors.jumlahPenolongSetiaUsaha}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl
              error={!!formErrors.penolongSetiaUsaha}
              fullWidth
              required
            >
              <Select
                size="small"
                value={penolongSetiaUsaha}
                displayEmpty
                onChange={(e) => {
                  setPenolongSetiaUsaha(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    penolongSetiaUsaha: "",
                  }));
                }}
              >
                {/* <MenuItem value={t("asistantSecretary")}>
                  {t("asistantSecretary")}
                </MenuItem> */}
                <MenuItem value={t("penolongSetiausahaCawangan")}>
                  {t("penolongSetiausahaCawangan")}
                </MenuItem>
              </Select>
            </FormControl>
            {formErrors.penolongSetiaUsaha && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.penolongSetiaUsaha}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              disabled
              sx={{ background: "#E8E9E8" }}
              value={jumlahBendahari}
              onChange={(e) => {
                setJumlahBendahari(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahBendahari: "",
                }));
              }}
              error={!!formErrors.jumlahBendahari}
              helperText={formErrors.jumlahBendahari}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl error={!!formErrors.bendahari} fullWidth required>
              <Select
                size="small"
                value={bendahari}
                displayEmpty
                onChange={(e) => {
                  setBendahari(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bendahari: "",
                  }));
                }}
              >
                {/* <MenuItem value={t("treasurer")}>
                  {t("treasurer")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem> */}
                <MenuItem value={t("bendahariCawangan")}>
                  {t("bendahariCawangan")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                {/* <MenuItem value={t("chiefTreasurer")}>
                  {t("chiefTreasurer")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem> */}
                {/* <MenuItem value={t("honoraryTreasurer")}>
                  {t("honoraryTreasurer")}{" "}{t("fasalCawangan")} 
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem> */}
              </Select>
            </FormControl>
            {formErrors.bendahari && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.bendahari}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahPenolongBendahari}
              onChange={(e) => {
                setJumlahPenolongBendahari(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahPenolongBendahari: "",
                }));
              }}
              error={!!formErrors.jumlahPenolongBendahari}
              helperText={formErrors.jumlahPenolongBendahari}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl
              error={!!formErrors.penolongBendahari}
              fullWidth
              required
            >
              <Select
                size="small"
                value={penolongBendahari}
                displayEmpty
                onChange={(e) => {
                  setPenolongBendahari(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    penolongBendahari: "",
                  }));
                }}
              >
                {/* <MenuItem value={t("asistantTreasurer")}>
                  {t("asistantTreasurer")}
                </MenuItem> */}
                <MenuItem value={t("penolongBendahariCawangan")}>
                  {t("penolongBendahariCawangan")}
                </MenuItem>
                {/* <MenuItem value={t("chiefAssistantTreasurer")}>
                  {t("chiefAssistantTreasurer")}
                </MenuItem> */}
                {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                  {t("honoraryAssistantTreasurer")} {t("fasalCawangan")} 
                </MenuItem> */}
              </Select>
            </FormControl>
            {formErrors.penolongBendahari && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.penolongBendahari}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahAhliBiasa}
              onChange={(e) => {
                setJumlahAhliBiasa(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahAhliBiasa: "",
                }));
              }}
              error={!!formErrors.jumlahAhliBiasa}
              helperText={formErrors.jumlahAhliBiasa}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl error={!!formErrors.ahliBiasa} fullWidth required>
              <Select
                size="small"
                value={ahliBiasa}
                displayEmpty
                onChange={(e) => {
                  setAhliBiasa(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    ahliBiasa: "",
                  }));
                }}
              >
                <MenuItem value={t("ordinaryCommitteeMember1")}>
                  {t("ordinaryCommitteeMember1")}
                </MenuItem>
              </Select>
            </FormControl>
            {formErrors.ahliBiasa && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.ahliBiasa}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={8}>
            <Typography
              sx={{
                fontWeight: "500 !important",
                color: "#666666",
                border: "1px solid #DADADA",
                borderRadius: "5px",
                py: 1,
                display: "flex",
                justifyContent: "center",
              }}
            >
              {total}{" "}
              {/* {parseInt(jumlahPengerusi || 0) +
                parseInt(jumlahTimbalan || 0) +
                parseInt(jumlahNaib || 0) +
                parseInt(jumlahSetiaUsaha || 0) +
                parseInt(jumlahPenolongSetiaUsaha || 0) +
                parseInt(jumlahBendahari || 0) +
                parseInt(jumlahPenolongBendahari || 0) +
                parseInt(jumlahAhliBiasa || 0)}{" "} */}
              Bilangan Ahli Jawatankuasa
            </Typography>
          </Grid>
          <Grid item />
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohJawatan,
                  titleName: "Tempoh Pelantikan Jawatankuasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: pemilihanAjk,
                  titleName: "Jenis Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kekerapan,
                  titleName: "Kekerapan Mesyuarat Jawatankuasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohPelucutan,
                  titleName:
                    "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohPelucutanWaktu,
                  titleName:
                    "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: pengerusi,
                  titleName: "Pengerusi",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahPengerusi,
                  titleName: "Bilangan Pengerusi",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: timbalan,
                  titleName: "Timbalan Pengerusi",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahTimbalan,
                  titleName: "Bilangan Timbalan Pengerusi",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: naib,
                  titleName: "Naib Pengerusi",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahNaib,
                  titleName: "Bilangan Naib Pengerusi",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: setiaUsaha,
                  titleName: "Setiausaha",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahSetiaUsaha,
                  titleName: "Bilangan Setiausaha",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: penolongSetiaUsaha,
                  titleName: "Penolong Setiausaha",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahPenolongSetiaUsaha,
                  titleName: "Bilangan Penolong Setiausaha",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bendahari,
                  titleName: "Bendahari",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahBendahari,
                  titleName: "Bilangan Bendahari",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: penolongBendahari,
                  titleName: "Penolong Bendahari",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahPenolongBendahari,
                  titleName: "Bilangan Penolong Bendahari",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: ahliBiasa,
                  titleName: "Ahli Jawatankuasa Biasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahAhliBiasa,
                  titleName: "Bilangan Ahli Jawatankuasa Biasa",
                },
              ],
              clause: "clause16",
              clauseCount: 16,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentEnamBelasCawangan;
