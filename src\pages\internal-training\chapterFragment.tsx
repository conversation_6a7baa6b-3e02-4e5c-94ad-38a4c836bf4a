import React, {useEffect, useState} from "react";
import {Box, Grid, SxProps, TextField, Theme, Typography} from "@mui/material";
import DurationComponent from "@/pages/internal-training/durationComponent";
import {useTranslation} from "react-i18next";
import {TrainingChapter} from "@/pages/internal-training/createStepTwo";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
import {DocumentUploadType} from "@/helpers";

interface ChapterFragmentProps {
  no: number,
  headerStyle: SxProps<Theme>,
  labelStyle: SxProps<Theme>,
  borderStyle: SxProps<Theme>,
  data: TrainingChapter,
  handleDataChange: (i: number, data: TrainingChapter) => void
}


const ChapterFragment: React.FC<ChapterFragmentProps> = ({
                                                           no,
                                                           headerStyle,
                                                           labelStyle,
                                                           borderStyle,
                                                           data,
  handleDataChange,
                                                         }) => {

  const {t, i18n} = useTranslation();

  const [hour, setHour] = useState(1)
  const [minute, setMinute] = useState(0)

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState<TrainingChapter>(data);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  useEffect(() => {
    setFormData((prevState) => ({
      ...prevState,
      duration: hour * 60 + minute,
    }));
  }, [hour, minute]);

  useEffect(() => {
    if(Object.keys(data).length > 0){
      if (data.duration) {
        setHour(Math.floor(data.duration / 60))
        setMinute(data.duration % 60)
      }
      const temp = {
        trainingCourseId: data.trainingCourseId,
        id: data.id,
        title: data.title || "",
        description: data.description || "",
        duration: data.duration || 60,
        youtubeLink: data.youtubeLink || "",
        media: null,
      }
      setFormData(temp);
    }

  }, [data]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const {name, value} = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({...prev, [name]: ""}));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (!file) return;

    setSelectedFile(file);
    setFormData((prevState) => ({
      ...prevState,
      media: file,
    }));
  };

  const {data: trainingDocData, isLoading: isTrainingDocLoading} = useCustom({
    url: `${API_URL}/society/document/documentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        trainingId: data.trainingCourseId,
        trainingMaterialId: data.id,
        type: DocumentUploadType.TRAINING_MATERIAL
      },
    },
    queryOptions: {
      enabled: data.id != 0,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    handleDataChange(no-1, formData);
  },[formData])
  return (<>
    <Box
      sx={borderStyle}
    >
      <Typography
        sx={headerStyle}
      >
        Nama Bab
      </Typography>
      <Grid container spacing={2} sx={{mt: 1}}>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {`${t("chapterTitle")} ${no}`} <span style={{color: "red"}}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size={"small"}
            fullWidth
            required
            name="title"
            value={formData.title}
            error={!!formErrors.title}
            onChange={handleInputChange}
          />
        </Grid>
        <DurationComponent setHour={setHour} setMinute={setMinute}
                           labelStyle={labelStyle} hour={hour} minute={minute}/>
      </Grid>
    </Box>
    <Box
      sx={borderStyle}
    >
      <Typography
        sx={headerStyle}
      >
        Medium Bahan Pelajaran
      </Typography>
      <Grid container spacing={2} sx={{mt: 1}}>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("chapterMedia")} <span style={{color: "red"}}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Box
            sx={{
              border: "2px solid #DADADA",
              borderRadius: "8px",
              p: 2,
              gap: 2,
              textAlign: "center",
              cursor: "pointer",
              height: "200px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
            }}
            onClick={() => {
              const element = document.getElementById(`media${no}`);
              if (element) {
                element.click();
              }
            }}
          >
            {selectedFile || uploadedFiles?.length ? (
              <Typography sx={{color: "#147C7C", mb: 1}}>
                {selectedFile
                  ? selectedFile.name
                  : uploadedFiles[0]?.name}
              </Typography>
            ) : (
              <>
                <Box
                  sx={{
                    width: 50,
                    aspectRatio: "1/1",
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                    textAlign: "center",
                    borderRadius: 20,
                    mb: 2,
                    // p: 5,
                    bgcolor: "#F2F4F7",
                  }}
                >
                  <img
                    width={30}
                    src={"/uploadFileIcon.svg"}
                    alt={"view"}
                  />
                </Box>

                <Typography
                  sx={{
                    color: "var(--primary-color)",
                    fontWeight: "500",
                    fontSize: "14px",
                  }}
                >
                  {t("muatNaik")}
                </Typography>
                <Typography
                  sx={{
                    color: "#667085",
                    fontWeight: "400",
                    fontSize: "12px",
                  }}
                >
                  {`SVG, PNG, JPG or GIF (max. 800x400px)`}
                </Typography>
              </>
            )}
            <input
              id={`media${no}`}
              type="file"
              hidden
              onChange={handleFileChange}
              accept=".svg,.png,.jpg,.jpeg,.gif"
            />
          </Box>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("youtubeLink")} <span style={{color: "red"}}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size={"small"}
            fullWidth
            required
            name="youtubeLink"
            value={formData.youtubeLink}
            error={!!formErrors.youtubeLink}
            onChange={handleInputChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("chapterDescription")} <span style={{color: "red"}}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size={"small"}
            fullWidth
            required
            multiline
            rows={4}
            name="description"
            value={formData.description}
            error={!!formErrors.description}
            onChange={handleInputChange}
          />
        </Grid>
      </Grid>
    </Box>
  </>);
}

export default ChapterFragment;
