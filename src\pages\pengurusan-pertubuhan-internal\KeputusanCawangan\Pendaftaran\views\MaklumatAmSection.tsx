import { useEffect, useState } from "react";
import { use<PERSON>arams } from "react-router-dom";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TileLayer } from "react-leaflet";
import { LatLng } from "leaflet";

import {
  Typography,
  Box,
  Grid,
  useMediaQuery,
  Theme,
  CircularProgress,
} from "@mui/material";
import useMutation from "../../../../../helpers/hooks/useMutation";
import DisabledTextField from "../../../../../components/input/DisabledTextField";
import { ButtonPrimary } from "../../../../../components/button";
import SelectFieldController from "../../../../../components/input/select/SelectFieldController";
import TextFieldController from "../../../../../components/input/TextFieldController";
import { useKeputusanCawanganPendaftaranContext } from "../KeputusanCawanganPendaftaranProvider";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { MALAYSIA } from "../../../../../helpers/enums";
import Input from "../../../../../components/input/Input";
import { useCustomMutation } from "@refinedev/core";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { fetchBranchByIdData } from "@/redux/APIcalls/branchByIdThunks";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

interface ListItem {
  value: any;
  label: any;
}

const MaklumatAmSection = () => {
  const { branchDataById } = useKeputusanCawanganPendaftaranContext();
  const { t } = useTranslation();
  const { id } = useParams();
  const { liquidationDetailData, societyDataById } =
    useKeputusanCawanganPendaftaranContext();

  const dispatch: AppDispatch = useDispatch();
  const organizationCoords = new LatLng(2.745564, 101.707021);
  const [roList, setRoList] = useState([]);
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { mutate: updateRo, isLoading } = useCustomMutation();

  // @ts-ignore
  const addressDataRedux = useSelector((state) => state?.addressData?.data);
  const StateList = addressDataRedux
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ value: item.id, label: item.name }));

  const [districtList, setDistrictList] = useState<ListItem[]>([]);

  const { control, handleSubmit, setValue, setError } = useForm<FieldValues>({
    defaultValues: {
      societyId: societyDataById?.id,
      roId: "",
      noteRo: "",
      branchId: id,
      roApprovalType: "BRANCH_REGISTRATION",
    },
  });

  useEffect(() => {
    if (branchDataById?.stateCode) {
      setDistrictList(
        addressDataRedux
          .filter((item: any) => item.pid === Number(branchDataById?.stateCode))
          .map((item: any) => ({ value: item.id, label: item.name }))
      );
      // console.log("branchDataById?.ro", branchDataById?.ro);
      // if (branchDataById?.ro) {
      //   setValue("roId", Number(branchDataById?.ro));
      // }
      // setValue("noteRo", branchDataById?.noteRo);
    }
  }, [branchDataById]);

  // const fetchRoList = async () => {
  //   try {
  //     const response = await fetch(
  //       `${API_URL}/society/user/getRoList?societyId=${societyDataById?.id}&branchId=${branchDataById?.id}`,
  //       {
  //         headers: {
  //           portal: localStorage.getItem("portal") || "",
  //           authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
  //           "Content-Type": "application/json",
  //         },
  //       }
  //     );
  //     if (!response.ok) {
  //       throw new Error(`Error: ${response.status} ${response.statusText}`);
  //     }
  //     const data = await response.json();
  //     setRoList(data?.data);
  //   } catch (error) {
  //     console.error("Failed to fetch data:", error);
  //     return null;
  //   }
  // };

  // useEffect(() => {
  //   if (societyDataById?.id && branchDataById?.id) {
  //     fetchRoList();
  //   }
  // }, [societyDataById, branchDataById]);

  // const roListOptions =
  //   roList?.map((item: any) => ({
  //     value: item.id,
  //     label: item.name,
  //   })) || [];

  const countWords = (str: string) =>
    str.trim().split(/\s+/).filter(Boolean).length;

  const onSubmit = (data: FieldValues) => {
    console.log("data", data);
    if (data.noteRo && countWords(data.noteRo) > 100) {
      setError("noteRo", {
        type: "manual",
        message: t("inputValidationErrorStringExceedsLimitWord", {
          label: "Note",
          value: 100,
        }),
      });
      return;
    }
    updateRo({
      url: `${API_URL}/society/roDecision/updateRo`,
      method: "patch",
      values: data,
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      successNotification: (data) => {
        dispatch(fetchBranchByIdData({ id: branchDataById?.id }));
        return {
          message: data?.data?.msg,
          type: "success",
        };
      },
      errorNotification: (data) => {
        return {
          message: data?.response?.data?.msg,
          type: "error",
        };
      },
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          pl: 2,
          p: 3,
          border: "0.5px solid #DADADA",
          borderRadius: "10px",
          width: "100%",
          marginBottom: "0.5rem",
        }}
      >
        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>{t("organizationName")}</Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField value={societyDataById?.societyName ?? "-"} />
          </Grid>
        </Grid>

        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>
                {t("namaRingkasPertubuhan")}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField value={societyDataById?.shortName ?? "-"} />
          </Grid>
        </Grid>

        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>{t("branchNameDetails")}</Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField value={branchDataById?.name ?? "-"} />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={{
          borderRadius: "10px",
          width: "100%",
        }}
      >
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #DADADA",
            borderRadius: "10px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("alamatTempatUrusan")}
          </Typography>

          {/* <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("placeOfBusinessLocationMap")}
                </Typography>
                <span style={{ color: "red" }}>*</span>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <MapContainer
                center={organizationCoords}
                scrollWheelZoom={false}
                zoom={13}
                style={{
                  height: "10rem",
                  width: "100%",
                  borderRadius: "5px",
                }}
              >
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution={`&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors`}
                />
                <Marker position={organizationCoords} />
              </MapContainer>
            </Grid>
          </Grid> */}

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("alamatTempatUrusan")}
                </Typography>
                <span style={{ color: "red" }}>*</span>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={branchDataById?.address ?? "-"} />
            </Grid>
          </Grid>
          <Input
            value={Number(branchDataById?.stateCode)}
            name="state"
            disabled
            options={StateList}
            type="select"
            label={t("negeri")}
          />
          <Input
            value={Number(branchDataById?.districtCode)}
            name="district"
            disabled
            label={t("daerah")}
            type="select"
            options={districtList}
          />

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("city")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={branchDataById?.city ?? "-"} />
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("postcode")}</Typography>
                <span style={{ color: "red" }}>*</span>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={branchDataById?.postcode ?? "-"} />
            </Grid>
          </Grid>
        </Box>
        {/* 
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("ROAction")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("responsibleRO")}
                <span style={{ marginLeft: "0.5rem", color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <SelectFieldController
                name="roId"
                control={control}
                options={roListOptions}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("remarks")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldController
                control={control}
                name="noteRo"
                multiline
                sx={{
                  minHeight: "126px",
                }}
                sxInput={{
                  minHeight: "126px",
                }}
              />
            </Grid>
          </Grid>

          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary type="submit" disabled={isLoading} sx={{}}>
              {isLoading ? <CircularProgress size={24} /> : t("update")}
            </ButtonPrimary>
          </Grid>
        </Box> */}
      </Box>
    </form>
  );
};

export default MaklumatAmSection;
