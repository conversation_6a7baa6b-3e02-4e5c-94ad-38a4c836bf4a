import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const DeleteIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg
      ref={ref}
      width="24"
      height="24"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{color, ...sx}}
      {...props}
    >
      <path
        d="M5.75 0.25C7.27499 0.25 8.73753 0.855802 9.81586 1.93414C10.8942 3.01247 11.5 4.47501 11.5 6C11.5 7.52499 10.8942 8.98753 9.81586 10.0659C8.73753 11.1442 7.27499 11.75 5.75 11.75C4.22501 11.75 2.76247 11.1442 1.68414 10.0659C0.605802 8.98753 0 7.52499 0 6C0 4.47501 0.605802 3.01247 1.68414 1.93414C2.76247 0.855802 4.22501 0.25 5.75 0.25ZM2.875 6C2.875 6.13071 2.92693 6.25607 3.01935 6.3485C3.11178 6.44093 3.23714 6.49286 3.36786 6.49286H8.13214C8.26286 6.49286 8.38822 6.44093 8.48065 6.3485C8.57307 6.25607 8.625 6.13071 8.625 6C8.625 5.86929 8.57307 5.74393 8.48065 5.6515C8.38822 5.55907 8.26286 5.50714 8.13214 5.50714H3.36786C3.30313 5.50714 3.23905 5.51989 3.17925 5.54466C3.11945 5.56943 3.06512 5.60573 3.01935 5.6515C2.97359 5.69726 2.93729 5.7516 2.91252 5.81139C2.88775 5.87119 2.875 5.93528 2.875 6Z"
        fill="#FF0000"/>
    </svg>
  );
});
