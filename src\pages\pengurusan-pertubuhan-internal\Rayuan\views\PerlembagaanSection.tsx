import { Box, Grid, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useSelector } from "react-redux";
import FasalContent from "../../../../components/FasalContent";
import { ConstitutionType } from "../../../../helpers/enums";
import { useCustom } from "@refinedev/core";
import { LoadingOverlay } from "../../../../components/loading";

export const PerlembagaanSection: React.FC = () => {
  const { t } = useTranslation();
  const { id, type } = useParams();
  const decodedId = atob(id ?? "");
  const decodedType = atob(type ?? "");

  const [fasal, setFasal] = useState([]);

  const [constitution, setConstitution] = useState<any>({});

  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  useEffect(() => {
    if (societyDataById) {
      setConstitution(
        societyDataById?.constitutionType || ConstitutionType.IndukNGO[1]
      );
      fetchConstitution({
        constitutionType:
          societyDataById?.constitutionType || ConstitutionType.IndukNGO[1],
      });
    }
  }, [societyDataById]);

  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });
  // ${decodedId}
  const allConstitutions = constitutionData?.data?.data || [];

  const [loading, setLoading] = useState(false);
  const fetchConstitution = async ({
    constitutionType,
  }: {
    constitutionType: any;
  }) => {
    try {
      setLoading(true);
      const response = await fetch(
        `${API_URL}/society/constitutioncontent/get?societyId=${societyDataById?.id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (constitutionType) {
        const updatedConstitutions = allConstitutions?.map((item: any) => {
          const updatedClauseContents = item?.clauseContents?.map(
            (clause: any, index: any) => {
              const existingItem = data?.data?.data?.find(
                (item: any) => item.clauseContentId === clause.id
              );
              if (existingItem) {
                return {
                  ...clause,
                  description: existingItem?.description,
                };
              }
              return { ...clause, description: clause?.content };
            }
          );
          return {
            ...item,
            clauseContents: updatedClauseContents,
          };
        });
        updatedConstitutions?.map((item: any) => {
          if (item.name === constitutionType) {
            setFasal(item.clauseContents);
          }
        });
      } else {
        setFasal([]);
      }
      setConstitution(data?.data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #D9D9D9",
          // backgroundColor: "#FCFCFC",
          borderRadius: "14px",
          overflow: "hidden",
        }}
      >
        <Grid
          sx={{
            display: "flex",
            alignItems: "center",
          }}
          container
          spacing={2}
        >
          <Grid item xs={6}>
            {t("latestConstitution")}
          </Grid>
        </Grid>
        <Box sx={{ mt: 2 }}>
          <Grid item xs={12}>
            {fasal.length > 0 ? (
              <FasalContent fasalContent={fasal} />
            ) : loading === true ? (
              <LoadingOverlay />
            ) : (
              <Typography className="label">{t("noData")}</Typography>
            )}
          </Grid>
        </Box>
      </Box>
    </Box>
  );
};

export default PerlembagaanSection;
