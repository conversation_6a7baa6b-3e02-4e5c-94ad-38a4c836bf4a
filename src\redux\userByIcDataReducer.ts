import { createSlice } from '@reduxjs/toolkit';

interface UserByIcStore {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: UserByIcStore = {
  data: null,
  loading: false,
  error: null,
};

export const userByIcDataSlice = createSlice({
  name: 'userByIcData',
  initialState,
  reducers: {
    setUserByIcDataRedux(state, action) {
      state.data = action.payload;
    },
    setUserByIcLoading(state, action) {
      state.loading = action.payload;
    },
    setUserByIcError(state, action) {
      state.error = action.payload;
    },
  },
});

export const { setUserByIcDataRedux, setUserByIcLoading, setUserByIcError } = userByIcDataSlice.actions;
export default userByIcDataSlice.reducer;
