import React from "react";
import {Typography} from "@mui/material";
import Box from "@mui/material/Box";
import {TrainingDoneIcon} from "@/components/icons/trainingDone";


interface TrainingStepperProps {
  currentPage: number;
  page: number;
  title: string;
  isChapter?: boolean;
}

const TrainingStepper: React.FC<TrainingStepperProps> = ({currentPage,page,title, isChapter= false}) => {

  return (
    <>
      <Box
        sx={{
          //flex: 5,
          borderRadius: 2.5,
          backgroundColor: currentPage === page ? "#0CA6A680" : "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box sx={{
          //height: "200px",
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-evenly",
        }}>
          <Typography
            sx={{
              width:"20%",
              color: "#666666",
              margin: "auto",
              //lineHeight: "18px",
              fontWeight: "400",
              fontSize: 12,
              textAlign: "center",
            }}
          >
            { isChapter ? `Bab ${page+1}` : ""}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              margin: "auto",
              //lineHeight: "18px",
              fontWeight: "400",
              fontSize: 12,
              textAlign: "center",
            }}
          >
            {title}
          </Typography>
          <Box sx={{width:"10%", margin: "auto",}}>
            <TrainingDoneIcon  sx={{
              width: "25px",
              height: "25px",}}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default TrainingStepper;
