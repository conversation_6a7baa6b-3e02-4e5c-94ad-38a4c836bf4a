import Grid from "@mui/material/Grid/Grid";
import { DayCalendarSkeleton } from "@mui/x-date-pickers";
import Typography from "@mui/material/Typography/Typography";
import { ToggleButton, ToggleButtonGroup } from "@mui/lab";
import Stack from "@mui/material/Stack/Stack";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import dayjs from "../../../helpers/dayjs";

import { JumbotronPublic } from "../../../components/jumbotron/Public";
import { Calendar } from "../../../components/calendar/Main";
import { CardCalendarSidebar } from "../../../components/card/calendar/Sidebar";

import "./activity-summary.css";

const CalendarPublicActivitySummaryPage = () => {
  const [category, setCategory] = useState<
    "ALL_ACTIVITIES_THIS_YEAR" | "ALL_ACTIVITIES_THIS_MONTH"
  >("ALL_ACTIVITIES_THIS_MONTH");
  const [loading, setLoading] = useState(true);

  const { t } = useTranslation();

  const monthsArray = Array.from(
    { length: 12 },
    (_val, index) => `${index + 1}`
  )
    .map((num) => (num.length === 1 ? num.padStart(2, "0") : num))
    .map((num) => `${new Date().getUTCFullYear()}-${num}-01`);

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, []);

  return (
    <>
      <JumbotronPublic
        breadcrumbs={[{ url: "/takwim", label: t("calendar") }]}
        title={t("calendarTitle")}
      />
      <div className="content">
        <Grid container spacing={2}>
          <Grid item md={3}>
            <CardCalendarSidebar />
          </Grid>
          <Grid item sm={12} md={9} sx={{ width: "100%" }}>
            <div className="calendars-view">
              <Typography
                sx={{ color: "var(--primary-color)", marginBottom: "1rem" }}
              >
                {t("calendarAllActivities")}
              </Typography>
              <Stack
                spacing={2}
                sx={{
                  position: "sticky",
                  top: "6.5rem",
                  backgroundColor: "white",
                  paddingBottom: "1rem",
                  zIndex: "2",
                  width: "100%",
                  alignItems: "center",
                }}
              >
                <ToggleButtonGroup
                  fullWidth
                  exclusive
                  color="primary"
                  value={category}
                  onChange={(_el, val) => setCategory(val)}
                >
                  <ToggleButton
                    value="ALL_ACTIVITIES_THIS_MONTH"
                    sx={{ textTransform: "capitalize" }}
                  >
                    {t("calendarThisMonth")}
                  </ToggleButton>
                  <ToggleButton
                    value="ALL_ACTIVITIES_THIS_YEAR"
                    sx={{ textTransform: "capitalize" }}
                  >
                    {t("calendarThisYear")}
                  </ToggleButton>
                </ToggleButtonGroup>
              </Stack>
              {category === "ALL_ACTIVITIES_THIS_MONTH" && (
                <Calendar
                  className="calendar-activities-this-month"
                  sx={{
                    margin: "0",
                    border: "1px solid #dadada99",
                    width: "100%",
                    // minHeight: "100rem",
                    borderRadius: "0.5rem",
                    marginBottom: "1.5rem",
                    maxHeight: "50rem",
                  }}
                  views={["day"]}
                  showDaysOutsideCurrentMonth
                  slotProps={{
                    calendarHeader: {
                      sx: {
                        justifyContent: "space-around",
                      },
                    },
                    day: {
                      className: "calendar-activities-this-month-date",
                    },
                    previousIconButton: {
                      hidden: true,
                    },
                    nextIconButton: {
                      hidden: true,
                    },
                  }}
                />
              )}
              {category === "ALL_ACTIVITIES_THIS_YEAR" && (
                <div style={{ display: "flex", flexWrap: "wrap" }}>
                  {monthsArray.map((refDate, index) => (
                    <Calendar
                      key={`${refDate}-${index}`}
                      className="all-activities-this-year-calendar"
                      loading={loading}
                      views={["day"]}
                      renderLoading={() => <DayCalendarSkeleton />}
                      referenceDate={dayjs(refDate)}
                      slotProps={{
                        previousIconButton: {
                          hidden: true,
                        },
                        nextIconButton: {
                          hidden: true,
                        },
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          </Grid>
        </Grid>
      </div>
    </>
  );
};

export default CalendarPublicActivitySummaryPage;
