import React,  { useEffect, useState } from "react";
import { Box, Typography, Card, CardMedia, List, ListItem, ListItemText, IconButton, Divider, useMediaQuery } from "@mui/material";
import { ChevronLeft, WhatsApp, Facebook, LinkedIn } from "@mui/icons-material";
import { useParams, useNavigate } from "react-router-dom";
import { artikelData, ArtikelObject } from "@/pages/artikel/data";
import ShareMenu from "@/components/share-menu";
import { useTranslation } from "react-i18next";

const Artikel: React.FC = () => {
  const { t } = useTranslation();
  const { slug } = useParams();
  const navigate = useNavigate();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [artikel, setArtikel] = useState<ArtikelObject | null>();

  useEffect(() => {
    if (slug) {
      const foundArtikel = artikelData.find((item) => item.slug === slug);
      setArtikel(foundArtikel || null);
    }
  }, [slug]);

  const handleViewArtikel = (slug: string) => {
    navigate(`/artikel/${slug}`);
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", p: 3, py: "86px", backgroundColor: "#F1F4FA" }}>
      <IconButton onClick={() => navigate(-1)} sx={{ alignSelf: "flex-start", mt: "50px", mb: "50px" }}>
        <ChevronLeft />
      </IconButton>

      { artikel &&
        <Box sx={{ display: "flex", flexDirection: isMobile ? "column" : "row", justifyContent: "center" }}>
          <Box sx={{ maxWidth: 800, p: "70px", bgcolor: "#fff", borderRadius: 2, boxShadow: 3 }}>
            <Typography variant="h4" gutterBottom sx={{ color: "#666666", fontSize: "25px", fontWeight: "500", lineHeight: "30px", mb: "29px" }}>
              {artikel.heading1}
            </Typography>
            <Card sx={{ mb: "52px" }} >
              <CardMedia
                component="img"
                height="300"
                image={artikel.img}
                alt={artikel.heading1}
              />
            </Card>
            {artikel.content.split("\n").map((line, index) => (
              <Typography mb={"16px"} color="text.secondary" textAlign="justify">
                {line}
              </Typography>
            ))}

          </Box>

          {/* Related Articles Sidebar */}
          <Box sx={{
              maxWidth: isMobile? "100%" : "400px",
              mt: isMobile ? "24px" : 0,
              ml: isMobile ? 0 : "24px"
            }}>
            {/* More Details and Share */}
            <Box sx={{ p: 3, bgcolor: "#fff", borderRadius: 2, boxShadow: 3, mb: "24px" }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {artikel.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">{artikel.date}</Typography>

              <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
                <ShareMenu url={`${window.location.origin}/artikel/${slug}`} title={artikel ? artikel.heading1 : ""} />
              </Box>
            </Box>

            {/* Related Articles */}
            <Box sx={{ p: 3, bgcolor: "#fff", borderRadius: 2, boxShadow: 3 }}>
              <Typography sx={{ mb: "45px", fontSize: "20px", color: "#666666", textAlign: "center", fontWeight: "400" }}>
              {t("bacaanBerkaitan")}
              </Typography>
              <List>
                {artikelData
                  .filter((article) => article.slug !== slug) // Exclude current article
                  .map((article, index) => (
                    <React.Fragment key={index}>
                      <ListItem sx={{ alignItems: "flex-start", p: 0, mb: "16px", cursor: "pointer" }}
                        onClick={() => handleViewArtikel(article.slug)}>
                        <CardMedia
                          component="img"
                          sx={{
                            minWidth: isMobile ? "200px" : "134px",
                            maxWidth: isMobile ? "200px" : "134px",
                            height: isMobile ? "120px" : "93px",
                            borderRadius: 1,
                            mr: "10px",
                            transition: "transform 0.3s ease-in-out",
                            "&:hover": {
                              transform: "scale(1.1)"
                            }
                          }}
                          image={article.img}
                          alt={article.heading1}
                        />
                        <ListItemText sx={{ m: 0 }}
                          primary={
                            <Typography
                              sx={{
                                display: "-webkit-box",
                                WebkitBoxOrient: "vertical",
                                WebkitLineClamp: 2,
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                fontSize: "14px",
                                lineHeight: "16px",
                                color: "#666666",
                                fontWeight: "500",
                                mb: "6px"
                              }}
                            >
                              {article.heading1}
                            </Typography>
                          }
                          secondary={
                            <Typography
                              sx={{
                                display: "-webkit-box",
                                WebkitBoxOrient: "vertical",
                                WebkitLineClamp: 4,
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                fontSize: "10px",
                                lineHeight: "12px",
                                color: "#666666"
                              }}
                            >
                              {article.description}
                            </Typography>
                          }
                        />
                      </ListItem>
                    </React.Fragment>
                  ))}
              </List>
            </Box>
          </Box>
        </Box>
      }

    </Box>
  );
};

export default Artikel;
