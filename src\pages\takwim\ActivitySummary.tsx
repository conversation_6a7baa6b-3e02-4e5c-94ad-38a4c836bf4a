import Grid from "@mui/material/Grid/Grid";
import Typography from "@mui/material/Typography/Typography";
import { ToggleButton, ToggleButtonGroup } from "@mui/lab";
import Stack from "@mui/material/Stack/Stack";
import { useTranslation } from "react-i18next";
import { useState } from "react";

import { JumbotronPublic } from "../../components/jumbotron/Public";
import { Calendar } from "../../components/calendar/Main";
import { CardCalendarSidebar } from "../../components/card/calendar/Sidebar";

const TakwimActivitySummaryPage = () => {
  const { t } = useTranslation();
  const [category, setCategory] = useState<string>("all");

  return (
    <>
      <JumbotronPublic
        breadcrumbs={[{ url: "/takwim", label: t('calendar') }]}
        title={t('calendarTitle')}
      />
      <div className="content">
        <Grid container spacing={2}>
          <Grid item md={3}>
            <CardCalendarSidebar />
          </Grid>
          <Grid item sm={12} md={9} sx={{ width: "100%" }}>
            <div style={{ padding: "1rem" }}>
              <Typography sx={{ color: '#0CA6A6', marginBottom: "1rem" }}>
                {t('calendarAllActivities')}
              </Typography>
              <Stack spacing={2} sx={{
                position: "sticky",
                top: "6.5rem",
                backgroundColor: "white",
                paddingBottom: "1rem",
                zIndex: "2",
                width: "100%",
                alignItems: "center"
              }}>
                <ToggleButtonGroup
                  fullWidth
                  exclusive
                  color="primary"
                  value={category}
                  onChange={(_el, val) => setCategory(val)}
                >
                  <ToggleButton value="all">Semua</ToggleButton>
                  <ToggleButton value="upcoming">Akan Datang</ToggleButton>
                  <ToggleButton value="past">Lepas</ToggleButton>
                </ToggleButtonGroup>
                <Calendar
                  sx={{
                    margin: "0",
                    border: "1px solid #dadada99",
                    width: "100%",
                    borderRadius: "0.5rem"
                  }}
                  showDaysOutsideCurrentMonth
                />
              </Stack>
            </div>
          </Grid>
        </Grid>
      </div>
    </>
  );
};

export default TakwimActivitySummaryPage;
