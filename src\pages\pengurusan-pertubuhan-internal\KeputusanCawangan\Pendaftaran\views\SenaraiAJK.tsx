import { Box, IconButton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { EyeIcon } from "../../../../../components/icons";
import {
  ApplicationStatusEnum,
  MALAYSIA,
  OrganisationPositions,
} from "../../../../../helpers/enums";
import { DataGrid } from "@mui/x-data-grid";
import { useKeputusanCawanganPendaftaranContext } from "../KeputusanCawanganPendaftaranProvider";
import { getLocalStorage, useQuery } from "@/helpers";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { DataTable } from "@/components";

const SenaraiAJK = () => {
  const { branchDataById } = useKeputusanCawanganPendaftaranContext();
  const navigate = useNavigate();
  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);
  const [totalListNonCitizen, setTotalListNonCitizen] = useState<number>(0);
  const [rowDataNonCitizen, setRowDataNonCitizen] = useState<any[]>([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  const addressList = getLocalStorage("address_list", null);
  const [stateList, setStateList] = useState(
    addressList
      .filter((item: any) => item.pid === MALAYSIA)
      .map((item: any) => ({ value: item.id, label: item.name }))
  );

  const form = useForm<any>();
  const { setValue, getValues, watch, reset } = form;

  const pageNonCitizen = watch("pageNonCitizen") || 1;
  const pageSizeNonCitizen = watch("pageSizeNonCitizen") || 10;

  const {
    data: nonCitizenList,
    refetch: fetchNonCitizenAjkList,
    isLoading: isLoadingNonCitizenAJKList,
  } = useQuery({
    url: `society/nonCitizenCommittee/getAll`,
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: branchDataById?.societyId,
      },
      {
        field: "branchId",
        operator: "eq",
        value: branchDataById?.id,
      },
      {
        field: "pageNo",
        operator: "eq",
        value: pageNonCitizen,
      },
      {
        field: "pageSize",
        operator: "eq",
        value: pageSizeNonCitizen,
      },
    ],
    autoFetch: !!branchDataById?.societyId,
    onSuccess: (data) => {
      setTotalListNonCitizen(data?.data?.data?.total ?? 0);
      setRowDataNonCitizen(data?.data?.data?.data ?? []);
    },
  });

  useEffect(() => {
    if (branchDataById?.branchCommittees) {
      setInternalList(branchDataById.branchCommittees);
    } else {
      setInternalList([]);
    }
    if (branchDataById?.branchNonCitizenCommittees) {
      setExternalList(branchDataById.branchNonCitizenCommittees);
    } else {
      setExternalList([]);
    }
  }, [branchDataById]);

  const [internalList, setInternalList] = useState([]);
  const [externalList, setExternalList] = useState([]);

  const handleViewAJK = (memberId: any) => {
    navigate(`cawangan-paparan-ajk/${memberId}`);
  };

  const handleViewAJKBukanWn = (memberId: any) => {
    navigate(`cawangan-paparan-ajk-bukan-wn/${memberId}`);
  };

  const internalColumns = [
    {
      field: "designationCode",
      headerName: t("position"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: (params: any) => {
        const row = params?.row;
        const position = positionsTranslatedList?.find(
          (item: any) => Number(item.value) === Number(row.designationCode)
        );
        return (
          <Typography className="label">
            {position?.label ? position?.label : "-"}
          </Typography>
        );
      },
    },
    {
      field: "committeeName",
      headerName: t("name"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
    },
    {
      field: "email",
      headerName: t("email"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
    },
    {
      field: "committeeStateCode",
      headerName: t("state"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: (params: any) => {
        const row = params?.row;
        const state = stateList.find(
          (item: any) => item.value === Number(row?.committeeStateCode)
        );
        return (
          <Typography className="label">
            {state?.label ? state?.label : "-"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      sortable: false,
      flex: 0.5,
      renderCell: (params: any) => (
        <IconButton
          onClick={() => handleViewAJK(params.row.id)}
          sx={{ color: "var(--primary-color)" }}
        >
          <EyeIcon sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }} />
        </IconButton>
      ),
    },
  ];

  const externalColumns = [
    {
      field: "designationCode",
      headerName: t("position"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: (params: any) => {
        const row = params?.row;
        const position = positionsTranslatedList.find(
          (item: any) => Number(item.value) === Number(row.designationCode)
        );
        return (
          <Typography className="label">
            {position?.label ? position?.label : "-"}
          </Typography>
        );
      },
    },
    {
      field: "name",
      headerName: t("name"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: (params: any) => {
        const row = params?.row;
        const statusLabel =
          ApplicationStatusEnum[row.applicationStatusCode] || "-";
        return (
          <Typography className="label">
            {statusLabel ? statusLabel : "-"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      sortable: false,
      flex: 0.5,
      renderCell: (params: any) => (
        <IconButton
          onClick={() => handleViewAJKBukanWn(params.row.id)}
          sx={{ color: "var(--primary-color)" }}
        >
          <EyeIcon sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }} />
        </IconButton>
      ),
    },
  ];

  return (
    <>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        {branchDataById?.branchCommittees?.length > 0 ? (
          <>
            <Box
              sx={{
                borderRadius: "10px",
                padding: "41px 25px 25px",
                border: "0.5px solid #DADADA",
                marginBottom: "13px",
              }}
            >
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight="500 !important"
                marginBottom="20px"
              >
                {t("ajkList")}
              </Typography>

              <DataGrid
                rows={internalList}
                columns={internalColumns as any}
                disableColumnMenu
                disableColumnSelector
                disableDensitySelector
                autoHeight
                disableRowSelectionOnClick
                sx={{
                  color: "#666666",
                  backgroundColor: "white",
                  border: "none",
                  fontFamily:
                    "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
                  "& .MuiDataGrid-columnHeaders": {
                    fontWeight: "bold",
                    fontSize: "16px",
                  },
                  "& .MuiDataGrid-cell": {
                    fontSize: "16px",
                  },
                  minHeight: "100px",
                }}
              />
            </Box>

            <Box
              sx={{
                borderRadius: "10px",
                padding: "41px 25px 25px",
                border: "0.5px solid #DADADA",
                marginBottom: "13px",
              }}
            >
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight="500 !important"
                marginBottom="20px"
              >
                {t("senaraiAjkBukanWn")}
              </Typography>

              <DataTable
                columns={externalColumns as any}
                rows={rowDataNonCitizen}
                page={pageNonCitizen}
                rowsPerPage={pageSizeNonCitizen}
                totalCount={totalListNonCitizen}
                onPageChange={(newPage) => setValue("pageNonCitizen", newPage)}
                onPageSizeChange={(newPageSize) => {
                  setValue("pageNonCitizen", 1);
                  setValue("pageSizeNonCitizen", newPageSize);
                }}
                isLoading={isLoadingNonCitizenAJKList}
                // clientPaginationMode={true}
              />
            </Box>
          </>
        ) : (
          <Typography className="label">{t("noData")}</Typography>
        )}
      </Box>
    </>
  );
};

export default SenaraiAJK;
