import React from "react";
import { useTranslation } from "react-i18next";
import { globalStyles } from "@/helpers";

import { Box, Typography } from "@mui/material";
import { FormFieldRow, Label, DisabledTextField } from "@/components";

const Detail = () => {
  const { t } = useTranslation();
  const classes = globalStyles();

  return (
    <Box className={classes.sectionBox} mb={2}>
      <Typography className="title" mb={2}>
        Maklumat Pertubuhan
      </Typography>

      <FormFieldRow
        label={<Label text="Alamat tempat urusan" />}
        value={<DisabledTextField value="1357 Lorong Surau Bahru" />}
      />

      <FormFieldRow
        label={<Label text="Negeri" />}
        value={<DisabledTextField value="Terengganu" />}
      />

      <FormFieldRow
        label={<Label text="Daerah" />}
        value={<DisabledTextField value="Kuala Terengganu" />}
      />

      <FormFieldRow
        label={<Label text="Bandar" />}
        value={<DisabledTextField value="Kuala Terengganu" />}
      />

      <FormFieldRow
        label={<Label text="Poskod" />}
        value={<DisabledTextField value="45000" />}
      />

      <FormFieldRow
        label={<Label text="Nombor telefon" />}
        value={<DisabledTextField value="019456765" />}
      />

      <FormFieldRow
        label={<Label text="Emel" />}
        value={<DisabledTextField value="<EMAIL>" />}
      />
    </Box>
  );
};

const MaklumatPertubhanDetail = React.memo(Detail);
export default MaklumatPertubhanDetail;
