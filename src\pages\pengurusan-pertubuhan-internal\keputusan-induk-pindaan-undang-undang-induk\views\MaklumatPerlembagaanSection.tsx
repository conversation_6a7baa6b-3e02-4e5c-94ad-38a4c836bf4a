import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Box,
  Grid,
  Button,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  CircularProgress,
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";

import { DokumenIcon } from "../../../../components/icons";
import DisabledTextField from "../../../../components/input/DisabledTextField";
import FasalContent from "../../../../components/FasalContent";
import { useCustom } from "@refinedev/core";
import useMutation from "../../../../helpers/hooks/useMutation";
import { setLocalStorage } from "../../../../helpers/utils";
import { API_URL } from "../../../../api";
import { useState } from "react";
import { ConstitutionType, MeetingTypeOption } from "../../../../helpers/enums";
import FasalContentKelulusan from "./fasalContentKelulusan";
import { useQuery } from "@/helpers";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const getMeetingLabel = (value: number): string => {
  const meeting = MeetingTypeOption.find(
    (option) => Number(option.value) === Number(value)
  );
  return meeting ? meeting.label : "Unknown Meeting Type";
};

const MaklumatPerlembagaanSection = () => {
  const { t } = useTranslation();
  const [meetingType, setMeetingType] = useState("");
  const [meetingList, setMeetingList] = useState([]);

  const [meetingId, setMeetingId] = useState("");
  const [meetingDate, setMeetingDate] = useState("");

  const [societyId, setSocietId] = useState("");
  const { id, amendmentId } = useParams();
  const decodedId = atob(id || "");

  const downloadFile = (filePath: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = filePath;
    link.download = fileName;
    link.click();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setMeetingForm((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const [formData, setFormData] = useState({
    organizationGoals: "",
    organizationLevel: "Negeri",
    organizationCategory: "",
    organizationSubCategory: "",
    hasBranch: 0,
    constitutionType: "",
  });

  const [meetingForm, setMeetingForm] = useState({
    meetingId: "",
    meetingDate: "",
    totalAttendees: 0,
    meetingMemberAttendances: [],
    branchId: "",
    branchNo: "",
    city: "",
    closing: "",
    confirmBy: "",
    district: "",
    id: "",
    mattersDiscussed: "",
    meetingAddress: "",
    meetingContent: "",
    meetingMethod: "",
    meetingMinute: "",
    meetingPlace: "",
    meetingPurpose: "",
    meetingTime: "",
    meetingTimeDurationMinutes: "",
    meetingTimeTo: "",
    meetingType: "",
    openingRemarks: "",
    otherMatters: "",
    othersDiscussionRemarks: "",
    closingRemarks: "",
    platformType: "",
    postcode: "",
    providedBy: "",
    societyId: "",
    societyNo: "",
    state: "",
  });

  const { data: amendmentData, isLoading: getAmendmentParamIsLoading } =
    useCustom({
      url: `${API_URL}/society/amendment/getAmendmentByParam`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          id: amendmentId,
        },
      },
      queryOptions: {
        enabled: !!amendmentId,
        onSuccess: (responseData) => {
          if (responseData.data.code === 200) {
            const data = responseData?.data?.data?.data?.[0];
            setSocietId(data?.societyId);
            // setFormData((prevState) => ({
            //   ...prevState,
            //   organizationGoals: data?.goal,
            //   organizationLevel: data?.societyLevel,
            //   organizationCategory: data?.categoryCodeJppm,
            //   organizationSubCategory: data?.subCategoryCode,
            //   hasBranch: data?.hasBranch == "0" ? 0 : 1,
            //   constitutionType: data?.constitutionType,
            // }));
            if (data.meetingType) {
              setMeetingType(data?.meetingType);
              setMeetingId(data?.meetingId);
              setMeetingDate(data?.createdDate);
              // setMeetingForm((prevState) => ({
              //   ...prevState,
              //   meetingDate: data?.createdDate,
              //   meetingId: data?.meetingId,
              // }));
            }
          }
        },
      },
    });

  // GET MEETING DETAILS
  const {
    data: meetingData,
    isLoading: isLoadingMeetingData,
    refetch: refetchMeetingData,
  } = useCustom({
    url: `${API_URL}/society/meeting/search`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: decodedId,
        meetingDate: meetingDate,
      },
    },
    queryOptions: {
      enabled: !!societyId && !!meetingType && !!meetingDate && !!decodedId,
      onSuccess: (data) => {
        const meetingSearchlist = data?.data?.data?.data || [];
        if (meetingSearchlist.length > 0) {
          const meetingListData = meetingSearchlist.filter(
            (item: any) => item?.meetingType === meetingType
          );
          setMeetingList(meetingListData);
          fetchMeeting();
        }
      },
    },
  });

  const {
    data: meeting,
    isLoading: isMeetingDocuLoading,
    refetch: fetchMeeting,
  } = useCustom({
    url: `${API_URL}/society/meeting/${meetingId}`,
    method: "get",
    queryOptions: {
      enabled: !!meetingId,
      onSuccess: (data) => {
        const response = data?.data?.data;
        setMeetingForm((prevState) => ({
          ...prevState,
          totalAttendees: response?.totalAttendees,
          meetingMemberAttendances: response?.meetingMemberAttendances,
          ...response,
        }));
      },
    },
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const meetingTypeOptions = MeetingTypeOption.filter((option) =>
    [2, 3, 4].includes(option.value)
  );

  const viewMeeting = async () => {
    try {
      if (id) {
        const societyId = atob(id);
        const committees = meetingForm?.meetingMemberAttendances?.map(
          (member: any) => ({
            name: member?.name,
            position: member?.position,
          })
        );
        const response = await fetch(`${API_URL}/society/pdf/meetingMinutes`, {
          method: "post",
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
          body: JSON.stringify({
            societyId: societyId,
            meetingTitle: "",
            meetingDescription: "",
            meetingDate: meetingForm?.meetingDate,
            meetingTimeFrom: meetingForm?.meetingTime,
            meetingTimeTo: meetingForm?.meetingTimeTo,
            meetingLocation: meetingForm?.meetingPlace,
            committees: committees,
            openingRemarks: meetingForm?.openingRemarks,
            mattersDiscussed: meetingForm?.mattersDiscussed,
            otherMatters: meetingForm?.otherMatters,
            closing: meetingForm?.closing,
            preparedBy: meetingForm?.providedBy,
            approvedBy: meetingForm?.confirmBy,
            othersDiscussionRemarks: meetingForm?.otherMatters,
            closingRemarks: meetingForm?.closing,
          }),
        });
        const data = await response.json();
        if (data.status === "SUCCESS") {
          let base64String = data?.data?.byte ?? "";

          if (base64String.startsWith("JVB")) {
            base64String = "data:application/pdf;base64," + base64String;
          } else if (base64String.startsWith("data:application/pdf;base64")) {
          } else {
          }
          downloadFile(base64String, `minit-mesyuarat.pdf`);
        }
      }
    } catch (error) {
      return null;
    }
  };

  const isApiLoading =
    isMeetingDocuLoading || isLoadingMeetingData || getAmendmentParamIsLoading;

  return (
    <>
      {isApiLoading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "300px",
          }}
        > 
          <CircularProgress />
        </Box>
      ) : (
        <Box
          sx={{
            borderRadius: "10px",
            padding: "41px 25px 25px",
            border: "0.5px solid #DADADA",
            marginBottom: "13px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("maklumatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  {t("meetingType")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingType}
                  disabled
                  displayEmpty
                  required
                  onChange={(e) => {
                    setMeetingType(e.target.value);
                    setFormData((prevState: any) => ({
                      ...prevState,
                      meetingType: e.target.value,
                    }));
                  }}
                >
                  {MeetingTypeOption.map((item: any, index) => (
                    <MenuItem key={index} value={item.value}>
                      {item.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  {t("meetingDate")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextField
                size="small"
                fullWidth
                required
                disabled
                name="meetingDate"
                value={meetingForm.meetingDate}
                onChange={handleInputChange}
                inputProps={{
                  min: new Date().toISOString().split("T")[0],
                  onKeyDown: (e) => e.preventDefault(),
                }}
                type="date"
              />
            </Grid>

            {meetingList.length > 0 ? (
              <>
                <Grid item xs={12} sm={3}>
                  <Typography sx={labelStyle}>
                    {t("meetingList")}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <FormControl fullWidth>
                    <Select
                      size="small"
                      value={meetingId}
                      displayEmpty
                      required
                      disabled
                      onChange={(e) => {
                        setMeetingForm((prevState: any) => ({
                          ...prevState,
                          meetingId: e.target.value,
                        }));
                      }}
                    >
                      {meetingList.map((items: any, index) => {
                        return (
                          <MenuItem key={index} value={items.id}>
                            {getMeetingLabel(items.meetingType)} (
                            {items.meetingDate})
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            ) : null}

            {meetingForm.totalAttendees || meetingForm.totalAttendees === 0 ? (
              <>
                <Grid item xs={12} sm={3}>
                  <Typography sx={labelStyle}>
                    {t("bilanganAhliYangHadir")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="bilanganAhliYangHadir"
                    value={meetingForm.totalAttendees}
                    placeholder="Contoh: 10 orang"
                    onChange={handleInputChange}
                    disabled
                  />
                </Grid>
              </>
            ) : null}

            {meetingForm.meetingMemberAttendances &&
            meetingForm.meetingMemberAttendances.length > 0 ? (
              <>
                <Grid item xs={12} sm={3}>
                  <Typography sx={labelStyle}>
                    {t("bilanganPemegangJawatan")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={9} container spacing={2}>
                  {meetingForm.meetingMemberAttendances?.map(
                    (member: any, index) => (
                      <Grid container item xs={12} spacing={2} key={index}>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            fullWidth
                            name="bilanganPemegangJawatan"
                            value={member.name}
                            onChange={handleInputChange}
                            disabled
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            fullWidth
                            name="bilanganPemegangJawatan"
                            value={member.position}
                            onChange={handleInputChange}
                            disabled
                          />
                        </Grid>
                      </Grid>
                    )
                  )}
                </Grid>
              </>
            ) : null}

            <Grid item xs={12} sm={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("minitMesyuarat")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={9}>
              <Button
                variant="outlined"
                startIcon={
                  <DokumenIcon sx={{ color: "var(--primary-color)" }} />
                }
                fullWidth
                onClick={viewMeeting}
                sx={{
                  textTransform: "none",
                  borderRadius: "8px",
                  fontWeight: 500,
                  color: "#666666",
                }}
              >
                {t("paparMinitMesyuarat")}
              </Button>
            </Grid>
          </Grid>
        </Box>
      )}

      <Box
        sx={{
          borderRadius: "10px",
          padding: "41px 25px 25px",
          border: "0.5px solid #DADADA",
          marginBottom: "13px",
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("checkTheConstitution")}
        </Typography>
        <FasalContentKelulusan societyId={decodedId} />
      </Box>
    </>
  );
};

export default MaklumatPerlembagaanSection;
