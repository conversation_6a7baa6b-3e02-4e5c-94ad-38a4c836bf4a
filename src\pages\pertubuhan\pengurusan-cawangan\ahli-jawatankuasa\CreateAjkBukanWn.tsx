import React, { useEffect, useState } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import Input from "../../../../components/input/Input";
import {
  IdTypes,
  OrganisationPositions,
  DurationOptions,
  CitizenshipStatus,
  GenderType,
} from "../../../../helpers/enums";
import { getLocalStorage } from "../../../../helpers/utils";
import FileUploader from "@/components/input/fileUpload";
import { DocumentUploadType, useQuery } from "@/helpers";
import dayjs from "dayjs";
import { ButtonPrimary } from "@/components";

interface FormValues {
  name: any;
  citizenshipStatus: any;
  identificationType: any;
  identificationNo: any;
  applicantCountryCode: any;
  gender: any;
  visaNo: number | any;
  visaExpirationDate: any;
  permitNo: any;
  permitExpirationDate: any;
  tujuanDMalaysia: any;
  tempohDMalaysia: any;
  stayDurationDigit: number | any;
  stayDurationUnit: any;
  designationCode: any;
  summary: any;
  societyNo: string | null;
  societyName: null;
}

export const CreateAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [searchParams] = useSearchParams();
  const { id: societyId } = useParams();
  const branchId = Number(searchParams.get("id"));
  const memberId = Number(searchParams.get("mId"));
  const addressList = getLocalStorage("address_list", null);
  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  const [branchData, setBranchData] = useState<any>({});

  const {
    data: nonCommitteeData,
    isLoading: nonCommitteeDataLoading,
    refetch: fetchNonCommitteeData,
  } = useQuery({
    url: `society/nonCitizenCommittee/${memberId}`,
    autoFetch: !!memberId,
    onSuccess: (data) => {
      setBranchData(data?.data?.data);
    },
  });

  useEffect(() => {
    if (branchData) {
      setFormValues(branchData);
    }
  }, [branchData]);

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!societyId,
    },
  });

  const [formValues, setFormValues] = useState<FormValues>({
    name: "",
    societyNo: null,
    societyName: null,
    citizenshipStatus: 2,
    identificationType: "",
    identificationNo: null,
    visaNo: "",
    visaExpirationDate: "",
    permitNo: "",
    permitExpirationDate: "",
    tujuanDMalaysia: "",
    tempohDMalaysia: "",
    stayDurationDigit: null,
    stayDurationUnit: "",
    designationCode: "",
    summary: "",
    applicantCountryCode: null,
    gender: null,
  });

  useEffect(() => {
    if (societyData) {
      setFormValues((prevValues) => ({
        ...prevValues,
        societyNo: societyData?.data?.data?.societyNo
          ? societyData?.data?.data?.societyNo
          : societyData?.data?.data?.applicationNo,
        societyName: societyData?.data?.data?.societyName,
      }));
    }
  }, [societyData]);

  const [debouncedData, setDebouncedData] = useState<FormValues>(formValues);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedData(formValues);
    }, 300); // Adjust delay as needed

    return () => {
      clearTimeout(handler);
    };
  }, [formValues]);

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value,
    });
  };

  const { mutate: editExternalComittee, isLoading: isLoadingCreate } =
    useCustomMutation();

  const CreateUser: (filteredData: any) => void = (filteredData) => {
    editExternalComittee(
      {
        url: `${API_URL}/society/branch/update`,
        method: "put",
        values: {
          id: branchId,
          societyId: societyId,
          societyNo: branchData.societyNo,
          branchId: branchId,
          branchNo: branchData.branchNo,
          branchNonCitizenCommittees: [filteredData],
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setErrors({});
          navigate(`../ahlijawatankuasa?id=${branchId}`);
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};
    const requiredFields = [
      "name",
      "identificationType",
      "identificationNo",
      "applicantCountryCode",
      "gender",
      "tujuanDMalaysia",
      "designationCode",
      "summary",
    ] as const;

    requiredFields.forEach((field) => {
      if (!formValues[field]) {
        newErrors[field] = t("fieldRequired");
      }
    });

    // Conditional validation for duration fields when identificationType is not 4 (MyPR)
    if (Number(formValues.identificationType) !== 4) {
      if (!formValues.stayDurationDigit) {
        newErrors.stayDurationDigit = t("fieldRequired");
      }
      if (!formValues.stayDurationUnit) {
        newErrors.stayDurationUnit = t("fieldRequired");
      }
    }

    if (Object.keys(newErrors).length === 0) {
      const filteredData = Object.fromEntries(
        Object.entries(formValues).filter(([key, value]) => value !== "")
      );

      if (memberId) {
        filteredData.id = memberId;
      }
      filteredData.id = memberId ? memberId : null;
      CreateUser(filteredData);
    } else {
      setErrors(newErrors);
    }
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "32px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };

  const handleSenaraiAjk = () => {
    navigate(-1);
  };

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  const [durationOptionsTranslated, setDurationOptionsTranslated] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  useEffect(() => {
    const filteredList = IdTypes.filter((item) => Number(item.value) > 3);

    const newOList = filteredList.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    if (newOList) {
      setIdTypeTranslatedList(newOList);
    }
  }, [t]);

  useEffect(() => {
    const newDurationList = DurationOptions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setDurationOptionsTranslated(newDurationList);
  }, [t]);

  return (
    <Box
      component="form"
      noValidate
      onSubmit={handleSubmit}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="h6" component="h2" sx={sectionStyle}>
          {t("nonCitizenAJK")}
        </Typography>

        <Input
          value={
            societyData?.data?.data?.societyNo
              ? societyData?.data?.data?.societyNo
              : societyData?.data?.data?.applicationNo
          }
          name="societyNo"
          onChange={handleChange}
          disabled
          required
          label={t("organizationNumber2")}
        />
        <Input
          value={societyData?.data?.data?.societyName}
          name="societyName"
          onChange={handleChange}
          disabled
          required
          label={t("organization_name")}
        />
        <Input
          value={formValues?.name ?? ""}
          name="name"
          onChange={handleChange}
          label={t("fullNameCapitalizedOnlyFirstLetter")}
          error={!!errors.name}
          helperText={errors.name}
          required
        />
        <Input
          value={
            formValues.citizenshipStatus
              ? Number(formValues.citizenshipStatus)
              : 2
          }
          name="citizenshipStatus"
          onChange={handleChange}
          label={t("citizenship")}
          required
          type="select"
          options={CitizenshipStatus.map((item) => ({
            ...item,
            label: t(item.label),
          }))}
          disabled
        />

        <Input
          value={formValues?.identificationType ?? ""}
          name="identificationType"
          onChange={handleChange}
          required
          label={t("idTypeCapitalizedOnlyFirstLetter")}
          options={idTypeTranslatedList}
          type="select"
          error={!!errors.identificationType}
          helperText={errors.identificationType}
        />
        <Input
          value={formValues?.identificationNo ?? ""}
          name="identificationNo"
          onChange={handleChange}
          required
          label={t("idNumberCapitalizedOnlyFirstLetter")}
          error={!!errors.identificationNo}
          helperText={errors.identificationNo}
          // inputProps={{ maxLength: 12 }}
        />
        <Input
          required
          name="gender"
          onChange={handleChange}
          value={formValues?.gender ?? ""}
          error={!!errors.gender}
          helperText={errors.gender}
          label={t("gender")}
          type="select"
          options={GenderType.map((item) => ({
            label: t(item.translateKey),
            value: item.code,
          }))}
        />
        <Input
          value={Number(formValues.applicantCountryCode) ?? ""}
          name="applicantCountryCode"
          onChange={handleChange}
          required
          label={t("originCountry")}
          options={CountryData}
          type="select"
          error={!!errors.applicantCountryCode}
          helperText={errors.applicantCountryCode}
        />
        <Input
          value={formValues?.visaNo ?? ""}
          name="visaNo"
          onChange={handleChange}
          label={t("nomborVisa")}
          error={!!errors.visaNo}
          helperText={errors.visaNo}
        />
        <Input
          value={
            formValues.visaExpirationDate
              ? dayjs(formValues.visaExpirationDate).format("DD-MM-YYYY")
              : ""
          }
          name="visaExpirationDate"
          type="date"
          onChange={handleChange}
          label={t("visaExpiryDate")}
          error={!!errors.visaExpirationDate}
          helperText={errors.visaExpirationDate}
        />
        <Input
          value={formValues?.permitNo ?? ""}
          name="permitNo"
          onChange={handleChange}
          label={t("nomborPermit")}
          error={!!errors.permitNo}
          helperText={errors.permitNo}
        />
        <Input
          value={
            formValues.permitExpirationDate
              ? dayjs(formValues.permitExpirationDate).format("DD-MM-YYYY")
              : ""
          }
          name="permitExpirationDate"
          type="date"
          onChange={handleChange}
          label={t("permitExpiryDate")}
          error={!!errors.permitExpirationDate}
          helperText={errors.permitExpirationDate}
        />
        <Input
          value={formValues?.tujuanDMalaysia ?? ""}
          name="tujuanDMalaysia"
          required
          onChange={handleChange}
          label={t("tujuanDiMalaysia")}
          error={!!errors.tujuanDMalaysia}
          helperText={errors.tujuanDMalaysia}
        />
        {Number(formValues.identificationType) !== 4 && (
          <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="body1"
                sx={{
                  color: "#666666",
                  fontWeight: "400 !important",
                  fontSize: "14px",
                }}
              >
                {t("tempohDiMalaysia")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationDigit ?? ""}
                    name="stayDurationDigit"
                    required
                    type="text"
                    inputMode="numeric"
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d*$/.test(value)) {
                        setFormValues({
                          ...formValues,
                          stayDurationDigit: parseInt(value) || null,
                        });
                      }
                    }}
                    error={!!errors.stayDurationDigit}
                    helperText={errors.stayDurationDigit}
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationUnit ?? ""}
                    name="stayDurationUnit"
                    required
                    type="select"
                    onChange={handleChange}
                    options={durationOptionsTranslated}
                    error={!!errors.stayDurationUnit}
                    helperText={errors.stayDurationUnit}
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        )}
        <Input
          value={Number(formValues?.designationCode) ?? ""}
          name="designationCode"
          onChange={handleChange}
          required
          options={positionsTranslatedList}
          label={t("position")}
          type="select"
          error={!!errors.designationCode}
          helperText={errors.designationCode}
        />
        <Input
          value={formValues?.summary ?? ""}
          name="summary"
          multiline
          rows={4}
          required
          onChange={handleChange}
          label={t("importanceOfPosition2")}
          error={!!errors.summary}
          helperText={errors.summary}
        />
      </Box>
      {formValues?.identificationNo ? (
        <FileUploader
          key={formValues?.identificationNo}
          title="ajkEligibilityCheck"
          type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
          societyId={Number(societyId)}
          branchId={branchId}
          validTypes={[
            "text/plain",
            "application/rtf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "application/vnd.oasis.opendocument.text",
            "application/pdf",
          ]}
          maxFileSize={10 * 1024 * 1024}
          disabled={formValues?.identificationNo === null}
          icNo={formValues?.identificationNo}
        />
      ) : null}

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <ButtonPrimary
          sx={{
            background: "white",
            border: "1px solid #DDDDDD",
            color: "#666666",
            boxShadow: "none",
          }}
          onClick={handleSenaraiAjk}
        >
          {t("reset")}
        </ButtonPrimary>
        <ButtonPrimary
          sx={{
            boxShadow: "none",
          }}
          type="submit"
        >
          {t("update")}
        </ButtonPrimary>
      </Box>
    </Box>
  );
};

export default CreateAjkBukanWn;
