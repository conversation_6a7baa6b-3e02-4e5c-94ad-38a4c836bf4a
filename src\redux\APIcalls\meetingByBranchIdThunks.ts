import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setMeetingByBranchIdDataRedux, setMeetingByBranchIdError, setMeetingByBranchIdLoading } from '../meetingByBranchIdDataReducer';

export const fetchMeetingByBranchIdData = createAsyncThunk(
  'meetingByBranchId/fetchData',
  async ({ id }: { id: string },
    { dispatch }) => {
    dispatch(setMeetingByBranchIdLoading(true));
    try {
      const response = await fetch(`${API_URL}/society/meeting/findByBranchId/${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
      const data = await response.json();

      dispatch(setMeetingByBranchIdDataRedux(data.data));
    } catch (error: any) {
      dispatch(setMeetingByBranchIdError(error.message));
    } finally {
      dispatch(setMeetingByBranchIdLoading(false));
    }
  }
);
