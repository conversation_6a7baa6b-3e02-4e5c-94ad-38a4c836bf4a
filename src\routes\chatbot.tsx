import { Route } from "react-router-dom";
import ChatbotPage from "../pages/chatbot/ChatbotPage";
import SpeechInputPage from "../pages/chatbot/SpeechInputPage";
import ChatbotFullPage from "../pages/chatbot/ChatbotFullPage";

export const chatbotRoute = {
  resources: [
    {
      name: "chatbot",
      list: "/chatbot",
    },
    {
      name: "speech-input",
      list: "/speech-input",
    },
    {
      name: "chatbot-full",
      list: "/chatbot-full",
    },
  ],
  routes: (
    <>
      <Route path="/chatbot">
        <Route index element={<ChatbotPage />} />
      </Route>
      {/* <Route path="/speech-input">
        <Route index element={<SpeechInputPage />} />
      </Route>
      <Route path="/chatbot-full">
        <Route index element={<ChatbotFullPage />} />
      </Route> */}
    </>
  ),
};
