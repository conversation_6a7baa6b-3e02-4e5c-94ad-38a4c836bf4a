import { createAsyncThunk } from '@reduxjs/toolkit';
import { setUserPermissionRedux } from '../userReducer';
import { API_URL } from '@/api';

export const fetchIsManagerBySocietyId = createAsyncThunk(
  'isManagerBySocietyId/fetchData',
  async ({ id }: { id: string },
    { dispatch }) => {
    try {
      const response = await fetch(`${API_URL}/society/isManageAuthorized?societyId=${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
      const data = await response.json();
      dispatch(setUserPermissionRedux(data?.data));

    } catch (error: any) {
      console.log('erorr in fetchIsManagerBySocietyId', error)
    }
  }
);
