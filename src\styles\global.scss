@import "@fontsource/epilogue/latin.css";
@import "leaflet/dist/leaflet.css";

:root {
  --primary-color: #0CA6A6; /* MUI default primary */
  --secondary-color: #28a7a7; /* MUI default secondary */
  --text-grey: #666666;
  --text-light-grey: #A5ACB8;
  --text-grey-disabled: #B3B3B3;
  --border-grey: #DADADA;
  --light-btn: #F4F4F4;
  --error: #FF0000;
  --indicator-yellow: #FFD100;
  --indicator-grey: #848484;
  --success: #00B69B;
  --link: #402DFF;
  --myDigitalId: #1C4283;
  --disabled-field: #E8E9E8;
}

body {
  overflow: hidden !important;
}

.MuiPaper-root {
  border-radius: 15px !important;
}

::-webkit-scrollbar {
  width: 5px;
  height: 4px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--secondary-color);
}

::-webkit-scrollbar-track {
  background: #dadada;
  border-radius: 4px;
}

.mainTitle {
  color: var(--primary-color)!important;
  line-height: 21px !important;
  font-weight: 500!important;
  font-family: 'Poppins';
  font-size: 20px !important;
}

.title {
  color: var(--primary-color);
  line-height: 21px !important;
  font-weight: 500!important;
  font-family: 'Poppins';
  font-size: 16px !important;
}

.title-no-height {
  color: var(--primary-color);
  font-weight: 500!important;
  font-family: 'Poppins';
  font-size: 16px !important;
}

.sub-title {
  color: var(--primary-color);
  line-height: 21px;
  font-weight: 500!important;
  font-family: 'Poppins';
  font-size: 14px !important;
}

.sub-title-notification {
  color: var(--text-light-grey);
  font-weight: 500!important;
  font-family: 'Poppins';
  font-size: 14px !important;
}

.label{
  color: var(--text-grey);
  line-height: 18px !important;
  font-weight: 400!important;
  font-family: "Poppins, sans-serif";
  font-size: 14px !important;
}

.title-login{
 font-family: "Poppins", sans-serif;
  font-size: 30px !important;
  font-weight: 600 !important;
  line-height: 24px;
  color: #55556D;
}

.step-header-login{
  font-family: "Poppins, sans-serif";
  color: var(--text-grey);
  margin-bottom: 1;
  font-size: 13px !important;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
}

.step-header-login-disabled{
  font-family: "Poppins, sans-serif";
  color: var(--text-grey-disabled);
  margin-bottom: 1;
  font-size: 13px !important;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
}

.sub-step-login{
  font-family: "Poppins, sans-serif";
  color: var(--primary-color);
  line-height: 18px !important;
  font-weight: 500!important;
  font-size: 15px !important;
}

.sub-step-login-disabled{
  font-family: "Poppins, sans-serif";
  color: var(--text-grey-disabled);
  line-height: 18px !important;
  font-weight: 500!important;
  font-size: 15px !important;
}

.label-login{
  color: var(--text-grey);
  margin-bottom: 1;
  font-family: "Poppins, sans-serif";
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
}

.error-text-login{
  color: var(--error);
  margin-bottom: 1;
  font-family: "Poppins, sans-serif";
  font-size: 12px !important;
  line-height: 21px;
  font-weight: 400 !important;
  text-align: center;
}

.btn-login{
  font-family: "Poppins !important, sans-serif";
  padding: 11px 16px 11px 16px !important;
  border-radius: 10px !important;
  font-weight: 400 !important;
  font-size: 14px;
  color: #fff !important;

}

.MuiDataGrid-root {
  border: none !important;
}

.MuiDataGrid-columnHeader {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #666666 !important;
}

.MuiDataGrid-cell {
  color: #666666 !important;
  font-size: 14px !important;
  white-space: normal !important;
  word-wrap: break-word !important;
}

.hover-card {
  background: none;
  border-radius: 15px;
  transition: all 0.3s ease-in-out;
}

.hover-card:hover {
  transform: scale(1.02);
  box-shadow: 6px 16px 16px rgba(211, 211, 211, 0.8);
}

.label-dashboard{
  color: var(--text-grey);
  font-weight: 500 !important;
  font-family: "Poppins, sans-serif";
  font-size: 14px !important;
}
.sub-label-dashboard{
  color: var(--text-grey);
  font-weight: 300 !important;
  font-family: "Poppins, sans-serif";
  font-size: 12px !important;
}

.status-pertubuhan-text{
  color: var(--text-grey);
  font-weight: 500 !important;
  font-family: "Poppins, sans-serif";
  font-size: 12px !important;
  padding: 0.3rem 0.5rem;
  text-align: "center";
  border-radius: 50px;
  min-width: 100px;
}

.my-map-modal{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
  height: 800px;
}

.checkboxStyled {
  width: 20px;
  height: 20px;
  border: 1px solid var(--indicator-grey);
  border-radius: 5px;
  appearance: none;
  cursor: pointer;
}

.checkboxStyled:checked {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.checkboxStyled:checked::before {
  content: "✔";
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

@media screen and (max-width: 959px) {
  .layout-container {
    width: auto;
    max-width: 576px;
    margin: 0 auto;
  }
}
@media screen and (max-width: 576px) {
  .layout-container {
    padding-left: 10px;
    padding-right: 10px;
  }
}

/* All snackbars (POP NOTIFICATION FOR SUCCESS AND ERROR AFTER API CALL) */

/* Success variant */
.SnackbarItem-variantSuccess {
  // background-color: #4caf50 !important;
  color: #fff !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  letter-spacing: 0.5px !important;
}

// /* Error variant */
// .SnackbarItem-variantError {
//   background-color: #f44336 !important;
//   color: #fff !important;
// }


// Quill Rick Text handling
.ql-align-center {
  text-align: center;
}

.ql-align-right {
  text-align: right;
}

.ql-align-justify {
  text-align: justify;
}

//colors from inline to class
/* Text Color Classes */
.ql-color-000000 { color: #000000 !important; }
.ql-color-434343 { color: #434343 !important; }
.ql-color-666666 { color: #666666 !important; }
.ql-color-999999 { color: #999999 !important; }
.ql-color-cccccc { color: #cccccc !important; }
.ql-color-efefef { color: #efefef !important; }
.ql-color-ffffff { color: #ffffff !important; }
.ql-color-980000 { color: #980000 !important; }
.ql-color-ff0000 { color: #ff0000 !important; }
.ql-color-ff9900 { color: #ff9900 !important; }
.ql-color-ffff00 { color: #ffff00 !important; }
.ql-color-00ff00 { color: #00ff00 !important; }
.ql-color-00ffff { color: #00ffff !important; }
.ql-color-4a86e8 { color: #4a86e8 !important; }
.ql-color-0000ff { color: #0000ff !important; }
.ql-color-9900ff { color: #9900ff !important; }
.ql-color-ff00ff { color: #ff00ff !important; }
.ql-color-ffcccc { color: #ffcccc !important; }
.ql-color-ffcc99 { color: #ffcc99 !important; }
.ql-color-ffffcc { color: #ffffcc !important; }
.ql-color-ccffcc { color: #ccffcc !important; }
.ql-color-ccffff { color: #ccffff !important; }
.ql-color-cfe2f3 { color: #cfe2f3 !important; }
.ql-color-d9d2e9 { color: #d9d2e9 !important; }
.ql-color-ead1dc { color: #ead1dc !important; }
.ql-color-dd7e6b { color: #dd7e6b !important; }
.ql-color-ea9999 { color: #ea9999 !important; }
.ql-color-f9cb9c { color: #f9cb9c !important; }
.ql-color-ffe599 { color: #ffe599 !important; }
.ql-color-b6d7a8 { color: #b6d7a8 !important; }
.ql-color-a2c4c9 { color: #a2c4c9 !important; }
.ql-color-a4c2f4 { color: #a4c2f4 !important; }
.ql-color-b4a7d6 { color: #b4a7d6 !important; }
.ql-color-d5a6bd { color: #d5a6bd !important; }
.ql-color-e69138 { color: #e69138 !important; }

/* Background Color Classes */
.ql-bg-000000 { background-color: #000000 !important; }
.ql-bg-434343 { background-color: #434343 !important; }
.ql-bg-666666 { background-color: #666666 !important; }
.ql-bg-999999 { background-color: #999999 !important; }
.ql-bg-cccccc { background-color: #cccccc !important; }
.ql-bg-efefef { background-color: #efefef !important; }
.ql-bg-ffffff { background-color: #ffffff !important; }
.ql-bg-980000 { background-color: #980000 !important; }
.ql-bg-ff0000 { background-color: #ff0000 !important; }
.ql-bg-ff9900 { background-color: #ff9900 !important; }
.ql-bg-ffff00 { background-color: #ffff00 !important; }
.ql-bg-00ff00 { background-color: #00ff00 !important; }
.ql-bg-00ffff { background-color: #00ffff !important; }
.ql-bg-4a86e8 { background-color: #4a86e8 !important; }
.ql-bg-0000ff { background-color: #0000ff !important; }
.ql-bg-9900ff { background-color: #9900ff !important; }
.ql-bg-ff00ff { background-color: #ff00ff !important; }
.ql-bg-ffcccc { background-color: #ffcccc !important; }
.ql-bg-ffcc99 { background-color: #ffcc99 !important; }
.ql-bg-ffffcc { background-color: #ffffcc !important; }
.ql-bg-ccffcc { background-color: #ccffcc !important; }
.ql-bg-ccffff { background-color: #ccffff !important; }
.ql-bg-cfe2f3 { background-color: #cfe2f3 !important; }
.ql-bg-d9d2e9 { background-color: #d9d2e9 !important; }
.ql-bg-ead1dc { background-color: #ead1dc !important; }
.ql-bg-dd7e6b { background-color: #dd7e6b !important; }
.ql-bg-ea9999 { background-color: #ea9999 !important; }
.ql-bg-f9cb9c { background-color: #f9cb9c !important; }
.ql-bg-ffe599 { background-color: #ffe599 !important; }
.ql-bg-b6d7a8 { background-color: #b6d7a8 !important; }
.ql-bg-a2c4c9 { background-color: #a2c4c9 !important; }
.ql-bg-a4c2f4 { background-color: #a4c2f4 !important; }
.ql-bg-b4a7d6 { background-color: #b4a7d6 !important; }
.ql-bg-d5a6bd { background-color: #d5a6bd !important; }
.ql-bg-e69138 { background-color: #e69138 !important; }
