import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Card,
} from "@mui/material";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { useForm } from "@refinedev/react-hook-form";
import DataTable, { IColumn } from "@/components/datatable";
import {
  capitalizeWords,
  MALAYSIA,
  PaymentTypeList,
  PermissionNames,
  pageAccessEnum,
} from "@/helpers";
import { SearchIcon } from "@/components/icons";
import FilterBar from "@/components/filter";
import { useSelector } from "react-redux";
import AuthHelper from "@/helpers/authHelper";

const PembayaranKaunter: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [paymentList, setPaymentList] = useState<any>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const { watch, setValue } = useForm({
    defaultValues: {
      organizationName: "",
      page: 1,
      rowsPerPage: 10,
      jenisPembayaran: "",
      negeri: "",
    },
  });

  const hasKaunterUpdatePermission = AuthHelper.hasAuthority([
    `${PermissionNames.MEJA_BANTUAN1.label}:${pageAccessEnum.Update}`,
    `${PermissionNames.MEJA_BANTUAN2.label}:${pageAccessEnum.Update}`,
    `${PermissionNames.MEJA_BANTUAN3.label}:${pageAccessEnum.Update}`,
  ]);

  const { mutate: fetchPaymentList, isLoading: paymentListDataIsLoading } =
    useCustomMutation();

  const handleFetchPaymentList = () => {
    fetchPaymentList(
      {
        url: `${API_URL}/society/payment/getPaymentList`,
        method: "post",
        values: {
          searchQuery: watch("organizationName"),
          paymentId: null,
          paymentStatus: 2, // 1 - PAID , 2 - UNPAID, 3 - CANCEL - 3, 4 - FAILED
          paymentMethod: "KAUNTER",
          page: watch("page"),
          size: watch("rowsPerPage"),
          paymentType: watch("jenisPembayaran"),
          stateCode: watch("negeri"),
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        },
      },
      {
        onSuccess: (data) => {
          const alldata = data?.data?.data;
          const total = alldata?.total;
          const transformData = alldata?.data?.map((item: any) => ({
            ...item,
            bayaranDiterima: false,
          }));

          setPaymentList(transformData);
          setTotalRecords(total);
        },
      }
    );
  };

  // const handleCheckboxChange = (index: any) => {
  //   setPaymentList((prevList: any) =>
  //     prevList.map((item: any, i: any) =>
  //       i === index ? { ...item, bayaranDiterima: !item.bayaranDiterima } : item
  //     )
  //   );
  // };

  // @ts-ignore
  const addressDataRedux = useSelector((state) => state?.addressData?.data);
  const StateList = addressDataRedux
    ?.filter((item: any) => item.pid === MALAYSIA)
    ?.map((item: any) => ({ id: item.id, value: item.name }));

  useEffect(() => {
    handleFetchPaymentList();
  }, []);

  const handleChangePage = (newPage: number) => {
    setValue("page", newPage);
    handleFetchPaymentList();
  };

  const handleRowPerPage = (newPageSize: number) => {
    setValue("rowsPerPage", newPageSize);
    handleFetchPaymentList();
  };

  const filterOptions = {
    jenisPembayaran: PaymentTypeList?.map(({ id, value }) => ({
      value: value,
      label: value,
    })),
    // @ts-ignore
    negeri: StateList?.map(({ id, value }) => ({
      value: id,
      label: value,
    })),
  };

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    jenisPembayaran: "jenisPembayaran",
    negeri: "negeri",
  });

  const onFilterChange = (filter: string, value: any) => {
    setValue("page", 1);
    switch (filter) {
      case "jenisPembayaran":
        setValue("jenisPembayaran", value);
        break;
      case "negeri":
        setValue("negeri", value);
        break;
    }
    handleFetchPaymentList();
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      setValue("page", 1);
      const value = (event.target as HTMLInputElement).value;
      setValue("organizationName", value);
      handleFetchPaymentList();
    }
  };

  const columnsViewData: IColumn[] = [
    // {
    //   field: "select",
    //   headerName: t("bayaranDiterima"),
    //   flex: 1,
    //   align: "center",
    //   renderCell: (params: any) => {
    //     const row = params?.row;
    //     return (
    //       <FormControlLabel
    //         label=""
    //         control={
    //           <Checkbox
    //             checked={row.bayaranDiterima}
    //             onChange={() => handleCheckboxChange(params?.rowIndex)}
    //           />
    //         }
    //       />
    //     );
    //   },
    // },
    {
      field: "icNo",
      headerName: t("noPengenalanDiriPemohon"),
      align: "center",
      flex: 1,
    },
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      align: "center",
      flex: 1,
    },
    {
      field: "paymentType",
      headerName: t("jenisPembayaran"),
      align: "center",
      flex: 1,
    },
    {
      field: "transactionId",
      headerName: t("paymentReferenceNumber"),
      align: "center",
      flex: 1,
    },
    {
      field: "stateCode",
      headerName: t("state"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        const state = StateList?.find(
          (item: any) => item?.id === Number(row?.stateCode)
        );
        return (
          <p
            style={{ fontSize: 14, color: "var(--text-grey)", fontWeight: 400 }}
          >
            {state?.value ? capitalizeWords(state?.value) : "-"}
          </p>
        );
      },
    },
    {
      field: "actions",
      align: "center",
      headerName: "",
      flex: 1,
      renderCell: (params: any) => {
        return (
          hasKaunterUpdatePermission && (
            <Box>
              <IconButton
                onClick={() => navigate("kemaskini", { state: params?.row })}
              >
                <img
                  src={"/editIcon.svg"}
                  alt="Filter Icon"
                  width="14"
                  height="14"
                />
              </IconButton>
            </Box>
          )
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          display: "flex",
          gap: "20px",
          minHeight: "calc(100vh - 60px - 83px)",
        }}
      >
        <Box
          sx={{
            height: "100%",
            flex: 3,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Card
            sx={{
              p: 3,
              borderRadius: "15px",
              boxShadow: "none",
              height: "100%",
              width: "100%",
            }}
          >
            <TextField
              fullWidth
              variant="outlined"
              placeholder={t("namaPertubuhan")}
              sx={{
                display: "block",
                boxSizing: "border-box",
                maxWidth: 570,
                marginInline: "auto",
                height: "40px",
                background: "var(--border-grey)",
                opacity: 0.5,
                border: "1px solid var(--text-grey)",
                borderRadius: "10px",
                "& .MuiOutlinedInput-root": {
                  height: "40px",
                  "& fieldset": {
                    border: "none",
                  },
                },
              }}
              onKeyDown={onSearchKeyDown}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon
                      sx={{
                        color: "var(--text-grey-disabled)",
                        marginLeft: "8px",
                      }}
                    />
                  </InputAdornment>
                ),
              }}
            />

            <FilterBar
              filterOptions={filterOptions}
              onFilterChange={onFilterChange}
              selectedFilters={selectedFilters}
              onSelectedFiltersChange={handleSelectedFiltersChange}
            />
            <Box mt={3}>
              <DataTable
                columns={columnsViewData}
                rows={paymentList ? paymentList : []}
                isLoading={paymentListDataIsLoading}
                page={watch("page")}
                rowsPerPage={watch("rowsPerPage")}
                totalCount={totalRecords}
                onPageChange={handleChangePage}
                onPageSizeChange={(newPageSize) => {
                  setValue("page", 1);
                  handleRowPerPage(newPageSize);
                }}
              />
            </Box>
          </Card>
        </Box>
      </Box>
    </>
  );
};

export default PembayaranKaunter;
