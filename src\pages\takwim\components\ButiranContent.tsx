import {
  Grid,
  <PERSON>,
  Typography,
  Link as <PERSON>i<PERSON>ink,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Tooltip,
} from "@mui/material";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import GroupsIcon from "@mui/icons-material/Groups";
import CustomAvatarGroup from "@/components/AvatarGroup";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import IosShareIcon from "@mui/icons-material/IosShare";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import { useEffect, useState } from "react";
import QRModal from "./QRModal";
import { IEvent, IEventOrganiser } from "@/types/event";
import { formatTimeWithPeriod } from "@/helpers/timeUtils";
import { IEventAttendee } from "@/types/eventAttendee";
import dayjs from "dayjs";
import { t } from "i18next";
import { useGetIdentity } from "@refinedev/core";
import { IIdentity } from "@/components/header/header-sidebar-authenticated";
import { useTakwim } from "@/contexts/takwimProvider";
import { useNavigate } from "react-router-dom";
import { eventService } from "@/services/eventService";
import { useNotification } from "@refinedev/core";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import AuthHelper from "@/helpers/authHelper";
import { PermissionNames, pageAccessEnum } from "@/helpers/enums";
import { CalendarEvent } from "calendar-link";
import LocalPhoneIcon from "@mui/icons-material/LocalPhone";

interface ButiranContentProps {
  data: IEvent | undefined;
  mainUser: IEventOrganiser | null;
  members: IEventAttendee[];
  // user?: { id: number | string } | null;
  setEvent: React.Dispatch<React.SetStateAction<IEvent | null>>;
}

const ButiranContent = ({
  data,
  mainUser,
  members,
  setEvent,
}: // user,
ButiranContentProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [qrModalOpen, setQrModalOpen] = useState(false);
  const [qrType, setQrType] = useState<"kedatangan" | "maklumbalas">(
    "kedatangan"
  );
  const [publishDialogOpen, setPublishDialogOpen] = useState(false);
  const [publishAction, setPublishAction] = useState<"publish" | "unpublish">(
    "publish"
  );
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const open = Boolean(anchorEl);
  const { data: user } = useGetIdentity<IIdentity>();
  const { isEventAdmin, setIsEventEnded, isEventEnded } = useTakwim();
  const [isPublishingLoading, setIsPublishingLoading] = useState(false);
  const navigate = useNavigate();
  const { open: openNotification } = useNotification();
  const [calendarAnchorEl, setCalendarAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const calendarMenuOpen = Boolean(calendarAnchorEl);

  // Add permission checks
  const userPermission = useSelector(getUserPermission);

  // Check if user has permission to edit events
  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      PermissionNames.TAKWIM?.label || "TAKWIM-AKT",
      accessType
    );
    // 2 is internal user
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }
  const hasEditEventPermission = checkPermissionAndUserGroup(pageAccessEnum.Update);

  // Check if user has permission to publish events

  // Check if user is the event creator or has admin permissions

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleQROption = (type: "kedatangan" | "maklumbalas") => {
    setQrType(type);
    setQrModalOpen(true);
    handleClose();
  };

  const calculateDuration = (data: IEvent | undefined) => {
    if (!data?.eventStartDate || !data?.eventEndDate) return null;

    const startDate = dayjs(data.eventStartDate);
    const endDate = dayjs(data.eventEndDate);

    // Check if same day
    if (startDate.isSame(endDate, "day")) {
      if (!data.startTime || !data.endTime) return null;

      const [startHour, startMinute] = data.startTime.split(":").map(Number);
      const [endHour, endMinute] = data.endTime.split(":").map(Number);

      const start = dayjs(data.eventStartDate)
        .hour(startHour)
        .minute(startMinute);
      const end = dayjs(data.eventEndDate).hour(endHour).minute(endMinute);

      const hoursDiff = end.diff(start, "hour", true);
      return `${Math.round(hoursDiff)} ${t("jam")}`;
    } else {
      // Different days
      const daysDiff = endDate.diff(startDate, "day");
      return `${daysDiff} ${t("day")}`;
    }
  };

  const handlePublishClick = () => {
    if (data?.published) {
      setPublishAction("unpublish");
    } else {
      setPublishAction("publish");
    }
    setPublishDialogOpen(true);
  };

  const handlePublish = async (eventNo: string | undefined): Promise<void> => {
    try {
      setPublishDialogOpen(false);
      setIsPublishingLoading(true);
      const result = await eventService.publishEvent(eventNo);

      if (result.code == 200) {
        openNotification?.({
          message:
            publishAction === "publish"
              ? "Acara berjaya diterbitkan."
              : "Acara berjaya dinyahterbit.",
          type: "success",
        });

        setEvent((prevEvent) =>
          prevEvent ? { ...prevEvent, published: !prevEvent.published } : null
        );
      }
    } catch (error) {
      openNotification?.({
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        type: "error",
      });
    } finally {
      setIsPublishingLoading(false);
    }
  };

  const handleAddToCalendarClick = (event: React.MouseEvent<HTMLElement>) => {
    setCalendarAnchorEl(event.currentTarget);
  };

  const handleCalendarMenuClose = () => {
    setCalendarAnchorEl(null);
  };

  const addToCalendar = async (
    calendarType: "google" | "outlook" | "office365" | "yahoo" | "ics"
  ) => {
    try {
      const { google, outlook, office365, yahoo, ics } = await import(
        "calendar-link"
      );

      if (!data?.eventStartDate || !data?.eventEndDate) {
        openNotification?.({
          message: "Event dates are missing",
          type: "error",
        });
        return;
      }

      const event: CalendarEvent = {
        title: data.eventName || "Event",
        description: data.description || "",
        start: data.startTime
          ? `${data.eventStartDate}T${data.startTime}`
          : data.eventStartDate,
        end: data.endTime
          ? `${data.eventEndDate}T${data.endTime}`
          : data.eventEndDate,
        location: data.venue || "",
      };

      let calendarUrl;
      switch (calendarType) {
        case "google":
          calendarUrl = google(event);
          break;
        case "outlook":
          calendarUrl = outlook(event);
          break;
        case "office365":
          calendarUrl = office365(event);
          break;
        case "yahoo":
          calendarUrl = yahoo(event);
          break;
        case "ics":
          calendarUrl = ics(event);
          break;
        default:
          calendarUrl = google(event);
      }

      window.open(calendarUrl, "_blank", "noopener,noreferrer");
      handleCalendarMenuClose();
    } catch (error) {
      openNotification?.({
        message:
          error instanceof Error
            ? error.message
            : "Failed to generate calendar link",
        type: "error",
      });
    }
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteEvent = async () => {
    try {
      if (!data?.eventNo) return;

      const result = await eventService.deleteEvent(data.eventNo);

      if (result.code === 200) {
        openNotification?.({
          message: "Acara berjaya dipadam.",
          type: "success",
        });
        // Navigate back to events list
        navigate("/takwim/activity");
      }
    } catch (error) {
      openNotification?.({
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
        type: "error",
      });
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  return (
    <>
      <Grid sx={{ display: "grid", gap: 3 }}>
        <Grid sx={{ display: "flex", justifyContent: "space-between" }}>
          <CustomAvatarGroup
            organizer={
              mainUser
                ? { ...mainUser, name: mainUser.name }
                : { id: 0, name: "" }
            }
            participants={members.map((member) => ({
              name: member.fullName.toUpperCase(),
              avatar: "",
            }))}
            picPhoneNumber={data?.picContactNo}
          />
          <Box sx={{ display: "flex", gap: 1 }}>
            {user?.id && hasEditEventPermission && (
              <>
                {!isEventEnded && (
                  <>
                    {/* STATUS BUTTON */}
                    {!isPublishingLoading ? (
                      <Tooltip
                        title={
                          data?.published
                            ? "Menyahterbitkan Acara"
                            : "Terbitkan Acara"
                        }
                      >
                        <Button
                          variant="outlined"
                          sx={{
                            minWidth: "40px",
                            height: "40px",
                            color: data?.published ? "primary.main" : "#FFD100",
                            borderColor: data?.published
                              ? "primary.main"
                              : "#FFD100",
                          }}
                          onClick={handlePublishClick}
                        >
                          {data?.published ? "Aktif" : "Draft"}
                        </Button>
                      </Tooltip>
                    ) : (
                      <Button
                        variant="text"
                        sx={{
                          minWidth: "40px",
                          height: "40px",
                          // color: data?.published ? "primary.main" : "#FFD100",
                        }}
                      >
                        <CircularProgress
                          size={20} // You can adjust this number (in pixels)
                          sx={{ color: "#0CA6A6" }}
                        />
                      </Button>
                    )}
                    {/* EDIT BUTTON */}
                    <Tooltip title="Kemaskini Acara">
                      <Button
                        onClick={() =>
                          navigate(`/takwim/edit-event/${data?.eventNo}`)
                        }
                        sx={{
                          minWidth: "40px",
                          height: "40px",
                          backgroundColor: "#E9D8FD",
                          borderRadius: "8px",
                          "&:hover": {
                            backgroundColor: "#D6BCFA",
                          },
                        }}
                      >
                        <EditOutlinedIcon sx={{ color: "#805AD5" }} />
                      </Button>
                    </Tooltip>
                  </>
                )}
                {/* SHARE BUTTON */}
                {data?.hasMax && (
                  <>
                    <Tooltip title="Jana QR dan Pautan">
                      <Button
                        onClick={handleClick}
                        sx={{
                          minWidth: "40px",
                          height: "40px",
                          backgroundColor: "#B2EBEB",
                          borderRadius: "8px",
                          "&:hover": {
                            backgroundColor: "#99E6E6",
                          },
                        }}
                      >
                        <IosShareIcon sx={{ color: "#319795" }} />
                      </Button>
                    </Tooltip>

                    <Menu
                      anchorEl={anchorEl}
                      open={open}
                      onClose={handleClose}
                      anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "right",
                      }}
                      transformOrigin={{
                        vertical: "top",
                        horizontal: "right",
                      }}
                    >
                      <MenuItem
                        sx={{
                          pointerEvents: "none",
                          color: "#666666",
                          fontWeight: 600,
                          "&:hover": {
                            backgroundColor: "transparent",
                          },
                        }}
                      >
                        Kod QR
                      </MenuItem>
                      <MenuItem onClick={() => handleQROption("kedatangan")}>
                        Kedatangan
                      </MenuItem>
                      <MenuItem onClick={() => handleQROption("maklumbalas")}>
                        Maklumbalas
                      </MenuItem>
                    </Menu>
                  </>
                )}
                {!isEventEnded && (
                  <>
                    {/* DELETE BUTTON */}
                    <Tooltip title="Hapus Acara">
                    <Button
                      onClick={handleDeleteClick}
                      sx={{
                        minWidth: "40px",
                        height: "40px",
                        backgroundColor: "#FFCCCC",
                        borderRadius: "8px",
                        "&:hover": {
                          backgroundColor: "#FFB3B3",
                        },
                      }}
                    >
                      <DeleteOutlineIcon sx={{ color: "#E53E3E" }} />
                    </Button>
                    </Tooltip>
                  </>
                )}
              </>
            )}
          </Box>
        </Grid>
        <Grid
          container
          sx={{ mb: 4, color: "#666666", display: "grid", gap: 3 }}
        >
          <Grid item xs={12} sm={6} display="flex" gap={2}>
            <CalendarTodayIcon />
            <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
              <Box sx={{ display: "grid" }}>
                <Typography variant="body1">
                  {dayjs(data?.eventStartDate).locale("ms").format("dddd")},{" "}
                  {dayjs(data?.eventStartDate).format("DD MMMM YYYY")},{" "}
                  {data?.startTime ? formatTimeWithPeriod(data.startTime) : ""}
                </Typography>
                <Typography variant="body2" className="detail-sub-title">
                  {t("Durasi")}: {calculateDuration(data) || "-"}
                </Typography>
                <>
                  <MuiLink
                    className="calendar-add-link"
                    onClick={handleAddToCalendarClick}
                    sx={{ cursor: "pointer" }}
                  >
                    Tambah Ke kalendar
                  </MuiLink>
                  <Menu
                    anchorEl={calendarAnchorEl}
                    open={calendarMenuOpen}
                    onClose={handleCalendarMenuClose}
                    anchorOrigin={{
                      vertical: "bottom",
                      horizontal: "left",
                    }}
                    transformOrigin={{
                      vertical: "top",
                      horizontal: "left",
                    }}
                  >
                    <MenuItem onClick={() => addToCalendar("google")}>
                      Google Calendar
                    </MenuItem>
                    <MenuItem onClick={() => addToCalendar("outlook")}>
                      Outlook
                    </MenuItem>
                    <MenuItem onClick={() => addToCalendar("office365")}>
                      Office 365
                    </MenuItem>
                    <MenuItem onClick={() => addToCalendar("yahoo")}>
                      Yahoo
                    </MenuItem>
                    <MenuItem onClick={() => addToCalendar("ics")}>
                      Apple/iOS Calendar (.ics)
                    </MenuItem>
                  </Menu>
                </>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
              <LocationOnIcon />
              <Box>
                {/* <Typography variant="body1">{data?.venue}asasas</Typography> */}
                <Typography variant="body1">{data?.venue}</Typography>
                <Typography variant="body1" className="detail-sub-title">
                  {data?.address1}, {data?.address2}
                </Typography>
                <MuiLink
                  className="calendar-add-link"
                  href={data?.mapUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Papar dalam maps
                </MuiLink>
              </Box>
            </Box>
          </Grid>

          {data?.collaboratorName && (
            <Grid item xs={12}>
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <GroupsIcon />
                <Box>
                  <Typography variant="body1">
                    {data?.collaboratorName}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          )}

          {data?.picContactNo && (
            <Grid item xs={12}>
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <LocalPhoneIcon />
                <Box>
                  <Typography variant="body1">{data?.picContactNo}</Typography>
                </Box>
              </Box>
            </Grid>
          )}
        </Grid>

        <Grid>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Deskripsi acara
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: "#666",
              mb: 4,
              whiteSpace: "pre-wrap", // <-- This preserves all spaces, tabs, and line breaks
              fontFamily: "inherit", // Optional: use a monospace font for clearer tabs
            }}
          >
            {data?.description}
          </Typography>
        </Grid>
      </Grid>
      <QRModal
        open={qrModalOpen}
        onClose={() => setQrModalOpen(false)}
        type={qrType}
        title={data?.eventName || ""}
        eventNo={data?.eventNo || ""}
      />
      <Dialog
        open={publishDialogOpen}
        onClose={() => setPublishDialogOpen(false)}
        PaperProps={{
          style: {
            borderRadius: "8px",
            padding: "16px",
          },
        }}
      >
        <DialogTitle sx={{ textAlign: "center" }}>
          {publishAction === "publish"
            ? "Pengesahan Penerbitan"
            : "Pengesahan Nyahterbit"}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {publishAction === "publish"
              ? "Adakah anda pasti untuk menerbitkan acara ini?"
              : "Adakah anda pasti untuk nyahterbit acara ini?"}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", gap: 2 }}>
          <Button
            variant="outlined"
            onClick={() => setPublishDialogOpen(false)}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={() => handlePublish(data?.eventNo)}
            sx={{
              bgcolor: "#4DB6AC",
              "&:hover": { bgcolor: "#3d8f86" },
            }}
          >
            {publishAction === "publish" ? "Terbit" : "Nyahterbit"}
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        PaperProps={{
          style: {
            borderRadius: "8px",
            padding: "16px",
          },
        }}
      >
        <DialogTitle sx={{ textAlign: "center" }}>
          Pengesahan Pemadaman
        </DialogTitle>
        <DialogContent>
          <Typography>Adakah anda pasti untuk memadam acara ini?</Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", gap: 2 }}>
          <Button variant="outlined" onClick={() => setDeleteDialogOpen(false)}>
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleDeleteEvent}
            sx={{
              bgcolor: "#E53E3E",
              "&:hover": { bgcolor: "#C53030" },
            }}
          >
            Padam
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ButiranContent;
