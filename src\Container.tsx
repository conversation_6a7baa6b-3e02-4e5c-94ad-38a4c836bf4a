import { useOutlet } from "react-router-dom";
import { ChatbotChatArea } from "./components/chatbot/ChatArea";
import { useChatbotContext } from "./contexts/chatbot";
import { useMediaQuery, useTheme } from "@mui/material";

export const Container = () => {
  const theme = useTheme();
  const outlet = useOutlet();
  const { isChatbotOpenedTablet } = useChatbotContext();
  const matchMobileSize = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <div style={{
      width: "100vw",
      height: "100vh",
      overflow: "hidden",
      display: "flex"
    }}>
      <div
        style={{
          width: `${isChatbotOpenedTablet ? 80 : 100}%`,
          height: "100vh",
          overflow: "auto",
          transition: "width 0.3s ease-out"
        }}>
        {outlet}
      </div>
      <div
        id="chatbot-chat-area"
        style={{
          width: matchMobileSize
            ? "0"
            : !isChatbotOpenedTablet
              ? "0"
              : "20%",
          height: "100vh",
          position: "relative",
          overflow: "hidden",
          ...(matchMobileSize && {
            display: "none"
          }),
          opacity: isChatbotOpenedTablet ? 1 : 0,
          transform: `translateX(${isChatbotOpenedTablet ? '0' : '100%'})`,
          transition: "opacity 0.3s ease-out, transform 0.3s ease-out",
          visibility: isChatbotOpenedTablet ? 'visible' : 'hidden',
        }}>
        <ChatbotChatArea />
      </div>
    </div>
  )
}
