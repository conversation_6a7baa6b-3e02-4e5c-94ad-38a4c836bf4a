import React, { useState } from "react";
import {
  Box,
  TextField,
  Typography,
  Grid,
  FormControl,
  useMediaQuery,
  Theme,
  Fade,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import {
  ButtonOutline,
  ButtonPrimary,
  Disabled<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TextFieldController,
} from "@/components";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { set } from "lodash";
import { FieldValues, useForm, Resolver } from "react-hook-form";
import { number, object, string } from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { API_URL } from "@/api";
import CustomPopover from "@/components/popover";

const AmendmentName = ({
  onUpdate,
  disableNext,
  currentData,
  isView,
}: {
  onUpdate: (data: any) => void;
  disableNext: boolean;
  currentData: any;
  isView: boolean;
}) => {
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { id } = useParams();
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const branchAmendRedux = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.data
  );

  const [bName, setBName] = useState("");
  const [pass, setPass] = useState(true);

  const resolver = yupResolver(
    object()
      .shape({
        branchName: string()
          .label(t("namaCawangan"))
          // .test({
          //   name: "branch_name_validation",
          //   message: t("branchNameHelper1"),
          //   test: (name, context) => {
          //     if (!name) {
          //       return context.createError({ message: t("fieldRequired") });
          //     }

          //     if (!name || name.length <= 6) {
          //       return false;
          //     }

          //     return ["cawangan ", "bahagian ", "negeri ", "daerah "].some(
          //       (prefix) => name.toLowerCase().startsWith(prefix)
          //     );
          //   },
          // })
          .test({
            name: "branch_name_exist",
            message: t("organizationNamePlaceholder"),
            test: async function (name) {
              const trimmedName = name?.trim().toLowerCase();

              // Skip API call if name is too short
              if (!trimmedName || trimmedName.length <= 6) {
                return true;
              }

              // Skip API call if name hasn't changed
              if (bName === trimmedName) {
                return pass;
              }

              try {
                const response = await fetch(
                  `${API_URL}/society/branch/checkBranchNameExist?branchName=${trimmedName}&societyId=${currentData?.societyId}`,
                  {
                    method: "GET",
                    headers: {
                      portal: localStorage.getItem("portal") || "",
                      Authorization: `Bearer ${localStorage.getItem(
                        "refine-auth"
                      )}`,
                    },
                  }
                );

                if (response.ok) {
                  const data = await response.json();
                  console.log("data?.data", data?.data);
                  // If data exists, the name is taken (return error)
                  if (!data?.data) {
                    setBName(trimmedName);
                    setPass(false);
                    return this.createError({
                      message: t("organizationNamePlaceholder"),
                    });
                  }
                  // If data doesn't exist, the name is available

                  setBName(trimmedName);
                  setPass(true);
                  return true;
                }

                // If response not OK, consider invalid

                setBName(trimmedName);
                setPass(false);
                return false;
              } catch (e) {
                setBName(trimmedName);
                setPass(false);
                return this.createError({ message: t("error") });
              }
            },
          })
          .required(),
      })
      .required()
  ) as unknown as Resolver<FieldValues>;

  const {
    control,
    handleSubmit,
    getValues,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FieldValues>({
    mode: "onChange",
    defaultValues: {
      branchName: currentData?.branchName || "",
    },
    resolver,
  });

  const updateHandler = (data: FieldValues) => {
    onUpdate({ branchName: data.branchName, amendmentType: "pinda_nama" });
  };

  const goNext = () => {
    navigate(`/pertubuhan/society/${id}/senarai/cawangan/branch-Info/bayaran`);
  };

  const goBack = () => {
    navigate(-1);
  };

  const resetAll = () => {
    setValue("branchName", "");
  };

  return (
    <Fade in={true} timeout={500}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mt: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("maklumatAsalCawangan")}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>{t("namaAsal")}</Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <DisabledTextField
                value={branchAmendRedux?.currentBranchName ?? "-"}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("changeBranchName")}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("branchNameDetails1")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <CustomPopover
                  customStyles={{
                    maxWidth: "210px",
                    backgroundColor: "white",
                    mt: 1,
                  }}
                  content={
                    <Typography
                      sx={{
                        color: "#FF0000",
                        fontSize: "12px",
                      }}
                    >
                      {t("branchNameHelper1")}
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextFieldController
                control={control}
                name="branchName"
                placeholder="Contoh: Cawangan Perak, Bahagian Perak"
                fullWidth
                required
                disabled={isView}
              />
            </Grid>
          </Grid>

          <Grid
            container
            spacing={2}
            sx={{ display: isView ? "none" : "block" }}
          >
            <Grid
              item
              xs={12}
              sx={{
                mt: 4,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={() => {
                  resetAll();
                }}
              >
                {t("semula")}
              </ButtonOutline>

              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={handleSubmit(updateHandler)}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </Grid>
        </Box>

        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              pr: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            {isView ? (
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={() => goBack()}
              >
                {t("back")}
              </ButtonPrimary>
            ) :  (
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                disabled={disableNext}
                onClick={() => goNext()}
              >
                {t("seterusnya")}
              </ButtonPrimary>
            )}
          </Grid>
        </Grid>
      </Box>
    </Fade>
  );
};

export default AmendmentName;
