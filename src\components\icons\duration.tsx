import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const DurationIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"
         style={{ color, ...sx }}
         {...props}
    >
      <g clipPath="url(#clip0_24590_21395)">
        <path d="M6 11.25C3.105 11.25 0.75 8.895 0.75 6C0.75 3.105 3.105 0.75 6 0.75C8.895 0.75 11.25 3.105 11.25 6C11.25 8.895 8.895 11.25 6 11.25ZM6 1.5C3.5175 1.5 1.5 3.5175 1.5 6C1.5 8.4825 3.5175 10.5 6 10.5C8.4825 10.5 10.5 8.4825 10.5 6C10.5 3.5175 8.4825 1.5 6 1.5Z" fill="#666666"/>
        <path d="M7.50001 7.875C7.43251 7.875 7.36501 7.86 7.30501 7.8225L5.43001 6.6975C5.37472 6.66385 5.32909 6.61646 5.29755 6.55995C5.26601 6.50343 5.24963 6.43972 5.25001 6.375V3.375C5.25001 3.165 5.41501 3 5.62501 3C5.83501 3 6.00001 3.165 6.00001 3.375V6.165L7.69501 7.1775C7.76482 7.22029 7.81878 7.28468 7.84871 7.3609C7.87864 7.43711 7.88291 7.52101 7.86086 7.59987C7.83881 7.67874 7.79165 7.74826 7.72654 7.79791C7.66142 7.84756 7.58189 7.87462 7.50001 7.875Z" fill="#666666"/>
      </g>
      <defs>
        <clipPath id="clip0_24590_21395">
          <rect width="12" height="12" fill="white"/>
        </clipPath>
      </defs>
    </svg>

  );
});
