import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import InputLabel from "@mui/material/InputLabel";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { Stack, Card } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { Select, Option } from "@/components/input";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCreate, useCustom } from "@refinedev/core";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
// import { StepperRegisterCawangan } from "../RegisterCawangan/components/Stepper";
import { useCustomMutation } from "@refinedev/core";
import { ApplicationStatus } from "@/helpers/enums";
import { API_URL } from "@/api";
import { getLocalStorage } from "@/helpers/utils";
import { useSelector } from "react-redux";

function PembayaranCawangan() {
  const branchAmendData = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.data
  );
  const { t } = useTranslation();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const [paymentMethod, setPaymentMethod] = useState("");
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const navigate = useNavigate();

  const [isChecked, setIsChecked] = useState(false);

  // const { mutate: editSocietyPaymentMethod } = useCustomMutation();
  const { mutate: makePayment } = useCustomMutation();

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  //const params = new URLSearchParams(window.location.search);
  const { id: societyId } = useParams();
  //TODO not recommended as local storage will easily override please take note
  const [searchParams] = useSearchParams();

  const { mutate: updateMeetingData, isLoading: isLoadingUpdateMeetingData } =
    useCustomMutation();

  const updateAmendment = async () => {
    updateMeetingData(
      {
        url: `${API_URL}/society/external/branchAmendment/update`,
        method: "patch",
        values: {
          id: branchAmendData?.id,
          applicationStatusCode:
            paymentMethod === "online"
              ? ApplicationStatus.MENUNGGU_BAYARAN_ONLINE
              : ApplicationStatus.MENUNGGU_BAYARAN_KAUNTER,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("error"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          const infoId = data?.data?.id;
          if (paymentMethod === "online") {
            navigate(`online?societyId=${societyId}`);
          } else {
            handleKaunterPayment(infoId);
          }
        },
      }
    );
  };

  const handleKaunterPayment = async (infoId: any) => {
    const getUserDetails = localStorage.getItem("user-details");
    const email = getUserDetails ? JSON.parse(getUserDetails).email : "";
    try {
      const paymentPayload = {
        societyId: societyId,
        branchId: branchAmendData?.branchId,
        branchAmendmentId: branchAmendData?.id,
        amount: 10,
        paymentMethod: "K",
        paymentType: "Pindaan Cawangan (Pembayaran KAUNTER)",
        email: email,
      };
      const makePaymentResponse = await fetch(
        `${API_URL}/society/payment/makePayment`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(paymentPayload),
        }
      );
      if (!makePaymentResponse.ok) {
        const errorText = await makePaymentResponse.text();
        console.error("Error Response Text:", errorText);
        throw new Error(`HTTP error! status: ${makePaymentResponse.status}`);
      }
      const paymentData = await makePaymentResponse.json();
      console.log("Payment Response Data:", paymentData);

      if (paymentData.status === "SUCCESS") {
        navigate(`kaunter?&societyId=${societyId}`);
      } else {
        console.error("Payment request failed:", {
          status: paymentData.status,
          message: paymentData.message,
          data: paymentData.data,
        });
      }
    } catch (error) {
      console.error("Payment error:", error);
    }
  };

  const { data: paymentStatus, isLoading } = useCustom({
    url: `${API_URL}/society/admin/integration/payment/status`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const onlinePaymentEnabled: boolean =
    paymentStatus?.data?.data?.payment?.toLowerCase() == "enabled";
  const alert: boolean = paymentStatus?.data?.data?.alert;

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <Box sx={{ display: "flex" }}>
      {/* <Card
        sx={{
          borderRadius: "16px",
          mr: 3,
          padding: "40px 30px",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
          height: "50%",
          display: "flex",
          width: { xs: "100%", md: "190px" },
          flexDirection: "column",
          flexShrink: 0,
        }}
      >
        <Typography
          sx={{ color: "var(--primary-color)", fontWeight: "400", marginBottom: "16px" }}
        >
          {t("langkahCarianDokumen")}
        </Typography>
        <Stack gap={2}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              marginBottom: "8px",
            }}
          >
            <Box
              sx={{
                width: "16px",
                height: "16px",
                border: "1px solid var(--primary-color)",
                backgroundColor: "var(--primary-color)",
                borderRadius: "4px",
                marginRight: "8px",
                flexShrink: 0,
              }}
            />
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontWeight: "500",
                fontSize: "14px",
              }}
            >
              {t("carianPertubuhanDanMaklumat")}
            </Typography>
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              marginBottom: "8px",
            }}
          >
            <Box
              sx={{
                width: "16px",
                height: "16px",
                border: "1px solid var(--primary-color)",
                backgroundColor: "var(--primary-color)",
                borderRadius: "4px",
                marginRight: "8px",
                flexShrink: 0,
              }}
            />
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontWeight: "500",
                fontSize: "14px",
              }}
            >
              {t("bayaran")}
            </Typography>
          </Box>
        </Stack>
      </Card> */}
      <Box sx={{ display: "flex", gap: 2 }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                  }}
                >
                  {t("payment")}
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontFamily: "Poppins",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: "21px",
                  textAlign: "left",
                  mb: 2,
                }}
              >
                {t("agreementText")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1,
                }}
              >
                <Checkbox
                  id="akuan-setuju-terima"
                  checked={isChecked}
                  onChange={handleCheckboxChange}
                  sx={{
                    color: "#00A7A7",
                    "&.Mui-checked": {
                      color: "#00A7A7",
                    },
                    padding: "0",
                  }}
                />
                <InputLabel
                  htmlFor="akuan-setuju-terima"
                  required
                  sx={{
                    color: "#333333",
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: 1.4,
                    "& .MuiFormLabel-asterisk": {
                      color: "#FF0000",
                    },
                  }}
                >
                  {t("agreementAcceptance")}
                </InputLabel>
              </Box>
            </Box>

            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 3,
              }}
            >
              <Box sx={{ mb: 2 }}>
                <Box sx={{ mb: 1 }}>
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      color: "#00A7A7",
                      fontSize: 16,
                      fontWeight: 600,
                    }}
                  >
                    {t("paymentMethod")}
                  </Typography>
                </Box>
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                >
                  <Grid item xs={4}>
                    {/* <Typography>1</Typography> */}
                  </Grid>
                  <Grid item xs={8}>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        color: "#FF0000",
                        marginLeft: "15px",
                      }}
                    >
                      {alert}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <InputLabel
                      htmlFor="cara-pembayaran"
                      required
                      sx={{
                        color: "#333333",
                        fontSize: 14,
                        fontWeight: 400,
                        minWidth: "150px",
                        "& .MuiFormLabel-asterisk": {
                          color: "#FF0000",
                        },
                      }}
                    >
                      {t("paymentMethod")}
                    </InputLabel>
                  </Grid>
                  <Grid item xs={8}>
                    <Select
                      value={paymentMethod}
                      onChange={handleChange}
                      id="cara-pembayaran"
                      t={t}
                      sx={{
                        width: "100%",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E5E5",
                          borderRadius: 1,
                        },
                      }}
                    >
                      <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                      {onlinePaymentEnabled ? (
                        <Option value="online">{t("pembayaranOnline")}</Option>
                      ) : (
                        <Option value="online" disabled>
                          {t("pembayaranOnline")}
                        </Option>
                      )}
                    </Select>
                  </Grid>
                </Grid>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontSize: 12,
                  marginTop: 10,
                  textAlign: "center",
                }}
              >
                {t("paymentNote")}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <ButtonPrimary
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  color: "white",
                  borderRadius: 1,
                  textTransform: "none",
                }}
                disabled={!isChecked || !paymentMethod}
                onClick={handleSubmit}
              >
                {t("hantar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          PaperProps={{
            style: {
              borderRadius: "8px",
              width: "400px",
              padding: "24px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          }}
        >
          <DialogContent sx={{ textAlign: "center", p: 0, mb: 3 }}>
            <Typography
              sx={{
                fontSize: 16,
                color: "#333333",
                fontWeight: 500,
              }}
            >
              {t("confirmSubmitApplication")}
            </Typography>
          </DialogContent>

          <DialogActions
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 1,
              p: 0,
            }}
          >
            <ButtonPrimary
              onClick={() => {
                handleCloseDialog();
                updateAmendment();
              }}
              sx={{
                color: "white",
              }}
            >
              {t("ya")}
            </ButtonPrimary>

            <Typography
              onClick={handleCloseDialog}
              sx={{
                fontFamily: "Poppins",
                fontSize: "12px",
                fontWeight: 400,
                lineHeight: "12px",
                textAlign: "center",
                textDecoration: "underline",
                textDecorationStyle: "solid",
                textUnderlinePosition: "from-font",
                textDecorationSkipInk: "none",
                color: "#DADADA",
                cursor: "pointer",
              }}
            >
              {t("kembali")}
            </Typography>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>
    </Box>
  );
}

export default PembayaranCawangan;
