import { useTranslation } from "react-i18next";
import {
  Control,
  Controller,
  FieldValues,
  RegisterOptions,
} from "react-hook-form";
import {
  MenuItem,
  Select,
  FormHelperText,
  FormControl,
  SelectChangeEvent,
  SelectProps,
  Chip,
  Box,
} from "@mui/material";
import { Cancel } from "@mui/icons-material";

export interface SelectFieldControllerProps
  extends Omit<SelectProps, "onChange" | "required"> {
  name: string;
  control: Control<FieldValues>;
  options: {
    value: string | number;
    label: string;
    /**
     * @default false
     */
    disabled?: boolean;
  }[];
  placeholder?: string;
  fullWidth?: boolean;
  rules?: RegisterOptions;
  onChange?: (event: SelectChangeEvent<any>) => void;
  required?: boolean;
  requiredCondition?: () => boolean;
}

export const SelectFieldController: React.FC<SelectFieldControllerProps> = ({
  name,
  control,
  options,
  placeholder = "",
  fullWidth = true,
  onChange,
  rules,
  required,
  requiredCondition,
  multiple = false,
  ...rest
}) => {
  const { t } = useTranslation();
  const enhancedRules = {
    ...rules,
    required: required ? t("fieldRequired") : undefined,
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={enhancedRules}
      render={({ field, fieldState: { error }, formState }) => {
        const handleDelete = (
          e: React.MouseEvent,
          valueToDelete: string | number
        ) => {
          if (!multiple) return;

          const newValue = Array.isArray(field.value)
            ? field.value.filter((val) => val !== valueToDelete)
            : [];
          field.onChange(newValue);
        };

        return (
          <FormControl
            fullWidth={fullWidth}
            error={!!error}
          >
            <Select
              {...field}
              {...rest}
              multiple={multiple}
              disabled={rest?.disabled ?? formState.isSubmitting}
              displayEmpty
              value={field.value ?? (multiple ? [] : "")}
              onChange={(event) => {
                const value = event.target.value;
                field.onChange(value);
                if (onChange) onChange(event);
              }}
              renderValue={(selected) => {
                if (!selected || (multiple && selected.length === 0)) {
                  return <span style={{ color: "#aaa" }}>{placeholder}</span>;
                }

                if (multiple) {
                  const selectedOptions = options.filter((item) =>
                    selected.includes(item.value)
                  );
                  return (
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                      {selectedOptions.map((option) => (
                        <Chip
                          key={option.value}
                          label={option.label}
                          onDelete={(e) => handleDelete(e, option.value)}
                          onMouseDown={(e) => e.stopPropagation()}
                          deleteIcon={<Cancel />}
                          sx={{
                            background: "#89bdbd",
                            color: "#fff",
                            height: "unset",
                            padding: "4px 0",
                            "& .MuiChip-deleteIcon": {
                              color: "inherit",
                              fontSize: "18px",
                              "&:hover": {
                                color: "inherit",
                              },
                            },
                          }}
                        />
                      ))}
                    </Box>
                  );
                } else {
                  const selectedOption = options.find(
                    (item) => item.value === selected
                  );
                  return selectedOption ? selectedOption.label : selected;
                }
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    "& .MuiMenuItem-root": {
                      fontSize: "14px",
                    },
                  },
                },
              }}
              sx={{
                backgroundColor: rest.disabled ? "#66666626" : "#FFF",
                height: multiple ? "auto" : "37px",
                fontSize: "14px",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: error ? "red !important" : "#DADADA !important",
                },
                "& .MuiSelect-select": {
                  minHeight: "unset",
                },
                ...rest.sx,
              }}
            >
              {options.map((item) => (
                <MenuItem
                  key={item.value}
                  value={item.value}
                  disabled={item?.disabled ?? false}
                >
                  {item.label}
                </MenuItem>
              ))}
            </Select>
            {error && <FormHelperText>{error.message}</FormHelperText>}
          </FormControl>
        );
      }}
    />
  );
};

SelectFieldController.displayName = "SelectFieldController";

export default SelectFieldController;
