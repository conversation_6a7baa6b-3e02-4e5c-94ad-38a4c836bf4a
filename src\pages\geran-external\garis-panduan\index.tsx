import { globalStyles } from "@/helpers";

import { Box, Typography, Button } from "@mui/material";

const GarisPanduan: React.FC = () => {
  const classes = globalStyles();

  return (
    <>
      <Box className={classes.section} mt={1} mb={1}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            Garis Panduan
          </Typography>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Typography className="label">
              Garis Panduan Geran Pertubuhan Prihatin <PERSON>
            </Typography>

            <Button
              className={classes.btnOutline}
              sx={{
                mt: 3,
                marginInline: "auto",
              }}
            >
              Panduan
            </Button>
          </Box>
        </Box>
      </Box>

      <Box className={classes.section} mb={1}>
        <Box
          className={classes.sectionBox}
          sx={{
            border: "1px solid var(--primary-color) !important",
          }}
        >
          <Typography
            sx={{
              color: "#FF0000",
              fontWeight: "500",
              fontSize: "12px !important",
              "& span": { color: "#666666", fontWeight: "400" },
            }}
          >
            NOTA
            <span>
              : Sila rujuk garis panduan permohonan sebagai rujukan sebelum
              memohon.
            </span>
          </Typography>
        </Box>
      </Box>
    </>
  );
};

export default GarisPanduan;
