import { getChatbotButtonPosition } from "@/redux/chatbotReducer";
import { Box, useTheme } from "@mui/material"
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { TypeAnimation } from "react-type-animation";

interface ChatbotSpeechBubbleProps {
  show?: boolean
  onChangeShowValue(value: boolean): void
}

export const ChatbotSpeechBubble = <
  PropType extends ChatbotSpeechBubbleProps = ChatbotSpeechBubbleProps
>({
  show = true,
  onChangeShowValue,
}: PropType) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const positionState = useSelector(getChatbotButtonPosition);

  const primary = theme.palette.primary.main;
  const introductionSequence = [
    t("rosieIntroduction.1", { emojiCode: "👋" }),
    10000,
    t("rosieIntroduction.2", { emojiCode: "😉"}),
    10000,
    // t("rosieIntroduction.3"),
    // 10000,
    () => onChangeShowValue(false)
  ]

  if (!show) return null
  return (
    <Box
      sx={{
        position: "fixed",
        bottom: "7rem",
        ...(positionState === "left" && { left: 0, ml: "1rem" }),
        ...(positionState === "center" && {
          left: "50%",
          transform: "translateX(-50%)",
        }),
        ...(positionState === "right" && { right: 0, mr: "1rem" }),
        minWidth: "10rem",
        maxWidth: "17rem",
        minHeight: "3rem",
        backgroundColor: primary,
        borderRadius: "1rem",
        p: 2,
        color: "white",
        rowGap: "1rem",
        fontSize: { xs: 12, sm: 13, md: 13 },
        transition: "all 0.3s ease-in-out",
        opacity: 1,
        transform: positionState === "center"
          ? "translateX(-50%) translateY(0)"
          : "translateY(0)",
      }}
      >
      <TypeAnimation sequence={introductionSequence} repeat={Infinity} />
    </Box>
  )
}