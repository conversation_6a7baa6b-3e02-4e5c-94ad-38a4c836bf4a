import React from "react";
import { Avatar, Box, Typography } from "@mui/material";
import { Notification } from "./MainNotificationPage";
import { NotificationColors } from "@/helpers";
import dayjs from "dayjs";

interface NotificationListProps {
  notifications: Notification[];
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
}) => {
  const getAvatarColor = (letter: string) => {
    const index =
      letter.toUpperCase().charCodeAt(0) % NotificationColors.length;
    return NotificationColors[index];
  };

  return (
    <Box>
      {notifications.map((noti: any) => {
        const createdDate = dayjs(noti.createdDate, "DD-MM-YYYY HH:mm:ss");
        const diffMinutes = dayjs().diff(createdDate, "minute");
        const timeAgo =
          diffMinutes < 60
            ? `${diffMinutes}m`
            : diffMinutes < 1440
            ? `${Math.floor(diffMinutes / 60)}j`
            : `${Math.floor(diffMinutes / 1440)}h`;

        return (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              py: 1,
              px: 2,
              m: 0,
              minHeight: 55,
              backgroundColor:
                noti.seen === false ? "var(--primary-color)40" : "transparent",
            }}
            key={noti?.notificationTrackerId}
          >
            <Avatar
              sx={{
                bgcolor: getAvatarColor(noti.subject.charAt(0)),
                width: 40, // Adjust size
                height: 40,
                fontSize: 18, // Adjust text size
              }}
            >
              {noti.subject.charAt(0).toUpperCase()}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Typography
                  variant="body1"
                  className="sub-step-login"
                  sx={{
                    display: "-webkit-box",
                    WebkitBoxOrient: "vertical",
                    WebkitLineClamp: 1,
                    overflow: "hidden",
                  }}
                >
                  {noti.subject}
                </Typography>
                <Typography variant="caption" sx={{ color: "gray" }}>
                  {timeAgo}
                </Typography>
              </Box>

              <Typography
                variant="body2"
                className="label-login"
                sx={{
                  display: "-webkit-box",
                  WebkitBoxOrient: "vertical",
                  WebkitLineClamp: 2,
                  overflow: "hidden",
                  pr: 3,
                }}
                dangerouslySetInnerHTML={{
                  __html: noti?.content.replace(/\r\n/g, " "),
                }}
              />
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};

export default NotificationList;
