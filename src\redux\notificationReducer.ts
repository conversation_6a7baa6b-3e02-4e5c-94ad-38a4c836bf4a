import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { Notification } from '@/pages/dashboard/notification/MainNotificationPage';


interface NotificationsState {
  groupedNotifications: Record<string, Notification[]>;
}

const initialState: NotificationsState = {
  groupedNotifications: {},
};

export const notificationsSlice = createSlice({
  name: "notifications",
  initialState,
  reducers: {
    setGroupedNotifications: (state, action: PayloadAction<Record<string, Notification[]>>) => {
      state.groupedNotifications = action.payload;
    },
    resetGroupedNotifications: (state) => {
      state.groupedNotifications = {
        ...initialState.groupedNotifications,
        // isView: state.data.isView
      };
    },
  },
});

export const { setGroupedNotifications } = notificationsSlice.actions;
export default notificationsSlice.reducer;
