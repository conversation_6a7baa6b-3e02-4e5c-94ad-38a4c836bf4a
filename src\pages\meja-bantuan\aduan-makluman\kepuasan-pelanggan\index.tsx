import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import { FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Card,
  TableContainer,
  Paper,
  TablePagination,
  Button,
  Divider,
  Menu,
  MenuItem,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import FilterListIcon from "@mui/icons-material/FilterList";
import { useForm } from "@refinedev/react-hook-form";
import useQuery from "../../../../helpers/hooks/useQuery";
import { MaklumBalasKategori } from "@/helpers/enums";

const KepuasanPelanggan: React.FC = () => {
  const { t } = useTranslation();

  const [feedbackCount, setFeedbackCount] = useState<any>([]);
  const [feedbackList, setFeedbackList] = useState<any>([]);
  const [filterCategory, setFilterCategory] = useState<number | string>("");
  const [filterDate, setFilterDate] = useState<dayjs.Dayjs | null>(null);
  const [datePickerAnchorEl, setDatePickerAnchorEl] =
    useState<HTMLElement | null>(null);
  const [categoryAnchorEl, setCategoryAnchorEl] = useState<HTMLElement | null>(
    null
  );

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    category: "category",
    date: "date",
  });

  const filterOptions = {
    category: Object.entries(MaklumBalasKategori).map(([key, value]) => ({
      label: key
        .replace(/_/g, " ")
        .toLowerCase()
        .replace(/\b\w/g, (c) => c.toUpperCase()),
      value: key,
    })),
  };

  const {
    data: feedbackCountData,
    isLoading: isLoadingFeedbackCount,
    refetch: fetchFeedbackCount,
  } = useQuery({
    url: "society/feedback/satisfaction/count",
    autoFetch: false,
  });

  const { watch, setValue } = useForm<FieldValues>({
    defaultValues: {
      identificationNo: "",
      page: 0,
      rowsPerPage: 10,
    },
  });

  const {
    data,
    isLoading,
    refetch: fetchFeedbackList,
  } = useQuery({
    url: "society/feedback/getSatisfactoryFeedback",
    filters: [
      ...(watch("identificationNo")
        ? [
            {
              field: "identificationNo",
              operator: "eq" as const,
              value: watch("identificationNo"),
            },
          ]
        : []),
      ...(filterDate
        ? [
            {
              field: "date",
              operator: "eq" as const,
              value: filterDate.format("YYYY-MM-DD"),
            },
          ]
        : []),
      ...(filterCategory
        ? [
            {
              field: "satisfaction",
              operator: "eq" as const,
              value: filterCategory,
            },
          ]
        : []),
      {
        field: "pageSize",
        operator: "eq" as const,
        value: watch("rowsPerPage"),
      },
      { field: "pageNo", operator: "eq" as const, value: watch("page") + 1 },
    ],
    autoFetch: false,
  });

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    setValue("page", 0);
    setFilterDate(date);
    setDatePickerAnchorEl(null);
    // Update the selected filter display
    const updatedFilters = {
      ...selectedFilters,
      date: date ? date.format("DD-MM-YYYY") : t("date"),
    };
    setSelectedFilters(updatedFilters);
    handleSelectedFiltersChange(updatedFilters);
  };

  const handleDatePickerOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setDatePickerAnchorEl(event.currentTarget);
  };

  const handleDatePickerClose = () => {
    setDatePickerAnchorEl(null);
  };

  const handleClearDate = () => {
    setValue("page", 0);
    setFilterDate(null);
    setDatePickerAnchorEl(null);
    const updatedFilters = { ...selectedFilters, date: t("date") };
    setSelectedFilters(updatedFilters);
    handleSelectedFiltersChange(updatedFilters);
  };

  const handleCategoryPickerOpen = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setCategoryAnchorEl(event.currentTarget);
  };

  const handleCategoryPickerClose = () => {
    setCategoryAnchorEl(null);
  };

  const handleCategorySelect = (value: string | number, label: string) => {
    setValue("page", 0);
    setFilterCategory(value);
    setCategoryAnchorEl(null);
    const updatedFilters = { ...selectedFilters, category: label };
    setSelectedFilters(updatedFilters);
    handleSelectedFiltersChange(updatedFilters);
  };

  const handleClearCategory = () => {
    setValue("page", 0);
    setFilterCategory("");
    setCategoryAnchorEl(null);
    const updatedFilters = { ...selectedFilters, category: t("category") };
    setSelectedFilters(updatedFilters);
    handleSelectedFiltersChange(updatedFilters);
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setValue("page", newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setValue("rowsPerPage", newRowsPerPage);
    setValue("page", 0);
  };

  const handleSearchInput = (searchInput: any) => {
    setValue("identificationNo", searchInput);
  };

  useEffect(() => {
    fetchFeedbackCount();
    fetchFeedbackList();
  }, []);

  useEffect(() => {
    fetchFeedbackList();
  }, [
    watch("rowsPerPage"),
    watch("page"),
    watch("identificationNo"),
    filterCategory,
    filterDate,
  ]);

  useEffect(() => {
    setFeedbackList(data?.data?.data?.data ?? []);
  }, [data]);

  useEffect(() => {
    const result = feedbackCountData?.data?.data ?? [];
    const transformData = Object.entries(result).map(([key, value]) => ({
      key: key,
      category:
        key === "countMemuaskan"
          ? "memuaskan"
          : key === "countSangatMemuaskan"
          ? "sangatMemuaskan"
          : "tidakMemuaskan",
      total: value,
    }));

    setFeedbackCount(transformData ?? []);
  }, [feedbackCountData]);

  const totalRecords = data?.data?.data?.total ?? 0;

  return (
    <>
      <Box>
        <Card
          sx={{
            px: 3,
            pt: 4,
            pb: 4,
            mb: 1,
            borderRadius: "15px",
            boxShadow: "none",
            height: "100%",
            width: "100%",
          }}
        >
          <Box
            sx={{
              border: "1px solid #e0e0e0",
              borderRadius: "15px",
              px: 2,
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                color: "var(--primary-color)",
                fontWeight: 600,
                px: 4,
                pt: 3,
              }}
            >
              {t("jumlahMaklumBalasKepuasanPelanggan")}
            </Typography>
            <TableContainer
              component={Paper}
              sx={{
                boxShadow: "none",
                backgroundColor: "white",
                borderRadius: 2.5 * 1.5,
              }}
            >
              <Table
                sx={{
                  minWidth: 650,
                  backgroundColor: "white",
                  borderRadius: "4px",
                  overflow: "hidden",
                }}
              >
                <TableHead
                  sx={{
                    "& th": {
                      textAlign: "center",
                    },
                  }}
                >
                  <TableRow>
                    <TableCell sx={{ fontWeight: 400 }}>
                      {t("category")}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 400 }}>{t("total")}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody
                  sx={{
                    "& td": {
                      textAlign: "center",
                    },
                    "& tr:last-of-type td": {
                      borderBottom: "none",
                    },
                  }}
                >
                  {feedbackCount.map((row: any, index: any) => (
                    <TableRow key={index}>
                      <TableCell sx={{ fontWeight: 300 }}>
                        {t(row.category)}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 300 }}>
                        {row.total}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </Card>
      </Box>

      <Box>
        <Card
          sx={{
            px: 3,
            pt: 1,
            pb: 4,
            borderRadius: "15px",
            boxShadow: "none",
            height: "100%",
            width: "100%",
          }}
        >
          <TextField
            fullWidth
            variant="outlined"
            placeholder={t("identityCardNo")}
            sx={{
              display: "block",
              boxSizing: "border-box",
              width: "90%",
              height: "40px",
              marginInline: "auto",
              marginTop: "12px",
              background: "rgba(132, 132, 132, 0.3)",
              opacity: 0.5,
              border: "1px solid rgba(102, 102, 102, 0.8)",
              borderRadius: "10px",
              "& .MuiOutlinedInput-root": {
                height: "40px",
                "& fieldset": {
                  border: "none",
                },
              },
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleSearchInput((e.target as HTMLInputElement).value);
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" sx={{ marginLeft: "8px" }}>
                  <img
                    src={"/search-icon.svg"}
                    alt="Filter Icon"
                    width="18"
                    height="18"
                  />
                </InputAdornment>
              ),
            }}
          />

          {/* Custom Filter Bar with Date Picker */}
          <Box
            sx={{
              maxWidth: "fit-content",
              minHeight: 40,
              display: "flex",
              marginInline: "auto",
              alignItems: "center",
              boxShadow: "0px 12px 12px rgba(234, 232, 232, 0.4)",
              borderRadius: "10px",
              padding: "4px",
              gap: "5px",
              mb: 2,
              mt: 2,
            }}
          >
            <Button
              variant="text"
              startIcon={
                <FilterListIcon
                  sx={{ fontSize: "16px !important", color: "#6B7280" }}
                />
              }
              disabled
              sx={{
                color: "#6B7280",
                textTransform: "none",
                textWrap: "nowrap",
                justifyContent: "center",
                alignItems: "center",
                display: "flex",
                width: "120px",
                "&:disabled": { color: "#6B7280" },
              }}
            >
              {t("filterBy")}
            </Button>

            {/* Category Filter */}
            <Box
              sx={{
                justifyContent: "center",
                alignItems: "center",
                display: "flex",
                width: "190px",
              }}
            >
              <Divider
                orientation="vertical"
                sx={{ backgroundColor: "#E5E7EB", height: 30 }}
              />
              <Button
                sx={{
                  display: "flex",
                  color: "#6B7280",
                  textTransform: "none",
                  justifyContent: "space-between",
                  gap: 1,
                  ml: 1,
                  width: "100%",
                  "&:hover": { backgroundColor: "#F9FAFB" },
                }}
                onClick={handleCategoryPickerOpen}
              >
                <Typography className="label" noWrap>
                  {t(selectedFilters.category)}
                </Typography>
                <Box sx={{ color: "#6B7280" }}>
                  <svg
                    style={{ fontSize: 12 }}
                    width="9"
                    height="7"
                    viewBox="0 0 9 7"
                    fill="none"
                  >
                    <path
                      d="M1 1L4.5 6L8 1"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Box>
              </Button>

              {/* Category Menu */}
              <Menu
                anchorEl={categoryAnchorEl}
                open={Boolean(categoryAnchorEl)}
                onClose={handleCategoryPickerClose}
                sx={{
                  maxHeight: 400,
                }}
              >
                <MenuItem onClick={handleClearCategory}>{t("clear")}</MenuItem>
                {filterOptions.category?.map((option) => (
                  <MenuItem
                    key={option.value}
                    onClick={() =>
                      handleCategorySelect(option.value, option.label)
                    }
                  >
                    {t(option.label)}
                  </MenuItem>
                ))}
              </Menu>
            </Box>

            {/* Date Filter */}
            <Box
              sx={{
                justifyContent: "center",
                alignItems: "center",
                display: "flex",
                width: "190px",
              }}
            >
              <Divider
                orientation="vertical"
                sx={{ backgroundColor: "#E5E7EB", height: 16 }}
              />
              <Button
                sx={{
                  display: "flex",
                  color: "#6B7280",
                  textTransform: "none",
                  justifyContent: "space-between",
                  gap: 1,
                  ml: 1,
                  width: "100%",
                  "&:hover": { backgroundColor: "#F9FAFB" },
                }}
                onClick={handleDatePickerOpen}
              >
                <Typography className="label" noWrap>
                  {filterDate ? filterDate.format("DD-MM-YYYY") : t("date")}
                </Typography>
                <Box sx={{ color: "#6B7280" }}>
                  <svg
                    style={{ fontSize: 12 }}
                    width="9"
                    height="7"
                    viewBox="0 0 9 7"
                    fill="none"
                  >
                    <path
                      d="M1 1L4.5 6L8 1"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Box>
              </Button>

              {/* Date Picker Menu */}
              <Menu
                anchorEl={datePickerAnchorEl}
                open={Boolean(datePickerAnchorEl)}
                onClose={handleDatePickerClose}
                sx={{
                  "& .MuiPaper-root": {
                    padding: 2,
                  },
                }}
              >
                <MenuItem onClick={handleClearDate}>{t("clear")}</MenuItem>
                <Box sx={{ p: 1 }}>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      value={filterDate}
                      onChange={handleDateChange}
                      format="DD-MM-YYYY"
                      slotProps={{
                        textField: {
                          size: "small",
                          inputProps: {
                            readOnly: true,
                          },
                          sx: {
                            width: "200px",
                            "& .MuiInputBase-input": {
                              fontSize: "14px",
                              cursor: "pointer",
                            },
                          },
                        },
                      }}
                    />
                  </LocalizationProvider>
                </Box>
              </Menu>
            </Box>
          </Box>
          <Box mt={3}>
            <Box
              sx={{
                border: "1px solid #e0e0e0",
                borderRadius: "15px",
                px: 2,
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  color: "var(--primary-color)",
                  fontWeight: 600,
                  px: 4,
                  pt: 3,
                }}
              >
                {t("senaraiMaklumBalasKepuasanPelanggan")}
              </Typography>
              <TableContainer
                component={Paper}
                sx={{
                  boxShadow: "none",
                  backgroundColor: "white",
                  borderRadius: 2.5 * 1.5,
                  p: 1,
                  mb: 3,
                }}
              >
                <Table
                  sx={{
                    minWidth: 650,
                    backgroundColor: "white",
                    borderRadius: "4px",
                    overflow: "hidden",
                  }}
                >
                  <TableHead
                    sx={{
                      "& th": {
                        textAlign: "center",
                      },
                    }}
                  >
                    <TableRow>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {t("name")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {t("idNumberPlaceholder")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {t("category")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {t("remarks")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {t("date")}
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody
                    sx={{
                      "& td": {
                        textAlign: "center",
                      },
                    }}
                  >
                    {feedbackList.map((row: any, index: any) => {
                      const formattedDate = dayjs(row.createdDate).format(
                        "DD-MM-YYYY"
                      );
                      const category =
                        MaklumBalasKategori[
                          row.satisfaction as keyof typeof MaklumBalasKategori
                        ];
                      return (
                        <TableRow key={index}>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {row.name}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {row.identificationNo}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {t(category)}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {row.details}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {formattedDate}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>

                {!feedbackList.length && !isLoading && (
                  <Typography
                    fontSize="0.875rem"
                    fontWeight={300}
                    textAlign="center"
                    lineHeight="24px"
                    marginTop="18px"
                    borderBottom={"1px solid rgba(224, 224, 224, 1)"}
                    paddingBottom="16px"
                  >
                    {t("tiadaData")}
                  </Typography>
                )}

                {feedbackList.length > 0 && (
                  <TablePagination
                    component="div"
                    count={totalRecords}
                    page={watch("page")}
                    onPageChange={handleChangePage}
                    rowsPerPage={watch("rowsPerPage")}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage={t("rowsPerPage")}
                  />
                )}
              </TableContainer>
            </Box>
          </Box>
        </Card>
      </Box>
    </>
  );
};

export default KepuasanPelanggan;
