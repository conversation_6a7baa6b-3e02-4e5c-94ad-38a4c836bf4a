import { createAction, createSlice } from "@reduxjs/toolkit"

export interface UserState {
  data: any | null
  token: string | null
  portal: null | number
  permission: boolean | null,
  aliranTugasAccess: boolean | null,
}

const initialState: UserState = {
  data: null,
  token: null,
  portal: null,
  permission: null,
  aliranTugasAccess: null
}

const user = 'user'

export const setUserDataRedux = createAction<UserState['data']>(`${user}/setUserDataRedux`)

export const setUserTokenRedux = createAction<UserState['token']>(`${user}/setUserTokenRedux`)

export const setUserPortalRedux = createAction<UserState['portal']>(`${user}/setUserPortalRedux`)

export const setUserPermissionRedux = createAction<UserState['permission']>(`${user}/setUserPermissionRedux`)

export const setAliranTugasAccessRedux = createAction<UserState['permission']>(`${user}/setAliranTugasAccessRedux`)

export const logoutRedux = createAction(`${user}/logout`)

export const userSlice = createSlice({
  name: user,
  initialState,
  reducers: {},
  selectors: {
    getAuthorizationHeader: (state) => {
      if (state?.portal && state?.token) {
        return {
          portal: `${state.portal}`,
          'Authorization': `Bearer ${state?.token ?? ''}`
        }
      }
      return null
    },
    getUserToken: (state) => state.token,
    getUserPortal: (state) => state.portal,
    getUserDetails: (state) => state.data,
    getUserPermission: (state) => state.permission ?? false,
    getAliranTugasAccess: (state) => state.aliranTugasAccess ?? false,

  },
  extraReducers: (builder) =>
    builder
      .addCase(setUserDataRedux, (state, action) => {
        state.data = action.payload
      })
      .addCase(setUserTokenRedux, (state, action) => {
        state.token = action.payload
      })
      .addCase(setUserPortalRedux, (state, action) => {
        state.portal = action.payload
      })
      .addCase(setUserPermissionRedux, (state, action) => {
        state.permission = action.payload
      })
      .addCase(setAliranTugasAccessRedux, (state, action) => {
        state.aliranTugasAccess = action.payload
      })
      .addCase(logoutRedux, (state) => {
        state.data = null
        state.token = null
        state.portal = null
        state.permission = null
        state.aliranTugasAccess = null
      })
})

export const {
  getAuthorizationHeader,
  getUserToken,
  getUserDetails,
  getUserPortal,
  getUserPermission,
  getAliranTugasAccess
} = userSlice.selectors

const userReducer = userSlice.reducer

export default userReducer
