import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IEvent } from '../../types/event';

interface TakwimState {
  data: IEvent[];
  selectedEvent: IEvent | null;
  loading: boolean;
  error: string | null;
}

const initialState: TakwimState = {
  data: [],
  selectedEvent: null,
  loading: false,
  error: null,
};

export const takwimSlice = createSlice({
  name: 'takwim',
  initialState,
  reducers: {
    setTakwimDataRedux: (state, action: PayloadAction<IEvent[]>) => {
      state.data = action.payload;
    },
    setSelectedEvent: (state, action: PayloadAction<IEvent>) => {
      state.selectedEvent = action.payload;
    },
    setTakwimLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setTakwimError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setTakwimDataRedux,
  setSelectedEvent,
  setTakwimLoading,
  setTakwimError
} = takwimSlice.actions;

export default takwimSlice.reducer;

