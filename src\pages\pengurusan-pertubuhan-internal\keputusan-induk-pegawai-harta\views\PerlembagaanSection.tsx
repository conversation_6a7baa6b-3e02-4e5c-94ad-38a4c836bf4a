import {
  Box,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
// import { PenubuhanIndukStepper } from "./Stepper";
import { ButtonPrimary } from "../../../../components/button";
import { useNavigate, useParams } from "react-router-dom";
import LabelTitle from "../../../../components/label/LabelTitle";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { API_URL } from "../../../../api";
import { useCustom } from "@refinedev/core";
import { ApplicationStatus, ConstitutionType } from "../../../../helpers/enums";
import FasalContent from "../../../../components/FasalContent";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};
type PerlembagaanSectionProps = { 
  societyDataById: any;
};
export const PerlembagaanSection: React.FC<PerlembagaanSectionProps> = ({ 
  societyDataById,
}) => {
 
  const { t } = useTranslation();
  const [fasal, setFasal] = useState([]);
  // const navigate = useNavigate();
  // const { id } = useParams();
  // const decodedId = atob(id || "");
  // const isMobile = useMediaQuery((theme: Theme) =>
  //   theme.breakpoints.down("sm")
  // );

  const { data: clauseContentData, isLoading: loadingData } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyDataById?.id,
        status: ApplicationStatus["AKTIF"],
        // amendmentId:amendmentId
      },
    },
    queryOptions: {
      enabled: !!societyDataById?.id,
    },
  });
  const clauseContent = clauseContentData?.data?.data?.data || [];
  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    }); 

  const allConstitutions = constitutionData?.data?.data || [];
  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyDataById?.id}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const constitutionType =
    societyData?.data?.data?.constitutionType || ConstitutionType.IndukNGO[1];

  const generatePerlembagaanAfter = () => {
    if (constitutionType) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContent.find((item: any) =>
              item.clauseNo
                ? Number(item.clauseNo) === Number(clause.id)
                : item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          setFasal(item.clauseContents);
        }
      });
    } else {
      setFasal([]);
    }
  };

  useEffect(() => {
    if (clauseContent) {
      generatePerlembagaanAfter();
    }
  }, [constitutionType, constitutionData, clauseContent]);

  const isLoading = loadingData || isConstitutionLoading;
  return (
    <Box>
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #D9D9D9",
          // backgroundColor: "#FCFCFC",
          borderRadius: "14px",
          overflow: "hidden",
        }}
      >
        <Typography sx={subTitleStyle}>{t("checkTheConstitution")}</Typography>

        <Grid container spacing={2}>
          {/* Sidebar for fasal navigation */}
          <Grid item xs={12}>
            {!isLoading && fasal.length > 0 && (
              <FasalContent fasalContent={fasal} />
            )}
          </Grid>

          {/* <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              onClick={() => console.log("save")}
            >
              {t("save")}
            </ButtonPrimary>
          </Grid> */}
        </Grid>
      </Box>
    </Box>
  );
};

export default PerlembagaanSection;
