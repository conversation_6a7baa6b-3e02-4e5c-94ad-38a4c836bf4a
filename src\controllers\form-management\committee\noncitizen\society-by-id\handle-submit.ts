import { CommitteeNonCitizenBySocietyRequestBody } from "@/components/form/comittee/noncitizen/BySocietyIdInner";
import { useMutation } from "@/helpers";
import { useFormCommitteeNonCitizenBySocietyIdContext } from "@/pages/pertubuhan/ajk/jawatankuasa/createAJKBukanWn";
import { FormikHelpers } from "formik";

export function useFormManagementCommitteeNonCitizenSocietyByIdHandleSubmit<
  Payload extends CommitteeNonCitizenBySocietyRequestBody = CommitteeNonCitizenBySocietyRequestBody
>() {
  const { fetchAsync: createAJK } = useMutation<{ data: Pick<Payload, "id"> }>({
    url: "society/nonCitizenCommittee/create",
    showSuccessNotification: false
  });
  const { fetchAsync: updateAJK } = useMutation({
    method: "put",
    msgSuccess: "nonCitizenCommitteeSuccessfullyUpdated"
  });

  const {
    setSocietyNonCitizenCommitteeId,
    setIsUploadingFilesAfterCreateAJK,
    redirectToAJKLists
  } = useFormCommitteeNonCitizenBySocietyIdContext();

  const handleSubmit = async (initialPayload: Payload, { setSubmitting }: FormikHelpers<Payload>) => {
    setSubmitting(true)
    try {
      if (typeof initialPayload?.id === "number") {
        await updateAJK(
          initialPayload,
          () => `society/nonCitizenCommittee/${initialPayload.id}/edit`
        )
        redirectToAJKLists();
      } else {
        const response = await createAJK(initialPayload);
        const societyNonCitizenCommitteeId = response.data.data?.id?.toString() ?? null;
        setSocietyNonCitizenCommitteeId(societyNonCitizenCommitteeId);
        setIsUploadingFilesAfterCreateAJK(true);
      }
    } finally {
      setSubmitting(false)
    }
  }

  return {
    handleSubmit
  }
}
