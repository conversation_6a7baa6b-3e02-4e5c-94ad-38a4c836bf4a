import React, { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, IconButton } from "@mui/material";
import { ButtonPrimary } from "../../../../components/button";
import {
  ApplicationStatusEnum,
  MALAYSIA,
  OrganisationPositions,
} from "../../../../helpers/enums";
import { EditIcon, EyeIcon, TrashIcon } from "../../../../components/icons";
import { API_URL } from "../../../../api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { DataGrid } from "@mui/x-data-grid";
import { getLocalStorage } from "../../../../helpers/utils";
import { useQuery } from "@/helpers";
import { DataTable } from "@/components";
import { useForm } from "react-hook-form";

interface ListItem {
  value: any;
  label: any;
  id?: any | null;
}

export const ListSenaraiAjk: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const addressList = getLocalStorage("address_list", null);
  const [totalListNonCitizen, setTotalListNonCitizen] = useState<number>(0);
  const [rowDataNonCitizen, setRowDataNonCitizen] = useState<any[]>([]);
  const [searchParams] = useSearchParams();

  const { id: societyId } = useParams();
  const branchId = searchParams.get("id");

  const form = useForm<any>();
  const { setValue, getValues, watch, reset } = form;

  const {
    data: nonCitizenList,
    refetch: fetchNonCitizenAjkList,
    isLoading: isLoadingNonCitizenAJKList,
  } = useQuery({
    url: `society/nonCitizenCommittee/getAll`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchId },
    ],
    // autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setTotalListNonCitizen(data?.data?.data?.total ?? 0);
      setRowDataNonCitizen(data?.data?.data?.data ?? []);
    },
  });

  const pageNonCitizen = watch("pageNonCitizen") || 1;
  const pageSizeNonCitizen = watch("pageSizeNonCitizen") || 10;

  const [stateList, setStateList] = useState(
    addressList
      ?.filter((item: any) => item.pid === MALAYSIA)
      ?.map((item: any) => ({ value: item.id, label: item.name }))
  );
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
    fontFamily: "Poppins",
  };

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  const handleDaftarAJK = () => {
    navigate(`../create-ajk?id=${branchId}`);
  };

  const handleDaftarAJKBukanWn = () => {
    navigate(`../create-ajk-bukan-wn?id=${branchId}`);
  };

  const handleEditAJK = (memberId: any) => {
    navigate(`../create-ajk?id=${branchId}&mId=${memberId}`);
  };

  const handleEditAJKBukanWn = (memberId: any) => {
    navigate(`../create-ajk-bukan-wn?id=${branchId}&mId=${memberId}`);
  };

  const handleViewAJKBukanWn = (memberId: any) => {
    navigate(`../view-ajk-bukan-wn?id=${branchId}&mId=${memberId}`);
  };

  const {
    data: branchData,
    isLoading,
    refetch,
  } = useCustom({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  const [internalList, setInternalList] = useState([]);
  const [externalList, setExternalList] = useState([]);

  useEffect(() => {
    if (branchData?.data?.data?.branchCommittees) {
      setInternalList(branchData.data.data.branchCommittees);
    } else {
      setInternalList([]);
    }
    if (branchData?.data?.data?.branchNonCitizenCommittees) {
      setExternalList(branchData.data.data.branchNonCitizenCommittees);
    } else {
      setExternalList([]);
    }
  }, [branchData]);

  const { mutate: deleteExternalCommittee, isLoading: isLoadingCreate } =
    useCustomMutation();
  const DeleteExternalCommittee: (id: any) => void = (id) => {
    deleteExternalCommittee(
      {
        url: `${API_URL}/society/branch/update`,
        method: "put",
        values: {
          id: branchId,
          societyId: branchData?.data?.data?.societyId,
          societyNo: branchData?.data?.data?.societyNo,
          branchNonCitizenCommittees: [{ id: id, applicationStatusCode: -1 }],
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          refetch();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: editNonCitizenIds, isLoading: isLoadingEdit } =
    useCustomMutation();

  const EditNonCitizenIds: (ids: any) => void = (ids) => {
    editNonCitizenIds(
      {
        url: `${API_URL}/society/nonCitizenCommittee/update-by-list`,
        method: "put",
        values: { ids },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          fetchNonCitizenAjkList({
            filters: [
              { field: "societyId", operator: "eq", value: societyId },
              { field: "branchId", operator: "eq", value: branchId },
            ],
          });
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const hantarNonCitizen = () => {
    const ids = rowDataNonCitizen
      ?.filter((item) => Number(item.applicationStatusCode) === 1)
      .map((item) => item.id);
    EditNonCitizenIds(ids);
  };

  const internalColumns = [
    {
      field: "designationCode",
      headerName: t("position"),
      headerAlign: "left",
      align: "left",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: (params: any) => {
        const row = params?.row;
        const position = positionsTranslatedList.find(
          (item: any) => Number(item.value) === Number(row?.designationCode)
        );
        return (
          <Typography className="label">
            {position?.label ? position?.label : "-"}
          </Typography>
        );
      },
    },
    {
      field: "committeeName",
      headerName: t("name"),
      headerAlign: "center",
      align: "left",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
    },
    {
      field: "email",
      headerName: t("email"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
    },
    {
      field: "committeeStateCode",
      headerName: t("state"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: (params: any) => {
        const row = params?.row;
        const state = stateList.find(
          (item: any) => item.value === Number(row?.committeeStateCode)
        );
        return (
          <Typography className="label">
            {state?.label ? state?.label : "-"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      sortable: false,
      flex: 0.5,
      renderCell: (params: any) => (
        <IconButton
          onClick={() => handleEditAJK(params.row.id)}
          sx={{ color: "var(--primary-color)" }}
        >
          <EditIcon sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }} />
        </IconButton>
      ),
    },
  ];

  const externalColumns = [
    {
      field: "designationCode",
      headerName: t("position"),
      headerAlign: "left",
      align: "left",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: ({ row }: any) => {
        return t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === Number(row?.designationCode)
            )?.label || ""
          }`
        );
      },
    },
    {
      field: "name",
      headerName: t("name"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      headerClassName: "header-style",
      cellClassName: "cell-style",
      renderCell: (params: any) => {
        const row = params?.row;
        const statusLabel =
          ApplicationStatusEnum[row.applicationStatusCode] || "-";
        return (
          <Typography className="label">
            {statusLabel ? statusLabel : "-"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      sortable: false,
      flex: 0.5,
      renderCell: (params: any) => {
        const row = params?.row;
        if (Number(row.applicationStatusCode) === 1) {
          return (
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                onClick={() => handleEditAJKBukanWn(params.row.id)}
                sx={{ color: "var(--primary-color)" }}
              >
                <EditIcon
                  sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }}
                />
              </IconButton>
              <IconButton
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  aspectRatio: "1/1",
                }}
                onClick={() => DeleteExternalCommittee(params.row.id)}
              >
                <TrashIcon
                  sx={{
                    fontSize: "0.9rem",
                    width: "0.9rem",
                    height: "0.9rem",
                    color: "red",
                  }}
                />
              </IconButton>
            </Box>
          );
        } else {
          return (
            <IconButton
              onClick={() => handleViewAJKBukanWn(params.row.id)}
              sx={{ color: "var(--primary-color)" }}
            >
              <EyeIcon
                sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }}
              />
            </IconButton>
          );
        }
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "#FCFCFC",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FCFCFC",
            borderRadius: "14px",
            mb: 3,
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", mb: 2, gap: 2 }}>
            <Typography variant="h6" component="h2" sx={sectionStyle}>
              {t("ajkList")}
            </Typography>
            <ButtonPrimary
              sx={{
                alignSelf: "flex-end",
                px: 2,
                py: 1,
                bgcolor: "transparent",
                color: "#666666",
                boxShadow: "none",
                border: "1px solid #67D1D1",
                fontSize: "12px",
                fontWeight: "500 !important",
              }}
              onClick={handleDaftarAJK}
            >
              {t("registerAJK")}
            </ButtonPrimary>
          </Box>
          <DataGrid
            rows={internalList}
            columns={internalColumns as any}
            loading={isLoading}
            disableColumnMenu
            disableColumnSelector
            disableDensitySelector
            autoHeight
            disableRowSelectionOnClick
            sx={{
              color: "#666666",
              backgroundColor: "white",
              border: "none",
              fontFamily:
                "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
              "& .MuiDataGrid-columnHeaders": {
                fontWeight: "bold",
                fontSize: "16px",
              },
              "& .MuiDataGrid-cell": {
                fontSize: "16px",
              },
              minHeight: "100px",
            }}
            localeText={{
              MuiTablePagination: {
                labelRowsPerPage: t("rowsPerPage"),
              },
            }}
          />
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", mb: 2, gap: 2 }}>
            <Typography variant="h6" component="h2" sx={sectionStyle}>
              {t("nonCitizenAJK")}
            </Typography>
            <ButtonPrimary
              sx={{
                alignSelf: "flex-end",
                px: 2,
                py: 1,
                bgcolor: "transparent",
                color: "#666666",
                boxShadow: "none",
                border: "1px solid #67D1D1",
                fontSize: "12px",
                fontWeight: "500 !important",
              }}
              onClick={handleDaftarAJKBukanWn}
            >
              {t("registerNonCitizenAJK")}
            </ButtonPrimary>
          </Box>
          <DataTable
            columns={externalColumns as any}
            rows={rowDataNonCitizen}
            page={pageNonCitizen}
            rowsPerPage={pageSizeNonCitizen}
            totalCount={totalListNonCitizen}
            onPageChange={(newPage) => setValue("pageNonCitizen", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("pageNonCitizen", 1);
              setValue("pageSizeNonCitizen", newPageSize);
            }}
            isLoading={isLoadingNonCitizenAJKList}
            clientPaginationMode={true}
          />
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 3 }}
          >
            <ButtonPrimary
              disabled={
                rowDataNonCitizen.length === 0 ||
                isLoadingEdit ||
                rowDataNonCitizen?.every(
                  (item) => Number(item.applicationStatusCode) !== 1
                )
              }
              onClick={hantarNonCitizen}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </Box>
        <ButtonPrimary
          disabled={
            branchData?.data?.data?.branchCommittees?.length +
              branchData?.data?.data?.branchNonCitizenCommittees?.length <
            7
          }
          onClick={() => navigate(`../dokumen-sokongan?id=${branchId}`)}
          sx={{ display: "block", ml: "auto", mt: 4 }}
        >
          {t("next")}
        </ButtonPrimary>
      </Box>
    </>
  );
};

export default ListSenaraiAjk;
