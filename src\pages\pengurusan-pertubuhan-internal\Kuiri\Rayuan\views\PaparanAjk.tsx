import {
  <PERSON>,
  Grid,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  IconButton,
  LinearProgress,
} from "@mui/material";
import { t } from "i18next";
import React, { useRef, useState } from "react";
import { useParams } from "react-router-dom";
import dayjs from "dayjs";
import { useUploadDocument } from "@/helpers";
import { API_URL } from "@/api";
import { ButtonPrimary } from "@/components";

interface UploadingFile {
  name: string;
  progress: number;
  size: string;
  isComplete?: boolean;
}

const labelStyle = {
  fontWeight: "500!important",
  marginBottom: "8px",
  fontSize: "14px",
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

function PaparanAjk() {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const { id } = useParams();

  const { upload } = useUploadDocument({
    type: "dokumen",
  });

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handlePreviewDocument = (fileUrl: string) => {
    window.open(fileUrl, "_blank");
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach((file) => {
      // Add to uploading state for UI feedback
      setUploadingFiles((prev) => [
        ...prev,
        {
          name: file.name,
          progress: 0,
          size: formatFileSize(file.size),
        },
      ]);

      // Upload using the hook
      upload({ file }, () => {
        // Update uploadingFiles to show completion
        setUploadingFiles((prev) =>
          prev.map((f) =>
            f.name === file.name ? { ...f, progress: 100, isComplete: true } : f
          )
        );
      });
    });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (!files) return;

    Array.from(files).forEach((file) => {
      setUploadingFiles((prev) => [
        ...prev,
        {
          name: file.name,
          progress: 0,
          size: formatFileSize(file.size),
        },
      ]);

      upload({ file }, () => {
        setUploadingFiles((prev) =>
          prev.map((f) =>
            f.name === file.name ? { ...f, progress: 100, isComplete: true } : f
          )
        );
      });
    });
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const response = await fetch(
        `${API_URL}/society/document/${documentId}`,
        {
          method: "DELETE",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );

      if (response.ok) {
        // Remove from uploaded files list
        setUploadedFiles((prev) =>
          prev.filter((file) => file.id !== documentId)
        );
        handleMenuClose();
      }
    } catch (error) {
      console.error("Delete error:", error);
    }
  };

  const [formData, setFormData] = useState({
    position: "",
    idType: "",
    icNo: "",
    title: "",
    name: "",
    gender: "",
    citizen: "",
    dateOfBirth: "",
    placeOfBirth: "",
    pekerjaan: "",
    residentialAddress: "",
    negeri: "",
    daerah: "",
    bandar: "",
    poskod: "",
    emel: "",
    phoneNumber: "",
    nomborTelefonRumah: "",
    nomborTelefonPejabat: "",
    employerName: "",
    employerAddress: "",
    negeri_majikan: "",
    daerah_majikan: "",
    bandar_majikan: "",
    poskod_majikan: "",
  });

  const HandleSend = () => {
    console.log("submit..");
  };

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            px: 2,
            py: 4,
          }}
        >
          Reformis Muda Perak
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        {/*  */}
        <Box
          sx={{
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairman")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("jawatan")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  fullWidth
                  required
                  value={formData.position}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      position: e.target.value,
                    })
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairmanPersonalInfo")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("idType")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      idType: e.target.value,
                    })
                  }
                  value={formData.idType}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("idNumber")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      icNo: e.target.value,
                    })
                  }
                  value={formData.icNo}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("title")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      title: e.target.value,
                    })
                  }
                  value={formData.title}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("fullName")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      name: e.target.value,
                    })
                  }
                  value={formData.name}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("gender")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      gender: e.target.value,
                    })
                  }
                  value={formData.gender}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("citizenship")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      citizen: e.target.value,
                    })
                  }
                  value={formData.citizen}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("dateOfBirth")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  type="date"
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      dateOfBirth: e.target.value,
                    })
                  }
                  value={
                    formData.dateOfBirth
                      ? dayjs(formData.dateOfBirth).format("DD-MM-YYYY")
                      : "-"
                  }
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("placeOfBirth")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      placeOfBirth: e.target.value,
                    })
                  }
                  value={formData.placeOfBirth}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("occupation")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      pekerjaan: e.target.value,
                    })
                  }
                  value={formData.pekerjaan}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("residentialAddress")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  rows={4}
                  multiline
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      residentialAddress: e.target.value,
                    })
                  }
                  value={formData.residentialAddress}
                />
              </Grid>

              {/*=========== negeri section ========*/}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("negeri")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      negeri: e.target.value,
                    })
                  }
                  value={formData.negeri}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("daerah")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      daerah: e.target.value,
                    })
                  }
                  value={formData.daerah}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("bandar")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      bandar: e.target.value,
                    })
                  }
                  value={formData.bandar}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("poskod")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      poskod: e.target.value,
                    })
                  }
                  value={formData.poskod}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("emel")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      emel: e.target.value,
                    })
                  }
                  value={formData.emel}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("phoneNumber")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      phoneNumber: e.target.value,
                    })
                  }
                  value={formData.phoneNumber}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("nomborTelefonRumah")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      nomborTelefonRumah: e.target.value,
                    })
                  }
                  value={formData.nomborTelefonRumah}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("nomborTelefonPejabat")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      nomborTelefonPejabat: e.target.value,
                    })
                  }
                  value={formData.nomborTelefonPejabat}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("employerInfo")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("employerName")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerName: e.target.value,
                    })
                  }
                  value={formData.employerName}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("employerAddress")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerAddress: e.target.value,
                    })
                  }
                  value={formData.employerAddress}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("negeri")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      negeri_majikan: e.target.value,
                    })
                  }
                  value={formData.negeri_majikan}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("daerah")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      daerah_majikan: e.target.value,
                    })
                  }
                  value={formData.daerah_majikan}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("bandar")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      bandar_majikan: e.target.value,
                    })
                  }
                  value={formData.bandar_majikan}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("poskod")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      poskod_majikan: e.target.value,
                    })
                  }
                  value={formData.poskod_majikan}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("ajkEligibilityCheck")}
          </Typography>

          <Box>
            <Box>
              <Box
                sx={{
                  border: "2px dashed #e0e0e0",
                  borderRadius: 1,
                  p: 4,
                  mb: 2,
                  textAlign: "center",
                  cursor: "pointer",
                  "&:hover": {
                    backgroundColor: "#f5f5f5",
                  },
                }}
                onClick={() => fileInputRef.current?.click()}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: "none" }}
                  onChange={handleFileUpload}
                  multiple
                  accept=".pdf,.docx,.txt"
                />
                <svg
                  width="28"
                  height="29"
                  viewBox="0 0 28 29"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ marginBottom: "8px" }}
                >
                  <path
                    d="M15.166 2.8335H15.4842C19.2889 2.8335 21.1913 2.8335 22.5124 3.7643C22.891 4.031 23.227 4.34727 23.5104 4.70353C24.4993 5.94695 24.4993 7.73741 24.4993 11.3183V14.288C24.4993 17.7451 24.4993 19.4736 23.9523 20.8541C23.0727 23.0735 21.2127 24.8241 18.8546 25.6519C17.3878 26.1668 15.5512 26.1668 11.8781 26.1668C9.77922 26.1668 8.72977 26.1668 7.89159 25.8726C6.54411 25.3996 5.48123 24.3992 4.97864 23.131C4.66602 22.3421 4.66602 21.3544 4.66602 19.3789V14.5002"
                    stroke="var(--primary-color)"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M24.4987 14.5005C24.4987 16.6483 22.7576 18.3894 20.6098 18.3894C19.8331 18.3894 18.9173 18.2533 18.1621 18.4556C17.4911 18.6354 16.967 19.1596 16.7872 19.8306C16.5848 20.5858 16.7209 21.5015 16.7209 22.2783C16.7209 24.426 14.9798 26.1672 12.832 26.1672"
                    stroke="var(--primary-color)"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12.8333 7.50016L3.5 7.50016M8.16667 2.8335V12.1668"
                    stroke="var(--primary-color)"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
                <Typography
                  sx={{
                    fontFamily: "DM Sans",
                    fontSize: "18px",
                    fontWeight: 500,
                    lineHeight: "18.23px",
                    textAlign: "center",
                    textUnderlinePosition: "from-font",
                    textDecorationSkipInk: "none",
                    color: "#222222",
                    marginBottom: "15px",
                  }}
                >
                  Click to upload
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: "var(--primary-color)",
                    display: "flex",
                    gap: 1,
                    justifyContent: "center",
                  }}
                >
                  <Box
                    component="span"
                    sx={{
                      bgcolor: "#3276E81A",
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                    }}
                  >
                    PDF
                  </Box>
                  <Box
                    component="span"
                    sx={{
                      bgcolor: "#3276E81A",
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                    }}
                  >
                    DOCX
                  </Box>
                  <Box
                    component="span"
                    sx={{
                      bgcolor: "#3276E81A",
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                    }}
                  >
                    TXT
                  </Box>
                  <Box
                    component="span"
                    sx={{
                      bgcolor: "#E3F2FD",
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                    }}
                  >
                    &gt;25 MB
                  </Box>
                </Typography>
              </Box>
              <Box sx={{ textAlign: "left", mt: 2 }}>
                {/* Show files being uploaded */}
                {uploadingFiles.map((file, index) => (
                  <Box
                    key={index}
                    sx={{
                      border: "1px solid #E0E0E0",
                      borderRadius: "8px",
                      backgroundColor: "#fff",
                      p: 2,
                      mb: 1,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        mb: 1,
                      }}
                    >
                      <Box>
                        <Typography>{file.name}</Typography>
                        {file.isComplete && (
                          <Typography
                            sx={{
                              color: "var(--primary-color)",
                              fontSize: "0.875rem",
                              mt: 0.5,
                            }}
                          >
                            Upload complete
                          </Typography>
                        )}
                      </Box>
                      <Box
                        sx={{ display: "flex", alignItems: "start", gap: 1 }}
                      >
                        {file.isComplete ? (
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              color: "#4CAF50",
                              p: 1,
                              borderRadius: 1,
                            }}
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                                fill="var(--primary-color)"
                              />
                            </svg>
                          </Box>
                        ) : (
                          <></>
                        )}
                      </Box>
                    </Box>
                    {!file.isComplete && (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          gap: 2,
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            color: "var(--primary-color)",
                            minWidth: "180px",
                          }}
                        >
                          {`${file.progress}%`} • Uploading • {file.size}
                        </Typography>

                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: 2,
                            width: "300px",
                          }}
                        >
                          <LinearProgress
                            variant="determinate"
                            value={file.progress}
                            sx={{
                              flex: 1,
                              height: 6,
                              borderRadius: 3,
                              backgroundColor: "#E0E0E0",
                              "& .MuiLinearProgress-bar": {
                                backgroundColor: "#00BCD4",
                                borderRadius: 3,
                              },
                            }}
                          />
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteDocument(file.name)}
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M14 5L5 14M5 5L14 14"
                                stroke="#9E9E9E"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </IconButton>
                        </Box>
                      </Box>
                    )}
                  </Box>
                ))}

                {/* Show uploaded files */}
                {uploadedFiles.map((file, index) => (
                  <Box
                    key={file.id}
                    sx={{
                      border: "1px solid #E0E0E0",
                      borderRadius: "8px",
                      backgroundColor: "#fff",
                      p: 2,
                      mb: 1,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        mb: 1,
                      }}
                    >
                      <Box>
                        <Typography
                          onClick={() => handlePreviewDocument(file.url)}
                          sx={{
                            cursor: "pointer",
                            "&:hover": {
                              color: "var(--primary-color)",
                              textDecoration: "underline",
                            },
                          }}
                        >
                          {file.name}
                        </Typography>
                        <Typography
                          sx={{
                            color: "var(--primary-color)",
                            fontSize: "0.875rem",
                            mt: 0.5,
                          }}
                        >
                          Upload complete
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            color: "#4CAF50",
                            p: 1,
                            borderRadius: 1,
                          }}
                        >
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                              fill="var(--primary-color)"
                            />
                          </svg>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrimary onClick={HandleSend}>{t("hantar")}</ButtonPrimary>
        </Box>
      </Box>
    </Box>
  );
}

export default PaparanAjk;
