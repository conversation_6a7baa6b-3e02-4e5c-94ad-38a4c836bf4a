import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setUserCommitteeBySocietyIdDataRedux, setUserCommitteeBySocietyIdError, setUserCommitteeBySocietyIdLoading } from '../userCommitteeBySocietyIdDataReducer';

export const fetchUserCommitteeBySocietyIdData = createAsyncThunk(
  'userCommitteeBySocietyId/fetchData',
  async ({ id }: { id: string },
    { dispatch }) => {
    dispatch(setUserCommitteeBySocietyIdLoading(true));
    try {
      const response = await fetch(`${API_URL}/society/appeal/getApplicantInfo?appealId=${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
      const data = await response.json();
      dispatch(setUserCommitteeBySocietyIdDataRedux(data.data));
    } catch (error: any) {
      dispatch(setUserCommitteeBySocietyIdError(error.message));
    } finally {
      dispatch(setUserCommitteeBySocietyIdLoading(false));
    }
  }
);
