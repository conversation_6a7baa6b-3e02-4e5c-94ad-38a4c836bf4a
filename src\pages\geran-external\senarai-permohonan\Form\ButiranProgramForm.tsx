import React from "react";
import { useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, Typography } from "@mui/material";
import { FormFieldRow, Label, TextFieldController } from "@/components";

const Form = () => {
  const classes = globalStyles();
  const { control } = useFormContext();

  return (
    <Box className={classes.sectionBox} mb={2}>
      <Typography className="title" mb={2}>
        Butiran program
      </Typography>

      <FormFieldRow
        label={<Label text="Nama Program" required />}
        value={<TextFieldController control={control} name="test" />}
      />

      <FormFieldRow
        label={<Label text="Tarikh" required />}
        value={<TextFieldController control={control} name="test" />}
      />

      <FormFieldRow
        label={<Label text="Tempat" required />}
        value={<TextFieldController control={control} name="test" />}
      />

      <FormFieldRow
        label={<Label text="Bilangan Peserta" required />}
        value={<TextFieldController control={control} name="test" />}
      />

      <FormFieldRow
        label={<Label text="Bilangan Program" required />}
        value={<TextFieldController control={control} name="test" />}
      />

      <FormFieldRow
        align="flex-start"
        label={<Label text="Ringkasan Program" required />}
        value={
          <TextFieldController
            control={control}
            name="test"
            multiline
            rows={3}
          />
        }
      />
    </Box>
  );
};

const ButiranProgramForm = React.memo(Form);
export default ButiranProgramForm;
