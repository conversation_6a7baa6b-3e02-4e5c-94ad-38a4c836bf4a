import { <PERSON>, <PERSON>rid, <PERSON><PERSON>ield, Typography } from "@mui/material";
import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { AJKpaparan } from "@/redux/ajkReducer";
import {
  designation,
  DocumentUploadType,
  getLocalStorage,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
  OccupationList,
  OrganisationPositions,
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { API_URL } from "@/api";
import { useCustom } from "@refinedev/core";
import { ButtonPrimary } from "@/components";

const labelStyle = {
  fontWeight: "500!important",
  marginBottom: "8px",
  fontSize: "14px",
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

function BranchPaparanAJK() {
  // const ajkPaparan = useSelector(AJKpaparan);

  const addressList = getLocalStorage("address_list", null);
  const { id, mid } = useParams();

  const branchId = Number(id);
  const memberId = Number(mid);

  const [committee, setCommittee] = useState<any>({});
  const [branchData, setBranchData] = useState<any>({});
  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${Number(branchId)}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if (branchList?.data?.data) {
      setBranchData(branchList?.data?.data);
    }
  }, [branchList]);

  const navigate = useNavigate();
  const getTitleName = (val: string) => {
    if (!val) return "Unknown Title"; // Handle null or undefined titleId
    const gelaran = ListGelaran.find((item) => item.value === val);
    return gelaran ? t(gelaran.label) : "Unknown Title"; // Fallback for unknown IDs
  };

  const getDistrict = (val = null) => {
    const address = addressList
      .filter((items: any) => items.id === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));

    return address?.[0]?.label || "-";
  };

  const StateList = addressList
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ value: item.id, label: item.name }));
  const getStateName = (stateCode: any) => {
    const stateName = addressList.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };
  const [formData, setFormData] = useState({
    position: "",
    idType: "",
    icNo: "",
    title: "",
    name: "",
    gender: "",
    citizen: "",
    dateOfBirth: "",
    placeOfBirth: "",
    pekerjaan: "",
    residentialAddress: "",
    negeri: "",
    daerah: "",
    bandar: "",
    poskod: "",
    emel: "",
    phoneNumber: "",
    nomborTelefonRumah: "",
    nomborTelefonPejabat: "",
    employerName: "",
    employerAddress: "",
    employerStateCode: "",
    employerDistrict: "",
    employerCity: "",
    employerPostcode: "",
  });

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  const [occupationTranslatedList, setOccupationTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));

    const newOList = OccupationList.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
    setOccupationTranslatedList(newOList);
  }, [t]);

  useEffect(() => {
    if (branchData?.branchCommittees) {
      console.log("memberId", memberId);
      const committeeFound = branchData.branchCommittees.find(
        (item: any) => Number(item.id) === Number(memberId)
      );
      if (committeeFound) {
        setCommittee(committeeFound);
        setFormData({
          ...formData,
          position:
            positionsTranslatedList[committeeFound?.designationCode]?.label,
          idType: committeeFound?.identityType
            ? t(
                `${
                  IdTypes.find(
                    (item: any) => item.value === committeeFound?.identityType
                  )?.label
                }`
              ) || "-"
            : "-",
          icNo: committeeFound?.committeeIcNo,
          title: committeeFound?.titleCode
            ? getTitleName(committeeFound?.titleCode)
            : "-",
          name: committeeFound?.committeeName,
          gender: committeeFound?.gender
            ? t(
                `${
                  ListGender.find(
                    (item: any) => item.value === committeeFound?.gender
                  )?.label
                }`
              ) || "-"
            : "-",
          citizen: committeeFound?.citizenshipStatus
            ? committeeFound?.citizenshipStatus
            : "-",
          dateOfBirth: committeeFound?.dateOfBirth
            ? committeeFound?.dateOfBirth
            : "",
          placeOfBirth: committeeFound?.placeOfBirth,
          pekerjaan: occupationTranslatedList[committeeFound?.jobCode]?.label,
          residentialAddress: committeeFound?.committeeAddress,
          negeri: committeeFound?.committeeStateCode
            ? getStateName(committeeFound?.committeeStateCode)
            : "-",
          daerah: committeeFound?.committeeDistrict
            ? getDistrict(committeeFound?.committeeDistrict)
            : "-",
          bandar: committeeFound?.committeeCity,
          poskod: committeeFound?.postcode,
          emel: committeeFound?.email,
          phoneNumber: committeeFound?.phoneNumber,
          nomborTelefonRumah: committeeFound?.homePhoneNumber,
          nomborTelefonPejabat: committeeFound?.officePhoneNumber,
          employerName: committeeFound?.committeeEmployerName,
          employerAddress: committeeFound?.committeeEmployerAddress,
          employerStateCode: committeeFound?.committeeEmployerStateCode
            ? getStateName(committeeFound?.committeeEmployerStateCode)
            : "-",
          employerDistrict: committeeFound?.committeeEmployerDistrict
            ? getDistrict(committeeFound?.committeeEmployerDistrict)
            : "-",
          employerCity: committeeFound?.committeeEmployerCity,
          employerPostcode: committeeFound?.committeeEmployerPostcode,
        });
      }
    }
  }, [branchData]);

  const goback = () => {
    navigate(-1);
  };

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            px: 2,
            py: 4,
          }}
        >
          {branchData?.name}
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        {/*  */}
        <Box
          sx={{
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairman")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("jawatan")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  value={formData.position}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      position: e.target.value,
                    })
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairmanPersonalInfo")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("idType")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      idType: e.target.value,
                    })
                  }
                  value={formData.idType}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">{t("idNumber")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      icNo: e.target.value,
                    })
                  }
                  value={formData.icNo}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("title")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      title: e.target.value,
                    })
                  }
                  value={formData.title}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("fullName")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      name: e.target.value,
                    })
                  }
                  value={formData.name}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("gender")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      gender: e.target.value,
                    })
                  }
                  value={formData.gender}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("citizenship")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      citizen: e.target.value,
                    })
                  }
                  value={formData.citizen}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("dateOfBirth")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  type="date"
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      dateOfBirth: e.target.value,
                    })
                  }
                  value={formData.dateOfBirth}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">{t("placeOfBirth")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      placeOfBirth: e.target.value,
                    })
                  }
                  value={formData.placeOfBirth}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("occupation")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      pekerjaan: e.target.value,
                    })
                  }
                  value={formData.pekerjaan}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("residentialAddress")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  rows={4}
                  multiline
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      residentialAddress: e.target.value,
                    })
                  }
                  value={formData.residentialAddress}
                />
              </Grid>

              {/*=========== negeri section ========*/}
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("negeri")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      negeri: e.target.value,
                    })
                  }
                  value={formData.negeri}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("daerah")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      daerah: e.target.value,
                    })
                  }
                  value={formData.daerah}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("bandar")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      bandar: e.target.value,
                    })
                  }
                  value={formData.bandar}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("poskod")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      poskod: e.target.value,
                    })
                  }
                  value={formData.poskod}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("emel")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      emel: e.target.value,
                    })
                  }
                  value={formData.emel}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("phoneNumber")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      phoneNumber: e.target.value,
                    })
                  }
                  value={formData.phoneNumber}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("nomborTelefonRumah")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      nomborTelefonRumah: e.target.value,
                    })
                  }
                  value={formData.nomborTelefonRumah}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("nomborTelefonPejabat")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={2}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  value={"+60"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      nomborTelefonPejabat: e.target.value,
                    })
                  }
                  value={formData.nomborTelefonPejabat}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("employerInfo")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography className="label">{t("employerName")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerName: e.target.value,
                    })
                  }
                  value={formData.employerName}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("employerAddress")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerAddress: e.target.value,
                    })
                  }
                  value={formData.employerAddress}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">{t("negeri")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerStateCode: e.target.value,
                    })
                  }
                  value={formData.employerStateCode}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography className="label">{t("daerah")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerDistrict: e.target.value,
                    })
                  }
                  value={formData.employerDistrict}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">{t("bandar")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerCity: e.target.value,
                    })
                  }
                  value={formData.employerCity}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography className="label">{t("poskod")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  disabled
                  size="small"
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      employerPostcode: e.target.value,
                    })
                  }
                  value={formData.employerPostcode}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}

        <Box>
          <Box sx={{ textAlign: "left", mt: 2 }}>
            {committee?.id && (
              <FileUploader
                title={t("ajkEligibilityCheck")}
                type={DocumentUploadType.CITIZEN_COMMITTEE}
                societyId={committee?.id}
                icNo={committee?.identificationNo}
                disabled={true}
                validTypes={[
                  "text/plain",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "application/pdf",
                ]}
                // onUploadComplete={handleUploadComplete}
              />
            )}
          </Box>
        </Box>

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrimary onClick={goback}>{t("back")}</ButtonPrimary>
        </Box>
      </Box>
    </Box>
  );
}

export default BranchPaparanAJK;
