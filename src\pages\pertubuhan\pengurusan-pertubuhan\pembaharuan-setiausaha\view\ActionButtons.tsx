import { useNavigate } from "react-router-dom";

import { IconButton } from "@mui/material";
import { EditIcon, TrashIcon, EyeIcon } from "@/components/icons";

interface ActionButtonsProps {
  statusCode: number;
  id: number;
  onDelete: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  statusCode,
  id,
  onDelete,
}) => {
  const navigate = useNavigate();

  const handleOnCLick = () => navigate(`${id}`)

  switch (statusCode) {
    case 1:
      return (
        <>
          <IconButton onClick={handleOnCLick} sx={{ p: 0.5 }}>
            <EditIcon sx={{ color: "var(--primary-color)" }} />
          </IconButton>
          <IconButton onClick={onDelete} sx={{ p: 0.5 }}>
            <TrashIcon sx={{ color: "#FF0000" }} />
          </IconButton>
        </>
      );
    case 36:
      return (
        <IconButton sx={{ p: 0.5 }} onClick={handleOnCLick}>
          <EditIcon sx={{ color: "var(--primary-color)" }} />
        </IconButton>
      );
    default:
      return (
        <IconButton sx={{ p: 0.5 }} onClick={handleOnCLick}>
          <EyeIcon />
        </IconButton>
      );
  }
};

export default ActionButtons;
