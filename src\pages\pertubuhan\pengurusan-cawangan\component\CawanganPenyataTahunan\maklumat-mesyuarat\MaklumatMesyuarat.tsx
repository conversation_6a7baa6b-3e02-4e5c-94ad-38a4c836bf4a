import { Box, Grid, Typography } from '@mui/material'
import React from 'react'
import Input from '@/components/input/Input';
import { useTranslation } from 'react-i18next';
import { Meeting } from '../interface';

type props = {
  sectionStyle: any
  meeting: Meeting | undefined
};
const MaklumatMesyuarat: React.FC<props> = ({ sectionStyle, meeting }) => {
  const { t } = useTranslation();

  return (
    <Box sx={{
      background: "white",
      border: "1px solid rgba(0, 0, 0, 0.12)",
      borderRadius: "14px",
      p: 3,
      mb: 2,
    }}>
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("maklumatMesyuarat")}
      </Typography>
      <Grid item xs={12}>
        <Input label={t("jumlahKehadiranAhliMesyuarat")} value={meeting?.totalAttendees} disabled />
      </Grid>
    </Box>
  )
}

export default MaklumatMesyuarat
