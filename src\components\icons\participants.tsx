import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const PaticipantsIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"
         style={{ color, ...sx }}
         {...props}
    >
      <path d="M6 6.19922C5.46957 6.19922 4.96086 5.98851 4.58579 5.61343C4.21071 5.23836 4 4.72965 4 4.19922C4 3.66879 4.21071 3.16008 4.58579 2.78501C4.96086 2.40993 5.46957 2.19922 6 2.19922C6.53043 2.19922 7.03914 2.40993 7.41421 2.78501C7.78929 3.16008 8 3.66879 8 4.19922C8 4.72965 7.78929 5.23836 7.41421 5.61343C7.03914 5.98851 6.53043 6.19922 6 6.19922ZM6 2.99922C5.336 2.99922 4.8 3.53522 4.8 4.19922C4.8 4.86322 5.336 5.39922 6 5.39922C6.664 5.39922 7.2 4.86322 7.2 4.19922C7.2 3.53522 6.664 2.99922 6 2.99922Z" fill="#666666"/>
      <path d="M10.8 9.39922C10.576 9.39922 10.4 9.22322 10.4 8.99922C10.4 8.77522 10.576 8.59922 10.8 8.59922C11.024 8.59922 11.2 8.42322 11.2 8.19922C11.2 7.66879 10.9893 7.16008 10.6142 6.78501C10.2391 6.40993 9.73043 6.19922 9.2 6.19922H8.4C8.176 6.19922 8 6.02322 8 5.79922C8 5.57522 8.176 5.39922 8.4 5.39922C9.064 5.39922 9.6 4.86322 9.6 4.19922C9.6 3.53522 9.064 2.99922 8.4 2.99922C8.176 2.99922 8 2.82322 8 2.59922C8 2.37522 8.176 2.19922 8.4 2.19922C8.93043 2.19922 9.43914 2.40993 9.81421 2.78501C10.1893 3.16008 10.4 3.66879 10.4 4.19922C10.4 4.69522 10.224 5.14322 9.92 5.49522C11.112 5.81522 12 6.90322 12 8.19922C12 8.86322 11.464 9.39922 10.8 9.39922ZM1.2 9.39922C0.536 9.39922 0 8.86322 0 8.19922C0 6.90322 0.88 5.81522 2.08 5.49522C1.784 5.14322 1.6 4.69522 1.6 4.19922C1.6 3.66879 1.81071 3.16008 2.18579 2.78501C2.56086 2.40993 3.06957 2.19922 3.6 2.19922C3.824 2.19922 4 2.37522 4 2.59922C4 2.82322 3.824 2.99922 3.6 2.99922C2.936 2.99922 2.4 3.53522 2.4 4.19922C2.4 4.86322 2.936 5.39922 3.6 5.39922C3.824 5.39922 4 5.57522 4 5.79922C4 6.02322 3.824 6.19922 3.6 6.19922H2.8C2.26957 6.19922 1.76086 6.40993 1.38579 6.78501C1.01071 7.16008 0.8 7.66879 0.8 8.19922C0.8 8.42322 0.976 8.59922 1.2 8.59922C1.424 8.59922 1.6 8.77522 1.6 8.99922C1.6 9.22322 1.424 9.39922 1.2 9.39922ZM8.4 11.7992H3.6C2.936 11.7992 2.4 11.2632 2.4 10.5992V9.79922C2.4 8.25522 3.656 6.99922 5.2 6.99922H6.8C8.344 6.99922 9.6 8.25522 9.6 9.79922V10.5992C9.6 11.2632 9.064 11.7992 8.4 11.7992ZM5.2 7.79922C4.66957 7.79922 4.16086 8.00993 3.78579 8.38501C3.41071 8.76008 3.2 9.26879 3.2 9.79922V10.5992C3.2 10.8232 3.376 10.9992 3.6 10.9992H8.4C8.624 10.9992 8.8 10.8232 8.8 10.5992V9.79922C8.8 9.26879 8.58929 8.76008 8.21421 8.38501C7.83914 8.00993 7.33043 7.79922 6.8 7.79922H5.2Z" fill="#666666"/>
    </svg>

  );
});
