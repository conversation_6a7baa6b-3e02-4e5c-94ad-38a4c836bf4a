import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { CrudFilter } from "@refinedev/core";
import { FieldValues, useForm } from "react-hook-form";
import useQuery from "../../../helpers/hooks/useQuery";
import {
  getLocalStorage,
  debounce,
  capitalizeWords,
} from "../../../helpers/utils";

import { Box, Typography, IconButton, CircularProgress } from "@mui/material";
import DataTable, { IColumn } from "../../../components/datatable";
import FormFieldRow from "../../../components/form-field-row";
import Label from "../../../components/label/Label";
import TextFieldController from "../../../components/input/TextFieldController";
import SelectFieldController from "../../../components/input/select/SelectFieldController";

import { EditIcon } from "../../../components/icons";
import { SocietyCategoryResponseBodyGet } from "@/types";
import { useBackendLocalization } from "@/helpers";

const PendaftaranIndukMenugguUlasanLuarTab = <
  CategoryData extends SocietyCategoryResponseBodyGet = SocietyCategoryResponseBodyGet
>() => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { getTranslation } = useBackendLocalization<CategoryData>({
    enAttribute: "categoryNameEn",
    bmAttribute: "categoryNameBm",
  });
  const [subCategoriesOptions, setSubCategoriesOptions] = useState<
    { label: string; value: string }[]
  >([]);

  const columns: IColumn[] = [
    {
      field: "applicationNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.applicationNo;
      },
    },
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.societyName;
      },
    },
    {
      field: "tarikhPermohonan",
      headerName: t("tarikhPermohonan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.registeredDate ? row?.registeredDate : "-";
      },
    },
    {
      field: "paymentDate",
      headerName: t("paymentDate"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.paymentDate ? row?.paymentDate : "-";
      },
    },
    {
      field: "roName",
      headerName: "RO",
      flex: 1,
      align: "center",
      renderCell: (params: any) => params?.row?.roName ?? "-",
    },
    {
      field: "stateName",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      renderCell: (params: any) =>
        params?.row?.stateName
          ? capitalizeWords(params?.row?.stateName, null, true)
          : "-",
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box>
            <IconButton
              onClick={() => {
                const id = btoa(row.id);
                navigate(
                  `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/external-agency/${id}`
                );
              }}
              sx={{ color: "black", minWidth: 0, p: 0.5 }}
            >
              <EditIcon
                sx={{
                  color: "var(--primary-color)",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const { control, setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      category: "",
      subCategory: "",
      societyName: "",
      page: 1,
      pageSize: 10,
    },
  });

  const { category, page, societyName, pageSize } = watch();

  const {
    data: externalAgencyReview,
    refetch: fetchExternalAgencyReview,
    isLoading,
  } = useQuery({
    url: "society/roDecision/getAllPending/society/externalAgencyReview",
    autoFetch: false,
  });

  const totalList = externalAgencyReview?.data?.data?.total ?? 0;
  const externalAgencyReviewList = externalAgencyReview?.data?.data?.data ?? [];

  const categories = getLocalStorage("category_list", []) as CategoryData[];
  const mainCategories = categories.filter((cat) => cat.level === 1) ?? [];

  const mainCategoriesOptions = mainCategories?.map((category) => ({
    value: category.id.toString(),
    label: getTranslation(category),
  }));

  const handleSearchSocietyName = useCallback(
    debounce((e) => {
      const value = e.target.value.trim();

      const filters: CrudFilter[] = [
        { field: "pageSize", value: pageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
      ];

      if (value) filters.push({ field: "societyName", value, operator: "eq" });

      fetchExternalAgencyReview({ filters });
    }, 2000),
    [pageSize, externalAgencyReviewList]
  );

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
    ];

    if (societyName) {
      filters.push({
        field: "societyName",
        value: societyName,
        operator: "eq",
      });
    }

    setValue("page", newPage);
    fetchExternalAgencyReview({ filters });
  };

  useEffect(() => {
    fetchExternalAgencyReview({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: 1,
          operator: "eq",
        },
      ],
    });
  }, []);

  useEffect(() => {
    if (category) {
      const subCategories =
        categories.filter((cat) => cat.pid === parseInt(category)) ?? [];
      const subCategoriesOptions = subCategories?.map((category) => ({
        value: category.id.toString(),
        label: getTranslation(category),
      }));
      setSubCategoriesOptions(subCategoriesOptions);
      setValue("subCategory", "");
    }
  }, [category]);

  return (
    <>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("persatuanMenungguUlasanLuar")}
          </Typography>

          <FormFieldRow
            label={<Label text={t("organization_category")} />}
            value={
              <SelectFieldController
                name="category"
                control={control}
                options={mainCategoriesOptions}
                placeholder={t("selectPlaceholder")}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationSubCategory2")} />}
            value={
              <SelectFieldController
                name="subCategory"
                control={control}
                options={subCategoriesOptions}
                placeholder={t("selectPlaceholder")}
                disabled={!category || subCategoriesOptions.length === 0}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("namaPertubuhan")} />}
            value={
              <TextFieldController
                name="societyName"
                control={control}
                onChange={handleSearchSocietyName}
              />
            }
          />
        </Box>
      </Box>

      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: "13px",
            padding: "15px 0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            marginBottom: 1,
            backgroundColor: "var(--primary-color)",
          }}
        >
          <Typography
            fontWeight="500 !important"
            fontSize="36px"
            color="#FFF"
            textAlign="center"
            lineHeight="30px"
            sx={{
              "& span": {
                fontSize: "20px",
              },
            }}
          >
            {isLoading ? (
              <CircularProgress
                size={25}
                sx={{
                  "& .MuiCircularProgress-svg": {
                    color: "#FFF",
                  },
                }}
              />
            ) : (
              totalList || 0
            )}{" "}
            <br />
            <span>{t("senaraiMenungguUlasanLuar")}</span>
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="37px"
          >
            {t("senaraiMenungguUlasanLuar")}
          </Typography>

          <DataTable
            columns={columns}
            rows={externalAgencyReviewList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            onPageChange={handleChangePage}
          />
        </Box>
      </Box>
    </>
  );
};

export default PendaftaranIndukMenugguUlasanLuarTab;
