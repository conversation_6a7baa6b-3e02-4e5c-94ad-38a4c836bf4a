import { useEffect } from "react";
import { useLocation, useNavigationType } from "react-router-dom";

const SCROLL_SELECTOR = ".main-layout-content";

const ScrollRestoration = () => {
  const location = useLocation();
  const navigationType = useNavigationType();

  // Save scroll on every scroll event
  useEffect(() => {
    const el = document.querySelector(SCROLL_SELECTOR);
    if (!el) return;

    const handleScroll = () => {
      sessionStorage.setItem(
        `scroll-${location.pathname}`,
        String(el.scrollTop)
      );
      // console.log("Live scroll:", el.scrollTop);
    };

    el.addEventListener("scroll", handleScroll);

    return () => {
      el.removeEventListener("scroll", handleScroll);
    };
  }, [location.pathname]);

  // Restore scroll
  useEffect(() => {
    const el = document.querySelector(SCROLL_SELECTOR);
    const y = sessionStorage.getItem(`scroll-${location.pathname}`);
    if (!el || y === null) return;

    const targetY = parseInt(y, 10);
    let attempts = 0;

    const tryScroll = () => {
      if (el.scrollHeight > el.clientHeight || attempts >= 10) {
        el.scrollTop = targetY;
        // console.log("Restoring scroll for", location.pathname, "to", targetY);
      } else {
        attempts++;
        requestAnimationFrame(tryScroll);
      }
    };

    tryScroll();
  }, [location.pathname, navigationType]);

  return null;
};

export default ScrollRestoration;
