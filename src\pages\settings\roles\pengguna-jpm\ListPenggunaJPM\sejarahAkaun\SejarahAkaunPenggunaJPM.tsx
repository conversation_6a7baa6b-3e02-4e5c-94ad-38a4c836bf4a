import React, { useState } from "react";
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Formik } from "formik";
import {
  SettingsJPPMUserCreateRequestBody,
  useFormManagementSettingsJPPMUserCreateInitialValue,
  useFormManagementSettingsJPPMUserCreateValidationSchema
} from "@/controllers";
import { useQuery, formatDate } from "@/helpers";
import { UserJPPMFormDisplay } from "../components/UserJPPMFormDisplay";

interface HistoricalUserData extends SettingsJPPMUserCreateRequestBody {
  createdDate?: number[] | string;
  formNumber?: number;
}

function SejarahAkaunPenggunaJPM() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id");
  const [expandedAccordion, setExpandedAccordion] = useState<number | false>(false);

  const { getInitialValue } = useFormManagementSettingsJPPMUserCreateInitialValue<SettingsJPPMUserCreateRequestBody>();
  const { getValidationSchema } = useFormManagementSettingsJPPMUserCreateValidationSchema();

  // Fetch current user data (includes historical data in the response)
  const { data: userResponse, isLoading: isLoadingUser } = useQuery<{
    data: SettingsJPPMUserCreateRequestBody
  }>({
    url: `user/admin/${userId}`,
    autoFetch: !!userId, // Auto-fetch when userId is available
  });

  const currentUserData = userResponse?.data?.data ?? null;

  // Extract historical data from the main user response
  const historicalData: HistoricalUserData[] =
    ((currentUserData as any)?.historicalUsers ? (currentUserData as any).historicalUsers : []);
  const currentInitialValue = getInitialValue(currentUserData);

  const handleAccordionChange = (panel: number) => (
    _event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  const formatAccordionTitle = (data: HistoricalUserData, index: number) => {
    const formNumber = data.formNumber || index + 1;
    const createdDate = data.createdDate
      ? formatDate(data.createdDate, "DD/MM/YYYY")
      : "-";
    return `Borang ${formNumber}: ${createdDate}`;
  };

  if (!userId) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error">
          {t("userIdRequired")}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      {/* Historical Data Section - Top */}
      {historicalData.length > 0 && (
        <Box>
          {historicalData.map((historicalUser, index) => {
            const historicalInitialValue = getInitialValue(historicalUser);
            return (
              <Box
                key={index}
                sx={{
                  mb: 2,
                  backgroundColor: "white",
                  borderRadius: "8px",
                  border: "1px solid white",
                }}
              >
                <Accordion
                  expanded={expandedAccordion === index}
                  onChange={handleAccordionChange(index)}
                  sx={{
                    boxShadow: "none",
                    "&:before": {
                      display: "none",
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      margin: "7px",
                      backgroundColor: "white",
                      borderRadius: "8px",
                      border: "1px solid #DADADA",
                      "& .MuiAccordionSummary-content": {
                        margin: "12px 0",
                      },
                      "&:hover": {
                        backgroundColor: "#f9f9f9",
                      },
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: "500",
                        color: "var(--primary-color)",
                      }}
                    >
                      {formatAccordionTitle(historicalUser, index)}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails
                    sx={{
                      backgroundColor: "white",
                      borderRadius: "10px",
                      border: "1px solid #DADADA",
                      margin: "24px"
                    }}
                  >
                    <Formik
                      initialValues={historicalInitialValue}
                      validationSchema={getValidationSchema()}
                      onSubmit={() => {}} // No submit for historical data
                      enableReinitialize
                    >
                      <UserJPPMFormDisplay
                        initialValues={historicalInitialValue}
                        isLoadingUserData={isLoadingUser}
                        isReadOnly={true}
                        showButtons={false}
                        showHistoricalLink={false}
                        title={`${t("maklumatPenggunaJPPM")}`}
                        userData={historicalUser}
                      />
                    </Formik>
                  </AccordionDetails>
                </Accordion>
              </Box>
            );
          })}
        </Box>
      )}

      {/* Current Account Section - Bottom */}
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Formik
            initialValues={currentInitialValue}
            validationSchema={getValidationSchema()}
            onSubmit={() => {}} // No submit for view-only page
            enableReinitialize
          >
            <UserJPPMFormDisplay
              initialValues={currentInitialValue}
              isLoadingUserData={isLoadingUser}
              isReadOnly={true}
              showButtons={false}
              showHistoricalLink={false}
              userData={currentUserData}
            />
          </Formik>
        </Box>
      </Box>
    </Box>
  );
}

export default SejarahAkaunPenggunaJPM;
