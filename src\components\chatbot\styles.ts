import { SxProps, Theme } from "@mui/material";

/**
 * Styles for the chatbot components
 */

// Container styles based on mobile/desktop and position
export const getContainerStyles = (
  isMobile: boolean,
  positionState: string
): SxProps<Theme> => ({
  ...(isMobile && {
    position: "fixed",
    width: 320,
    height: 480,
    bottom: 24,
    ...(positionState === "right" && { right: 24 }),
    ...(positionState === "left" && { left: 24 }),
    ...(positionState === "center" && {
      left: "50%",
      transform: "translateX(-50%)",
    }),
    zIndex: 2001,
    "@media screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.75), screen and (min-resolution: 144dpi) and (max-resolution: 168dpi)":
      {
        transform: "scale(0.75)",
        transformOrigin: "bottom right",
      },
  }),
  ...(!isMobile && {
    height: "inherit",
  }),
});

// Chat box styles
export const chatBoxStyles: SxProps<Theme> = {
  position: "relative",
  display: "flex",
  flexDirection: "column",
  borderRadius: 3,
  bgcolor: "rgba(255, 255, 255, 0.9)",
  border: "3px solid turquoise",
};

// Header styles
export const headerStyles: SxProps<Theme> = {
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  p: 2,
};

// Header title styles
export const headerTitleStyles: SxProps<Theme> = {
  fontWeight: "bold",
  display: "flex",
  alignItems: "center",
  gap: 1,
};

// Close button styles
export const closeButtonStyles: SxProps<Theme> = {
  backgroundColor: "transparent",
  "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.1)" },
  boxShadow: "none",
};

// Message container styles
export const messageContainerStyles: SxProps<Theme> = {
  flex: 1,
  p: 2,
  overflowY: "auto",
};

// Animation keyframes
export const animationKeyframes = `
  @keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
  }
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  @keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
  }
  .blinking-cursor {
    animation: blink 1s infinite;
  }
`;
