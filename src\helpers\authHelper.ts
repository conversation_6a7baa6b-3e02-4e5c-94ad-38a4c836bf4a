import { TOKEN_KEY, USER_DETAILS_KEY } from "@/helpers";
import { pageAccessEnum } from "@/helpers/enums";

class AuthHelper {
  static getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  }

  static setToken(token: string) {
    localStorage.setItem(TOKEN_KEY, token);
  }

  static clearToken() {
    localStorage.removeItem(TOKEN_KEY);
  }

  static getUserAuthorities(): string[] {
    const token = this.getToken();
    if (!token) return [];

    try {
      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT (base64)
      const authorities = payload.authorities.map((auth: string) => auth.toLowerCase());
      return authorities || [];
    } catch (error) {
      console.error('Invalid token format:', error);
      return [];
    }
  }

  static hasAuthority(requiredAuthorities: string | string[]): boolean {
    const userAuthorities: string[] = this.getUserAuthorities();

    if (!userAuthorities || userAuthorities.length === 0) {
        return false;
    }

    if (typeof requiredAuthorities === 'string') {
        // Check for a single authority
        return userAuthorities.includes(requiredAuthorities.toLowerCase());
    }

    if (Array.isArray(requiredAuthorities)) {
        // Check if at least one required authority exists in the user's authorities
        return requiredAuthorities.some(auth => userAuthorities.includes(auth.toLowerCase()));
    }

    return false;
  }

  static hasPageAccess(permissionLabel: string, accessType: number): boolean {
    const userAuthorities: string[] = this.getUserAuthorities();

    if (!userAuthorities || userAuthorities.length === 0) {
        return false;
    }

    // Check if user has the specific permission with the numeric access type
    const permissionString = `${permissionLabel}:${accessType}`;
    return userAuthorities.includes(permissionString.toLowerCase());
  }
}

export default AuthHelper;