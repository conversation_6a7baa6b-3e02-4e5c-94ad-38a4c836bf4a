import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import InputLabel from "@mui/material/InputLabel";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import { Select, Option } from "../../../components/input";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCreate } from "@refinedev/core";
import Input from "../../../components/input/Input";
import { useNavigate, useSearchParams } from "react-router-dom";

export const Term = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const [isAgreed, setIsAgreed] = useState(false);
  const [searchParams] = useSearchParams();
  // const appealId = searchParams.get("appealId");
  const societyId = searchParams.get("societyId");

  const navigate = useNavigate();

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("terms")}
                </Typography>
              </Box>

              <Box sx={{ pl: 5 }}>
                <List
                  component="ol"
                  sx={{
                    gridColumn: 2,
                    listStyleType: "decimal",
                    pl: 0,
                    py: 0,
                    "& ::marker": {},

                    mb: 4,
                    alignItems: "center",
                  }}
                >
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term1")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term2")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term3")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term4")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term5")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term6")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term7")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term8")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term9")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                  <ListItem
                    component="li"
                    sx={{
                      display: "list-item",
                      pl: 0,
                      py: 0,
                      mt: 2,
                    }}
                  >
                    <ListItemText
                      primary={t("term10")}
                      primaryTypographyProps={{
                        variant: "body2",
                        maxWidth: "120ch",
                        fontWeight: "500 !important",
                      }}
                    />
                  </ListItem>
                </List>
              </Box>
            </Box>
            <Grid container spacing={2} pl={5} pt={2} mb={2}>
              <Box sx={{ fontSize: "14px" }}>
                <Checkbox
                  sx={{ p: 0, mr: 1 }}
                  checked={isAgreed}
                  onChange={(e) => setIsAgreed(e.target.checked)}
                />
                {t("setuju")}{" "}
                <Typography sx={{ display: "inline", color: "red" }}>
                  *
                </Typography>
              </Box>
            </Grid>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 15,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={() => navigate(`../butiran?societyId=${societyId}`)}
                disabled={!isAgreed}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("bayar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmBuyDokumen")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>
    </Box>
  );
};

export default Term;
