import React, { useState } from "react";
import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import KehadiranAJK from "./KehadiranAJK";
import MaklumatMesyuaratPenyataTahunan from "./MaklumatMesyuaratPenyataTahunan";
import AlamatTempatMesyuarat from "./AlamatTempatMesyuarat";
import MaklumatMesyuarat from "./MaklumatMesyuarat";
import useQuery from "@/helpers/hooks/useQuery";
import { Meeting } from "../interface";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { ApplicationStatus } from "@/helpers";
import MessageDialog from "@/components/dialog/message";

export const CreateMesyuaratAgung: React.FC = () => {
  const { id } = useParams(); // Access the dynamic `id` from the URL (e.g., "406")
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  // ====
  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;

  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const [statementComplete, setStatementComplete] = useState(false);

  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const { t } = useTranslation();
  const navigate = useNavigate();

  const [businessCoords, setBusinessCoords] = useState<[number, number]>([
    2.745564, 101.707021,
  ]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const { mutate: saveGeneralInfo } = useCustomMutation();

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const handleNextActions = () => {
    if (isDisabled || statementComplete) {
      navigate(`../ajk`);
    } else {
      saveGeneralInfo(
        {
          url: `${API_URL}/society/statement/societyInfo/update`,
          method: "put",
          values: {
            meetingDate: selectedDate,
            societyId: societyId,
            statementId: statementId,
            meetingType: meeting?.meetingType,
            meetingId: meeting?.id,
            branchId: branchDataRedux.id,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess: () => {
            handleNext();
            navigate(`../maklumat-am`, {
              state: {
                societyId: societyId,
                statementId: statementId,
                year: year,
                branchId: branchDataRedux.id,
              },
            });
          },
        }
      );
    }
  };

  const { isLoading: isMeetinglistLoading } = useQuery({
    url: `society/statement/general/meeting/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const meetings = data?.data?.data || [];
      setMeetings(meetings);
      const availableList = meetings.map((item: any) => {
        return item.meetingDate;
      });
      setAvailableDateList(availableList);
    },
  });
  const [selectedDate, setSelectedDate] = useState("");
  const [meetingId, setMeetingId] = useState<string | number>("");
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [meeting, setMeeting] = useState<Meeting>();
  const handleSetMeeting = (meeting: Meeting) => {
    setMeetingId(meeting?.id);
    setMeeting(meeting);
  };
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [filteredMeetings, setFilteredMeeting] = useState<Meeting[]>([]);
  const handleSetFilteredMeeting = (meetings: Meeting[]) => {
    setFilteredMeeting(meetings);
    if (meetings && meetings.length > 0) {
      setIsDialogOpen(false);
    } else {
      setIsDialogOpen(true);
    }
  };

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
      const generalInfo = data?.data?.data || [];
      setSelectedDate(generalInfo?.meetingDate);
      setMeetingId(generalInfo?.meetingId);
    },
  });

  const [savedMeetingDate, setSavedMeetingDate] = useState("");

  function goMeetingPage() {
    navigate(`../../mesyuarat`);
  }

  function resetChanges() {
    setFilteredMeeting([]);
    setMeeting(undefined);
  }

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        {/* peringatan */}
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <br />
            <span style={{ fontSize: 12 }}>
              {t("penyataTahunan_peringatan_point")}
            </span>
          </Typography>
        </Box>

        {/* Maklumat Mestyuarat Penyata Tahunan */}
        {isMeetinglistLoading ? null : (
          <MaklumatMesyuaratPenyataTahunan
            isDisabled={isDisabled || statementComplete}
            selectedDate={selectedDate}
            meetingId={meetingId}
            setSelectedDate={setSelectedDate}
            filteredMeetings={filteredMeetings}
            handleSetFilteredMeeting={handleSetFilteredMeeting}
            sectionStyle={sectionStyle}
            businessCoords={businessCoords}
            meetings={meetings}
            meeting={meeting}
            handleSetMeeting={handleSetMeeting}
            availableDateList={availableDateList}
            setSavedMeetingDate={setSavedMeetingDate}
            savedMeetingDate={savedMeetingDate}
            resetChanges={resetChanges}
          />
        )}

        {meeting ? (
          // Maklumat Mestyuarat Penyata Tahunan
          <>
            {/* alamat tempat mesyuarat */}
            <AlamatTempatMesyuarat
              sectionStyle={sectionStyle}
              meeting={meeting}
            />
            {/* maklumat mesyuarat */}
            <MaklumatMesyuarat sectionStyle={sectionStyle} meeting={meeting} />
            {/* kehadiran ajk */}
            <KehadiranAJK ahli={meeting?.meetingMemberAttendances} />
            {/* Minit Mesyuarat */}
            {/* <MinitMesyuarat subTitleStyle={subTitleStyle} /> */}
          </>
        ) : null}

        {/* Buttons */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            mt: 2,
          }}
        >
          <ButtonOutline variant="outlined" onClick={() => handleBackActions()}>
            {t("semula")}
          </ButtonOutline>
          <ButtonPrimary onClick={handleNextActions}>{t("next")}</ButtonPrimary>
        </Box>
      </Box>
      <MessageDialog
        open={isDialogOpen}
        onClickFunction={() => goMeetingPage()}
        buttonText={t("Viewlist")}
        onClose={() => setIsDialogOpen(false)}
        message={t("createMeetingReminder")}
      />
    </>
  );
};

export default CreateMesyuaratAgung;
