import { useDispatch, useSelector } from "react-redux"
import { useEffect } from "react"
import isEqual from "lodash.isequal"

import { getUserDetails, getUserToken, setUserDataRedux, setUserTokenRedux } from "@/redux/userReducer";
import { getLocalStorage, USER_DETAILS_KEY } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

export function useUserRedux() {
  const dispatch = useDispatch();
  const savedUserDataRedux = useSelector(getUserDetails);
  const savedUserToken = useSelector(getUserToken);

  const savedUserDataLocalStorage = getLocalStorage(USER_DETAILS_KEY, null);
  const savedTokenLocalStorage = AuthHelper.getToken();

  useEffect(() => {
    if (!savedUserDataRedux || !savedUserToken || !isEqual(savedUserDataRedux, savedUserDataLocalStorage)) {
      dispatch(setUserDataRedux(savedUserDataLocalStorage))
      dispatch(setUserTokenRedux(savedTokenLocalStorage))
    }
  }, [])
}
