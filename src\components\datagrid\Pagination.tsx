import { TablePaginationProps } from "@mui/material";
import { GridPagination } from "@mui/x-data-grid";
import { ElementType } from "react";
import { DataTablePagination } from "../datatable/Pagination";

interface GridPaginationOwnProps {
  component?: ElementType;
}

export type GridPaginationProps = Omit<Partial<Omit<TablePaginationProps, "component">> & GridPaginationOwnProps, "ref">

export const DataGridPagination = <
  PropType extends GridPaginationProps = GridPaginationProps
>({  labelDisplayedRows = () => "", ...props }: PropType) =>
  <GridPagination ActionsComponent={DataTablePagination} {...{ labelDisplayedRows }} {...props} />
