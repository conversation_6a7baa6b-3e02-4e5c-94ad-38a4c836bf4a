import { useTranslation } from "react-i18next";
import { useSecretaryReformContext } from "./Provider";

import { Box, Typography, Fade } from "@mui/material";
import SecretaryForm from "./view/SecretaryForm";
import MeetingForm from "./view/meeting-form";
import { CustomSkeleton, SocietyBanner } from "@/components";
import { useParams } from "react-router-dom";
import { ROApprovalType, useMutation, useQuery } from "@/helpers";
import { useEffect, useState } from "react";

const formComponents: Record<number, React.ReactNode> = {
  1: <SecretaryForm />,
  2: <MeetingForm />,
};

const FormPembaharuanSetiaUsaha: React.FC = () => {
  // const secretaryId = params.secretaryId;
  // const location = useLocation();
  // const hasSec = location.pathname.includes("pembaharuan-setiausaha");
  const { societyId, secretaryId } = useParams();

  const { t, i18n } = useTranslation();
  const {
    formStep,
    societyData,
    isViewOnly,
    isLoadingSociety,
    isSecretaryReformSuccess,
  } = useSecretaryReformContext();
  const isMyLanguage = i18n.language === "my";

  const secretaryReformList = [
    {
      id: 1,
      label: t("maklumatSetiausahaBaru"),
    },
    {
      id: 2,
      label: t("maklumatMesyuarat"),
    },
  ];

  const renderForm = () => formComponents[formStep] || null;

  const [ROQuery, setROQuery] = useState<any>(null);
  const [ROApproval, setROApproval] = useState<any>(null);
  const [secretary, setSecretary] = useState<any>(null);

  const { fetch: getRoApproval, isLoading: isLoadingRoApproval } = useMutation({
    url: "society/roApproval/getAll",
    method: "post",
    onSuccess: (data) => {
      const queryData = data?.data?.data;
      if (queryData?.length > 0) {
        setROApproval(queryData?.[0]);
      }
    },
    errorNotification: () => {
      return {
        message: t("error"),
        type: "error",
      };
    },
    onSuccessNotification: () => {},
    onErrorNotification() {},
  });

  const { fetch: getQuery, isLoading: isLoadingQuery } = useMutation({
    url: "society/roQuery/getQuery",
    method: "post",
    onSuccess: (data) => {
      const queryData = data?.data?.data;
      if (queryData?.length > 0) {
        setROQuery(queryData?.[0]);
      }
    },
    errorNotification: () => {
      return {
        message: t("error"),
        type: "error",
      };
    },
    onSuccessNotification: () => {},
    onErrorNotification() {},
  });

  const {
    data: secretaryResponse,
    refetch: fetchSecretary,
    isLoading: isLoadingSecretary,
  } = useQuery({
    url: `society/secretary/principal/${secretaryId}`,
    autoFetch: false,
    onSuccess: (res) => {
      const data = res?.data?.data;
      if (!data) return;
      setSecretary(data);
    },
  });

  useEffect(() => {
    if (secretaryId && secretaryId) fetchSecretary();
  }, [secretaryId, secretaryId]);

  useEffect(() => {
    if (secretary && societyId) {
      if (
        Number(secretary?.applicationStatusCode) === 3 ||
        Number(secretary?.applicationStatusCode) === 4
      ) {
        const roApprovalPayload = {
          societyId: societyId,
          type: ROApprovalType.SOCIETY_NEW_SECRETARY.code,
          principalSecretaryId: secretary?.id,
        };
        getRoApproval(roApprovalPayload);
      } else if (Number(secretary?.applicationStatusCode) === 36) {
        const payload = {
          societyId: societyId,
          roApprovalType: ROApprovalType.SOCIETY_NEW_SECRETARY.code,
          principalSecretaryId: secretary?.id,
        };
        getQuery(payload);
      }
    }
  }, [secretary, societyId]);

  return (
    <Box sx={{ display: "flex", gap: "22px" }}>
      <Box
        sx={{
          display: {
            xs: "none",
            lg: "block",
          },
        }}
      >
        <Box
          sx={{
            minWidth: "190px",
            background: "#fff",
            boxShadow: "0px 12px 12px 0px #EAE8E866",
            borderRadius: "15px",
            padding: "20px 12px",
            height: "fit-content",
            display: {
              xs: "none",
              lg: "block",
            },
          }}
        >
          <Typography
            fontWeight="400 !important"
            fontSize="12px"
            color="var(--primary-color)"
            mb="35px"
          >
            {isMyLanguage
              ? "Langkah Pembaharuan Setiausaha"
              : "Secretary Reform Measures"}
          </Typography>

          {secretaryReformList.map((item) => (
            <Box
              key={item.label}
              sx={{
                display: "flex",
                gap: 1,
                marginBottom: "15px",
                "&:nth-last-of-type(1)": {
                  marginBottom: 0,
                },
              }}
            >
              <Box
                sx={{
                  backgroundColor:
                    formStep > item.id || isSecretaryReformSuccess || isViewOnly
                      ? "var(--primary-color)"
                      : "#FFF",
                  flexShrink: 0,
                  padding: 0,
                  margin: 0,
                  width: "12px",
                  height: "12px",
                  border:
                    formStep >= item.id ||
                    isSecretaryReformSuccess ||
                    isViewOnly
                      ? "1px solid var(--primary-color)"
                      : "1px solid #DADADA",
                  borderRadius: "3px",
                  marginTop: "3px",
                }}
              />
              <Typography
                fontSize="12px"
                color={
                  formStep >= item.id || isSecretaryReformSuccess || isViewOnly
                    ? "var(--primary-color)"
                    : "#DADADA"
                }
                fontWeight={
                  formStep > item.id || isSecretaryReformSuccess || isViewOnly
                    ? "500 !important"
                    : "400 !important"
                }
              >
                {item.label}
              </Typography>
            </Box>
          ))}
        </Box>

        {ROQuery?.note || ROApproval?.note ? (
          <Box
            sx={{
              minWidth: "190px",
              mt: 2,
              background: "#fff",
              boxShadow: "0px 12px 12px 0px #EAE8E866",
              borderRadius: "15px",
              padding: "20px 12px 1px 12px",
              display: {
                xs: "none",
                lg: "block",
              },
            }}
          >
            <Typography
              fontWeight="400 !important"
              fontSize="12px"
              color="var(--primary-color)"
              mb="15px"
            >
              {t("remarks")}
            </Typography>
            <Typography fontWeight="400 !important" fontSize="12px" mb="35px">
              {ROQuery?.note
                ? ROQuery.note
                : ROApproval?.note
                ? ROApproval.note
                : null}
            </Typography>
          </Box>
        ) : null}
      </Box>

      <Box sx={{ width: "100%" }}>
        <Box
          sx={{
            backgroundColor: "white",
            p: 3,
            borderRadius: "16px",
            marginBottom: 3,
          }}
        >
          <SocietyBanner
            societyName={societyData?.societyName ?? ""}
            societyNo={societyData?.societyNo ?? ""}
            isLoading={isLoadingSociety}
          />
        </Box>

        {societyData ? (
          <Fade in={true} timeout={500} key={formStep}>
            <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "16px" }}>
              {renderForm()}
            </Box>
          </Fade>
        ) : (
          <CustomSkeleton />
        )}
      </Box>
    </Box>
  );
};

export default FormPembaharuanSetiaUsaha;
