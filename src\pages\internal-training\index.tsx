import React, {useState} from "react";
import {Box, IconButton, InputAdornment, Stack, TextField, Typography} from "@mui/material";
import InternalTrainingDashboard from "@/pages/internal-training/dashboard";
import InternalTrainingDashboardSidebar from "@/pages/internal-training/dashboardSidebar";
import InternalTrainingHeader from "@/pages/internal-training/trainingHeader";


const InternalTrainingIndex: React.FC = () => {

  return (<>
    <InternalTrainingHeader />
    <InternalTrainingDashboard />
  </>);
}

export default InternalTrainingIndex;
