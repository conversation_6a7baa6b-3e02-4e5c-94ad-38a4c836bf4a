import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Grid,
  CircularProgress,
  Typography,
  FormControl,
  useMediaQuery,
  Theme,
  Select,
  MenuItem,
} from "@mui/material";
import MaklumatAmSection from "./views/MaklumatAmSection";
import SenaraiPemohonSection from "./views/SenaraiPemohonSection";
import AccordionComp from "../../View/Accordion";
import {
  ButtonOutline,
  ButtonPrimary,
  DialogConfirmation,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import { useNavigate, useParams } from "react-router-dom";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { ApplicationStatusList, useMutation, useQuery } from "@/helpers";
// import DialogConfirmation from "./views/DialogConfirmation";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

const KeputusanCawanganBukanWargaNegara = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [societyInfo, setSocietyInfo] = useState<{
    id: number | null;
    societyName: string;
    societyNo: string;
    applicationNo: string;
  }>({
    id: null,
    societyName: "",
    societyNo: "",
    applicationNo: "",
  });
  const { id } = useParams();
  const idNonCitizen = atob(id || "");
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(0);

  const {
    data: nonCommitteeData,
    isLoading: nonCommitteeDataLoading,
    refetch: fetchNonCommitteeData,
  } = useQuery({
    url: `society/nonCitizenCommittee/${idNonCitizen}`,
    autoFetch: true,
  });

  const { fetch: updateDecision, isLoading: isLoadingDecisionUpdate } =
    useMutation({
      url: "society/roDecision/updateApprovalStatus",
      method: "patch",
      onSuccess: (data) => {
        setIsSuccess(true);
        setIsDialogOpen(false);
        navigate(-1);
      },
    });

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [readStatus, setReadStatus] = useState<ReadStatusType>({ 0: true });
  const societyId = nonCommitteeData?.data?.data?.societyId || null;
  const { data: societyDataInfo } = useCustom({
    url: `${API_URL}/society/${nonCommitteeData?.data?.data?.societyId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!nonCommitteeData?.data?.data?.societyId,
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        setSocietyInfo({
          id: responseData?.id,
          societyName: responseData?.societyName,
          societyNo: responseData?.societyNo,
          applicationNo: responseData?.applicationNo,
        });
      },
    },
  });

  const sectionItems = [
    {
      subTitle: t("generalInformation"),
      component: (
        <MaklumatAmSection
          info={nonCommitteeData?.data?.data}
          societyInfo={societyInfo}
        />
      ),
    },
    // {
    //   subTitle: t("applicantList"),
    //   component: <SenaraiPemohonSection />,
    // },
  ];

  const decisionOptions = [
    {
      value: 3,
      label: t("lulus"),
    },
    {
      value: 4,
      label: t("tolak"),
    },
  ];

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        applicationStatusCode: "",
        rejectReason: "",
        note: "",
        roApprovalType: "BRANCH_NON_CITIZEN",
      },
    });

  const onSubmit = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const handleFormSubmit = () => {
    const payload = getValues();
    const data = {
      ...payload,
      branchNonCitizenId: idNonCitizen,
      societyId: societyId,
    };
    updateDecision(data);
  };

  // =====

  const {
    handleSubmit: handleSubmitRo,
    control: controlRo,
    setValue: setValueRo,
    getValues: getValuesRo,
    watch: watchRo,
  } = useForm();
  const { mutate: updateRo, isLoading: isUpdatingRo } = useCustomMutation();

  const onSubmitRo = (data: any) => {
    updateRo(
      {
        url: `${API_URL}/society/roDecision/updateRo`,
        method: "patch",
        values: {
          societyNonCitizenId: nonCommitteeData?.data?.data?.id,
          roApprovalType: "BRANCH_AMENDMENT",
          roId: data.ro,
          noteRo: data.noteRo,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { data: roListData, isLoading: isRoListLoading } = useCustom({
    url: `${API_URL}/society/user/getRoList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyId,
      },
    },
  });

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const roList =
    roListData?.data?.data && Array.isArray(roListData.data.data)
      ? roListData.data.data
      : [];

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            {societyInfo?.societyName} <br />
            {societyInfo?.societyNo}
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          {sectionItems.map((item, index) => {
            return (
              <AccordionComp
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </AccordionComp>
            );
          })}

          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            {/* ======== */}
            <form onSubmit={handleSubmitRo(onSubmitRo)}>
              <Box
                sx={{
                  p: 3,
                  mb: 3,
                  border: "1px solid #D9D9D9",
                  // backgroundColor: "#FCFCFC",
                  borderRadius: "14px",
                }}
              >
                <Typography variant="h6" component="h2" sx={subTitleStyle}>
                  {t("ROAction")}
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("responsibleRO")}{" "}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <Controller
                      name="ro"
                      defaultValue={getValuesRo("ro")}
                      control={controlRo}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <Select
                            {...field}
                            size="small"
                            displayEmpty
                            value={field.value || ""}
                          >
                            {!isRoListLoading &&
                              roList.map((item: any) => (
                                <MenuItem key={item.id} value={item.id}>
                                  {item.name}
                                </MenuItem>
                              ))}
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>{t("remarks")}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextFieldController
                      control={controlRo}
                      name="noteRo"
                      multiline
                      sx={{
                        minHeight: "126px",
                      }}
                      sxInput={{
                        minHeight: "126px",
                      }}
                    />
                  </Grid>
                </Grid>
              </Box>
              <Grid
                item
                xs={12}
                sx={{
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrimary
                  type="submit"
                  sx={{
                    display: "block",
                    backgroundColor: "var(--primary-color)",
                    width: "100px",
                    minWidth: "unset",
                    height: "32px",
                    color: "white",
                    "&:hover": { backgroundColor: "#19ADAD" },
                    textTransform: "none",
                    fontWeight: 400,
                    fontSize: "8px",
                  }}
                >
                  {t("update")}
                </ButtonPrimary>
              </Grid>
            </form>
            {/* ======== */}

            <form onSubmit={handleSubmit(onSubmit)}>
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 3,
                  }}
                >
                  <Typography color={"primary"}>{t("keputusan")}</Typography>
                </Box>
                <Grid container>
                  <Grid item xs={12} sm={4}>
                    <Typography variant="body1" sx={labelStyle}>
                      {t("statusPermohonan")}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                    <SelectFieldController
                      control={control}
                      name="applicationStatusCode"
                      options={decisionOptions}
                      disabled={isFormDisabled}
                      sx={{
                        background: isFormDisabled
                          ? "rgba(218, 218, 218, 0.5)"
                          : "",
                      }}
                      required
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography variant="body1" sx={labelStyle}>
                      {t("remarks")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextFieldController
                      control={control}
                      name="note"
                      multiline
                      disabled={isFormDisabled}
                      // required={watch("applicationStatusCode") === 36}
                      sx={{
                        minHeight: "92px",
                        background: isFormDisabled
                          ? "rgba(218, 218, 218, 0.5)"
                          : "",
                      }}
                      sxInput={{
                        minHeight: "92px",
                      }}
                    />
                  </Grid>
                </Grid>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "34px",
                  gap: "10px",
                }}
              >
                <ButtonOutline
                  onClick={() =>
                    navigate(
                      "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk"
                    )
                  }
                >
                  {t("back")}
                </ButtonOutline>
                <ButtonPrimary
                  type="submit"
                  disabled={isFormDisabled || nonCommitteeDataLoading}
                >
                  {nonCommitteeDataLoading ? (
                    <CircularProgress
                      size="0.5rem"
                      sx={{
                        display: "block",
                      }}
                    />
                  ) : (
                    t("hantar")
                  )}
                </ButtonPrimary>
              </Box>
            </form>
          </Box>
        </Box>

        <DialogConfirmation
          open={isDialogOpen}
          onClose={handleDialogClose}
          onAction={handleFormSubmit}
          isMutating={isLoadingDecisionUpdate}
          onConfirmationText={t("permohonanConfirmation")}
          onSuccessText={t("applicationSuccessSubmited")}
          isSuccess={isSuccess}
          decisionLabel={
            ApplicationStatusList.find(
              (item) => item.id === getValues().applicationStatusCode
            )?.value || "-"
          }
        />
      </Box>
    </>
  );
};

export default KeputusanCawanganBukanWargaNegara;
