import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../api";

import {
  Typography,
  Box,
  Grid,
  useMediaQuery,
  Theme,
  Select,
  MenuItem,
  TextField,
  IconButton,
  FormControl,
} from "@mui/material";
import FormFieldRow from "../../../../components/form-field-row";
import Label from "../../../../components/label/Label";
import DisabledTextField from "../../../../components/input/DisabledTextField";
import { ButtonPrimary } from "../../../../components/button";
import { EyeIcon } from "../../../../components/icons";
import { useState } from "react";

const MaklumatPemohonSection = () => {
  const { t } = useTranslation();
  const { amendmentId, id } = useParams();
  const decodedId = atob(id || "");
  const [namePemohon, setNamePemohon] = useState("");
  const [emelPemohon, setEmelPemohon] = useState("");
  const { data: amendmentData } = useCustom({
    url: `${API_URL}/society/amendment/getAmendmentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        id: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId,
      onSuccess: (responseData) => {
        if (responseData.data.code === 200) {
          const data = responseData?.data?.data?.data?.[0];
          setNamePemohon(data.applicantName);
          setEmelPemohon(data.applicantEmail);
        }
      },
    },
  });

  return (
    <Box
      sx={{
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("maklumatPemohon")}
        </Typography>
        {/* <DisabledTextField value="-" /> */}
        <FormFieldRow
          label={<Label text={t("namePemohon")} />}
          value={
            <FormControl fullWidth required>
              <TextField
                value={namePemohon}
                fullWidth
                required
                disabled
                size="small"
                sx={{ backgroundColor: "#66666626" }}
              />
            </FormControl>
          }
        />

        <FormFieldRow
          label={<Label text={t("emelPemohon")} />}
          value={
            <FormControl fullWidth required>
              <TextField
                value={emelPemohon}
                fullWidth
                required
                disabled
                size="small"
                sx={{ backgroundColor: "#66666626" }}
              />
            </FormControl>
          }
        />
      </Box>
    </Box>
  );
};

export default MaklumatPemohonSection;
