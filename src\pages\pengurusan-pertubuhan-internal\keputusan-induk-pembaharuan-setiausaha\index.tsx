/* eslint-disable react-refresh/only-export-components */
import { useState, useContext, createContext, useEffect, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { TFunction } from "i18next";
import { useForm, FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import {
  DecisionOptionsCode,
  useQuery,
  DocumentUploadType,
  filterEmptyValuesOnObject,
  useMutation,
} from "@/helpers";

import {
  Box,
  Grid,
  IconButton,
  Typography,
  Fade,
  useMediaQuery,
  Theme,
} from "@mui/material";
import {
  DialogConfirmation,
  ButtonPrimary,
  Label,
  SelectFieldController,
  TextFieldController,
  CustomSkeleton,
  FormFieldRow,
  DisabledTextField,
} from "@/components";
import MaklumatSetiausahaSection from "./views/MaklumatSetiausahaSection";
import MaklumatMesyuaratSection from "./views/MaklumatMesyuaratSection";
import KeputusanPermohonanSection from "./views/KeputusanPermohonanSection";
import MaklumbalasTable from "./views/MaklumbalasTable";
import AccordionComp from "../View/Accordion";
import DialogQueries from "./views/DialogQueries";

import {
  IApprovalSecretaryDetail,
  IMeetingDetail,
  ISocietyDetail,
  IApiResponse,
  IROList,
} from "@/types";

import { AddIcon } from "@/components/icons";
import PerlembagaanSection from "./views/PerlembagaanSection";

interface KeputusanIndukPembaharuanSetiausahaContextProps {
  secretaryDetailData: IApprovalSecretaryDetail | null;
  meetingDetailData: IMeetingDetail | null;
  societyDetailData: ISocietyDetail | null;
  meetingFilePreview: string;
  roList: IROList[];
}

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

const KeputusanIndukPembahruanSetiuasahaContext = createContext<
  KeputusanIndukPembaharuanSetiausahaContextProps | undefined
>(undefined);

export const useKeputusanIndukPembaharuanSetiausahaContext =
  (): KeputusanIndukPembaharuanSetiausahaContextProps => {
    const context = useContext(KeputusanIndukPembahruanSetiuasahaContext);

    if (!context) {
      throw new Error(
        "useKeputusanIndukPembahruanSetiuasahaContext must be used within a KeputusanIndukPembaharuanSetiusahaProvider"
      );
    }
    return context;
  };

type ReadStatusType = {
  [key: number]: boolean;
};

function KeputusanIndukPembaharuanSetiausaha() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const { id } = useParams();
  const { t } = useTranslation();

  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isOpenDialogQueries, setIsOpenDialogQueries] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [openMaklumbalasTable, setOpenMaklumbalasTable] = useState(false);

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});
  const [meetingFilePreview, setMeetingFilePreview] = useState("");

  const { data: secretaryRes, isLoading: isLoadingSecretaryDetail } = useQuery<
    IApiResponse<IApprovalSecretaryDetail>
  >({
    url: `society/new/secretaries/${id}`,
    onSuccess: (res) => {
      const data = res?.data?.data;

      if (data) {
        const societyId = data?.societyId;
        const meetingId = data?.meetingId;

        fetchSociety();
        fetchMeeting();
        fetchRoList({
          filters: [
            {
              field: "societyId",
              operator: "eq",
              value: societyId,
            },
          ],
        });

        getMeetingDocumentFile({
          filters: [
            {
              field: "societyId",
              operator: "eq",
              value: societyId,
            },
            {
              field: "meetingId",
              operator: "eq",
              value: meetingId,
            },
            {
              field: "type",
              operator: "eq",
              value: DocumentUploadType.MEETING,
            },
          ],
        });
      }
    },
  });

  const secretaryDetailData = secretaryRes?.data?.data ?? null;
  const userRo = secretaryDetailData?.userRo ?? false;
  const queriesList = secretaryDetailData?.queries ?? [];
  const lastQuery = queriesList.at(-1) ?? null;

  const {
    data: societyRes,
    refetch: fetchSociety,
    isLoading: isLoadingSociety,
  } = useQuery<IApiResponse<ISocietyDetail>>({
    url: `society/${secretaryDetailData?.societyId}`,
    autoFetch: false,
  });

  const { data: meetingRes, refetch: fetchMeeting } = useQuery<
    IApiResponse<IMeetingDetail>
  >({
    url: `society/meeting/${secretaryDetailData?.meetingId}`,
    autoFetch: false,
  });

  const { refetch: getMeetingDocumentFile } = useQuery({
    url: "society/document/documentByParam",
    autoFetch: false,
    onSuccess: ({ data }) => {
      const meetingDocuments = data?.data;

      if (Array.isArray(meetingDocuments) && meetingDocuments.length > 0) {
        const [firstDocument] = meetingDocuments;
        const meetingUrl = firstDocument?.url;

        if (meetingUrl) {
          setMeetingFilePreview(meetingUrl);
        } else {
          console.warn("Meeting URL not found");
        }
      } else {
        console.warn("No meeting documents available.");
      }
    },
  });

  const { data: roListData, refetch: fetchRoList } = useQuery<
    IApiResponse<IROList[]>
  >({
    url: "society/user/getRoList",
    autoFetch: false,
  });

  const meetingDetailData = meetingRes?.data?.data ?? null;
  const societyDetailData = societyRes?.data?.data ?? null;
  const roList = roListData?.data?.data ?? [];

  const { fetch: createApproval, isLoading } = useMutation({
    url: "society/roApproval/secretary/create",
    onSuccess: () => setIsSuccess(true),
  });
  const { fetch: updateRO, isLoading: isUpdatingRO } = useMutation({
    url: "society/secretary/principal/ro",
    method: "put",
  });
  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        principalSecretaryId: Number(id),
        societyId: societyDetailData?.id,
        societyNo: societyDetailData?.societyNo,
        decisionCode: "",
        rejectReason: "",
        note: "",
      },
    });

  const decisionCode = watch("decisionCode");
  const requiredNote = (userRo && decisionCode === 4) || decisionCode === 36; // decision tolak for RO or decision Kuiri

  const methodsRoAction = useForm<FieldValues>({
    defaultValues: {
      id: id,
      ro: "",
      notePpp: "",
    },
  });

  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
  } = methodsRoAction;

  useEffect(() => {
    if (secretaryDetailData) {
      setValueRoAction("ro", secretaryDetailData.ro ?? "");
      setValueRoAction("noteRO", secretaryDetailData.noteRo ?? "");
      setValueRoAction("roName", secretaryDetailData.roName ?? "");
    }
  }, [secretaryDetailData]);

  const getDecisionOptions = (
    t: TFunction<"translation", undefined>,
    userRo: boolean
  ) => {
    let options = DecisionOptionsCode(t);

    if (userRo) {
      options = options
        .map((option) => {
          if (option.value === 3) {
            return { ...option, label: t("DISYORKAN") };
          }
          if (option.value === 4) {
            return { ...option, label: t("TIDAK_DISYORKAN") };
          }
          return option;
        })
        .filter((option) => option.value !== 36);
    }

    return options;
  };

  const decisionOptions = useMemo(
    () => getDecisionOptions(t, userRo),
    [userRo]
  );

  const decisionLabel = useMemo(
    () =>
      decisionOptions.find((item) => item.value === Number(decisionCode))
        ?.label || "",
    [decisionCode, decisionOptions]
  );

  const sectionItems = [
    {
      subTitle: t("secretaryInformation"),
      component: (
        <MaklumatSetiausahaSection
          onClickMaklumbalas={() => setOpenMaklumbalasTable(true)}
        />
      ),
    },
    {
      subTitle: t("maklumatMesyuarat"),
      component: <MaklumatMesyuaratSection />,
    },
    {
      subTitle: t("perlembagaanPertubuhan"),
      component: <PerlembagaanSection/>,
    },
    {
      subTitle: t("keputusanPermohonan"),
      component: <KeputusanPermohonanSection />,
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const onSubmit = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const payload = getValues();
    const filterPayload = filterEmptyValuesOnObject(payload);

    createApproval(filterPayload);
  };

  useEffect(() => {
    if (!secretaryDetailData) return;

    const { reviews, societyId, societyNo } = secretaryDetailData;

    const lastReview =
      reviews
        ?.filter((review: any) => [3, 4].includes(review.decision))
        ?.at(-1) ?? null;

    setValue("societyId", societyId);
    setValue("societyNo", societyNo);

    if (lastReview) {
      const { decision, note } = lastReview;

      setValue("decisionCode", decision);
      setValue("note", note);
      setIsFormDisabled(true);
    } else {
      setIsFormDisabled;
    }
  }, [secretaryDetailData]);

  if (isLoadingSecretaryDetail || !secretaryDetailData)
    return <CustomSkeleton />;

  const roListOptions =
    roList.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const onSubmitRoAction = (data: FieldValues) => {
    updateRO(data);
  };

  const renderSuccessText = () => {
    switch (decisionLabel) {
      case "Lulus":
        return t("ApplicationApproved")
      case "Tolak":
        return t("ApplicationRejected")
      case "Kuiri":
        return t("ApplicationRequestInquiry")
      default:
        return t("applicationSuccessSubmited");
    }
  };

  return (
    <KeputusanIndukPembahruanSetiuasahaContext.Provider
      value={{
        roList,
        secretaryDetailData,
        meetingDetailData,
        societyDetailData,
        meetingFilePreview,
      }}
    >
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography fontSize="14px" fontWeight="500 !important">
              {isLoadingSociety ? (
                "Loading..."
              ) : (
                <>
                  {societyDetailData?.societyName} <br />
                  {societyDetailData?.societyNo}
                </>
              )}
            </Typography>
          </Box>
        </Box>

        <Fade in={openMaklumbalasTable} timeout={500} unmountOnExit>
          <Box>
            <MaklumbalasTable onBack={() => setOpenMaklumbalasTable(false)} />
          </Box>
        </Fade>

        <Fade in={!openMaklumbalasTable} timeout={500} unmountOnExit>
          <Box>
            <Box sx={{ mt: 4 }}>
              {sectionItems.map((item, index) => {
                return (
                  <AccordionComp
                    key={index}
                    subTitle={item.subTitle}
                    currentIndex={index + 1}
                    currentExpand={currentExpandSection}
                    readStatus={readStatus}
                    onChangeFunc={handleChangeCurrentExpandSection}
                  >
                    {item.component}
                  </AccordionComp>
                );
              })}
            </Box>

            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 3,
                  }}
                >
                  <Typography color={"primary"}>{t("kuiri")}</Typography>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    gap: "4px",
                    justifyContent: "flex-end",
                    marginBottom: "27px",
                  }}
                >
                  <IconButton
                    aria-label="close"
                    sx={{
                      padding: "9px 10px",
                      borderRadius: "5px",
                      backgroundColor: "var(--primary-color)",
                      cursor: "default",
                      pointerEvents: "none",
                    }}
                  >
                    <AddIcon color="#fff" />
                  </IconButton>

                  <ButtonPrimary
                    onClick={() => setIsOpenDialogQueries(true)}
                    sx={{
                      backgroundColor: "#FFF",
                      width: "133px",
                      minWidth: "unset",
                      height: "32px",
                      color: "#666666",
                      textTransform: "none",
                      fontWeight: 400,
                      fontSize: "8px",
                      border: "1px solid var(--primary-color)",
                    }}
                  >
                    {t("historyInquiry")}
                  </ButtonPrimary>
                </Box>

                <FormFieldRow
                  align="flex-start"
                  label={<Label text={`${t("remarks")} ${t("kuiri")}`} />}
                  value={
                    <DisabledTextField
                      multiline
                      row={3}
                      value={lastQuery?.note ?? ""}
                    />
                  }
                />
              </Box>
            </Box>

            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
            >
              <form onSubmit={handleSubmitRoAction(onSubmitRoAction)}>
                <Box
                  sx={{
                    p: 3,
                    mb: 3,
                    border: "1px solid #D9D9D9",
                    borderRadius: "14px",
                  }}
                >
                  <Typography variant="h6" component="h2" sx={subTitleStyle}>
                    Tindakan RO
                  </Typography>

                  <FormFieldRow
                    label={<Label text={t("responsibleRO")} />}
                    value={
                      <SelectFieldController
                        control={controlRoAction}
                        name="ro"
                        options={roListOptions}
                        onChange={(e) => {
                          const value = e.target.value;
                          const selectedRo = roList.find(
                            (ro) => ro.identificationNo === value
                          );

                          setValue("roName", selectedRo?.name ?? "");
                        }}
                        // disabled={userRo}
                      />
                    }
                  />

                  <FormFieldRow
                    align="flex-start"
                    label={<Label text={`${t("remarks")}`} />}
                    value={
                      <TextFieldController
                        control={controlRoAction}
                        name="noteRO"
                        multiline
                        sx={{
                          minHeight: "126px",
                        }}
                        sxInput={{
                          minHeight: "126px",
                        }}
                        disabled={userRo}
                      />
                    }
                  />
                </Box>

                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrimary
                    type="submit"
                    sx={{
                      display: "block",
                      backgroundColor: "var(--primary-color)",
                      width: "100px",
                      minWidth: "unset",
                      height: "32px",
                      color: "white",
                      "&:hover": { backgroundColor: "#19ADAD" },
                      textTransform: "none",
                      fontWeight: 400,
                      fontSize: "8px",
                    }}
                    disabled={userRo || isUpdatingRO}
                  >
                    {t("update")}
                  </ButtonPrimary>
                </Grid>
              </form>
              <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                  sx={{
                    pl: 2,
                    p: 3,
                    mt: 1,
                    borderRadius: "10px",
                    border: "0.5px solid #dfdfdf",
                  }}
                >
                  <Box
                    sx={{
                      mb: 3,
                    }}
                  >
                    <Typography color={"primary"}>{t("keputusan")}</Typography>
                  </Box>
                  <Grid container>
                    <Grid item xs={12} sm={4}>
                      <Label text={t("statusPermohonan")} />
                    </Grid>

                    <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                      <SelectFieldController
                        control={control}
                        name="decisionCode"
                        options={decisionOptions}
                        disabled={isFormDisabled}
                        required
                      />
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Label
                        text={t("remarks")}
                        required={requiredNote}
                      />
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextFieldController
                        control={control}
                        name="note"
                        multiline
                        disabled={isFormDisabled}
                        sx={{
                          minHeight: "92px",
                        }}
                        sxInput={{
                          minHeight: "92px",
                        }}
                        required={requiredNote}
                      />
                    </Grid>
                  </Grid>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    marginTop: "34px",
                    gap: "10px",
                  }}
                >
                  <ButtonPrimary
                    onClick={() =>
                      navigate(
                        "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk"
                      )
                    }
                    sx={{
                      backgroundColor: "#FFF",
                      width: "100px",
                      minWidth: "unset",
                      height: "32px",
                      color: "#666666",
                      textTransform: "none",
                      fontWeight: 400,
                      fontSize: "8px",
                      border: "1px solid #DADADA",
                    }}
                  >
                    {t("back")}
                  </ButtonPrimary>

                  <ButtonPrimary
                    type="submit"
                    sx={{
                      display: "block",
                      backgroundColor: "var(--primary-color)",
                      width: "100px",
                      minWidth: "unset",
                      height: "32px",
                      color: "white",
                      "&:hover": { backgroundColor: "#19ADAD" },
                      textTransform: "none",
                      fontWeight: 400,
                      fontSize: "8px",
                    }}
                  >
                    {t("hantar")}
                  </ButtonPrimary>
                </Box>
              </form>
            </Box>
          </Box>
        </Fade>
      </Box>

      <DialogQueries
        open={isOpenDialogQueries}
        onClose={() => setIsOpenDialogQueries(false)}
        queries={queriesList}
      />

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isLoading}
        onConfirmationText={t("sureToSubmit")}
        onSuccessText={renderSuccessText()}
        isSuccess={isSuccess}
        decisionLabel={decisionLabel}
      />
    </KeputusanIndukPembahruanSetiuasahaContext.Provider>
  );
}

export default KeputusanIndukPembaharuanSetiausaha;
