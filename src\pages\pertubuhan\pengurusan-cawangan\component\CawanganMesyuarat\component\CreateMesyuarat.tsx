import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  TextField,
  Typography,
  Grid,
  useMediaQuery,
  Theme,
  Select,
  MenuItem,
  FormControl,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useCreate, useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import { MALAYSIA, MeetingMethods, MeetingTypeOption } from "@/helpers/enums";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TileLayer, useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { DocumentUploadType, useUploadPresignedUrl } from "@/helpers";
import {
  FormMeetingAttendees,
  FormMeetingAttendeesBaseRef,
} from "@/components/form/meeting/Attendees";
import { OrganizationManagementMeetingRequestBodyCreateOnlyAttendees } from "@/types";
import {
  FormMeetingDateTime,
  FormMeetingDateTimeRef,
} from "@/components/form/meeting/DateTime";
import { useSelector } from "react-redux";
import dayjs from "dayjs";

const RecenterAutomatically = ({ lat, lng }: { lat: number; lng: number }) => {
  const map = useMap();
  useEffect(() => {
    map.setView([lat, lng]);
  }, [lat, lng]);
  return null;
};

type UploadParams = {
  type: DocumentUploadType;
  societyId: number | null;
  societyNo: number | null;
  branchId: number | null;
  branchNo: number | null;
  meetingId: number | null;
  societyCommitteeId: number | null;
  icNo: string;
  name: string;
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CreateMesyuarat: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const params = useParams();
  const formMeetingAttendeesRef =
    useRef<
      FormMeetingAttendeesBaseRef<OrganizationManagementMeetingRequestBodyCreateOnlyAttendees>
    >();
  const formMeetingDateTimeRef = useRef<FormMeetingDateTimeRef>();

  const societyId: string = params.id || "";

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const getUserDetails = localStorage.getItem("user-details");
  const [meetingType, setMeetingType] = useState("");
  const [meetingMethod, setMeetingMethod] = useState("");
  const [platformType, setPlatformType] = useState("");
  const [meetingContent, setMeetingContent] = useState("");

  const { mutate: createMeeting, isLoading: isCreating } = useCreate();

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSaved, setIsSaved] = useState(false);

  const [formData, setFormData] = useState<any>({});

  const [meetingCoords, setMeetingCoords] = useState<[number, number]>([
    2.745564, 101.707021,
  ]);

  //@ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const uploadParams: UploadParams = {
    type: DocumentUploadType.MEETING,
    societyId: null,
    societyNo: null,
    branchId: null,
    branchNo: null,
    meetingId: null,
    societyCommitteeId: null,
    icNo: "",
    name: "",
  };

  const { upload } = useUploadPresignedUrl({
    onSuccessUpload: () => {
      setSelectedFile(null);
      navigate(
        `/pertubuhan/society/${societyId}/senarai/cawangan/view/mesyuarat`
      );
    },
  });

  const { data: addressList, isLoading: isLoadingAddress } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const { data: meetingList, isLoading } = useCustom({
    url: `${API_URL}/society/admin/meeting/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState: any) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData((prevState: any) => ({
        ...prevState,
        meetingMinute: file.name,
      }));
    }
  };
  const handleSave = async () => {
    if (
      formData.meetingMethod == MeetingMethods.BERSEMUKA ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      if (formData.postcode.toString().length !== 5) {
        return {
          message: t("postcodeValidation"),
          type: "error",
        };
      }
    }

    // Validate FormMeetingDateTime
    const dateTimeErrors = formMeetingDateTimeRef.current?.getErrors();
    if (dateTimeErrors && Object.keys(dateTimeErrors).length > 0) {
      return {
        message: t("pleaseCompleteAllRequiredFields"),
        type: "error",
      };
    }

    const dateTimeValue = formMeetingDateTimeRef.current?.getValue();
    const formattedTime = dateTimeValue?.meetingTime ?? "";
    const formattedTime2 = dateTimeValue?.meetingTimeTo ?? "";

    try {
      const params = new URLSearchParams({
        branchId: societyId,
      }).toString();
      const societyResponse = await fetch(
        `${API_URL}/society/branch/getBranchesByParam?${params}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const societyData = await societyResponse.json();

      if (societyData.status === "SUCCESS") {
        const attendeesValue = formMeetingAttendeesRef.current?.getValue();
        const totalAttendees = attendeesValue?.totalAttendees ?? 7;
        const data = {
          societyId: parseInt(societyId),
          branchId: branchDataRedux.id,
          society_no: societyData.societyNo,
          meetingType: formData.meetingType?.toString() ?? "",
          meetingPlace: formData.meetingPlace ?? "",
          meetingPurpose: formData.meetingPurpose ?? "",
          meetingMethod: formData.meetingMethod?.toString() ?? "",
          platformType: formData.platformType?.toString() ?? "",
          meetingDate: dateTimeValue?.meetingDate ?? "",
          meetingTime: formattedTime ?? "",
          meetingTimeTo: formattedTime2 ?? "",
          GISInformation: "",
          meetingAddress: formData.meetingAddress ?? "",
          state: formData.state?.toString() ?? "",
          district: formData.district?.toString() ?? "",
          city: formData.city ?? "",
          postcode: formData.postcode ?? "",
          totalAttendees,
          providedBy: formData.providedBy ?? "",
          confirmBy: formData.confirmBy ?? "",
          meetingMinute: selectedFile ? selectedFile.name : "",
          status: 1,
          openingRemarks: formData.openingRemarks,
          mattersDiscussed: formData.mattersDiscussed,
          otherMatters: formData.othersDiscussionRemarks,
          closing: formData.closingRemarks,
        };

        if (selectedFile) {
          createMeeting({
            resource: "society/meeting/create",
            values: data,
            meta: {
              headers: {
                portal: localStorage.getItem("portal"),
                authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
              },
            },
            successNotification: (data) => {
              setIsSaved(true);

              // Handle file upload
              uploadParams.name = selectedFile.name;
              uploadParams.societyId = societyData.data.id;
              uploadParams.societyNo = societyData.data.societyNo;
              uploadParams.icNo = societyData.data.identificationNo;
              uploadParams.meetingId = data?.data?.data;

              upload({
                file: selectedFile,
                params: uploadParams,
              });

              return {
                message: data?.data?.msg,
                type: "success",
              };
            },
            errorNotification: (data) => {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            },
          });
        } else {
          createMeeting({
            resource: "society/meeting/create",
            values: data,
            meta: {
              headers: {
                portal: localStorage.getItem("portal"),
                authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
              },
            },
            successNotification: (data) => {
              setIsSaved(true);
              // Updated navigation path
              navigate(
                `/pertubuhan/society/${societyId}/senarai/cawangan/view/mesyuarat`
              );
              return {
                message: data?.data?.msg,
                type: "success",
              };
            },
            errorNotification: (data) => {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            },
          });
        }
      }
    } catch (error) {
      console.error("Error saving meeting:", error);
    }
  };

  const addressData = addressList?.data?.data || [];
  const meetingData = meetingList?.data?.data || [];
  const meetingTypeOptions = MeetingTypeOption.filter((option) =>
    [2, 3, 4].includes(option.value)
  );

  return (
    <>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("mesyuaratPenubuhan")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingType")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingType}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setMeetingType(e.target.value);
                    setFormData((prevState: any) => ({
                      ...prevState,
                      meetingType: e.target.value,
                    }));
                  }}
                >
                  {meetingTypeOptions.map((option, index) => (
                    <MenuItem
                      key={`meeting-type-option-${index}`}
                      value={option.value}
                    >
                      {t(option?.translateKey ?? "pleaseSelect")}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingMethod")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingMethod || ""}
                  displayEmpty
                  required
                  disabled={isLoading}
                  onChange={(e) => {
                    setMeetingMethod(e.target.value);
                    setFormData((prevState: any) => ({
                      ...prevState,
                      meetingMethod: e.target.value,
                    }));
                  }}
                >
                  <MenuItem value="" disabled>
                    {isLoading ? "Loading..." : t("pleaseSelect")}
                  </MenuItem>
                  {!isLoading &&
                    meetingData
                      .filter((item: any) => item.pid === 2)
                      .map((item: any) => (
                        <MenuItem key={item.id} value={item.id}>
                          {i18n.language === "en" ? item.nameEn : item.nameBm}
                        </MenuItem>
                      ))}
                </Select>
              </FormControl>
            </Grid>
            {meetingMethod == MeetingMethods.ATAS_TALIAN ||
            meetingMethod == MeetingMethods.HYBRID ? (
              <>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("platformType")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth>
                    <Select
                      size="small"
                      value={platformType || ""}
                      displayEmpty
                      required
                      disabled={isLoading}
                      onChange={(e) => {
                        setPlatformType(e.target.value);
                        setFormData((prevState: any) => ({
                          ...prevState,
                          platformType: e.target.value,
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoading ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoading &&
                        meetingData
                          .filter((item: any) => item.pid === 3)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {i18n.language === "en"
                                ? item.nameEn
                                : item.nameBm}
                            </MenuItem>
                          ))}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            ) : (
              <></>
            )}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("tujuanMesyuarat")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                fullWidth
                required
                name="meetingPurpose"
                placeholder={t("Thepurposeofthemeetingisto")}
                value={formData.meetingPurpose}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("masaDanTarikhMesyuarat")}
          </Typography>
          <FormMeetingDateTime
            // @ts-expect-error
            ref={formMeetingDateTimeRef}
            meetingTimeFromAttribute="meetingTime"
            defaultValues={{
              meetingDate: null,
              meetingTime: null,
              meetingTimeTo: null,
            }}
            branchRegistrationDate={
              branchDataRedux?.submissionDate
                ? dayjs(branchDataRedux.submissionDate)
                : undefined
            }
          />

          <Grid container spacing={2}>
            {meetingMethod == MeetingMethods.BERSEMUKA ||
            meetingMethod == MeetingMethods.HYBRID ? (
              <>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("namaTempatMesyuarat")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="meetingPlace"
                    value={formData.meetingPlace}
                    onChange={handleInputChange}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingLocation")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Box>
                    <MapContainer
                      center={meetingCoords}
                      zoom={13}
                      style={{
                        height: "150px",
                        width: "100%",
                        borderRadius: "8px",
                      }}
                    >
                      <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                      <Marker position={meetingCoords} />
                      <RecenterAutomatically
                        lat={meetingCoords[0]}
                        lng={meetingCoords[1]}
                      />
                    </MapContainer>
                  </Box>
                </Grid>
              </>
            ) : (
              <></>
            )}
          </Grid>
        </Box>

        {meetingMethod == MeetingMethods.BERSEMUKA ||
        meetingMethod == MeetingMethods.HYBRID ? (
          <>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("alamatTempatMesyuarat")}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingPlaceAddress")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="meetingAddress"
                    value={formData.meetingAddress}
                    onChange={handleInputChange}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("state")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth required>
                    <Select
                      size="small"
                      value={formData.state}
                      displayEmpty
                      required
                      disabled={isLoadingAddress}
                      onChange={(e) => {
                        setFormData((prevState: any) => ({
                          ...prevState,
                          state: e.target.value,
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoadingAddress ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoadingAddress &&
                        addressData
                          ?.filter((item: any) => item.pid === MALAYSIA)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("district")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth required>
                    <Select
                      size="small"
                      value={formData.district}
                      displayEmpty
                      required
                      disabled={isLoadingAddress || !formData.state}
                      onChange={(e) => {
                        setFormData((prevState: any) => ({
                          ...prevState,
                          district: e.target.value,
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoadingAddress ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoadingAddress &&
                        addressData
                          ?.filter((item: any) => item.pid == formData.state)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("city")}</Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("postcode")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="postcode"
                    value={formData.postcode}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      const value = e.target.value;
                      if (/^\d{0,5}$/.test(value)) {
                        handleInputChange(e);
                      }
                    }}
                    type="text"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
          </>
        ) : (
          <></>
        )}

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatMesyuarat")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("totalAttendMember")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  defaultValue={1}
                  name="totalAttendees"
                  value={formData.totalAttendees}
                  onKeyDown={(e) => {
                    if (
                      e.key.toLowerCase() === "e" ||
                      e.key === "+" ||
                      e.key === "-" ||
                      e.key === "."
                    ) {
                      e.preventDefault();
                    }
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value)) {
                      handleInputChange(e);
                    }
                  }}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <FormMeetingAttendees
          // @ts-expect-error
          ref={formMeetingAttendeesRef}
          defaultValues={{
            totalAttendees: formData.totalAttendees,
          }}
          onSubmit={() => {}}
        />

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("minitMesyuarat")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("minitMesyuarat")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box
                sx={{
                  border: "2px solid #DADADA",
                  borderRadius: "8px",
                  p: 2,
                  gap: 2,
                  textAlign: "center",
                  cursor: "pointer",
                  height: "200px",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                }}
                onClick={() => {
                  const element = document.getElementById("meetingMinute");
                  if (element) {
                    element.click();
                  }
                }}
              >
                {selectedFile ? (
                  <Typography sx={{ color: "#147C7C", mb: 1 }}>
                    {selectedFile.name}
                  </Typography>
                ) : (
                  <>
                    <Box
                      sx={{
                        width: 50,
                        aspectRatio: "1/1",
                        display: "flex",
                        justifyContent: "center",
                        alignContent: "center",
                        textAlign: "center",
                        borderRadius: 20,
                        mb: 2,
                        // p: 5,
                        bgcolor: "#F2F4F7",
                      }}
                    >
                      <img
                        width={30}
                        src={"/uploadFileIcon.svg"}
                        alt={"view"}
                      />
                    </Box>

                    <Typography
                      sx={{
                        color: "var(--primary-color)",
                        fontWeight: "500",
                        fontSize: "14px",
                      }}
                    >
                      {t("muatNaik")}
                    </Typography>
                    <Typography
                      sx={{
                        color: "#667085",
                        fontWeight: "400",
                        fontSize: "12px",
                      }}
                    >
                      {"PDF, DOCX, DOC or TXT <25 MB"}
                    </Typography>
                  </>
                )}
                <input
                  id="meetingMinute"
                  type="file"
                  hidden
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx,.svg,.png,.jpg"
                />
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => {
                setFormData({
                  meetingPurpose: "",
                  meetingDate: "",
                });
                setMeetingMethod("");
                setPlatformType("");
                setMeetingContent("");
                // Reset FormMeetingDateTime component
                formMeetingDateTimeRef.current?.resetDefaultValue({
                  meetingDate: null,
                  meetingTime: null,
                  meetingTimeTo: null,
                });
              }}
            >
              {t("semula")}
            </ButtonOutline>

            {!isSaved && (
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={handleSave}
                disabled={isCreating}
              >
                {isCreating ? t("saving") : t("update")}
              </ButtonPrimary>
            )}
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default CreateMesyuarat;
