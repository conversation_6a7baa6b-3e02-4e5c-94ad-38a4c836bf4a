import React from "react";
import { Outlet } from "react-router-dom";
import { Box } from "@mui/material";
import { useTranslation } from "react-i18next";
import CustomTabContainer from "../../../components/customTab";

const SemakanUmum = () => {
  const { t } = useTranslation();

  const tabs = [
    { name: t("noPendaftaranLama"), navigate_screen: "no-pendaftaran-lama" },
    { name: t("senaraiKelabu"), navigate_screen: "cawangan" },
    { name: t("senaraiHitamKeputihan"), navigate_screen: "cawangan" },
    { name: `${t("semakanIndividu")} (${t("pemegangJawatan")})`, navigate_screen: "pemegang-jawatan" },

  ];

  return (
    <Box>
      <CustomTabContainer
        tabs={tabs.map((tab) => ({
          name: tab.name,
          navigate_screen: tab.navigate_screen
        }))}
      />
      <Outlet />
    </Box>
  );
};

export default SemakanUmum;
