import { Box, Grid, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { useNavigate, useParams } from "react-router-dom";

import { useSelector } from "react-redux";

import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { fetchAppealByIdData } from "@/redux/APIcalls/appealByIdThunks";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";
import { API_URL } from "@/api";
import Input from "@/components/input/Input";

interface FormValues {
  year?: any | null;
}

export const PenyataTahuanSection: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();

  const fasalContent = [
    {
      title: `${t("clause")} 1: ${t("name")}`,
      content: (
        <>
          <Typography>1. Per<PERSON><PERSON>an ini dikenali dengan nama</Typography>
          <Typography sx={{ margin: "20px 50px" }}>
            Pertubuhan Pendidikan Anak Jati Selangor
          </Typography>
          <Typography>selepas ini disebut "Pertubuhan".</Typography>
          <br />
          <Typography>2. Singkatan Nama: PPAS</Typography>
          <Typography>
            3. Takrif Nama: Pertubuhan khas anak jati Selangor
          </Typography>
          <Typography>4. Bertaraf: Negeri</Typography>
        </>
      ),
    },
    {
      title: `${t("clause")} 2: ${t("placeOfBusiness")}`,
      content: (
        <>
          <Typography>
            1. Alamat berdaftar dan tempat urusan pertubuhan adalah
          </Typography>
          <br />
          <Typography>dan alamat untuk surat-menyurat adalah</Typography>
          <br />
          <Typography>
            atau di tempat lain atau tempat-tempat yang akan ditetapkan dari
            semasa ke semasa oleh Mesyuarat Jawatankuasa.
          </Typography>
          <br />
          <Typography>
            2. Tempat urusan berdaftar dan alamat surat-menyurat Pertubuhan
            tidak boleh diubah tanpa kebenaran Pendaftar Pertubuhan terlebih
            dahulu.
          </Typography>
        </>
      ),
    },
    {
      title: `${t("clause")} 3: ${t("goals")}`,
      content: (
        <Typography>
          1. Matlamat 1 <br />
          <br />
          2. Matlamat 2 <br />
          <br />
          3. Matlamat 3
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 4: ${t("skills")}`,
      content: (
        <>
          <Typography>
            1. Kriteria keahlian Pertubuhan adalah seperti berikut: <br />
            <br />
            A. Ahli Biasa <br />
            i. Kewarganegaraan : <br />
            ii. Umur Minima : tahun <br />
            iii. Kawasan/Negeri Tempat Tinggal Ahli: <br />
            iv. Keturunan/Bangsa: <br />
            v. Jantina : <br />
            vi. Agama : <br />
            vii. Kriteria Keahlian Yang Lain: <br />
            <br />
            B. Ahli Bersekutu <br />
            <br />
            C. Ahli Kehormat <br />
            <br />
            D. Ahli Seumur Hidup <br />
            <br />
            E. Ahli Remaja <br />
            <br />
            2. Tiap-tiap permohonan menjadi ahli hendaklah direkod dan
            dicadangkan serta disokong oleh ahli dan dihantar kepada Setiausaha
            yang dikehendaki mengemukakan permohonan itu dengan segera kepada
            Mesyuarat Jawatankuasa untuk dipertimbangkan. Jawatankuasa boleh
            mengikut budi bicaranya menolak sebarang permohonan. <br />
            <br />
            3. Tiap-tiap pemohon yang permohonannya telah diluluskan seperti
            yang tersebut di atas, hendaklah diterima menjadi ahli Pertubuhan
            setelah membayar yuran yang ditetapkan dan berhaklah ia menjadi
            ahli.
          </Typography>
        </>
      ),
    },
    {
      title: `${t("clause")} 5: ${t("suspensionAndDismissalOfMembers")}`,
      content: (
        <>
          <Typography>
            1. Ahli yang hendak berhenti daripada menjadi ahli Pertubuhan
            hendaklah memberi kenyataan bertulis ... terlebih dahulu kepada
            Setiausaha dan menjelaskan segala tunggakannya. <br />
            <br />
            2. Mana-mana ahli yang gagal mematuhi Perlembagaan Pertubuhan atau
            bertindak dengan cara yang akan mencemarkan nama baik Pertubuhan
            boleh dipecat atau digantung keahliannya bagi suatu tempoh masa yang
            difikirkan munasabah oleh Jawatankuasa. Sebelum Jawatankuasa memecat
            atau menggantung keahlian ahli tersebut, ahli itu hendaklah
            diberitahu akan sebab-sebab bagi pemecatan atau penggantungannya
            secara bertulis. Ahli tersebut juga hendaklah diberi peluang dalam
            tempoh ... dari tarikh pemecatan atau penggantungannya untuk memberi
            penjelasan dan membela dirinya. Pemecatan atau penggantungannya itu
            hendaklah dilaksanakan melainkan keputusan Mesyuarat Agung menunda
            atau membatalkan keputusan itu atas rayuan oleh ahli tersebut.
          </Typography>
        </>
      ),
    },
    {
      title: `${t("clause")} 6: ${t("positionOfAuthority")}`,
      content: (
        <>
          1. Satu Jawatankuasa seperti berikut yang dinamakan Ahli Jawatankuasa
          Pertubuhan hendaklah dipilih setiap ... dalam Mesyuarat Agung ...:{" "}
          <br />
          <br />
          i. Seorang orang ... <br />
          ii. ... orang ... <br />
          iii. ... orang ... (pilihan) <br />
          iv. Seorang ... <br />
          v. ... orang ... <br />
          vi. Seorang ... <br />
          vii. ... orang ... (pilihan) <br />
          viii. ... orang ... <br />
          <br />
          2. Pemegang-pemegang jawatan Pertubuhan ini dan tiap-tiap pegawai yang
          menjalankan tugas eksekutif dalam Pertubuhan ini hendaklah warganegara
          Malaysia dan bukan warganegara yang mendapat kebenaran Pendaftar
          Pertubuhan. (Nota: selaraskan dengan pilihan di Fasal 4 Keahlian)
          <br />
          <br />
          3. Nama calon untuk jawatan-jawatan di atas hendaklah dicadang serta
          disokong dan pemilihan dijalankan dengan kaedah mengundi oleh
          ahli-ahli dalam Mesyuarat Agung .... Ahli Jawatankuasa sebelumnya
          boleh dicalonkan semula untuk jawatan-jawatan tersebut. <br />
          <br />
          4. Fungsi Jawatankuasa ialah mengelola dan mengaturkan kerja-kerja
          harian Pertubuhan dan membuat keputusan atas perkara-perkara mengenai
          perjalanan Pertubuhan mengikut dasar am yang telah ditetapkan oleh
          Mesyuarat Agung. Jawatankuasa tidak boleh mengambil tindakan yang
          bertentangan dengan keputusan Mesyuarat Agung melainkan Jawatankuasa
          terlebih dahulu merujuk kepada Mesyuarat Agung. Jawatankuasa hendaklah
          mengemukakan laporan berkenaan kegiatan Pertubuhan bagi tahun
          sebelumnya dalam tiap-tiap Mesyuarat Agung.
          <br />
          <br />
          5. Jawatankuasa hendaklah bermesyuarat sekurang-kurangnya ... kali
          setahun. Notis bagi tiap-tiap mesyuarat hendaklah diberikan kepada
          Ahli Jawatankuasa sekurang-kurangnya tujuh (7) hari sebelum tarikh
          mesyuarat. Pengerusi dengan bersendirian, atau tidak kurang daripada
          empat (4) orang Ahli Jawatankuasa bersama-sama, boleh memanggil supaya
          diadakan Mesyuarat Jawatankuasa pada bila-bila masa.
          Sekurang-kurangnya satu perdua (1/2) daripada bilangan Ahli
          Jawatankuasa hendaklah hadir bagi mengesahkan perjalanan dan
          mencukupkan kuorum mesyuarat. <br />
          <br />
          6. Jika timbul perkara mustahak yang memerlukan kelulusan Jawatankuasa
          dan Mesyuarat Jawatankuasa tidak dapat diadakan, maka Setiausaha boleh
          mendapatkan kelulusan daripada Jawatankuasa secara edaran kepada Ahli
          Jawatankuasa. Syarat-syarat seperti berikut hendaklah disempurnakan
          sebelum mendapat keputusan Jawatankuasa: <br />
          <br />
          a. Perkara yang dibangkitkan itu hendaklah dijelaskan dengan terang
          kepada tiap-tiap Ahli Jawatankuasa mengikut kaedah edaran yang
          ditetapkan; <br />
          <br />
          b. Sekurang-kurangnya satu perdua (1/2) daripada bilangan Ahli
          Jawatankuasa mestilah menyatakan persetujuan atau bantahan mereka
          terhadap perkara itu; dan <br />
          <br />
          c. Keputusan hendaklah dengan undi yang terbanyak. Sebarang keputusan
          yang diperolehi melalui kaedah edaran yang ditetapkan hendaklah
          dilaporkan oleh Setiausaha kepada Mesyuarat Jawatankuasa berikutnya
          untuk disahkan dan dicatatkan dalam minit mesyuarat. <br />
          <br />
          7. Ahli Jawatankuasa yang tidak menghadiri Mesyuarat Jawatankuasa tiga
          (3) kali berturut-turut tanpa alasan yang memuaskan akan disifatkan
          sebagai telah meletakkan jawatan. <br />
          <br />
          8. Jika seorang Ahli Jawatankuasa meninggal dunia atau meletakkan
          jawatan atau kekosongan oleh apa-apa sebab sekalipun, calon yang kedua
          atau seterusnya mendapat undi terbanyak dalam pemilihan yang lalu
          hendaklah dipanggil untuk mengisi kekosongan itu. Jika calon yang
          sedemikian tidak ada atau menolak jawatan itu, maka Jawatankuasa
          mempunyai kuasa untuk melantik ahli Pertubuhan yang lain mengisi
          kekosongan itu sehingga Mesyuarat Agung ... diadakan. <br />
          <br />
          9. Jawatankuasa melalui mesyuarat boleh memberi arahan kepada
          Setiausaha dan pegawai-pegawai lain untuk menjalankan urusan-urusan
          Pertubuhan, dan melantik pengurus dan kakitangan yang difikirkan
          mustahak. Jawatankuasa boleh menggantung atau melucutkan jawatan
          mana-mana pengurus atau kakitangan kerana cuai dalam pekerjaan, tidak
          jujur, tidak cekap, khinat, ingkar menjalankan keputusan Jawatankuasa,
          atau sebab-sebab yang difikirkan boleh menjejaskan kepentingan
          Pertubuhan. <br />
          <br />
          10. Jawatankuasa boleh menubuhkan Jawatankuasa Kecil jika difikirkan
          mustahak dan memberikan tugas khas kepada mana-mana Ahli Jawatankuasa.
          <br />
          <br />
          11. Jawatankuasa hendaklah bertanggungjawab memelihara dokumen
          Pertubuhan pada setiap masa. <br />
          <br />
          12. Jawatankuasa yang lama hendaklah menyerahkan semua dokumen
          Pertubuhan seperti sijil pendaftaran, perlembagaan berdaftar, salinan
          Penyata Tahunan, surat-surat maklum balas, buku-buku akaun, buku bank,
          buku cek dan resit, geran-geran tanah, senarai aset dan sebagainya
          kepada Jawatankuasa baharu. <br />
          <br />
          13. Jawatankuasa melalui mesyuarat boleh menggantung atau melucutkan
          mana-mana Ahli Jawatankuasa kerana cuai dalam menjalankan tugas,
          ingkar menjalankan keputusan Jawatankuasa atau sebab-sebab yang
          difikirkan boleh menjejaskan kepentingan Pertubuhan. Sebelum
          Jawatankuasa menggantung atau melucutkan jawatan seseorang Ahli
          Jawatankuasa, Ahli Jawatankuasa tersebut hendaklah diberitahu akan
          sebab-sebab bagi penggantungan atau pelucutannya secara bertulis. Ahli
          Jawatankuasa tersebut juga hendaklah diberi peluang dalam tempoh ...
          hari dari tarikh penggantungan atau pelucutannya untuk memberi
          penjelasan dan membela dirinya. Penggantungan atau pelucutannya itu
          hendaklah dilaksanakan melainkan keputusan Mesyuarat Agung menunda
          atau membatalkan keputusan itu atas rayuan oleh ahli jawatankuasa
          tersebut.
        </>
      ),
    },
    {
      title: `${t("clause")} 7: ${t("positionOfAuthorityTasks")}`,
      content: (
        <>
          1. Pengerusi dalam tempoh memegang jawatannya hendaklah
          mempengerusikan semua Mesyuarat Agung dan Mesyuarat Jawatankuasa serta
          bertanggungjawab atas kesempurnaan perjalanan mesyuarat. Pengerusi
          mempunyai undi pemutus dan beliau hendaklah menandatangani minit
          mesyuarat tersebut. <br />
          <br />
          2. Timbalan Pengerusi hendaklah membantu Pengerusi dalam urusan
          pentadbiran Pertubuhan dan memangku tugas Pengerusi semasa
          ketiadaannya. <br />
          <br />
          a. Naib Pengerusi hendaklah memangku jawatan Pengerusi dan Timbalan
          Pengerusi semasa ketiadaannya. (Jika ada) <br />
          <br />
          3. Setiausaha hendaklah menjalankan kerja pentadbiran Pertubuhan
          mengikut Perlembagaan dan menjalankan arahan-arahan Mesyuarat Agung
          dan Jawatankuasa. Setiausaha bertanggungjawab mengendalikan urusan
          surat-menyurat dan menyimpan semua rekod serta dokumen Pertubuhan,
          kecuali buku-buku akaun dan dokumen kewangan. Setiausaha hendaklah
          menyimpan buku daftar ahli yang mengandungi maklumat terperinci ahli
          mengikut kategori seperti nama, tempat dan tarikh lahir, nombor kad
          pengenalan, pekerjaan, nama dan alamat majikan dan alamat rumah
          kediaman tiap-tiap ahli. Setiausaha hendaklah hadir dalam semua
          mesyuarat dan membuat catatan mesyuarat kecuali atas sebab-sebab
          munasabah. Setiausaha hendaklah dalam masa 60 hari dari tarikh
          kelulusan Mesyuarat Agung diadakan mengemukakan Penyata Tahunan kepada
          Pendaftar Pertubuhan. <br />
          <br />
          4. Penolong Setiausaha hendaklah membantu Setiausaha menjalankan
          kerja-kerjanya dan memangku jawatan itu semasa ketiadaan Setiausaha.
          <br />
          <br />
          5. Bendahari hendaklah bertanggungjawab dalam semua hal ehwal kewangan
          Pertubuhan dan memastikan penyata kewangan Pertubuhan yang terdiri
          daripada penyata penerimaan dan perbelanjaan serta kunci kira-kira
          adalah tepat dan teratur. <br />
          <br />
          a. Penolong Bendahari hendaklah membantu Bendahari menjalankan
          kerja-kerjanya dan memangku jawatan itu semasa ketiadaan Bendahari.
          (Jika ada) <br />
          <br />
          6. Ahli Jawatankuasa Biasa hendaklah membantu Jawatankuasa
          melaksanakan tugas yang diarahkan kepadanya.
        </>
      ),
    },
    {
      title: `${t("clause")} 8: ${t("financialResources")}`,
      content: (
        <>
          <Typography>
            Sumber kewangan Pertubuhan adalah daripada: <br />
            Bayaran masuk: <br />
            RM ... (Ringgit Malaysia ... Sahaja) <br />
            <br />
            Yuran: <br />
            RM ... (Ringgit Malaysia ... Sahaja) <br />
            <br />
            a. Yuran ... hendaklah dijelaskan kepada Bendahari terlebih dahulu
            dalam tempoh ... hari dari awal tiap-tiap .... <br />
            <br />
            b. Yuran seumur hidup sebanyak RM ... perlu dijelaskan oleh ahli
            semasa permohonan. <br />
            <br />
            c. Ahli yang mempunyai tunggakan yuran lebih daripada ... akan
            menerima surat peringatan yang ditandatangani oleh Setiausaha atau
            wakilnya, dan hilanglah hak-hak keistimewaannya sebagai ahli
            sehingga tunggakannya dijelaskan.
            <br />
            <br />
            d. Ahli yang mempunyai tunggakan yuran lebih daripada jumlah yuran
            bagi tempoh ... dengan sendirinya terhenti daripada menjadi ahli
            Pertubuhan dan Jawatankuasa boleh mengarahkan supaya tindakan yang
            sah diambil terhadapnya, dengan syarat mereka berpuas hati yang ahli
            itu telah menerima kenyataan berkenaan tunggakannya terlebih dahulu.
            <br />
            <br />
            e. Jawatankuasa mempunyai kuasa menetapkan yuran masuk semula bagi
            sesiapa yang telah terlucut keahliannya disebabkan tunggakan yuran.
            <br />
            <br />
            f. Yuran khas atau kutipan wang daripada ahli-ahli untuk perkara
            yang tertentu boleh dipungut dengan persetujuan Mesyuarat Agung.
            Sekiranya ada ahli yang gagal membayar yuran khas atau wang tersebut
            dalam tempoh yang telah ditetapkan, maka yuran khas atau wang itu
            akan dianggap sama seperti tunggakan yuran. <br />
            <br />
            3. Sumbangan <br />
            ... <br />
            <br />
            4. Kegiatan Ekonomi <br />
            ...
          </Typography>
        </>
      ),
    },
    {
      title: `${t("clause")} 9: ${t("financialMangement")}`,
      content: (
        <Typography>
          1. Tertakluk kepada peruntukan-peruntukan dalam Perlembagaan ini, wang
          Pertubuhan ini boleh digunakan untuk perkara-perkara yang berfaedah
          bagi menjalankan tujuan-tujuan Pertubuhan, termasuklah belanja
          pentadbiran, bayaran gaji, biayaan dan perbelanjaan Ahli Jawatankuasa,
          Juruaudit dan kakitangan yang bergaji serta upah Juruaudit luar dan
          bertauliah. <br />
          <br />
          2. Bendahari dibenarkan menyimpan wang tunai dalam tangan tidak lebih
          daripada RM ... (Ringgit Malaysia ... Sahaja) pada satu-satu masa.
          Wang yang lebih daripada jumlah itu hendaklah dimasukkan ke akaun bank
          yang diluluskan oleh Jawatankuasa dalam tempoh ... hari. Akaun bank
          tersebut hendaklah dibuka atas nama Pertubuhan. <br />
          <br />
          3. Segala cek atau penyata pengeluaran wang daripada akaun Pertubuhan
          hendaklah ditandatangani oleh Pengerusi, Setiausaha dan Bendahari.
          Walau bagaimanapun, Jawatankuasa melalui mesyuarat berhak melantik
          sesiapa di antara mereka sebagai pengganti untuk menandatangani cek
          atau pengeluaran wang Pertubuhan semasa ketiadaan mana-mana
          penandatangan tersebut. <br />
          <br />
          4. Perbelanjaan yang lebih daripada RM ... (Ringgit Malaysia ...
          Sahaja) pada satu-satu masa tidak boleh dilakukan tanpa kelulusan
          Mesyuarat Jawatankuasa. Perbelanjaan yang lebih daripada RM ...
          (Ringgit Malaysia ...Sahaja) bagi satu-satu masa tidak boleh dilakukan
          tanpa kelulusan Mesyuarat Agung. Perbelanjaan yang kurang daripada RM
          ... (Ringgit Malaysia ... Sahaja) boleh diluluskan oleh Pengerusi,
          Setiausaha dan Bendahari. <br />
          <br />
          5. Tahun kewangan Pertubuhan ini hendaklah bagi tempoh 12 bulan iaitu
          bermula dari .... Setiausaha adalah bertanggungjawab untuk
          mengemukakan dokumen dalam masa 60 hari selepas Mesyuarat Agung kepada
          Pendaftar Pertubuhan apabila disahkan dan/atau berakhirnya setiap
          tahun kalendar sekiranya tiada Mesyuarat Agung. <br />
          <br />
          6. Jawatankuasa yang melebihi tempoh pelantikan jawatannya tidak
          mempunyai kuasa untuk membuat sebarang pengeluaran wang Pertubuhan.{" "}
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 10: ${t("auditor")}`,
      content: (
        <Typography>
          1. ... orang yang bukan Ahli Jawatankuasa Pertubuhan hendaklah
          dilantik dalam Mesyuarat Agung ... sebagai Juruaudit Dalam. Mereka
          yang memegang jawatan selama ... boleh dilantik semula. <br />
          <br />
          2. Juruaudit dalam dan/atau luar bertauliah adalah dikehendaki
          memeriksa penyata kewangan Pertubuhan bagi setiap berakhirnya tahun
          kewangan. Juruaudit hendaklah membuat perakuan dan menandatangi
          penyata kewangan tersebut untuk pertimbangan Mesyuarat Jawatankuasa
          atau Mesyuarat Agung ... Mereka juga dikehendaki oleh Pengerusi untuk
          mengaudit penyata kewangan Pertubuhan pada bila-bila masa dalam tempoh
          perkhidmatan mereka dan membuat laporan kepada Jawatankuasa. <br />
          <br />
          3. Penyata kewangan bagi tempoh 12 bulan hendaklah disediakan oleh
          Bendahari dan diperiksa oleh Juruaudit dalam dan/atau luar bertauliah
          dengan segera setelah tamat tahun kewangan. Penyata kewangan yang
          telah diaudit itu hendaklah diedarkan untuk makluman ahli-ahli dan
          dikemukakan untuk diluluskan oleh Mesyuarat Agung ... yang berikut.
          Setiap salinan dokumen tersebut hendaklah disimpan di alamat tempat
          urusan Pertubuhan untuk makluman ahli. <br />
          <br />
          4. Jika seorang Juruaudit meninggal dunia atau meletakkan jawatan atau
          ditamatkan perkhidmatan, maka Jawatankuasa hendaklah melantik ahli
          lain atau Juruaudit dalam dan/atau luar bertauliah untuk mengisi
          kekosongan tersebut sehingga Mesyuarat Agung ... diadakan.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 11: ${t("propertyOfficerAndOrPublicOfficer")}`,
      content: (
        <Typography>
          1. Segala harta tidak alih Pertubuhan hendaklah didaftarkan atas nama
          Pertubuhan dan segala surat cara pelaksanaan berkaitan dengan harta
          ini dianggap sah dan berkuatkuasa seolah-olah ia telah dilaksanakan
          oleh seorang tuan punya berdaftar. Surat cara itu hendaklah
          disempurnakan oleh Pengerusi, Setiausaha dan Bendahari pertubuhan pada
          masa itu yang mana perlantikan mereka disahkan melalui sijil perakuan
          yang dikeluarkan oleh Pendaftar Pertubuhan dan dimeteri dengan meteri
          Pertubuhan. <br />
          <br />
          2. Harta tidak alih Pertubuhan tidak boleh dijual, digadai, ditarik
          balik atau ditukar milik tanpa persetujuan Mesyuarat Agung. <br />
          <br />
          3. Pertubuhan boleh melantik Pegawai Awam yang mana pelantikan mereka
          disahkan dengan sijil perakuan yang dikeluarkan oleh Pendaftar
          Pertubuhan dan dimeteri dengan meteri Pertubuhan.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 12: ${t("generalMeeting")}`,
      content: (
        <Typography>
          1. Pengelolaan Pertubuhan ini terserah kepada Mesyuarat Agung
          ahli-ahli. Sekurang-kurangnya satu perdua (1/2) daripada jumlah ahli
          yang berhak mengundi atau dua (2) kali jumlah bilangan Ahli
          Jawatankuasa, mengikut mana yang kurang, hendaklah hadir dalam
          Mesyuarat Agung bagi mengesahkan perjalanan mesyuarat dan mencukupkan
          kuorum untuk mesyuarat. <br />
          <br />
          2. Jika kuorum tidak cukup selepas setengah jam daripada waktu yang
          telah ditetapkan untuk mesyuarat, maka mesyuarat itu hendaklah
          ditangguhkan kepada suatu tarikh (tidak lebih daripada 30 hari) yang
          ditetapkan oleh Jawatankuasa dan jika kuorum tidak cukup selepas
          setengah jam daripada waktu yang telah ditetapkan untuk mesyuarat yang
          telah ditangguhkan itu, maka ahli-ahli yang hadir berkuasa menjalankan
          mesyuarat tetapi tidak berkuasa meminda Perlembagaan Pertubuhan.{" "}
          <br />
          <br />
          3. Mesyuarat Agung ... bagi Pertubuhan hendaklah diadakan sebelum ...
          setiap ... daripada tarikh Mesyuarat Agung terakhir. Tarikh, masa dan
          tempat ditetapkan oleh Mesyuarat Jawatankuasa. <br />
          <br />
          4. Tugas-tugas Mesyuarat Agung ... adalah: <br />
          <br />
          a. mengesahkan minit Mesyuarat Agung tahun lalu; <br />
          <br />
          b. mempertimbang laporan Jawatankuasa berkenaan aktiviti Pertubuhan
          tahun lalu; <br />
          <br />
          c. mempertimbang laporan Bendahari dan penyata kewangan tahun lalu
          yang telah diaudit; <br />
          <br />
          d. memilih Ahli Jawatankuasa dan melantik Juruaudit (jika berkenaan);
          dan <br />
          <br />
          e. menguruskan perkara-perkara lain yang dibentangkan dalam mesyuarat.
          <br />
          <br />
          5. Setiausaha hendaklah menghantar notis panggilan mesyuarat kepada
          tiap-tiap ahli sekurang-kurangnya ... hari sebelum Mesyuarat Agung ...
          diadakan, agenda mesyuarat, salinan minit mesyuarat, laporan aktiviti
          tahunan, penyata kewangan Pertubuhan bagi tahun lalu yang telah
          diaudit serta cadangan dan pencalonan untuk pemilihan pegawai-pegawai.
          Setiap salinan dokumen tersebut hendaklah disimpan di alamat tempat
          urusan Pertubuhan untuk makluman ahli. <br />
          <br />
          6. Mesyuarat Agung Khas bagi Pertubuhan boleh diadakan: <br />
          <br />
          a. apabila difikirkan mustahak oleh Jawatankuasa; atau <br />
          <br />
          b. atas permintaan bertulis kepada Setiausaha oleh tidak kurang
          daripada satu perlima (1/5) dari jumlah ahli yang berhak mengundi
          dengan menerangkan tujuan mengadakannya. <br />
          <br />
          7. Mesyuarat Agung Khas atas permintaan ahli-ahli hendaklah diadakan
          dalam tempoh 30 hari dari tarikh penerimaan permintaan mesyuarat
          tersebut. <br />
          <br />
          8. Pengumuman dan agenda untuk Mesyuarat Agung Khas itu hendaklah
          diedarkan oleh Setiausaha kepada semua ahli-ahli sekurang-kurangnya 14
          hari sebelum tarikh yang telah ditetapkan untuk mesyuarat. <br />
          <br />
          9. Fasal 12(1) dan 12(2) dalam Perlembagaan ini berkenaan kuorum dan
          penangguhan Mesyuarat Agung ... adalah terpakai untuk Mesyuarat Agung
          Khas, dengan syarat jika kuorum tidak mencukupi selepas setengah jam
          dari waktu yang telah ditetapkan bagi Mesyuarat Agung Khas atas
          permintaan ahli-ahli, maka mesyuarat tersebut hendaklah dibatalkan dan
          sekurang-kurangnya enam (6) bulan dari tarikh ini, Mesyuarat Agung
          Khas atas permintaan ahli-ahli dengan tujuan yang sama tidak boleh
          diadakan. <br />
          <br />
          10. Setiausaha hendaklah menghantar kepada tiap-tiap ahli satu salinan
          minit Mesyuarat Agung ... atau Mesyuarat Agung Khas dalam tempoh tiga
          (3) bulan setelah selesai mesyuarat tersebut. <br />
          <br />
          11. Mesyuarat Agung Pertama hendaklah diadakan dengan segera dan tidak
          melebihi setahun daripada tarikh penubuhan Pertubuhan selepas cukup
          jumlah ahli yang dirasakan sesuai oleh Jawatankuasa Pertubuhan atau
          apabila telah melebihi dua (2) kali jumlah bilangan Ahli Jawatankuasa.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 13: ${t("constitutionalAmendment")}`,
      content: (
        <Typography>
          Perlembagaan ini selain alamat berdaftar dan tempat urusan Pertubuhan
          tidak boleh dipinda kecuali dengan keputusan Mesyuarat Agung.
          Permohonan untuk pindaan perlembagaan hendaklah dikemukakan kepada
          Pendaftar Pertubuhan dalam tempoh 60 hari dari tarikh keputusan
          Mesyuarat Agung yang meluluskan pindaan itu dan hanya boleh
          dikuatkuasakan mulai daripada tarikh pindaan itu diluluskan oleh
          Pendaftar Pertubuhan.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 14: ${t("constitutionalInterpretation")}`,
      content: (
        <Typography>
          1. Dalam tempoh di antara dua (2) Mesyuarat Agung, Jawatankuasa boleh
          memberikan tafsirannya kepada Perlembagaan ini dan Jawatankuasa, jika
          perlu, boleh memutuskan perkara-perkara yang kurang jelas dalam
          Perlembangaan ini. <br />
          <br />
          2. Kecuali perkara-perkara yang bertentangan atau tidak selaras dengan
          dasar yang telah dibuat dalam Mesyuarat Agung, keputusan Jawatankuasa
          terhadap ahli-ahli adalah muktamad jika tidak diubah oleh keputusan
          Mesyuarat Agung.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 15: ${t("adviser")}`,
      content: (
        <Typography>
          Jawatankuasa boleh, jika difikirkan perlu, melantik orang-orang yang
          layak menjadi Penasihat/ Penaung bagi Pertubuhan ini dengan syarat
          orang yang dilantik itu menyatakan persetujuannya secara bertulis
          terlebih dahulu.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 16: ${t("ban")}`,
      content: (
        <Typography>
          1. Sebarang bentuk perjudian seperti yang ditafsirkan dalam Akta Rumah
          Judi Terbuka 1953 adalah dilarang di premis pertubuhan. <br />
          <br />
          2. Pertubuhan atau ahli-ahlinya tidak harus cuba menghalang atau
          dengan apa cara juga mengganggu perniagaan atau harga barang-barang
          atau mengambil peranan dalam gerakan kesatuan sekerja seperti definisi
          dalam Akta Kesatuan Sekerja 1959. <br />
          <br />
          3. Pertubuhan ini tidak boleh menjalankan loteri sama ada dikhaskan
          kepada ahli-ahli atau tidak, atas nama Pertubuhan atau
          pegawai-pegawainya atau Jawatankuasa atau ahli tanpa kelulusan
          daripada pihak berkuasa yang berkenaan. <br />
          <br />
          4. "Faedah" seperti yang ditafsirkan di bawah Seksyen 2 Akta
          Pertubuhan 1966 tidak boleh diberikan oleh Pertubuhan kepada mana-mana
          ahlinya. <br />
          <br />
          5. Mengutip derma tanpa kebenaran pihak berkuasa yang berkenaan.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 17: ${t("liquidation")}`,
      content: (
        <Typography>
          1. Pertubuhan ini boleh dibubarkan secara sukarela dengan persetujuan
          sekurang-kurangnya tiga perlima (3/5) daripada jumlah ahli yang berhak
          mengundi dan diputuskan dalam suatu Mesyuarat Agung Khas. <br />
          <br />
          2. Sekiranya Pertubuhan hendak dibubarkan secara yang disebutkan di
          atas, maka segala tunggakan dan tanggungan Pertubuhan yang sah
          mengikut Perlembagaan hendaklah dijelaskan dan baki wang yang tinggal
          hendaklah diselesaikan mengikut cara yang dipersetujui dalam Mesyuarat
          Agung Khas berkenaan. <br />
          <br />
          3. Permohonan pembubaran ini hendaklah dikemukakan kepada Pendaftar
          Pertubuhan dalam tempoh 14 hari dari tarikh keputusan Mesyuarat Agung
          Khas bagi pembubaran itu.
        </Typography>
      ),
    },
    {
      title: `${t("clause")} 18: ${t("flagEmblemBadges")}`,
      content: (
        <Typography>
          1. Bendera <br />
          ... <br />
          Keterangan bendera: <br /> ... <br />
          <br />
          2. Keterangan Lambang <br /> ... <br />
          Keterangan Lambang: <br /> ... <br />
          <br />
          3. Keterangan Lencana <br /> ... <br /> Keterangan Lencana: <br /> ...
        </Typography>
      ),
    },
  ];

  const [formValues, setFormValues] = useState<FormValues>({ year: null });
  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };
  const dispatch: AppDispatch = useDispatch();
  const [statementList, setStatementList] = useState<any>({});
  const [statements, setStatements] = useState<any>({});
  const {
    data: appealDataById,
    loading: loadingAppeal,
    error: errorAppeal,
  } = useSelector((state: any) => state.appealByIdData);

  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  useEffect(() => {
    if (id) {
      dispatch(fetchAppealByIdData({ id: id }));
    }
  }, []);

  useEffect(() => {
    if (appealDataById?.societyId) {
      dispatch(fetchSocietyByIdData({ id: appealDataById.societyId }));
    }
  }, [appealDataById?.societyId]);

  const isEmpty = (obj: any) => Object.keys(obj).length === 0;
  const getYearById = (id: any) => {
    const statement = statementList.find(
      (statement: any) => statement.value === id
    );
    return statement ? statement.label : null;
  };

  const fetchStatementList = async () => {
    try {
      const response = await fetch(
        `${API_URL}/society/statement/list?societyId=${societyDataById?.id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      console.log("statement years", data?.data?.data);
      const statements =
        data?.data?.data?.map((item: any) => ({
          value: item.id,
          label: item.statementYear,
        })) || [];
      console.log("statements", statements);
      setStatementList(statements);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return null;
    }
  };

  const fetchStatementInfo = async () => {
    const year = formValues.year;

    try {
      const response = await fetch(
        `${API_URL}/society/statement/getGeneralInfo?societyId=${
          societyDataById?.id
        }&statementId=${formValues.year}&year=${getYearById(formValues.year)}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      console.log("statement years", data?.data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return null;
    }
  };

  useEffect(() => {
    if (societyDataById && isEmpty(statementList)) {
      fetchStatementList();
    }
  }, [societyDataById]);

  useEffect(() => {
    if (formValues?.year && societyDataById) {
      fetchStatementInfo();
    }
  }, [formValues?.year]);

  return (
    <Box>
      {!isEmpty(statementList) ? (
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
            overflow: "hidden",
          }}
        >
          <Input
            value={formValues.year ? formValues?.year : ""}
            name="year"
            onChange={handleChange}
            options={statementList}
            type="select"
            required
            label={t("selectStatementYear")}
          />
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
            }}
            container
            spacing={2}
          >
            <Grid item xs={4}>
              <Typography sx={{ fontSize: 14 }} className="label">
                {t("annualStatementRecord")}
              </Typography>
            </Grid>
            <Grid item xs={8}>
              <Box
                sx={{
                  border: "1px solid var(--primary-color)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  p: "8px 0px 8px 0px",
                  borderRadius: 3,
                  gap: 2,
                  cursor: "pointer",
                }}
              >
                <img height={16} width={15} src="/addDocument.png" />
                <Typography className="label">
                  {t("showConstitution")}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      ) : (
        <Typography className="label">{t("noData")}</Typography>
      )}
    </Box>
  );
};

export default PenyataTahuanSection;
