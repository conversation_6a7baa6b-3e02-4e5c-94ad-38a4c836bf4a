import React from "react";
import { Dialog, DialogContent, CircularProgress, Typography, Box } from "@mui/material";

interface LoadingDialogProps {
  open: boolean;
  message?: string;
  /**
   * @default false
   */
  blur?: boolean;
}

export const LoadingDialog: React.FC<LoadingDialogProps> = ({ 
  open, 
  message = "Memproses...",
  blur = false 
}) => {
  return (
    <Dialog
      open={open}
      PaperProps={{
        style: {
          backgroundColor: "#fff",
          boxShadow: "none",
          padding: "20px",
          borderRadius: "8px",
        },
      }}
      slotProps={{
        backdrop: {
          style: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            backdropFilter: blur ? "blur(4px)" : "none",
          },
        },
      }}
    >
      <DialogContent>
        <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 2 }}>
          <CircularProgress sx={{ color: "#4DB6AC" }} />
          <Typography>{message}</Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default LoadingDialog;