import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Alert,
  Menu,
  MenuItem,
  useMediaQuery,
  Theme,
  Card,
  CardContent,
  Grid,
  Button,
  Divider,
  SvgIcon,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Search,
  FilterList,
  Add,
  MoreVert,
  Campaign,
  Circle,
  Edit,
  Visibility,
  Description,
  Payment,
  Delete,
  Timer,
} from "@mui/icons-material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { ButtonPrimary } from "@/components/button";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import { DataGrid, GridColDef, useGridApiRef } from "@mui/x-data-grid";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import {
  NewSocietyBranchStatus,
  ApplicationStatusEnum,
  HideOrDisplayFlex,
  HideOrDisplayInherit,
  designation,
  CitizenshipStatus,
  COMMITTEE_TASK_TYPE,
} from "@/helpers/enums";
import CustomDataGrid from "@/components/datagrid";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { DataTable } from "@/components";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";

const AhliPertubuhan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const [openSearchModal, setOpenSearchModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);
  const [searchPertubuhan, setSearchPertubuhan] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [pageSize, setPageSize] = useState(5);
  const [page, setPage] = useState(0);
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [memberId, setMemberId] = useState<string>("");
  const isManager = useSelector(getUserPermission);
  const { isAliranModuleAccess, fetchAliranTugasAccess } =
    usejawatankuasaContext();

  const apiRef = useGridApiRef();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const editableAndDeleteable = [
    NewSocietyBranchStatus.BARU_1,
    NewSocietyBranchStatus.DIBENARKAN_1,
    NewSocietyBranchStatus.AKTIF_1,
  ];
  useEffect(() => {
    fetchAliranTugasAccess(COMMITTEE_TASK_TYPE.PENGURUSAN_AJK);
  }, []);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchPertubuhan);
    }, 1000);

    return () => {
      clearTimeout(handler);
    };
  }, [searchPertubuhan]);

  const handleClick = (event: React.MouseEvent<HTMLElement>, index: number) => {
    setAnchorEl(event.currentTarget);
    setSelectedRowIndex(index);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedRowIndex(null);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchPertubuhan(e.target.value);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-menu" : undefined;

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const {
    data,
    isLoading: isForgotPasswordLoading,
    refetch: fetchAhli,
  } = useCustom<any>({
    url: `${API_URL}/society/branch/committee/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        branchId: branchDataRedux.id,
        pageNo: page + 1,
        pageSize: pageSize,
      },
    },
    queryOptions: {
      retry: false,
      cacheTime: 0,
    },
  });

  var memberList = data?.data?.data;

  const { mutate: deleteMember, isLoading } = useCustomMutation();
  const clickDeleteMebmer = (memberId: string) => {
    setMemberId(memberId);
    setOpenConfirmDelete(true);
  };

  const handleDeleteMember = () => {
    deleteMember(
      {
        url: `${API_URL}/society/branch/committee/${memberId}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          // memberList.data = memberList.data?.filter(
          //   (e: any) => e.committeeIcNo != memberId
          // );
          setOpenConfirmDelete(false)
          fetchAhli();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    fetchAhli();
  }, [page, pageSize]);

  const columns: GridColDef[] = [
    {
      flex: 1,
      field: "committeeName",
      headerName: t("name"),
      headerAlign: "left",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "left" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params: any) => params.row.committeeName ?? "-",
    },
    {
      flex: 1,
      field: "citizen",
      headerName: t("citizen"),
      headerAlign: "left",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "left" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params: any) =>
        params.row.citizenshipStatus
          ? t(
              CitizenshipStatus.find(
                (item) => item.value === parseInt(params.row.citizenshipStatus)
              )?.label || "-"
            )
          : "-",
    },
    {
      flex: 1,
      field: "position",
      headerName: t("position"),
      headerAlign: "left",

      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "left" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params: any) =>
        designation[params.row.designationCode]?.name ?? "-",
    },
    {
      flex: 1.5,
      field: "actions",
      headerName: "",
      align: "right",
      renderCell: (params: any) => {
        const row = params?.row; 
        return isManager || isAliranModuleAccess ? (
          <>
            <IconButton
              size="small"
              sx={{ color: "#55556D" }}
              onClick={() =>
                navigate(`edit/${row.committeeIcNo}`, {
                  state: {
                    row,
                    isAccess: isManager || isAliranModuleAccess,
                  },
                })
              }
            >
              <EditIcon
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
            <IconButton
              size="small"
              sx={{ color: "#FF6B6B" }}
              onClick={() => clickDeleteMebmer(row.id)}
            >
              <TrashIcon
                sx={{
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </>
        ) : (
          <>
            <IconButton
              size="small"
              sx={{ color: "#55556D" }}
              onClick={() =>
                navigate(`edit/${row.committeeIcNo}`, {
                  state: {
                    row,
                    isAccess: isManager || isAliranModuleAccess,
                  },
                })
              }
            >
              <EyeIcon
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 22,
              fontWeight: "700 !important",
            }}
          >
            {memberList?.total ?? 0}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            {t("jumlahAhliPertubuhan")}
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("senaraiAhliPertubuhan")}
          </Typography>

          <DataTable
            columns={columns as any[]}
            rows={memberList?.data || []} // Correctly slice data
            page={page + 1} // Page is 1-indexed for pagination
            rowsPerPage={pageSize}
            totalCount={memberList?.total}
            onPageChange={(newPage: number) => setPage(newPage - 1)}
            onPageSizeChange={(newRowsPerPage: number) =>
              setPageSize(newRowsPerPage)
            }
          />
        </Box>
      </Box>
      {isManager || isAliranModuleAccess ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("memberRegister")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    fontWeight: "500!important",
                    fontSize: 14,
                  }}
                >
                  {t("memberRegister")}
                </Typography>

                <ButtonPrimary
                  onClick={() => {
                    navigate(`daftar`, {
                      state: {
                        isAccess: isManager || isAliranModuleAccess,
                      },
                    });
                  }}
                >
                  {t("registerButton")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("sertaPertubuhan")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    color: "#666666",
                    fontWeight: "500!important",
                    fontSize: 14,
                  }}
                >
                  {t("permintaanSertaPertubuhan")}
                </Typography>

                <ButtonPrimary
                  onClick={() => {
                    navigate(`serta-pertubuhan`);
                  }}
                >
                  {t("senaraiPermintaan")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>{" "}
        </>
      ) : (
        <></>
      )} 
      <ConfirmationDialog
        status={1}
        turn
        open={openConfirmDelete}
        onClose={() => setOpenConfirmDelete(false)}
        title={t("deleteAhliConfirmation")}
        message={`${t("deleteAhliConfirmation")}`}
        onConfirm={handleDeleteMember}
        onCancel={() => setOpenConfirmDelete(false)}
      />
    </>
  );
};

export default AhliPertubuhan;
