import { useState } from "react";
import { Tabs, Tab, Box } from "@mui/material";

interface TabItem {
  label: string;
  content: React.ReactNode;
}

interface CustomTabsProps {
  tabs: TabItem[];
  initialTab?: number;
  onTabChange?: (index: number) => void;
}

const TabPanel = ({
  children,
  value,
  index,
}: {
  children?: React.ReactNode;
  value: number;
  index: number;
}) => {
  return (
    <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`}>
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

export const CustomTabsPanel: React.FC<CustomTabsProps> = ({
  tabs,
  initialTab = 0,
  onTabChange,
}) => {
  const [tabValue, setTabValue] = useState<number>(initialTab);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    if (onTabChange) onTabChange(newValue);
  };

  return (
    <>
      <Tabs
        value={tabValue}
        onChange={handleChange}
        variant="fullWidth"
        sx={{
          background: "#fff",
          borderRadius: "10px",
          minHeight: "30px",
          padding: "5px",
          mb: 1,
          fontSize: "14px",
          "& .Mui-selected": {
            color: "#fff !important",
            background: "var(--primary-color)",
            borderRadius: "10px !important",
          },
          "& .MuiTab-root": {
            textTransform: "none",
            padding: "4px 0",
            minHeight: "30px",
          },
          "& .MuiTabs-indicator": {
            display: "none",
          },
        }}
      >
        {tabs.map((tab, idx) => (
          <Tab key={idx} label={tab.label} />
        ))}
      </Tabs>

      {tabs.map((tab, idx) => (
        <TabPanel key={idx} value={tabValue} index={idx}>
          {tab.content}
        </TabPanel>
      ))}
    </>
  );
};
