export const Tick<PERSON>ddBackgroundSVG = `<svg width="36" height="32" viewBox="0 0 36 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_25143_20788)">
<path d="M28.556 34.0038V36.945L24.7743 34.7231V39.14L22.2543 37.661V33.2442L21.6433 32.8845L20.4642 32.1921L18.4727 31.0223V28.0778L18.542 28.1181L21.4782 29.8425L22.2543 30.2996V25.8828L22.502 26.0274L24.7743 27.3652V31.782L28.556 34.0038Z" stroke="white" stroke-width="0.5"/>
<path d="M17.2111 33.8484L19.1036 37.2064C18.2416 37.1896 17.3399 37.0383 16.392 36.7526C15.4475 36.4669 14.4599 36.0232 13.4295 35.4182C11.6889 34.3929 10.0475 33.0451 8.51498 31.3711C6.9825 29.6972 5.64819 27.8652 4.51204 25.872C3.3792 23.8821 2.48085 21.7947 1.8203 19.6165C1.15645 17.4384 0.826172 15.3274 0.826172 13.2938C0.826172 11.2602 1.15645 9.53583 1.8203 8.13415C2.48085 6.73247 3.3792 5.7039 4.51204 5.04508L5.02727 4.78286C6.04452 4.31563 7.20709 4.14088 8.51498 4.24844C8.57113 4.2518 8.62727 4.25849 8.68342 4.26521C9.79314 4.38286 10.9623 4.73247 12.181 5.31399C12.5906 5.50894 13.0067 5.73079 13.4295 5.97953C14.7968 6.78289 16.0882 7.77446 17.3069 8.95429C18.5256 10.1375 19.6486 11.4484 20.679 12.8871L18.8493 13.9829C18.0533 12.9274 17.2012 11.9661 16.2996 11.1056C15.3946 10.2417 14.4401 9.51563 13.4295 8.92404C12.4089 8.32236 11.4478 7.90894 10.5396 7.68037C9.48269 7.41483 8.49847 7.40135 7.59021 7.63328C7.14103 7.75093 6.70837 7.93247 6.29553 8.17113C4.33039 9.31062 3.34617 11.5123 3.34617 14.7728C3.34617 18.0333 4.33039 21.3913 6.29553 24.84C8.25736 28.2888 10.6386 30.8333 13.4295 32.4737C14.1032 32.8703 14.7539 33.1795 15.3847 33.4014C16.0155 33.6232 16.6232 33.7745 17.2111 33.8484Z" stroke="white" stroke-width="0.5"/>
<path d="M18.4826 28.0742H18.4727L18.542 28.1179C18.5222 28.1045 18.5024 28.091 18.4826 28.0742Z" stroke="white"/>
<path d="M35.1602 30.6416V33.5828L31.3785 35.5088L31.1308 35.6331L28.5547 36.9441V34.0029L28.8585 33.8483L31.3785 32.5677L32.4784 32.0063L35.1602 30.6416Z" stroke="white" stroke-width="0.5"/>
<path d="M31.3789 24.0039V28.4207L28.6938 29.7854L24.7734 31.7821V27.3653L25.0773 27.2106L27.3529 26.0543L28.8589 25.2879L31.3789 24.0039Z" stroke="white" stroke-width="0.5"/>
<path d="M20.4639 32.1914L20.3615 32.2452L17.2107 33.8485C16.6228 33.7746 16.0151 33.6233 15.3842 33.4015C14.7534 33.1797 14.1028 32.8704 13.429 32.4738C10.6382 30.8334 8.2569 28.2889 6.29506 24.8402C4.32992 21.3914 3.3457 18.0368 3.3457 14.7729C3.3457 11.5091 4.32992 9.31075 6.29506 8.17126C6.70791 7.9326 7.14057 7.75109 7.58974 7.63344C8.498 7.40151 9.48222 7.41495 10.5391 7.6805C10.1494 8.7158 9.95121 9.95613 9.95121 11.4116C9.95121 12.5511 10.0734 13.704 10.3145 14.8671L8.31304 15.8855L8.07524 16.0066L6.30827 17.0284L11.6653 26.4334L13.1813 25.6637L15.0672 24.7023C16.1108 26.0234 17.2503 27.146 18.4822 28.0738H18.4723V31.0217L20.4639 32.1914Z" stroke="white" stroke-width="0.5"/>
<path d="M32.6418 14.6992L18.2715 23.0723L15.0679 24.7026L13.182 25.6639L11.666 26.4337L13.1225 25.5833L14.9291 24.5312L18.0634 22.7059L26.0363 18.0606L32.6418 14.6992Z" stroke="white" stroke-width="0.5"/>
<path d="M32.642 14.6997L26.0365 18.0611L24.4809 15.3317L24.2695 14.9619L30.875 11.6006L32.642 14.6997Z" stroke="white" stroke-width="0.5"/>
<path d="M18.0604 18.5816L16.3 19.6102L12.951 21.5632L11.6663 22.3127L8.68715 17.0825L8.07617 16.0069L8.31397 15.8858L10.3154 14.8674L14.6817 12.6455L18.0604 18.5816Z" stroke="white" stroke-width="0.5"/>
<path d="M26.0359 18.0611L18.0631 22.7065L14.9288 24.5317L13.1222 25.5838L11.6657 26.4342L6.30859 17.0291L8.07557 16.0073L8.68658 17.0829L11.6657 22.3132L12.9504 21.5636L16.2994 19.6107L18.0598 18.5821L24.269 14.9619L24.4803 15.3317L26.0359 18.0611Z" stroke="white" stroke-width="0.5"/>
<path d="M35.1606 30.6418L32.4788 32.0065L31.3789 32.5678L28.8589 33.8484L28.5551 34.003L24.7734 31.7813L28.6938 29.7846L31.3789 28.4199L35.1606 30.6418Z" stroke="white" stroke-width="0.5"/>
<path d="M22.2542 26.1543V30.2988L21.478 29.8417L18.5419 28.1173C18.5221 28.1039 18.5022 28.0904 18.4824 28.0736L22.2542 26.1543Z" stroke="white" stroke-width="0.5"/>
<path d="M31.3794 24.0038L28.8594 25.2879L27.3534 26.0543L25.0778 27.2106L24.7739 27.3652L22.5016 26.0274L22.2539 25.8828L28.8594 22.5215L31.3794 24.0038Z" stroke="white" stroke-width="0.5"/>
<path d="M22.2542 33.2435V35.6032L19.1034 37.2065L17.2109 33.8485L20.3618 32.2452L20.4641 32.1914L21.6432 32.8838L22.2542 33.2435Z" stroke="white" stroke-width="0.5"/>
<path d="M27.2848 9.52637L25.4551 10.6222L18.8496 13.9835L20.6793 12.8877L25.3626 10.5045L27.2848 9.52637Z" stroke="white" stroke-width="0.5"/>
<path d="M27.2846 9.52598L25.3624 10.5041L20.6791 12.8873C19.6486 11.4487 18.5257 10.1377 17.307 8.95455C16.0883 7.77472 14.7969 6.78312 13.4295 5.97976C13.0068 5.73102 12.5906 5.50917 12.1811 5.31421C10.9624 4.7327 9.79322 4.38312 8.68349 4.26548C8.62734 4.25875 8.5712 4.25203 8.51505 4.24867C7.20716 4.14111 6.04459 4.3159 5.02734 4.78312L11.3785 1.54279C12.4552 0.988168 13.7037 0.769681 15.1206 0.887328C16.653 1.01842 18.2945 1.59321 20.035 2.61842C21.4024 3.42178 22.6938 4.41337 23.9125 5.59321C25.1312 6.7764 26.2541 8.08732 27.2846 9.52598Z" stroke="white" stroke-width="0.5"/>
</g>
<defs>
<clipPath id="clip0_25143_20788">
<rect width="36" height="40" fill="white"/>
</clipPath>
</defs>
</svg>`;
