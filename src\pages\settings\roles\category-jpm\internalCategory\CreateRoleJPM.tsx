import {
  Box,
  FormControlLabel,
  FormHelperText,
  Grid,
  Radio,
  RadioGroup,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useCallback, useEffect, useRef, useState } from "react";
import Input from "../../../../../components/input/Input";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { fetchPermissionsListData } from "../../../../../redux/APIcalls/permissionsListThunks";
import { LoadingOverlay } from "../../../../../components/loading";
import { useNavigate, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../../api";
import { useCustomMutation } from "@refinedev/core";
import dayjs from "@/helpers/dayjs";
import { useFormatDateLocalization, pageAccessEnum } from "@/helpers";

interface Permission {
  parentModule?: string | null;
  permission?: string | null;
  childModule?: string | null;
  code?: string | null;
  status: number;
}

export interface PermissionList {
  id: number;
  pid: number;
  level: number;
  name: string;
  code?: string;
  description: string;
  oldRoleId: null;
  userRole: null;
}

export type PermissionListsResponseBodyGet<
  BaseType extends PermissionList = PermissionList
> = BaseType & {
  children: BaseType[];
};

interface PageAccessByUserRoleResponseBodyGet {
  /**
   * 2025-03-18 02:42:13
   */
  createdDate: string;
  createdBy: number;
  /**
   * "2025-03-18 02:47:14"
   */
  modifiedDate: string;
  modifiedBy: number;
  id: number;
  pid: number;
  level: number;
  name: string;
  userRole: string;
  oldRoleId: null;
  description: string;
  status: number;
}

interface FormValues {
  userRole: string | null;
  permissions: Permission[] | any;
  oldPermissions?: any;
  description: string | null;
  status: number | null;
  modifiedDate: number[] | null;
  createdByName: string | null;
}

function CreateRoleJPM<
  PageAccessType extends PageAccessByUserRoleResponseBodyGet = PageAccessByUserRoleResponseBodyGet,
  PermissionListType extends PermissionListsResponseBodyGet = PermissionListsResponseBodyGet
>() {
  const { t } = useTranslation();
  const initialFormValues: FormValues = {
    userRole: null,
    permissions: [],
    oldPermissions: null,
    description: null,
    status: null,
    modifiedDate: null,
    createdByName: null,
  };
  const [formValues, setFormValues] = useState<FormValues>(initialFormValues);
  const [updateFormValues, setUpdateFormValues] = useState<FormValues | null>(
    null
  );
  const { formatDate } = useFormatDateLocalization();
  const [backupSavedRoles, setBackupSavedRoles] = useState<Permission[]>([]);
  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );

  const currDate = new Date();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const roleToEdit = searchParams.get("role");
  const roleId = searchParams.get("id");
  const [savedRoles, setSavedRoles] = useState<Permission[]>([]);
  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const dispatch: AppDispatch = useDispatch();

  const permissionsList = useSelector<RootState, PermissionListType[]>(
    (state) => state.permissionsListData.data
  );
  const isLoading = useSelector(
    (state: RootState) => state.permissionsListData.loading
  );
  const error = useSelector(
    (state: RootState) => state.permissionsListData.error
  );

  useEffect(() => {
    if (roleToEdit) {
      const fetchData = async () => {
        try {
          const response = await fetch(
            `${API_URL}/user/userRole/getByRole?role=${encodeURIComponent(
              roleToEdit
            )}`,
            {
              method: "GET",
              headers: {
                portal: localStorage.getItem("portal") || "",
                Authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
                "Content-Type": "application/json",
              },
            }
          );
          if (!response.ok) {
            throw new Error("Failed to fetch data");
          }
          const result = await response.json();
          const data = result?.data;
          if (data.role) {
            FetchCurrPermissions(data.role);
            const updateFormValues = {
              userRole: data.role?.trim(),
              oldPermissions: data?.pageManagementResponse,
              description: data?.description,
              status: data?.status,
              modifiedDate: data?.modifiedDate,
              createdByName: data?.createdByName,
            };
            // @ts-expect-error
            setUpdateFormValues((prev) => ({
              ...prev,
              ...updateFormValues,
            }));
            setFormValues((prev) => ({
              ...prev,
              ...updateFormValues,
            }));
          }
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      };
      fetchData();
    }
  }, [roleToEdit]);

  useEffect(() => {
    if (permissionsList?.length === 0 && !isLoading && !error) {
      dispatch(fetchPermissionsListData());
    }
  }, [permissionsList?.length]);

  const permissions = [
    { key: "CREATE", value: pageAccessEnum.Create, label: "createAdd" },
    { key: "VIEW", value: pageAccessEnum.Read, label: "read" },
    { key: "UPDATE", value: pageAccessEnum.Update, label: "update" },
    { key: "DELETE", value: pageAccessEnum.Delete, label: "delete" }
  ] as const;

  // Mapping function to convert old permission labels to new ones
  const mapOldToNewPermissionLabel = (oldLabel: string): string => {
    const labelMapping: { [key: string]: string } = {
      "KEL": "KEL-PI",
      "PU": "PU-MBS",
      "PP": "PP-SP",
      "MB-KA": "MB-KA",
      "MB-NB": "MB-NB",
      "MB-SA": "MB-SA",
      "TAKWIM-AKT": "TAKWIM-AKT"
    };
    return labelMapping[oldLabel] || oldLabel;
  };

  // Mapping function to convert string actions to numeric values
  const mapActionToNumeric = (action: string): string => {
    const actionMapping: { [key: string]: string } = {
      "CREATE": pageAccessEnum.Create.toString(),
      "VIEW": pageAccessEnum.Read.toString(),
      "UPDATE": pageAccessEnum.Update.toString(),
      "DELETE": pageAccessEnum.Delete.toString()
    };
    return actionMapping[action] || action;
  };



  const { mutateAsync: editRoles, isLoading: isLoadingEdit } =
    useCustomMutation();

  const EditRoles = async () => {
    const values = {
      id: roleId ?? null,
      userRole: formValues?.userRole?.trim(),
      permissions: formValues.permissions,
      userStatus: Number(formValues.status),
      description: formValues.description,
    };
    const isUpdate = values?.id !== null;
    await editRoles(
      {
        url: `${API_URL}/user/pageAccess/addPermission`,
        method: "post",
        values,
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "ERROR") {
            return {
              message:
                data?.data?.data === "Ralat. Kategori Pengguna ini telah wujud."
                  ? t("roleNameAlreadyInUse")
                  : t(data?.data?.data),
              type: "error",
            };
          } else {
            setErrors({});

            navigate(-1);
            const message = t(
              `jppmUserCategory${isUpdate ? "Updated" : "Created"}Successfully`,
              {
                name: values.userRole?.trim(),
              }
            );
            return {
              message,
              type: "success",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  function FetchCurrPermissions(role: string) {
    fetch(`${API_URL}/user/pageAccess/getByUserRole`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: JSON.stringify({
        userRole: [role],
      }),
    })
      .then<{ data: PageAccessType[][] }>((response) => {
        if (!response.ok) {
          throw new Error("Erorr when fetching ROApproval");
        }
        return response.json();
      })
      .then((data) => {
        if (data?.data) {
          const flatTester = data?.data.flat();
          const savedRoles = flatTester
            .map<Permission[]>((item) => {
              // The backend now returns code: "PU-MBS:1" instead of name
              const codeString = (item as any).code || item.name;
              const [childRoleCode, action] = codeString.split(":");

              // Check if action is already numeric (new format) or string (old format)
              const isNewFormat = /^\d+$/.test(action);

              let targetCode: string;
              let mappedAction: string;

              if (isNewFormat) {
                // New format: "PU-MBS:1" - childRoleCode is the code
                targetCode = childRoleCode;
                mappedAction = action;
              } else {
                // Old format: "PU:CREATE" - map to new format
                targetCode = mapOldToNewPermissionLabel(childRoleCode);
                mappedAction = mapActionToNumeric(action);
              }

              // Find the permission by code, then get the name
              let foundChild: any = null;
              let foundParent: any = null;

              // Search through all parents and their children to find the matching code
              for (const permission of permissionsList || []) {
                const childWithCode = permission.children?.find((child: any) => child.code === targetCode);
                if (childWithCode) {
                  foundChild = childWithCode;
                  foundParent = permission;
                  break;
                }
                // Also check if the parent itself has the code
                if (permission.code === targetCode) {
                  foundChild = permission;
                  foundParent = null; // This is a parent-level permission
                  break;
                }
              }

              if (!foundChild) {
                return [];
              }

              if (!foundParent) {
                // This is a parent-level permission
                const permissionList = foundChild;
                return permissionList.children?.map((childItem: any) => ({
                  parentModule: permissionList.name,
                  permission: mappedAction,
                  childModule: childItem.name,
                  code: childItem.code || null,
                  status: 1,
                })) || [];
              }

              // This is a child-level permission
              return [
                {
                  parentModule: foundParent.name,
                  permission: mappedAction,
                  childModule: foundChild.name,
                  code: foundChild.code || null,
                  status: item.status,
                },
              ];
            })
            .flat();
          setSavedRoles(savedRoles);
          setBackupSavedRoles(savedRoles);
          // @ts-expect-error
          setUpdateFormValues((prev) => ({
            ...prev,
            permissions: savedRoles,
          }));
          setFormValues((prev) => ({
            ...prev,
            permissions: savedRoles,
          }));
        }
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }

  const handleCheckboxChange = useCallback(
    (
      parentModule: string,
      action: number,
      moduleName: string,
      checked: boolean
    ) => {
      setSavedRoles((prev) => {
        // Find the code from the permissionsList
        const parent = permissionsList?.find((permission) => permission.name === parentModule);
        const childModule = parent?.children?.find((child) => child.name === moduleName);
        const code = childModule?.code || null;

        const newRole = {
          parentModule,
          permission: action.toString(),
          childModule: moduleName,
          code,
          status: 1,
        };

        if (checked) {
          // Check if the role already exists
          if (
            !prev.some(
              (item) =>
                item.parentModule === parentModule &&
                item.permission === action.toString() &&
                item.childModule === moduleName
            )
          ) {
            return [...prev, newRole];
          }
        } else {
          // Remove role if unchecked
          return prev.filter(
            (item) =>
              item.parentModule !== parentModule ||
              item.permission !== action.toString() ||
              item.childModule !== moduleName
          );
        }

        return prev;
      });
    },
    [permissionsList]
  );

  const getCheckboxValue = (name: string) => {
    const [parentModule, permission, role] = name.split(":");
    const selectedItem =
      savedRoles?.find(
        (item) =>
          item.parentModule === parentModule &&
          item.permission === permission &&
          item.childModule === role &&
          item.status === 1
      ) ?? null;
    return selectedItem !== null;
  };

  const renderCheckbox = (
    role: string,
    permission: (typeof permissions)[number],
    parentModule: string
  ) => (
    <Grid
      item
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
      xs={2}
      key={`${parentModule}:${permission.value}`}
    >
      <input
        type="checkbox"
        name={`${parentModule}:${permission.value}:${role}`}
        className="checkboxStyled"
        checked={getCheckboxValue(`${parentModule}:${permission.value}:${role}`)}
        onChange={(e) =>
          handleCheckboxChange(parentModule, permission.value, role, e.target.checked)
        }
      />
    </Grid>
  );

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setErrors({});
    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  }, []);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    const newErrors: { [key in keyof FormValues]?: string } = {};

    if (!formValues.userRole) {
      newErrors.userRole = t("fieldRequired");
    }
    if (!formValues.description) {
      newErrors.description = t("fieldRequired");
    }
    if (!formValues.status) {
      newErrors.status = t("fieldRequired");
    }
    if (!savedRoles || savedRoles?.length < 0) {
      newErrors.status = t("fieldRequired");
    }
    if (Object.keys(newErrors).length === 0) {
      setErrors({});
      EditRoles();
    }
    setErrors(newErrors);
  };
  const resetForm = () => {
    if (roleToEdit) {
      setFormValues({
        ...updateFormValues!,
        permissions: backupSavedRoles,
      });
      setSavedRoles(backupSavedRoles);
    } else {
      setFormValues(initialFormValues);
      setSavedRoles([]);
    }
  };

  useEffect(() => {
    if (savedRoles.length > 0) {
      setFormValues((prev) => ({
        ...prev,
        permissions: savedRoles,
      }));
    }
  }, [savedRoles]);

  const statusRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (errors.status && statusRef.current) {
      statusRef.current.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }, [errors.status]);

  return (
    <Box
      sx={{ display: "grid", gap: 2 }}
      component="form"
      onSubmit={handleSubmit}
    >
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className={"title"}>
            {t("addCategoryPenggunaJPPM")}
          </Typography>
          <Box
            sx={{
              mt: 3,
            }}
          >
            <Input
              required
              disabled={!!roleId}
              name="userRole"
              label={t("kategoriPengguna")}
              value={formValues?.userRole ? formValues.userRole : ""}
              onChange={handleChange}
              error={!!errors.userRole}
              helperText={errors.userRole}
            />
            <Input
              required
              name="description"
              label={t("descriptionCategory")}
              value={formValues?.description ? formValues.description : ""}
              onChange={handleChange}
              error={!!errors.description}
              helperText={errors.description}
            />
            <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ display: "flex", alignItems: "center" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("status")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8} ref={statusRef}>
                <RadioGroup
                  value={formValues.status}
                  onChange={handleChange}
                  name="status"
                  row
                >
                  <FormControlLabel
                    value={1}
                    className="label"
                    control={
                      <Radio />
                    }
                    label={t("active")}
                  />
                  <FormControlLabel
                    value={0}
                    className="label"
                    control={
                      <Radio />
                    }
                    label={t("inactive")}
                  />
                </RadioGroup>
                {errors.status ? (
                  <FormHelperText error>{errors.status}</FormHelperText>
                ) : null}
              </Grid>
            </Grid>
            <Input
              value={
                formValues?.createdByName
                  ? formValues?.createdByName
                  : roleId
                  ? ""
                  : userName
              }
              name="createdByName"
              label={t("recordedBy")}
              onChange={handleChange}
              disabled
              error={!!errors.createdByName}
              helperText={errors.createdByName}
            />
            <Input
              value={
                formValues?.modifiedDate
                  ? formatDate(
                      dayjs(formValues.modifiedDate as unknown as string, "YYYY-MM-DD hh:mm:ss"),
                      t("dateFormatWithPrefix", {
                        prefix: t("datePrefixOn"),
                        dateFormat: "dddd, MMM D YYYY, h:mm:ss A",
                      })
                    )
                  : roleId
                  ? ""
                  : formatDate(
                      currDate,
                      t("dateFormatWithPrefix", {
                        prefix: t("datePrefixOn"),
                        dateFormat: "dddd, MMM D YYYY, h:mm:ss A",
                      })
                    )
              }
              name="modifiedDate"
              type="text"
              label={t("lastUpdate")}
              onChange={handleChange}
              disabled
              error={!!errors.modifiedDate}
              helperText={errors.modifiedDate}
            />
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          p: 2,
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            borderRadius: "14px",
            display: "grid",
            gap: 1,
            mb: 2,
          }}
        >
          {isLoading || permissionsList?.length === 0 ? (
            <LoadingOverlay />
          ) : (
            permissionsList?.map((item: any, index: number) => (
              <Box
                sx={{
                  display: "grid",
                  gap: 1,
                  border: "1px solid var(--border-grey)",
                  py: 3,
                  px: 4,
                  borderRadius: "10px",
                }}
                key={index}
              >
                {index === 0 && (
                  <>
                    <Typography sx={{ mb: 5 }} className={"title"}>
                      {t("ketetapanKebenaranAkses")}
                    </Typography>
                    <Grid container sx={{ mb: 1 }}>
                      <Grid item xs={4}>
                        <Typography
                          sx={{ fontWeight: "500!important" }}
                          className="label"
                        >
                          {t("screenList")}
                        </Typography>
                      </Grid>
                      {permissions.map((permission) => (
                        <Grid item xs={2} sx={{ textAlign: "center" }} key={permission.key}>
                          <Typography
                            sx={{ fontWeight: "500!important" }}
                            className="label"
                          >
                            {t(permission.label)}
                          </Typography>
                        </Grid>
                      ))}
                    </Grid>
                  </>
                )}
                <Typography
                  fontSize={`14px !important`}
                  fontWeight={"400 !important"}
                  className="title"
                >
                  {item.name}
                </Typography>
                {item.children.map((role: any) => (
                  <Grid container alignItems="center" key={role.name}>
                    <Grid item xs={4} sx={{ textAlign: "left" }}>
                      <Box sx={{ ml: 4 }}>
                        <Typography className="label">{role.name}</Typography>
                      </Box>
                    </Grid>
                    {permissions.map((permission) =>
                      renderCheckbox(role.name, permission, item.name)
                    )}
                  </Grid>
                ))}
              </Box>
            ))
          )}
        </Box>
        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline variant="outlined" onClick={resetForm}>
            {t("previous")}
          </ButtonOutline>
          <ButtonPrimary
            type="submit"
            variant="contained"
            sx={{
              width: isMobile ? "100%" : "auto",
            }}
            disabled={isLoading || permissionsList?.length === 0 ? true : false}
          >
            {t("hantar")}
          </ButtonPrimary>
        </Grid>
      </Box>
    </Box>
  );
}

export default CreateRoleJPM;
