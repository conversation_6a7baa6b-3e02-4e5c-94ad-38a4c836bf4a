import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Pagination,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import { getLocalStorage } from "../../../../helpers/utils";
import CustomDataGrid from "../../../../components/datagrid";
import { API_URL } from "../../../../api";
import { useNavigate } from "react-router-dom";
import { useCustom } from "@refinedev/core";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import {
  ApplicationStatusEnum,
  MALAYSIA,
  PermissionNames,
  pageAccessEnum,
  SebabRyuanList,
  StatusPermohonan,
} from "../../../../helpers/enums";
import { Edit, HourglassBottomRounded } from "@mui/icons-material";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { FieldValues, useForm } from "react-hook-form";
import { EditIcon, EyeIcon } from "../../../../components/icons";
import DataTable from "@/components/datatable";
import { useQuery } from "@/helpers";
import Input from "@/components/input/Input";
import AuthHelper from "@/helpers/authHelper";

interface FormValues {
  userId?: number | null;
  negeri?: string | null;
  statusPertubuhan?: string | null;
  jenisPemegangJawatan?: string | null;
  carian?: string | null;
}

function RayuanTab() {
  const navigate = useNavigate();

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [appeals, setAppeals] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [isLoadingData, setIsloadingData] = useState<boolean>(true);
  const [pageSize, setPageSize] = useState(5);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [page, setPage] = useState(0);

  const [formData, setFormData] = useState({
    negeri: "",
    carian: "",
  });
  const handleInputCarian = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const hasKelulusanUpdatePermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Update
  );

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const customCell = {
    "text-align": "center" /* Center align content */,
    "background-color": "#f5f5f5" /* Example background color */,
    padding: "10px" /* Padding for better spacing */,
    border: "1px solid #ddd" /* Adding borders */,
  };

  const handleClearSearch = () => {
    setFormData({
      negeri: "",
      carian: "",
    });
  };

  const { refetch: fetchAppeal } = useQuery({
    url: `society/admin/appeal/findAllByParam`,
    filters: [
      { field: "pageNo", operator: "eq", value: page + 1 },
      { field: "pageSize", operator: "eq", value: rowsPerPage },
      { field: "searchQuery", operator: "eq", value: formData.carian },
      { field: "state", operator: "eq", value: formData.negeri },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const list = data?.data?.data;
      console.log(list);
      setTotal(list.total);
      setAppeals(list.data);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};

    let data = appeals;

    // if every parameter is empty
    if (formData.carian == "" && formData.negeri == "") {
    } else {
      fetchAppeal();
    }
    // setTotal(data.length);
    // setDisplaySenaraiAjk(data);

    // FetchUsers(false);
  };

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const negeri = addressList?.data?.data || [];
  const columns: GridColDef[] = [
    {
      field: "namaPemegangJawatan",
      headerName: t("pertubuhan"),
      // width:117,
      flex: 1.5,
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>{params.row.societyName ?? "-"}</div>
        </div>
      ),
    },
    {
      field: "noPengenalanDiri",
      headerName: t("organizationNumber"),
      flex: 1.5,
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>{params.row.societyNo ?? "-"}</div>
        </div>
      ),
    },
    {
      field: "jawatan",
      headerName: t("sebabRayuan"),
      flex: 1.5,
      cellClassName: "",
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>
            {SebabRyuanList.find((item) => item.value === params.row.idSebab)
              ?.label ?? "-"}
          </div>
        </div>
      ),
    },
    {
      field: "namaPertubuhan",
      headerName: t("statusPermohonan"),
      flex: 1.5,
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>
            {params.row.applicationStatusCode
              ? StatusPermohonan[params.row.applicationStatusCode] ?? "-"
              : "-"}
          </div>
        </div>
      ),

      // renderCell: (params: any) => params?.row?.constitution_type ?? "-",
    },
    {
      field: "statusPertubuhan",
      headerName: t("negeri"),
      flex: 1.5,
      align: "center",
      headerAlign: "center",
      renderHeader: (params) => (
        <div className="custom-header" style={{ textAlign: "center" }}>
          <div>{params.colDef.headerName}</div>
        </div>
      ),
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>{params.row.stateCode ?? "-"}</div>
        </div>
      ),

      // renderCell: (params: any) => params?.row?.roBertanggungjawab ?? "-",
    },

    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "right",
      renderCell: (params: any) => {
        return (
          <>
            <IconButton
              sx={{ color: "black" }}
              onClick={() =>
                navigate("pertubuhan/rayuan", {
                  state: {
                    appeal: params.row,
                  },
                })
              }
            >
              <EyeIcon />
            </IconButton>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    fetchAppeal();
  }, [page, rowsPerPage]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage - 1);
  };

  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
  };
  return (
    <>
      <Box component="form" onSubmit={handleSubmit}>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("senaraiPertubuhan")}
            </Typography>
            <Grid container spacing={2}>
              {/* state */}
              <Grid item xs={12} sm={12}>
                <FormControl
                  fullWidth
                  error={!!formErrors.subOrganizationCategory}
                >
                  <Input
                    value={formData.negeri}
                    size="small"
                    label={t("negeri")}
                    options={negeri
                      .filter((item: any) => item.pid === MALAYSIA)
                      .map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                    type="select"
                    onChange={(e) => {
                      setFormData((prevState) => ({
                        ...prevState,
                        negeri: e.target.value,
                      }));
                    }}
                    error={!!formErrors.negeri}
                    helperText={formErrors.negeri}
                  />
                </FormControl>
              </Grid>

              {/* finding/carian */}
              <Grid item xs={12} sm={12}>
                <FormControl
                  fullWidth
                  error={!!formErrors.subOrganizationCategory}
                >
                  <Input
                    value={formData.carian}
                    size="small"
                    label={t("Carian")}
                    error={!!formErrors.carian}
                    helperText={formErrors.carian}
                    onChange={(e) => {
                      setFormData((prevState) => ({
                        ...prevState,
                        carian: e.target.value,
                      }));
                    }}
                  />
                </FormControl>
              </Grid>
            </Grid>
            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  // flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    // width: isMobile ? "100%" : "auto",
                  }}
                  onClick={handleClearSearch}
                >
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary
                  type="submit"
                  variant="contained"
                  sx={{
                    // width: isMobile ? "100%" : "auto",
                    boxShadow: "none",
                  }}
                >
                  {t("search")}
                </ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/* ============= */}
        {appeals ? (
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                textAlign: "center",
                color: "#fff",
                borderRadius: "13px",
                backgroundColor: "var(--primary-color)",
                py: 2,
              }}
            >
              <Typography variant="h5" gutterBottom>
                {total}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
                {t("rekodDijumpai")}
              </Typography>
            </Box>

            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mt: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("")}
              </Typography>

              <DataTable
                columns={columns as any[]}
                rows={appeals} // Correctly slice data
                page={page + 1} // Page is 1-indexed for pagination
                rowsPerPage={rowsPerPage}
                totalCount={total}
                onPageChange={handlePageChange}
                onPageSizeChange={handleRowsPerPageChange}
                pagination
              />
            </Box>
          </Box>
        ) : null}
      </Box>
      <style>
        {`
      .custom-header {
        white-space: normal;  /* Allow wrapping text */
        overflow: hidden;
        /* text-overflow: ellipsis; */
        line-height: 19.5px;
        font-weight: 500;
      }
      .custom-cell {
        white-space: normal;  /* Allow wrapping text */
        overflow: hidden;
        /* text-overflow: ellipsis; */
        line-height: 19.5px;
      }
    `}
      </style>
    </>
  );
}

export default RayuanTab;
