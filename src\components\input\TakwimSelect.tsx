import React from 'react';
import { Grid, Typography, Select, MenuItem, SelectChangeEvent, SxProps, Theme } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

interface TakwimSelectProps {
  label: string;
  value?: string | number;
  onChange?: (event: SelectChangeEvent<any>) => void;
  options: Array<{
    value: string | number;
    label: string;
  }>| Array<any>;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  displayValue?: (value: string | number) => string;
  sx?: SxProps<Theme>; // Add sx prop for styling
}

export const TakwimSelect: React.FC<TakwimSelectProps> = ({
  label,
  value,
  onChange,
  options,
  placeholder = 'Sila pilih',
  required = false,
  disabled = false,
  error = false,
  helperText,
  displayValue,
  sx, // Destructure the sx prop
}) => {
  return (
    <Grid container spacing={2} alignItems="center" sx={sx}>
      {label && (
        <Grid item xs={12} sm={3}>
          <Typography
            variant="body1"
            sx={{
              color: '#666666',
              fontSize: '14px',
            }}
          >
            {label}
            {required && <span style={{ color: 'red' }}>*</span>}
          </Typography>
        </Grid>
      )}
      <Grid item xs={12} sm={label ? 9 : 12}>
        <Select
          fullWidth
          size="small"
          value={value || ''}
          onChange={onChange}
          displayEmpty
          disabled={disabled}
          error={error}
          IconComponent={(props) => (
            <KeyboardArrowDownIcon {...props} sx={{ color: '#6C6F93' }} />
          )}
          sx={{
            borderRadius: '5px',
            backgroundColor: 'white',
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: error ? 'red' : '#DADADA',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#DADADA',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#DADADA',
            },
            '& .MuiSelect-select': {
              fontSize: '14px',
              padding: '8.5px 14px',
            },
          }}
          renderValue={(selected) => {
            if (displayValue && selected !== '') {
              return displayValue(selected);
            }

            if (selected === '') {
              // return <em style={{ fontStyle: 'normal'}}>{placeholder}</em>;
              return <span style={{ fontStyle: 'normal'}}>{placeholder}</span>;

            }

            const option = options.find(opt => opt.value === selected);
            return option ? option.label : selected.toString();
          }}
        >
          {options.map((option) => (
            <MenuItem
              key={option.value}
              value={option.value}
              sx={{
                fontSize: '14px',
                fontWeight: option.value === 0 ? 'bold' : '',
                '&:hover': {
                  backgroundColor: '#f5f5f5',
                },
              }}
            >
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {helperText && (
          <Typography
            variant="caption"
            color={error ? 'error' : 'textSecondary'}
            sx={{ ml: 1, mt: 0.5 }}
          >
            {helperText}
          </Typography>
        )}
      </Grid>
    </Grid>
  );
};

export default TakwimSelect;



