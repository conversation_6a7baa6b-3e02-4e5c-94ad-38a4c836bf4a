export interface IEventAttendee {
  // id: number;
  fullName:string;
  email: string;
  phoneNumber: string;
  // identificationNo: string;
  societyId: string;
  applicationStatusCode: string;
  createdAt: string;
  updatedAt: string;
  present: boolean;
  attendanceNo: string;
  isFeedbackCompleted: boolean;
}

export interface IAttendeeRequest {
  eventNo: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  identificationNo: string;
  societyId: string;
}
export interface IAttendeeCancelRequest {
  eventNo: string;
  attendanceNo: string;
}

export interface AttendanceUpdateRequest {
  eventNo: string;
  identificationNo: string;
  present: boolean;

}

export interface AttendanceUpdateResponse{
  eventName: string;
  attendanceNo: string;
}

export interface AttendeesName extends AttendeesCancelledName {
  fullName: string;
  present: boolean;
  dateRegistered: string;
  timeRegistered: string;
  dateCheckedIn: string;
  timeCheckedIn: string;
}

export interface AttendeesCancelledName {
  fullName: string;
  present: boolean;
  societyNameList: string[];
  positionList: string[];
  dateCancelled: string;
  timeCancelled: string;
}
