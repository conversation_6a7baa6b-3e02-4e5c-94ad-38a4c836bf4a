import { useTranslation } from "react-i18next";
import { useCallback, useEffect, useMemo } from "react";
import { Control, useWatch, useFormContext } from "react-hook-form";
import { debounce as debounceFn, useDebounce } from "@/helpers";

import { Box, TextField, IconButton, Typography } from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import FormFieldRow from "@/components/form-field-row";
import Label from "@/components/label/Label";
import TextFieldController from "@/components/input/TextFieldController";

import { TrashIcon } from "@/components/icons";

interface MemberAttendancesProps {
  disabledState: boolean;
  isEditable: boolean;
  isViewOnly: boolean;
  meetingDetail?: boolean;
}

interface IMember {
  id?: number;
  tempId?: number;
  name: string;
  position: string;
  status?: number;
}

const MemberAttendances: React.FC<MemberAttendancesProps> = ({
  disabledState,
  isEditable,
  isViewOnly,
  meetingDetail,
}) => {
  const { t, i18n } = useTranslation();
  const { setValue, control } = useFormContext();
  const isMyLanguage = i18n.language === "my";

  const memberAttendances = useWatch({
    control,
    name: "memberAttendances",
    defaultValue: [],
  });
  const totalAttendees = useWatch({
    control,
    name: "totalAttendees",
    defaultValue: 0,
  });

  const debouncedTotalAttendees = useDebounce(totalAttendees, 500);
  const debouncedMemberAttendances = useDebounce(memberAttendances, 300);

  const filteredMemberAttendances = useMemo(
    () =>
      debouncedMemberAttendances?.filter((member: any) => member.status !== -1),
    [debouncedMemberAttendances]
  );

  const handleAddMember = useCallback(() => {
    const newMember = {
      name: "",
      position: "",
      tempId: memberAttendances.length + 1,
    };
    setValue("memberAttendances", [...memberAttendances, newMember]);
    setValue("totalAttendees", memberAttendances.length + 1);
  }, [memberAttendances, setValue]);

  const handleRemoveMember = (idx: number, id?: number) => {
    let updatedMembers = [...memberAttendances];
    const deletedList =
      updatedMembers.filter((member) => member.status === -1) ?? [];

    if (isEditable && id) {
      updatedMembers = updatedMembers.map((member) =>
        member.id === id ? { ...member, status: -1 } : member
      );
    } else {
      updatedMembers = updatedMembers.filter(
        (_, index) => index !== idx + deletedList.length
      );
    }

    const activeMembers = updatedMembers.filter((m) => m.status !== -1);
    setValue("memberAttendances", updatedMembers);
    setValue("totalAttendees", activeMembers.length);
  };

  const handleMemberChange = useCallback(
    debounceFn(
      (idx: number, id: number | null, key: keyof IMember, value: string) => {
        const deletedList =
          memberAttendances.filter((member: any) => member.status === -1) ?? [];

        const updatedList = memberAttendances.map(
          (member: IMember, index: number) => {
            if (id && member.id === id) {
              return { ...member, [key]: value };
            }

            if (!id && index === idx + deletedList.length) {
              return { ...member, [key]: value };
            }

            return member;
          }
        );

        setValue("memberAttendances", updatedList);
      },
      500
    ),
    [memberAttendances, setValue]
  );

  const updateMemberAttendances = useCallback(
    (newTotal: number) => {
      const currentTotal = memberAttendances.filter(
        (m: IMember) => m.status !== -1
      ).length;

      if (newTotal > currentTotal) {
        const newMembers = Array.from(
          { length: newTotal - currentTotal },
          (_, i) => ({
            name: "",
            position: "",
            tempId: memberAttendances.length + i + 1,
          })
        );
        setValue("memberAttendances", [...memberAttendances, ...newMembers]);
      } else if (newTotal < currentTotal) {
        const updatedMembers = memberAttendances
          .map((member: IMember, index: number) =>
            index >= newTotal && member.id ? { ...member, status: -1 } : member
          )
          .filter((_: any, index: number) => index < newTotal);

        setValue("memberAttendances", updatedMembers);
      }
    },
    [memberAttendances]
  );

  useEffect(() => {
    if (debouncedTotalAttendees && !isNaN(debouncedTotalAttendees)) {
      updateMemberAttendances(totalAttendees);
    }
  }, [debouncedTotalAttendees]);

  return (
    <Box
      sx={{
        borderRadius: "10px",
        padding: "41px 25px 25px",
        border: "0.5px solid #DADADA",
        marginBottom: "15px",
      }}
    >
      <Typography
        fontSize="14px"
        color="var(--primary-color)"
        fontWeight={500}
        marginBottom="20px"
      >
        {isMyLanguage
          ? "Kehadiran Ahli Mesyuarat"
          : "Attendance of Meeting Members"}
      </Typography>

      <FormFieldRow
        label={<Label text={t("jumlahKehadiranAhliMesyuarat")} />}
        value={
          <TextFieldController
            name="totalAttendees"
            control={control}
            type="number"
            disabled={disabledState}
            sx={{
              background: disabledState ? "#DADADA" : "FFF",
            }}
          />
        }
      />

      <FormFieldRow
        align="flex-start"
        label={<Label text={t("kehadiranAjk")} />}
        value={
          filteredMemberAttendances.length > 0 ? (
            filteredMemberAttendances.map((member: IMember, index: number) => (
              <Box
                key={`${member.id ?? member.tempId}-${index}`}
                sx={{
                  display: "flex",
                  gap: "10px",
                  position: "relative",
                  marginBottom: 1,
                  "&:last-of-type": { marginBottom: 0 },
                }}
              >
                <TextField
                  size="small"
                  placeholder="Nama"
                  defaultValue={member.name}
                  disabled={disabledState}
                  onChange={(e) =>
                    handleMemberChange(
                      index,
                      member.id ?? null,
                      "name",
                      e.target.value
                    )
                  }
                  sx={{ flex: 1.7 }}
                />
                <TextField
                  size="small"
                  placeholder="Jawatan"
                  defaultValue={member.position}
                  disabled={disabledState}
                  onChange={(e) =>
                    handleMemberChange(
                      index,
                      member.id ?? null,
                      "position",
                      e.target.value
                    )
                  }
                  sx={{ flex: 1.3 }}
                />
                {!isViewOnly && (!meetingDetail || isEditable) && (
                  <IconButton
                    sx={{
                      position: "absolute",
                      right: "-15px",
                      top: "50%",
                      transform: "translateY(-50%)",
                      padding: 0,
                    }}
                    onClick={() => handleRemoveMember(index, member.id)}
                  >
                    <TrashIcon color="red" />
                  </IconButton>
                )}
              </Box>
            ))
          ) : (
            <Typography
              sx={{
                fontSize: "14px",
                color: "#666666",
                fontWeight: "400 !important",
                "& span": {
                  color: "red",
                },
              }}
            >
              {isMyLanguage ? "Sila tambah AJK" : "Please add a committee."}
            </Typography>
          )
        }
      />

      <Box
        sx={{
          marginLeft: "auto",
          display: "flex",
          gap: "10px",
          width: "fit-content",
        }}
      >
        <ButtonPrimary
          onClick={handleAddMember}
          sx={{
            backgroundColor: "var(--primary-color)",
            width: "100px",
            height: "32px",
            color: "white",
            "&:hover": { backgroundColor: "#19ADAD" },
            textTransform: "none",
            fontWeight: 400,
            fontSize: "8px",
          }}
          disabled={disabledState}
        >
          {t("addAjk")}
        </ButtonPrimary>
      </Box>
    </Box>
  );
};

export default MemberAttendances;
