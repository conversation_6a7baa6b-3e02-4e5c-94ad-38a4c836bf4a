import React from "react";
import { Box, Typography, Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import BlockIcon from "@mui/icons-material/Block";
import { isExternalPortal, isInternalPortal, PORTAL_EXTERNAL, PORTAL_INTERNAL } from "@/helpers";

const ForbiddenPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleGoHome = () => {
    if (isExternalPortal()) {
      navigate("/pertubuhan");
    } else if (isInternalPortal()) {
      navigate("/internal-user");
    } else {
      navigate("/");
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "100vh",
        backgroundColor: "#f5f5f5",
        padding: 3,
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          backgroundColor: "white",
          borderRadius: 3,
          padding: 6,
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
          maxWidth: 500,
          width: "100%",
          textAlign: "center",
        }}
      >
        {/* Forbidden Icon */}
        <BlockIcon
          sx={{
            fontSize: 120,
            color: "#d32f2f",
            marginBottom: 3,
          }}
        />

        {/* Error Code */}
        <Typography
          variant="h1"
          sx={{
            fontSize: "4rem",
            fontWeight: "bold",
            color: "#d32f2f",
            marginBottom: 1,
            fontFamily: "Poppins, sans-serif",
          }}
        >
          403
        </Typography>

        {/* Error Title */}
        <Typography
          variant="h4"
          sx={{
            fontSize: "1.5rem",
            fontWeight: "600",
            color: "#333",
            marginBottom: 2,
            fontFamily: "Poppins, sans-serif",
          }}
        >
          {t("forbidden") || "Access Forbidden"}
        </Typography>

        {/* Error Message */}
        <Typography
          variant="body1"
          sx={{
            fontSize: "1rem",
            color: "#666",
            marginBottom: 4,
            lineHeight: 1.6,
            fontFamily: "Poppins, sans-serif",
          }}
        >
          {t("forbiddenMessage") || 
            "You don't have permission to access this resource. Please contact your administrator if you believe this is an error."}
        </Typography>

        {/* Action Buttons */}
        <Box
          sx={{
            display: "flex",
            gap: 2,
            flexDirection: { xs: "column", sm: "row" },
            width: "100%",
          }}
        > 
          <Button
            variant="contained"
            onClick={handleGoHome}
            sx={{
              flex: 1,
              borderRadius: 2,
              padding: "12px 24px",
              fontFamily: "Poppins, sans-serif",
              textTransform: "none",
              backgroundColor: "var(--primary-color)",
              "&:hover": {
                backgroundColor: "#147C7C",
              },
            }}
          >
            {t("goHome") || "Go Home"}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default ForbiddenPage;
