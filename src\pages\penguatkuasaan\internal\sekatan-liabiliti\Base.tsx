import { useTranslation } from "react-i18next";
import { useMemo, useState } from "react";
import { GridColDef } from "@mui/x-data-grid";
import { useNavigate } from "react-router-dom";

import { Box, IconButton, Typography, useTheme } from "@mui/material"
import { ButtonSmallWithIcon } from "@/components";
import { AddIcon, EditIcon, EyeIcon } from "@/components/icons";
import { SearchGrey } from "@/components/input/SearchGrey";
import FilterBar, { type FilterOption } from "@/components/filter";
import { DataGridUI } from "@/components/datagrid/UI";

import { getLocalStorage, liabilityRestrictionSectionOptions, toBase64, useDataGrid } from "@/helpers";

export interface PenguatkuasaanSekatanLiabilitiResponseBodyGet {
  blacklistUserId: number
  branchId: null
  branchNo: null
  effectiveDate: string
  id: number
  identificationNo: string
  isCompleted: boolean
  name: string
  notes: string
  noticeReferenceNo: string
  removalStatus: boolean
  section: string
  societyCancellationId: number
  societyId: number
  societyNo: string
  whitelistDate: string

  offenseSection?: string | null
}

export const PenguatkuasaanInternalSekatanLiabiliti = <
  Data extends PenguatkuasaanSekatanLiabilitiResponseBodyGet = PenguatkuasaanSekatanLiabilitiResponseBodyGet
>() => {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [filterValues, setFilterValues] = useState<Record<string, null | string | number>>({
    searchQuery: null,
    societyStateCode: null,
    section: null,
    isWhitelisted: null,
  })
  const { societyStateCode, section, searchQuery, isWhitelisted } = filterValues
  const {
    dataGridProps: { sx, ...dataGridProps },
  } = useDataGrid<Data>({
    resource: "society/blacklist/getAll",
    meta: {
      params: {
        ...(searchQuery && { searchQuery }),
        ...(societyStateCode && { societyStateCode }),
        ...(section && { section }),
        ...(typeof isWhitelisted === "boolean" && { isWhitelisted })
      },
    },
  });

  const primary = theme.palette.primary.main;
  const filterOptions = useMemo(() => (
    {
      negeri: getLocalStorage("address_list", [])
        ?.filter((item: any) => item.level === 1)
        ?.map((item: any) => (
          {
            label: item.name,
            value: item.id
          }
        )) ?? [],
      section: liabilityRestrictionSectionOptions
        .map((item) => ({
          label: item.description,
          value: item.code
        })),
      status: [
        {
          label: t("whitelisted"),
          value: true
        },
        {
          label: t("notWhitelisted"),
          value: false
        }
      ] as unknown as FilterOption[],
    }
  ), []);
  const initialSelectedFilters = {
    negeri: "negeri",
    section: "section",
    status: "status"
  }
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >(() => initialSelectedFilters);
  const columns: GridColDef<Data>[] = [
    {
      field: "name",
      headerName: t("name"),
      headerAlign: "center",
      minWidth: 51.6 * 3,
    },
    {
      field: "societyNo",
      headerName: t("ppmNumber"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => row?.societyNo ?? "-",
      minWidth: 51.6 * 4
    },
    {
      field: "effectiveDate",
      headerName: "Tarikh Sekatan",
      renderCell: ({ row }) => row?.effectiveDate ?? "-",
      align: "center",
      minWidth: 51.6 * 3
    },
    {
      field: "section",
      headerName: t("section"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => {
        const initialValue = row?.section.split("_")
        const [_section, ...values] = initialValue
        return values.reduce((acc, val, index) => (`${acc}${index === 0 ? val : `(${val.toLowerCase()})`}`), "") ?? "-"
      }
    },
    {
      field: "removalStatus",
      headerName: t("status"),
      headerAlign: "center",
      align: "center",
      minWidth: 51.6 * 3,
      renderCell: ({ row }) => {
        const removalStatus = row?.removalStatus ?? false
        return (
          <Typography
            className="status-pertubuhan-text"
            sx={{
              backgroundColor: "#fff",
              border: `2px solid var(--${
                removalStatus ? "success" : "error"
              })`,
              textAlign: "center"
            }}
          >
            {t(`${removalStatus ? "w" : "notW"}hitelisted`)}
          </Typography>
        )
      }
    },
    {
      field: "whitelistDate",
      headerName: "Tarikh Pemutihan",
      align: "center",
      renderCell: ({ row }) => row?.whitelistDate ?? "-",
      minWidth: 51.6 * 3
    },
    {
      field: "actions",
      headerName: t("action"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => {
        const getURL = (mode: "UPDATE" | "VIEW" = "VIEW") => `${mode === "UPDATE" ? "update/" : ""}${toBase64(row.id.toString())}`
        return (
          <div style={{
            display: "flex"
          }}>
            <IconButton
              color="primary"
              onClick={() => navigate(getURL("UPDATE"))}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              onClick={() => navigate(getURL())}
            >
              <EyeIcon />
            </IconButton>
          </div>
        )
      },
    },
  ];

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };
  const handleCreateLiabilityRestrictionButton = () =>
    navigate("create")
  const getParamsName = (filter: string) => {
    switch (filter) {
      case "negeri":
        return "societyStateCode";
      case "status":
        return "isWhitelisted";
      default:
        return filter;
    }
  }
  const onFilterChange = (filter: string, value: number | string) => {
    const paramsName = getParamsName(filter);
    setFilterValues((prev) => ({
      ...prev,
      [paramsName]: value
    }))
  };

  return (
    <Box
      sx={{
        marginTop: "0.5rem",
        backgroundColor: "white",
        borderRadius: "1rem",
        padding: 2,
        boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)"
      }}
    >
      <Box
        sx={{
          border: "0.5px solid #dadada",
          borderRadius: "10px",
          padding: "1.5rem"
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "1.5rem"
          }}
        >
          <Typography
            color={primary}
            sx={{
              fontSize: 14,
              fontWeight: "medium",
            }}
          >
            {t("listOfLiabilityRestriction")}
          </Typography>
          <ButtonSmallWithIcon
            icon={<AddIcon color="#ffffff" />}
            onClick={handleCreateLiabilityRestrictionButton}
          >
            {t("liabilityRestrictionCreate")}
          </ButtonSmallWithIcon>
        </div>
        <SearchGrey placeholder={t("carian")} onSearchKeyDown={(val) => onFilterChange("searchQuery", val)} />
        <FilterBar
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          selectedFilters={selectedFilters}
          onSelectedFiltersChange={handleSelectedFiltersChange}
        />
        <DataGridUI
          {...dataGridProps}
          autoHeight
          columns={columns}
          noResultMessage={t("noData")}
        />
      </Box>
    </Box>
  )
}
