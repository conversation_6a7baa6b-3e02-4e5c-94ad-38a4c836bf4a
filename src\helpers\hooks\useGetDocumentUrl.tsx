import { CrudFilter } from "@refinedev/core";
import useQuery from "./useQuery";
import { IApiResponse, IDocumentList } from "@/types";


interface UseGetDocumentProps {
  onSuccess?: (data: IDocumentList | null) => void;
  onSuccessAll?: (documents: IDocumentList[]) => void;
}

export const useGetDocument = ({
  onSuccess,
  onSuccessAll,
}: UseGetDocumentProps) => {
  const { refetch, isLoading } = useQuery<IApiResponse<IDocumentList[]>>({
    url: "society/document/documentByParam",
    autoFetch: false,
    onSuccess: (data) => {
      const documents = data?.data?.data;

      if (!Array.isArray(documents)) {
        console.warn("Invalid response: expected an array.");
        onSuccess?.(null);
        onSuccessAll?.([]);
        return;
      }

      if (documents.length === 0) {
        console.warn("No documents available.");
        onSuccess?.(null);
        onSuccessAll?.([]);
        return;
      }

      const firstDocument = documents[0];

      onSuccess?.(firstDocument);
      onSuccessAll?.(documents);
    },
  });

  const getDocument = (filters: CrudFilter[]) => {
    refetch({ filters });
  };

  return { getDocument, isLoading };
};
