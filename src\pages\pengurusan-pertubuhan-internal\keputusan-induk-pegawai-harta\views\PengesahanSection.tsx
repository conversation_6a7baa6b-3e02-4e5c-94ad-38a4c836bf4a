import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Typography, Box } from "@mui/material";
import FormFieldRow from "../../../../components/form-field-row";
import Label from "../../../../components/label/Label";
import DisabledTextField from "../../../../components/input/DisabledTextField";
import { formatDate } from "@/helpers";

type PengesahanSectionProps = {
  propertyOfficer: any;
  societyDataById: any;
};
const PengesahanSection: React.FC<PengesahanSectionProps> = ({
  propertyOfficer,
  societyDataById
}) => {
  const { t } = useTranslation();
  const { id } = useParams();
  console.log(propertyOfficer)
  return (
    <Box
      sx={{
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("propertyOfficerConfirmation")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("namaPertubuhan")} />}
          value={<DisabledTextField value={societyDataById?.societyName} />}
        />

        <FormFieldRow
          label={<Label text={t("organizationNumber2")} />}
          value={<DisabledTextField value={societyDataById?.societyNo} />}
        />
      </Box>


      {propertyOfficer?.map((item: any, index: any) => (
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #DADADA",
            borderRadius: "10px",
            marginBottom: 1,
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("officerName")} {index + 1}
          </Typography>

          <FormFieldRow
            label={<Label text={t("officerName")} />}
            value={<DisabledTextField value={item.name} />}
          />

          <FormFieldRow
            label={<Label text={t("noKadPengenalan")} />}
            value={<DisabledTextField value={item.identificationNo} />}
          />
        </Box>)) ?? null}





      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("tarikhBayar")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("tarikhBayar")} />}
          value={<DisabledTextField value={formatDate(propertyOfficer?.[0].paymentDate, "DD-MM-YYYY", { parseFormat: "HH:mm DD:MM:YYYY" })} />}
        />
      </Box>
    </Box>
  );
};

export default PengesahanSection;
