import { useQuery } from './useQuery';
import { BaseRecord, HttpError } from "@refinedev/core";

export function useApi<
  TData extends BaseRecord = BaseRecord,
  TError extends HttpError = HttpError
>(url: string | null, config?: Record<string, any>) {
  // Remove any leading slash to prevent double slashes
  const cleanUrl = url?.startsWith('/') ? url.substring(1) : url;

  const { data: responseData, isLoading, error, refetch } = useQuery<TData, TError>({
    url: cleanUrl || '',
    autoFetch: !!url,
    ...config
  });

  return {
    data: responseData,
    isLoading,
    error,
    refetch
  };
}

