import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Typography, Box, Grid, useMediaQuery, Theme } from "@mui/material";
import useMutation from "../../../../../helpers/hooks/useMutation";
import DisabledTextField from "../../../../../components/input/DisabledTextField";
import { ButtonPrimary } from "../../../../../components/button";
import SelectFieldController from "../../../../../components/input/select/SelectFieldController";
import TextFieldController from "../../../../../components/input/TextFieldController";
// import TextArea from "@/components/input/TextArea";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { getLocalStorage, MALAYSIA, ROApprovalType, useQuery } from "@/helpers";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

const MaklumatAmSection = ({
  info,
  societyInfo,
}: {
  info: any;
  societyInfo: { societyName: string; societyNo: string };
}) => {
  const { t } = useTranslation();
  const { id, branchAmendmentId } = useParams();
  const [roList, setRoList] = useState([]);
  const societyId = atob(id || "");
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const { fetch: updateRo } = useMutation({
    url: "society/liquidate/approval/ro",
    method: "put",
  });

  const addressList = getLocalStorage("address_list", null);

  const getStateName = (stateCode: any) => {
    const stateName = addressList.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };

  const { mutate: updateApproval, isLoading: isUpdatingStatus } =
    useCustomMutation();

  const { control, handleSubmit, setValue, getValues } = useForm<FieldValues>({
    defaultValues: {
      id: branchAmendmentId,
      ro: "",
      note: "",
      branchAmendmentId: "",
    },
  });

  // const { data: roListData, isLoading: isRoListLoading } = useCustom({
  //   url: `${API_URL}/society/user/getRoList`,
  //   method: "get",
  //   config: {
  //     headers: {
  //       portal: localStorage.getItem("portal"),
  //       authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
  //     },
  //     query: {
  //       branchId: info.branchId,
  //     },
  //   },
  //   queryOptions: {
  //     onSuccess(data) {
  //       const roListData = data?.data?.data || [];
  //       const options = roListData.map((data: any) => ({
  //         label: data?.name,
  //         value: data?.id,
  //       }));
  //       setRoList(options);
  //     },
  //   },
  // });

  const onSubmit = () => {
    const payload = getValues();
    const data = {
      roId: payload.roId,
      noteRo: payload.note,
      roApprovalType: "BRANCH_AMENDMENT",
      branchAmendmentId: payload.id,
    };

    // console.log(data)

    return
    updateApproval({
      url: `${API_URL}/society/roDecision/updateRo`,
      method: "patch",
      values: data,
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      successNotification: () => {
        return {
          message: t("messageKeputusanPermohonanSuccess"),
          type: "success",
        };
      },
      errorNotification: () => {
        return {
          message: t("messageKeputusanPermohonanError"),
          type: "error",
        };
      },
    });
  };

  return (
    <Box
      sx={{
        pl: 2,
        p: 3,
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #DADADA",
            borderRadius: "10px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("organizationInfo")}
          </Typography>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("organizationName")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={societyInfo?.societyName ?? "-"} />
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("orgNumber")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={societyInfo?.societyNo ?? "-"} />
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("branchNameDetails")}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={info?.originalBranchName ?? "-"} />
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t(`branchNumber`)}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={info?.branchNo ?? "-"} />
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("branchState")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField
                value={
                  info?.stateCode ? getStateName(info?.originalStateCode) : "-"
                }
              />
            </Grid>
          </Grid>
        </Box>

        {/* <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("ROAction")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("responsibleRO")}
                <span style={{ marginLeft: "0.5rem", color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <SelectFieldController
                name="roId"
                control={control}
                options={roList}
                // required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("remarks")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldController
                control={control}
                name="note"
                multiline
                defaultValue={getValues("note")}
                sx={{
                  minHeight: "92px",
                }}
                sxInput={{
                  minHeight: "92px",
                }}
              />
            </Grid>
          </Grid>
        </Box>

        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonPrimary
            type="submit"
            sx={{
              display: "block",
              backgroundColor: "var(--primary-color)",
              width: "100px",
              minWidth: "unset",
              height: "32px",
              color: "white",
              "&:hover": { backgroundColor: "#19ADAD" },
              textTransform: "none",
              fontWeight: 400,
              fontSize: "8px",
            }}
          >
            {t("update")}
          </ButtonPrimary>
        </Grid> */}
      </form>
    </Box>
  );
};

export default MaklumatAmSection;
