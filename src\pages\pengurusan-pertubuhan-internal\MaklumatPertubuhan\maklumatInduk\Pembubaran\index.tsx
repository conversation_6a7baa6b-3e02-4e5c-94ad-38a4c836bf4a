import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { FieldValues, useForm } from "react-hook-form";
import { useParams } from "react-router-dom";

import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import { DecisionOptionsCode } from "../../../../../helpers/enums";
import { ButtonPrimary } from "../../../../../components/button";
import { filterEmptyValuesOnObject } from "../../../../../helpers/utils";
import useMutation from "../../../../../helpers/hooks/useMutation";
import DisabledTextField from "../../../../../components/input/DisabledTextField";
import { DialogConfirmation } from "@/components";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

function Pembubaran() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const decisionOptions = DecisionOptionsCode(t);

  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const { fetch: createApproval, isLoading } = useMutation({
    url: "society/liquidate/approval/create",
    onSuccess: () => setIsSuccess(true),
  });

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        liquidationId: id,
        societyId: "",
        decisionCode: "",
        rejectReason: "",
        note: "",
      },
    });

  const { decisionCode } = watch();

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});

  const onSubmit = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const payload = getValues();
    const filterPayload = filterEmptyValuesOnObject(payload);

    createApproval(filterPayload);
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              Society Name
              <br />
              Society No
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            backgroundColor: "white",
            padding: "18px 16px",
            borderRadius: "14px",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="16px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("maklumatPermohonanPembubaranPertubuhan")}
            </Typography>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationName")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationNumber")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("submissionDate")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("decisionDate")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("diluluskanOleh")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("keputusan")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value="" />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("remarks")} ({t("jikaAda")})
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField multiline row={3} value="" />
              </Grid>
            </Grid>
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              sx={{
                display: "block",
                backgroundColor: "var(--primary-color)",
                width: "100px",
                minWidth: "unset",
                height: "32px",
                color: "white",
                "&:hover": { backgroundColor: "#19ADAD" },
                textTransform: "none",
                fontWeight: 400,
                fontSize: "8px",
              }}
              onClick={() => navigate(-1)}
            >
              {t("back")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isLoading}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        decisionLabel={decisionCode}
      />
    </>
  );
}

export default Pembubaran;
