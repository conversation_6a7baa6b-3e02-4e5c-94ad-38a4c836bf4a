import Navbar from "../../../components/navbar";
import Footer from "../../landing-page/mainLanding/footer";
import Box from "@mui/material/Box/Box";
import { useOutlet } from "react-router-dom";

const CalendarPublicPage = () => {
  const outlet = useOutlet();

  return (
    <div style={{ height: "100dvh", overflow: "auto" }}>
      <Navbar />
      {outlet}
      <Box
        sx={{
          backgroundColor: "var(--primary-color)",
          padding: "120px 60px 60px 60px",
          // mt: "60px",
          "@media (max-width: 800px)": {
            padding: "60px 30px 0px 30px",
            // mt: "60px",
          },
          "@media (max-width: 450px)": {
            padding: "60px 15px 0px 15px",
            // mt: "60px",
          },
        }}
      >
        <Footer />
      </Box>
    </div>
  );
};

export default CalendarPublicPage;
