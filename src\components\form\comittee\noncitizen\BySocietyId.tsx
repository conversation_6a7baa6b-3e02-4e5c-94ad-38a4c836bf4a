import { Formik } from "formik"
import { CommitteeNonCitizenBySocietyRequestBody, FormCommitteeNonCitizenBySocietyIdInner } from "./BySocietyIdInner"
import { useFormManagementCommitteeNonCitizenSocietyByIdHandleSubmit, useFormManagementCommitteeNonCitizenSocietyByIdInitialValue, useFormManagementCommitteeNonCitizenSocietyByIdValidationSchema } from "@/controllers"
import { useEffect } from "react"
import { useFormCommitteeNonCitizenBySocietyIdContext } from "@/pages/pertubuhan/ajk/jawatankuasa/createAJKBukanWn"

export const FormCommitteeNonCitizenBySocietyId = <
  FormData extends CommitteeNonCitizenBySocietyRequestBody = CommitteeNonCitizenBySocietyRequestBody
>() => {
  const { getInitialValue } = useFormManagementCommitteeNonCitizenSocietyByIdInitialValue<FormData>()
  const { handleSubmit } = useFormManagementCommitteeNonCitizenSocietyByIdHandleSubmit<FormData>()
  const { getValidationSchema } = useFormManagementCommitteeNonCitizenSocietyByIdValidationSchema()
  const { setSocietyNonCitizenCommitteeId } = useFormCommitteeNonCitizenBySocietyIdContext();

  const initialValue = getInitialValue();

  useEffect(() => {
    if (typeof initialValue?.id?.toString() === "string") {
      setSocietyNonCitizenCommitteeId(initialValue.id.toString());
    }
  }, [initialValue?.id])

  return (
    <Formik
      initialValues={initialValue}
      onSubmit={handleSubmit}
      isInitialValid={false}
      validationSchema={getValidationSchema()}
      enableReinitialize
    >
      {() => <FormCommitteeNonCitizenBySocietyIdInner {...{ initialValue }} />}
    </Formik>
  )
}
