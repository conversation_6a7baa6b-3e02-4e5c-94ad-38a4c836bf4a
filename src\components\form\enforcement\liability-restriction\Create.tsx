import { useTranslation } from "react-i18next";
import { useBack } from "@refinedev/core";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

import { Box, Button, FormControlLabel, FormHelperText, Grid, MenuItem, Radio, RadioGroup, Typography, useTheme } from "@mui/material"
import { TextFieldControllerFormik } from "@/components/input";
import { Field, FieldProps, Form, useFormikContext } from "formik";
import { DatePickerFormik } from "@/components/input/DatePickerFormik";
import { DialogConfirmation } from "@/components/dialog";

import { type PenguatkuasaanSekatanLiabilitiResponseBodyGet } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Base";
import { liabilityRestrictionSectionOptions, SocietyCancellationSectionEnums } from "@/helpers";
import { EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create002";

export interface FormEnforcementLiabilityRestrictionCreateProps<
  SocietyLists extends EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo = EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo
> {
  /**
   * @default false
   */
  viewOnly?: boolean

  /**
   * @default CREATE
   */
  mode?: "UPDATE" | "VIEW" | "CREATE"

  /**
   * @default []
   */
  societyRegisteredlists?: SocietyLists[]
}

export const FormEnforcementLiabilityRestrictionCreate = <
  SocietyLists extends EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo = EnforcementLiabilityRestrictionSocietyRegisteredResponseBodyGetByIdentificationNo,
  PropType extends FormEnforcementLiabilityRestrictionCreateProps<SocietyLists> = FormEnforcementLiabilityRestrictionCreateProps<SocietyLists>,
  RequestBody extends PenguatkuasaanSekatanLiabilitiResponseBodyGet = PenguatkuasaanSekatanLiabilitiResponseBodyGet
>({
  viewOnly = false,
  mode = "CREATE",
  societyRegisteredlists = []
}: PropType) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isSubmitting, isValid, values, setFieldValue, submitForm: handleSubmit } = useFormikContext<RequestBody>();
  const back = useBack();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionSuccess, setActionSuccess] = useState(false);
  const navigate = useNavigate();

  const primary = theme.palette.primary.main;
  const error = theme.palette.error.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const conditionalInputProps = viewOnly && { helperTextFallbackValue: null };
  const societyRegisteredlistDropdowns = societyRegisteredlists.map((item) => ({
    label: `${item?.societyNo ?? item.applicationNo} - ${item.societyName}`,
    value: item?.societyNo ?? item?.applicationNo ?? null
  }))
  const selectedSectionLists = liabilityRestrictionSectionOptions
    .filter((item) => mode === "CREATE"
      ? item.section === values.offenseSection
      : item.code === values.section
    )

  const submitForm = async () => {
    try {
      await handleSubmit();
      setActionSuccess(true);
      setTimeout(() => {
        navigate("../..", { relative: "path" });
      }, 1000);
    } catch {
      setActionSuccess(false)
    }
  };
  const handleClose = () => {
    setDialogOpen(false);
    setActionSuccess(false);
  };

  return (
    <>
      <Form>
        <Box
          sx={{
            border: "0.5px solid #dadada",
            borderRadius: "10px",
            padding: "1.5rem"
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "1.5rem"
            }}
          >
            <Typography
              color={primary}
              sx={{
                fontSize: 14,
                fontWeight: "medium",
              }}
            >
              {t("liabilityRestrictionInformation")}
            </Typography>
          </div>
          <Grid container spacing={2}>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("name")}
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="name"
                placeholder="Nama Pemegang Jawatan"
                disabled
              />
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("myKadNo")}
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="identificationNo"
                placeholder="XXXXXX-XX-XXXX"
                disabled
              />
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("organizationName")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="societyNo"
                select
                helperTextComponentPlacement="INSIDE"
                disabled={viewOnly}
                onValueChange={(societyNo) => {
                  const selectedSociety = societyRegisteredlists.find((item) => item.societyNo === societyNo) ?? null
                  setFieldValue("societyId", selectedSociety?.id)
                }}
                {...conditionalInputProps}>
                {societyRegisteredlistDropdowns.map((item, index) => (
                  <MenuItem key={`society-option-list-${index}`} value={item.value ?? ""}>
                    {item.label}
                  </MenuItem>
                ))}
              </TextFieldControllerFormik>
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("letterReferenceNumber")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="noticeReferenceNo"
                helperTextComponentPlacement="INSIDE"
                disabled={viewOnly}
                {...conditionalInputProps}
              />
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("offenseSection")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <Field name="offenseSection">
                {({ field, meta }: FieldProps) => {
                  return (
                    <>
                      <RadioGroup row {...field}>
                        {SocietyCancellationSectionEnums.map((item, index) => (
                          <FormControlLabel
                            key={`society-cancellation-${index}`}
                            disabled={viewOnly}
                            value={item.code}
                            control={
                              <Radio />
                            }
                            label={`${t("section")} ${item.code}`}
                          />
                        ))}
                      </RadioGroup>
                      {!viewOnly && (
                        <FormHelperText
                          error={!!meta?.error}
                        >
                          {meta?.error ?? " "}
                        </FormHelperText>
                      )}
                    </>
                  )
                }}
              </Field>
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("sectionDetails")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="section"
                select
                helperTextComponentPlacement="INSIDE"
                disabled={viewOnly || !values.offenseSection}
                {...conditionalInputProps}
              >
                {selectedSectionLists
                  .map((item, index) => (
                    <MenuItem key={`section-option-list-${index}`} value={item?.code ?? ""}>
                      {t(item.description)}
                    </MenuItem>
                  ))}
              </TextFieldControllerFormik>
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("dateOfCommencementOfTheOffence")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <DatePickerFormik
                name="effectiveDate"
                disableFuture
                disabled={viewOnly}
                {...conditionalInputProps}
              />
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("remarks")}
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="notes"
                multiline
                rows={4}
                helperTextComponentPlacement="INSIDE"
                disabled={viewOnly}
                {...conditionalInputProps}
              />
            </Grid>
          </Grid>
        </Box>
        {["CREATE", "VIEW"].includes(mode) && (
          <div style={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "flex-end",
            marginTop: "1rem",
            columnGap: "0.5rem"
          }}>
            {mode === "VIEW" && (
              <Button
                type="button"
                sx={{
                  textTransform: "capitalize",
                  minWidth: "7.5rem"
                }}
                variant="outlined"
                onClick={back}
              >
                {t("back")}
              </Button>
            )}
            {!viewOnly && (
              <Button
                type="button"
                sx={{
                  textTransform: "capitalize",
                  minWidth: "7.5rem"
                }}
                variant="contained"
                disabled={!isValid || isSubmitting}
                onClick={() => setDialogOpen(true)}
              >
                {t("simpan")}
              </Button>
            )}
          </div>
        )}
      </Form>
      {!viewOnly && values && (
        <DialogConfirmation
          isMutating={isSubmitting}
          open={dialogOpen}
          onClose={handleClose}
          onAction={submitForm}
          onConfirmationText={t("areYouSureContinueLiabilityRestriction", {
            name: `<b>${values.name}?</b>`
          })}
          isSuccess={actionSuccess}
          onSuccessText={t("continueLiabilityRestrictionSuccess", {
            name: values.name
          })}
        />
      )}
    </>
  )
}
