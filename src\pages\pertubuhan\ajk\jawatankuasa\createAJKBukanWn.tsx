import { createContext, Dispatch, FC, SetStateAction, useContext, useEffect, useState } from "react";
import { Box } from "@mui/material";
import { usejawatankuasaContext } from "./jawatankuasaProvider";
import { FormCommitteeNonCitizenBySocietyId } from "@/components/form/comittee/noncitizen/BySocietyId";
import { useNavigate } from "react-router-dom";

export interface FormCommitteeNonCitizenBySocietyIdContextType {
  societyNonCitizenCommitteeId: string | null;
  setSocietyNonCitizenCommitteeId: Dispatch<SetStateAction<string | null>>;
  isUploadingFilesAfterCreateAJK: boolean;
  setIsUploadingFilesAfterCreateAJK: Dispatch<SetStateAction<boolean>>;
  redirectToAJKLists: () => void;
}

export const FormCommitteeNonCitizenBySocietyIdContext = createContext<FormCommitteeNonCitizenBySocietyIdContextType>(null!);

export function useFormCommitteeNonCitizenBySocietyIdContext() {
  const context = useContext(FormCommitteeNonCitizenBySocietyIdContext);
  if (!context) {
    throw new Error("useFormCommitteeNonCitizenBySocietyIdContext must be used within a FormCommitteeNonCitizenBySocietyIdContext.Provider");
  }
  return context;
}


export const CreateAjkBukanWn: FC = () => {
  const navigate = useNavigate();
  const [shouldFetch, setShouldFetch] = useState(true);
  const { fetchAddressList, fetchSociety } = usejawatankuasaContext();

  const [societyNonCitizenCommitteeId, setSocietyNonCitizenCommitteeId] =
    useState<string | null>(null);
  const [isUploadingFilesAfterCreateAJK, setIsUploadingFilesAfterCreateAJK] = useState(false);

  const redirectToAJKLists = () => {
    setTimeout(() => {
      navigate("../update-ajk", { relative: "path" });
    }, 2000);
  }

  useEffect(() => {
    if (shouldFetch) {
      fetchAddressList();
      fetchSociety();
      setShouldFetch(false);
    }
  }, [shouldFetch]);

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <FormCommitteeNonCitizenBySocietyIdContext.Provider
          value={{
            societyNonCitizenCommitteeId,
            setSocietyNonCitizenCommitteeId,
            isUploadingFilesAfterCreateAJK,
            setIsUploadingFilesAfterCreateAJK,
            redirectToAJKLists
          }}>
          <FormCommitteeNonCitizenBySocietyId />
        </FormCommitteeNonCitizenBySocietyIdContext.Provider>
      </Box>
    </Box>
  );
};

export default CreateAjkBukanWn;
