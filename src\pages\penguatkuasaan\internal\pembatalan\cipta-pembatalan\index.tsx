import { useEffect } from "react";
import { useForm, FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { CrudFilter } from "@refinedev/core";
import { globalStyles, useQuery, useQueryFilterForm } from "@/helpers";

import { Box, Typography, IconButton, Button } from "@mui/material";
import {
  ButtonPrimary,
  FormFieldRow,
  Label,
  DataTable,
  IColumn,
  TextFieldController,
} from "@/components";

import { IApiPaginatedResponse, ISocietyDetail } from "@/types";

import { EditIcon } from "@/components/icons";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";

const CiptaPembatalan: React.FC = () => {
  const { t, i18n } = useTranslation();
  const classes = globalStyles();
  const navigate = useNavigate();

  const isMyLanguage = i18n.language === "my";

  const formMethods = useForm<FieldValues>({
    defaultValues: {
      pageNo: 1,
      pageSize: 5,
      societyNo: "",
    },
  });
  const { buildFilters } = useQueryFilterForm({
    formMethods,
    pageKey: "pageNo",
  });
  const { control, handleSubmit, watch, setValue } = formMethods;

  const pageNo = watch("pageNo");
  const pageSize = watch("pageSize");

  const columns: IColumn<ISocietyDetail>[] = [
    {
      field: "",
      headerName: "No",
      flex: 1,
      align: "center",
      renderCell: ({ rowIndex }) => {
        const number = pageNo * pageSize + rowIndex + 1 - pageSize;
        return <>{number}</>;
      },
    },
    {
      field: "societyNo",
      headerName: t("ppmNumber"),
      flex: 1,
      align: "center",
    },
    {
      field: "societyName",
      headerName: t("pertubuhan"),
      flex: 1,
      align: "center",
    },
    {
      field: "",
      headerName: t("status"),
      flex: 1,
      align: "center",
      renderCell: () => {
        return (
          <Typography
            className={classes.statusBadge}
            sx={{
              border: "1px solid var(--primary-color)",
            }}
          >
            {isMyLanguage ? "Aktif" : "Active"}
          </Typography>
        );
      },
    },
    {
      field: "",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton
              onClick={() => {
                navigate(`cipta-pembatalan/${row.id}`);
              }}
            >
              <EditIcon color="#1DC1C1" />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const {
    data: societyListResponse,
    refetch: fetchActiveSociety,
    isLoading: isLoadingSocietyList,
  } = useQuery<IApiPaginatedResponse<ISocietyDetail>>({
    url: "society/cancellation/getAllActiveSociety",
    autoFetch: false,
  });

  const societyList = societyListResponse?.data.data?.data ?? [];
  const totalSocietyList = societyListResponse?.data?.data?.total ?? 0;

  const applyFilters = (pageSize: number, page: number) => {
    const filters = buildFilters(pageSize, page);

    fetchActiveSociety({ filters });
  };

  const handleSearchSociety = () => {
    setValue("pageNo", 1);
    applyFilters(pageSize, 1);
  };

  const handleChangePage = (newPage: number) => {
    setValue("pageNo", newPage);
    applyFilters(pageSize, newPage);
  };

  const handleChangePageSize = (newPageSize: number) => {
    setValue("pageSize", newPageSize);
    setValue("pageNo", 1);
    applyFilters(newPageSize, 1);
  };

  useEffect(() => {
    applyFilters(pageSize, 1);
  }, []);

  return (
    <>
      <form onSubmit={handleSubmit(handleSearchSociety)}>
        <Box className={classes.section} mb={2}>
          <Box className={classes.sectionBox} mb={2}>
            <Typography className="title" mb={1}>
              {isMyLanguage ? "Carian Pertubuhan" : "Organization Search"}
            </Typography>

            <FormFieldRow
              label={<Label text={t("ppmNumber")} />}
              value={<TextFieldController control={control} name="societyNo" />}
            />
          </Box>

          <ButtonPrimary
            type="submit"
            sx={{ marginLeft: "auto" }}
            className={classes.btnSubmit}
          >
            {isMyLanguage ? "Cari Pertubuhan" : "Find an organization"}
          </ButtonPrimary>
        </Box>
      </form>

      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography className="title" mb={1}>
            {isMyLanguage ? "Senarai Pertubuhan" : "Organization List"}
          </Typography>

          <Box sx={{ width: "80%", marginInline: "auto" }}>
            <DataTable
              columns={columns}
              rows={societyList}
              page={pageNo}
              rowsPerPage={pageSize}
              pagination={pageSize > 5}
              totalCount={totalSocietyList}
              isLoading={isLoadingSocietyList}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
            />

            {!(pageSize > 5) && (
              <Button
                onClick={() => handleChangePageSize(10)}
                className={classes.btnOutline}
                sx={{
                  mt: 3,
                  marginInline: "auto",
                }}
              >
                {t("seeFullView")}
                <ArrowRightAltIcon sx={{ color: "#666666B2" }} />
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default CiptaPembatalan;
