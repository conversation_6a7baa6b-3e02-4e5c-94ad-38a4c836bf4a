import React from 'react';
import { Grid, TextField, Typography, TextFieldProps } from '@mui/material';

interface TakwimTextFieldProps extends Omit<TextFieldProps, 'label' | 'value' | 'onChange'> {
  label: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  multiline?: boolean; // <-- Add this
  rows?: number;
}

export const TakwimTextField: React.FC<TakwimTextFieldProps> = ({
  label,
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  error = false,
  helperText,
  rows = 4,
  multiline = false, // <-- Add this
  ...rest // <-- Forward other props
}) => {
  return (
    <Grid container spacing={2} alignItems="flex-start">
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{
            color: '#666666',
            fontSize: '14px',
            mt: 1,
          }}
        >
          {label}
          {required && <span style={{ color: 'red' }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        <TextField
          fullWidth
          multiline={multiline}
          rows={multiline ? rows : undefined}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          error={error}
          helperText={helperText}
          sx={{
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'white',
              '& fieldset': {
                borderColor: '#DADADA',
              },
              '&:hover fieldset': {
                borderColor: '#DADADA',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#DADADA',
              },
            },
            '& .MuiInputBase-input': {
              fontSize: '14px',
              lineHeight: '1.5',
            },
          }}
          {...rest}
        />
      </Grid>
    </Grid>
  );
};

export default TakwimTextField;
