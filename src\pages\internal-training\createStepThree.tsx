import {TrainingFormProps} from "@/pages/internal-training/createStepOne";
import React, {useEffect, useState} from "react";
import {TrainingChapter} from "@/pages/internal-training/createStepTwo";
import {Box, Grid, IconButton, TextField, Typography} from "@mui/material";
import {useTranslation} from "react-i18next";
import DurationComponent from "@/pages/internal-training/durationComponent";
import {DeleteIcon} from "@/components/icons/delete";
import {TrainingAddIcon} from "@/components/icons/trainingAdd";
import {ButtonOutline, ButtonPrimary} from "@/components";
import ChapterFragment from "@/pages/internal-training/chapterFragment";
import QuestionFragment from "@/pages/internal-training/questionFragment";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import ConfirmationDialog from "@/pages/internal-training/confirmDialog";
import {useNavigate} from "react-router-dom";

export interface TrainingAnswer {
    answer: string,
    correct: boolean,
    sequenceOrder: number,
}

export interface TrainingQuestion {
    id: number,
    question: string,
    answers: TrainingAnswer[]
}

const CreateStepThree: React.FC<TrainingFormProps> = ({
                                                          headerStyle,
                                                          labelStyle,
                                                          borderStyle,
                                                          handleNext,
                                                          courseId,
                                                          isUpdate
                                                      }) => {

    const {t, i18n} = useTranslation();
    const navigate = useNavigate();

    const [openModal, setOpenModal] = useState(false);
    const [totalQuestion, setTotalQuestion] = useState<TrainingQuestion[]>([{
        id: 0,
        question: "",
        answers: [],
    }])

    const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
    const [formData, setFormData] = useState({
        no: 0,
        timeLimit: 0,
        passingScore: 0,
    });

    const [hour, setHour] = useState(0)
    const [minute, setMinute] = useState(0)

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const {name, value} = e.target;
        setFormData((prevState) => ({
            ...prevState,
            [name]: value,
        }));
        setFormErrors((prev) => ({...prev, [name]: ""}));
    };

    const hourCallback = (e: number) => {
        setHour(e);
    }

    const minuteCallback = (e: number) => {
        setMinute(e);
    }

    useEffect(() => {
        setFormData((prevState) => ({
            ...prevState,
            timeLimit: hour * 60 + minute,
        }));
    }, [hour, minute]);

    const handleAddChapter = (e: React.MouseEvent<HTMLButtonElement>) => {
        const temp = {
            id: 0,
            question: "",
            answers: [],
        }
        totalQuestion.push(temp)
        const newArray = totalQuestion.slice();
        setTotalQuestion(newArray)
    }

    const handleDeleteChapter = (i: number) => {
        totalQuestion.splice(i, 1);
        const newArray = totalQuestion.slice();
        setTotalQuestion(newArray)
    }

    const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {

    }

    const handleSave = (e: React.MouseEvent<HTMLButtonElement>) => {
        if (isUpdate) EditQuiz();
        else CreateQuiz();
    }

    const {mutate: createQuiz, isLoading: isQuizLoadingCreate} = useCustomMutation();
    const CreateQuiz = (): void => {
        const currentDate = new Date();
        const formattedDate = currentDate.toISOString().split("T")[0];
        createQuiz(
            {
                url: `${API_URL}/society/admin/training/quiz`,
                method: "post",
                values: {
                    trainingCourseId: courseId,
                    title: "E-ROSES Basics Quiz",
                    description: "Test your knowledge of E-ROSES basics",
                    isMandatory: true,
                    minScore: formData.passingScore,
                    timeLimitMinutes: formData.timeLimit,
                    sequenceOrder: 1,
                    quizNo: formData.no
                },
                config: {
                    headers: {
                        "Content-Type": "application/json",
                        portal: localStorage.getItem("portal") || "",
                        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
                    },
                },
                successNotification: (data) => {
                    if (data?.data?.data) {
                        //handleNext(courseId!,"pelajaran");
                        totalQuestion.map((e, i) => CreateQuestion(data?.data?.data, i))
                        return {
                            message: data?.data?.msg,
                            type: "success",
                        };
                    } else {
                        return {
                            message: t("error") + data?.data?.msg,
                            type: "error",
                        };
                    }
                },
                errorNotification: (data) => {
                    return {
                        message: data?.response?.data?.msg,
                        type: "error",
                    };
                },
            },
            {
                onError(error, variables, context) {
                    console.log(error);
                },
            }
        );
    };

    const {mutate: editQuiz, isLoading: isQuizLoadingEdit} = useCustomMutation();
    const EditQuiz = (): void => {
        const currentDate = new Date();
        const formattedDate = currentDate.toISOString().split("T")[0];
        editQuiz(
            {
                url: `${API_URL}/society/admin/training/quiz`,
                method: "put",
                values: {
                    trainingCourseId: courseId,
                    title: "E-ROSES Basics Quiz",
                    description: "Test your knowledge of E-ROSES basics",
                    isMandatory: true,
                    minScore: formData.passingScore,
                    timeLimitMinutes: formData.timeLimit,
                    sequenceOrder: 1,
                    quizNo: formData.no
                },
                config: {
                    headers: {
                        "Content-Type": "application/json",
                        portal: localStorage.getItem("portal") || "",
                        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
                    },
                },
                successNotification: (data) => {
                    if (data?.data?.data) {
                        //handleNext(courseId!,"pelajaran");
                        totalQuestion.map((e, i) => EditQuestion(data?.data?.data, i))
                        return {
                            message: data?.data?.msg,
                            type: "success",
                        };
                    } else {
                        return {
                            message: t("error") + data?.data?.msg,
                            type: "error",
                        };
                    }
                },
                errorNotification: (data) => {
                    return {
                        message: data?.response?.data?.msg,
                        type: "error",
                    };
                },
            },
            {
                onError(error, variables, context) {
                    console.log(error);
                },
            }
        );
    };

    const {mutate: createQuestion, isLoading: isQuestionLoadingCreate} = useCustomMutation();
    const CreateQuestion = (quizId: number, i: number): void => {
        const currentDate = new Date();
        const formattedDate = currentDate.toISOString().split("T")[0];
        const answers = totalQuestion[i].answers.map((e, i) => {
            return {
                optionText: e.answer,
                isCorrect: e.correct,
                sequenceOrder: e.sequenceOrder,
            }
        })
        createQuestion(
            {
                url: `${API_URL}/society/admin/training/quiz/questions`,
                method: "post",
                values: {
                    trainingQuizId: quizId,
                    questionText: totalQuestion[i].question,
                    questionType: "MULTIPLE_CHOICE",
                    points: 10,
                    sequenceOrder: i + 1,
                    options: answers
                },
                config: {
                    headers: {
                        "Content-Type": "application/json",
                        portal: localStorage.getItem("portal") || "",
                        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
                    },
                },
                successNotification: (data) => {
                    if (data?.data?.data) {
                        setOpenModal(false);
                        navigate("/latihan-internal");
                        return {
                            message: data?.data?.msg,
                            type: "success",
                        };
                    } else {
                        return {
                            message: t("error") + data?.data?.msg,
                            type: "error",
                        };
                    }
                },
                errorNotification: (data) => {
                    return {
                        message: data?.response?.data?.msg,
                        type: "error",
                    };
                },
            },
            {
                onError(error, variables, context) {
                    console.log(error);
                },
            }
        );
    };

    const {mutate: editQuestion, isLoading: isQuestionLoadingEdit} = useCustomMutation();
    const EditQuestion = (quizId: number, i: number): void => {
        const currentDate = new Date();
        const formattedDate = currentDate.toISOString().split("T")[0];
        const answers = totalQuestion[i].answers.map((e, i) => {
            return {
                optionText: e.answer,
                isCorrect: e.correct,
                sequenceOrder: e.sequenceOrder
            }
        })
        editQuestion(
            {
                url: `${API_URL}/society/admin/training/quiz/questions`,
                method: "put",
                values: {
                    id: totalQuestion[i].id,
                    trainingQuizId: quizId,
                    questionText: totalQuestion[i].question,
                    questionType: "MULTIPLE_CHOICE",
                    points: 10,
                    sequenceOrder: i + 1,
                    options: answers
                },
                config: {
                    headers: {
                        "Content-Type": "application/json",
                        portal: localStorage.getItem("portal") || "",
                        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
                    },
                },
                successNotification: (data) => {
                    if (data?.data?.data) {
                        setOpenModal(false);
                        navigate("/latihan-internal");
                        return {
                            message: data?.data?.msg,
                            type: "success",
                        };
                    } else {
                        return {
                            message: t("error") + data?.data?.msg,
                            type: "error",
                        };
                    }
                },
                errorNotification: (data) => {
                    return {
                        message: data?.response?.data?.msg,
                        type: "error",
                    };
                },
            },
            {
                onError(error, variables, context) {
                    console.log(error);
                },
            }
        );
    };

    const {data: trainingData, isLoading: isTrainingLoading} = useCustom({
        url: `${API_URL}/society/admin/training/courses/${courseId}/quiz`,
        method: "get",
        config: {
            headers: {
                portal: localStorage.getItem("portal"),
                authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
        },
        queryOptions: {
            enabled: isUpdate && courseId != 0,
            retry: false,
            cacheTime: 0,
        },
    });

    const trainingDetails = trainingData?.data?.data || {};
    console.log("trainingQuiz", trainingData)

    useEffect(() => {
        if (isUpdate && Object.keys(trainingDetails).length > 0) {
            const temp = {
                id: trainingDetails.id,
                no: trainingDetails.quizNo,
                timeLimit: trainingDetails.timeLimitMinutes,
                passingScore: trainingDetails.minScore,
            }
            if (trainingDetails.timeLimitMinutes > 59) {
                const tempH = Math.floor(trainingDetails.timeLimitMinutes / 60);
                setHour(tempH)
            }
            setMinute(trainingDetails.timeLimitMinutes % 60);
            setFormData(temp);
            const tempArr = trainingDetails.questions.map((q: any) => {
                const tempQ = {
                    id: q.id,
                    question: q.questionText,
                    answers: q.options.map((o: any) => {
                        const tempA = {
                            answer: o.optionText,
                            correct: o.isCorrect,
                            sequenceOrder: o.sequenceOrder
                        }
                        return tempA
                    }),
                }
                return tempQ;
            });
            console.log("tempArr",tempArr);
            setTotalQuestion(tempArr);
        }
    }, [trainingDetails]);

  const handleDataChange = (i: number, data: TrainingQuestion) => {
    //totalChapter[i].id = data.id;
    totalQuestion[i].question = data.question;
    totalQuestion[i].answers = data.answers;
    setTotalQuestion(totalQuestion)
  }

    return (<>
            <Box sx={{display: "flex"}}>
                <Box sx={{width: "85%",}}>
                    <Box
                        sx={{
                            borderRadius: 2.5,
                            backgroundColor: "#fff",
                            //display: "inline",
                            px: 2,
                            py: 2,
                            mb: 1,
                        }}
                    >
                        <Box
                            sx={borderStyle}
                        >
                            <Typography
                                sx={headerStyle}
                            >
                                Ketetapan Quiz
                            </Typography>
                            <Grid container spacing={2} sx={{mt: 1}}>
                                <Grid item xs={12} sm={4}>
                                    <Typography sx={labelStyle}>
                                        {t("trainingQuizNo")} <span style={{color: "red"}}>*</span>
                                    </Typography>
                                </Grid>
                                <Grid item xs={12} sm={8}>
                                    <TextField
                                        size={"small"}
                                        fullWidth
                                        required
                                        name="no"
                                        value={formData.no}
                                        error={!!formErrors.no}
                                        onChange={handleInputChange}
                                    />
                                </Grid>
                                <DurationComponent setHour={setHour} setMinute={setMinute}
                                                   labelStyle={labelStyle} hour={hour} minute={minute}/>
                                <Grid item xs={12} sm={4}>
                                    <Typography sx={labelStyle}>
                                        {t("trainingMinScore")} <span style={{color: "red"}}>*</span>
                                    </Typography>
                                </Grid>
                                <Grid item xs={6} sm={1}>
                                    <TextField
                                        size={"small"}
                                        fullWidth
                                        required
                                        name="passingScore"
                                        value={formData.passingScore}
                                        error={!!formErrors.passingScore}
                                        onChange={handleInputChange}
                                    />
                                </Grid>
                                <Grid item xs={6} sm={1}>
                                    <Typography sx={labelStyle}>
                                        {`/ 100%`}
                                    </Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>

                                </Grid>
                            </Grid>
                        </Box>
                    </Box>
                    {totalQuestion.map((e: TrainingQuestion, index) => {
                        return (<Box
                            key={index}
                            sx={{
                                borderRadius: 2.5,
                                backgroundColor: "#fff",
                                //display: "inline",
                                px: 2,
                                py: 2,
                                mb: 1,
                            }}
                        >
                            <QuestionFragment no={index + 1} headerStyle={headerStyle} borderStyle={borderStyle}
                                              labelStyle={labelStyle} handleDataChange={handleDataChange} data={e}/>
                            {index === totalQuestion.length - 1 ?
                                <Grid
                                    item
                                    xs={12}
                                    sx={{
                                        mt: 2,
                                        display: "flex",
                                        flexDirection: "row",
                                        justifyContent: "flex-end",
                                        gap: 1,
                                    }}
                                >
                                    <ButtonOutline
                                        sx={{
                                            bgcolor: "white",
                                            "&:hover": {bgcolor: "white"},
                                            width: "auto",
                                        }}
                                        onClick={handleSaveDraft}
                                    >
                                        {t("save")}
                                    </ButtonOutline>
                                    <ButtonPrimary
                                        variant="contained"
                                        sx={{
                                            width: "auto",
                                        }}
                                        onClick={(e) => setOpenModal(true)}
                                        //disabled={true}
                                    >
                                        {t("next")}
                                    </ButtonPrimary>
                                </Grid> : <></>}
                        </Box>)
                    })}
                </Box>
                <Box sx={{width: "15%"}}>
                    <Box
                        sx={{
                            borderRadius: 2.5,
                            backgroundColor: "#fff",
                            //display: "inline",
                            px: 2,
                            py: 2,
                            mb: 1,
                            ml: 2,
                        }}
                    >
                        {totalQuestion.map((e, i) => {
                            return <Grid container spacing={1} key={i}>
                                <Grid item xs={10}>
                                    <Box
                                        sx={borderStyle}
                                    >
                                        <Typography key={i} sx={{
                                            fontSize: 14,
                                            color: "#666666",
                                            fontWeight: "400 !important",
                                        }}>
                                            {`${t("question")} ${i + 1}`}
                                        </Typography>
                                    </Box></Grid>
                                {i > 0 ?
                                    <Grid item xs={2}>
                                        <IconButton
                                            sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                alignContent: "center",
                                                mt: 1
                                            }}
                                            onClick={() => {
                                                handleDeleteChapter(i)
                                            }}
                                        >
                                            <DeleteIcon
                                                sx={{
                                                    color: "#FF0000",
                                                }}
                                            />
                                        </IconButton></Grid> : <Grid item xs={2}></Grid>}
                            </Grid>
                        })}
                        <Box>
                            <Grid container sx={{mt: 1}}>
                                <Grid item xs={3}>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            borderRadius: 2.5,
                                            backgroundColor: "#0CA6A6",
                                            width: 36,
                                            height: 36,
                                        }}
                                    >
                                        <TrainingAddIcon sx={{color: "#FFF",}}/>
                                    </Box>
                                </Grid>
                                <Grid item xs={9}>
                                    <ButtonOutline
                                        sx={{
                                            bgcolor: "white",
                                            "&:hover": {bgcolor: "white"},
                                            width: "75%",
                                            minWidth: "75%"
                                        }}
                                        onClick={handleAddChapter}
                                    >
                                        {t("addQuestion")}
                                    </ButtonOutline>
                                </Grid>
                            </Grid>
                        </Box>
                    </Box>
                </Box>
            </Box>
            <ConfirmationDialog text={"Adakah anda pasti untuk mencipta latihan ini"} handleSave={handleSave}
                                labelStyle={labelStyle} openModal={openModal} setOpenModal={setOpenModal}/>
        </>
    );
}

export default CreateStepThree;
