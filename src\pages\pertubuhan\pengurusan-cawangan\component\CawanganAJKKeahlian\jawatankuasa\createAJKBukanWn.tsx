import React, { useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import ButtonPrimary from "@/components/button/ButtonPrimary";
import { ButtonOutline } from "@/components/button";
import { Controller, useForm } from "react-hook-form";
import Input from "@/components/input/Input";
import useMutation from "@/helpers/hooks/useMutation";
import { API_URL } from "@/api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { usejawatankuasaContext } from "./jawatankuasaProvider";
import {
  capitalizeWords,
  DocumentUploadType,
  IdTypes,
  OrganisationPositions,
  useQuery,
} from "@/helpers";
import dayjs from "dayjs";
import FileUploader from "@/components/input/fileUpload";
import { AjkNonCiizen } from "../../CawanganPenyataTahunan/interface";
import { useSelector } from "react-redux";

export const CreateAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const location = useLocation();
  const ajkNonCitizen: AjkNonCiizen = location.state?.ajkNonCitizen;
  const view = location.state?.view;
  const [ajkId, setAjkId] = useState<number>();
  const { id } = useParams();
  const [userICCorrect, setUserICCorrect] = useState(false);
  const [userNameMatchIC, setUserNameMatchIC] = useState(false);
  const [positionList, setPositionList] = useState<
    { value: number; label: string; designationCode?: string }[]
  >([]);

  useEffect(() => {
    if (ajkNonCitizen) {
      setAjkId(ajkNonCitizen.id);
      Object.entries(ajkNonCitizen).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, []);

  const handleSenaraiAjkResets = () => {
    reset();
    reset({
      identificationNo: "",
      name: "",
      placeOfBirth: "",
      otherDesignationCode: "",
      stayDurationDigit: "",
      tujuanDMalaysia: "",
      permitNo: "",
      visaNo: "",
      residentialAddress: "",
      residentialCity: "",
    });
  };

  const {
    addressList,
    society,
    fetchAddressList,
    fetchSociety,
    meetingId,
    documentIds,
    appointmentDateG,
    savedMeetingDate,
  } = usejawatankuasaContext();

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);

  useEffect(() => {
    fetchAddressList();
    fetchSociety();
    setShouldFetch(false);
  }, [shouldFetch]);

  const form = useForm<AjkNonCiizen | any>();
  const {
    control,
    formState: { errors },
    watch,
    handleSubmit,
    reset,
    setValue,
    getValues,
  } = form;

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  const { fetch: mutate, isLoading: isCreatingAJK } = useMutation({
    url: "society/nonCitizenCommittee/create",
    onSuccess: (response) => {
      const ajkId = response.data.data.id;
      setAjkId(ajkId);
    },
  });

  const { mutate: updateAJK, isLoading: isLoadingAJK } = useCustomMutation();

  const onSubmit = (value: AjkNonCiizen) => {
    if (ajkId) {
      updateAJK(
        {
          url: `${API_URL}/society/nonCitizenCommittee/${ajkId}/edit`,
          method: "put",
          values: {
            ...value,
            branchId: branchDataRedux.id,
            branchNo: branchDataRedux.branchNo,
            meetingId: meetingId,
            documentId: documentIds,
            appointedDate: savedMeetingDate
              ? savedMeetingDate
              : appointmentDateG,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            return {
              message: data?.data?.msg,
              type: "success",
            };
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {},
        }
      );
    } else {
      mutate({
        ...value,
        societyId: id,
        branchId: branchDataRedux.id,
        branchNo: branchDataRedux.branchNo,
        meetingId: meetingId,
        documentId: documentIds,
        appointedDate: savedMeetingDate ? savedMeetingDate : appointmentDateG,
      });
    }
  };

  const { data, isLoading } = useCustom<any>({
    url: `${API_URL}/user/auth/validateId`,
    method: "get",
    config: {
      query: {
        identificationNo: watch("identificationNo"),
        name: watch("name")?.trim().toUpperCase(),
        sessionIdentificationNo: watch("identificationNo"),
      },
    },
    queryOptions: {
      enabled:
        watch("identificationNo")?.length > 11 &&
        !!watch("name") &&
        watch("identificationType") === "4",
      retry: false,
      cacheTime: 0,
    },
    successNotification(data) {
      //reset before new call
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      const { name, message, status, userExist, integrationOff } =
        data?.data?.data || {};

      if (!integrationOff) {
        if (status === "Y") {
          setUserICCorrect(true);
          if (name) {
            setUserNameMatchIC(true);
          }
        } else {
          setUserICCorrect(false);
          setUserNameMatchIC(true);
        }
      }

      return false;
    },
    errorNotification(error) {
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      return false;
    },
  });

  let identificationNoHelperText: string | undefined = undefined;
  if (watch("identificationType") === "4") {
    if (typeof errors.identificationNo?.message === "string") {
      identificationNoHelperText = errors.identificationNo.message;
    } else if (watch("identificationNo")?.length === 12 && !userICCorrect) {
      identificationNoHelperText = t("IcDoesNotExist");
    }
  } else if (typeof errors.identificationNo?.message === "string") {
    identificationNoHelperText = errors.identificationNo.message;
  }

  let nameHelperText: string | undefined = undefined;
  if (watch("identificationType") === "4") {
    if (typeof errors.name?.message === "string") {
      nameHelperText = errors.name.message;
    } else if (
      watch("identificationNo")?.length === 12 &&
      watch("name")?.trim() !== undefined &&
      !userNameMatchIC
    ) {
      nameHelperText = t("invalidName");
    }
  } else if (typeof errors.name?.message === "string") {
    nameHelperText = errors.name.message;
  }

  const {
    data: positionListRes,
    isLoading: isLoadingPositionListRes,
    refetch: refetchSocietyList,
  } = useQuery({
    url: "society/nonCitizenCommittee/getPositionsList",
    filters: [
      {
        field: "societyId",
        value: parseInt(id ? id : ""),
        operator: "eq",
      },
      {
        field: "branchId",
        value: parseInt(branchDataRedux?.id),
        operator: "eq",
      },
      {
        field: "appointedDate",
        value: savedMeetingDate ? savedMeetingDate : appointmentDateG,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        console.log("list", data?.data?.data);
        const newList = data?.data?.data?.map((item: any) => {
          const position = OrganisationPositions.find(
            (p) => p.value === Number(item.designationCode)
          );
          const label = position?.label
            ? `${t(position.label)}${
                item.positionHolder ? ` - ${item.positionHolder}` : ""
              }`
            : item.designationCode;

          return {
            label,
            value: Number(item?.activeCommitteeId),
            designationCode: item.designationCode,
          };
        });

        setPositionList(newList);
      }
    },
  });

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <form noValidate onSubmit={handleSubmit(onSubmit)}>
          <Box
            sx={{
              px: 2,
              py: 1,
              mb: 3,
              borderRadius: "14px",
            }}
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                fontWeight: "bold",
                fontSize: 14,
                color: "var(--primary-color)",
              }}
            >
              {t("maklumat")} {t("nonCitizenAJK")}
            </Typography>
          </Box>

          <Box sx={{ pl: 2 }}>
            <Controller
              name="societyNo"
              control={control}
              defaultValue={society?.societyNo}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.societyNo?.message}
                    label={t("organizationNumber")}
                    disabled
                    value={society?.societyNo}
                  />
                );
              }}
            />
            <Controller
              name="societyName"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.societyName?.message}
                    label={t("organizationName")}
                    disabled
                    value={society?.societyName}
                  />
                );
              }}
            />
            <Controller
              name="name"
              control={control}
              defaultValue={getValues("name")}
              rules={{
                required: t("fieldRequired"),
              }}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    error={!!nameHelperText}
                    helperText={nameHelperText}
                    label={t("name")}
                    value={getValues("name")}
                  />
                );
              }}
            />
            <Controller
              name="citizenshipStatus"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    label={t("citizenship")}
                    disabled
                    value={"Bukan Warganegara"}
                  />
                );
              }}
            />
            <Controller
              name="identificationType"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              defaultValue={getValues("identificationType")}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    error={!!errors.identificationType?.message}
                    label={t("idType")}
                    value={t(getValues("identificationType"))}
                    type="select"
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;
                      if (inputType !== "4" && value === "4") {
                        setValue("identificationNo", "");
                      }

                      setValue(field.name, value);
                    }}
                    options={IdTypes.filter(
                      (item) =>
                        Number(item.value) === 4 || Number(item.value) === 5
                    )}
                  />
                );
              }}
            />
            <Controller
              name="identificationNo"
              rules={{
                required: t("fieldRequired"),
                validate: (value) => {
                  const type = getValues("identificationType");
                  if (type === "4" && value.length !== 12) {
                    return t("fieldRequired");
                  }
                  return true;
                },
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    error={!!identificationNoHelperText}
                    helperText={identificationNoHelperText}
                    label={t("idNumber")}
                    inputProps={
                      getValues("identificationType") === "4"
                        ? {
                            inputMode: "numeric",
                            pattern: "[0-9]*",
                            maxLength: 12,
                            minLength: 12,
                          }
                        : undefined
                    }
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;

                      if (inputType === "4") {
                        value = value.replace(/\D/g, "").slice(0, 12);
                      }

                      setValue(field.name, value);
                    }}
                  />
                );
              }}
            />
            <Controller
              name="applicantCountryCode"
              control={control}
              defaultValue={getValues("applicantCountryCode")}
              rules={{
                required: t("fieldRequired"),
              }}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    disabled={view}
                    helperText={errors.applicantCountryCode?.message as string}
                    error={!!errors.applicantCountryCode?.message}
                    label={t("originCountry")}
                    type="select"
                    value={parseInt(getValues("applicantCountryCode"))}
                    options={CountryData}
                  />
                );
              }}
            />
            <Controller
              name="visaNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    error={!!errors.visaNo?.message}
                    label={t("nomborVisa")}
                  />
                );
              }}
            />
            <Controller
              name="visaExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    label={t("visaExpiryDate")}
                    type="date"
                    onChange={(newValue) =>
                      setValue("visaExpirationDate", newValue.target.value)
                    }
                    value={
                      getValues("visaExpirationDate")
                        ? dayjs(getValues("visaExpirationDate")).format(
                            "DD-MM-YYYY"
                          )
                        : ""
                    }
                  />
                );
              }}
            />
            <Controller
              name="permitNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    error={!!errors.permitNo?.message}
                    label={t("permitNumber")}
                  />
                );
              }}
            />
            <Controller
              name="permitExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    label={t("permitExpiryDate")}
                    type="date"
                    onChange={(newValue) =>
                      setValue("permitExpirationDate", newValue.target.value)
                    }
                    value={
                      getValues("permitExpirationDate")
                        ? dayjs(getValues("permitExpirationDate")).format(
                            "DD-MM-YYYY"
                          )
                        : ""
                    }
                  />
                );
              }}
            />
            <Controller
              name="tujuanDMalaysia"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    helperText={errors.tujuanDMalaysia?.message as string}
                    error={!!errors.tujuanDMalaysia?.message}
                    label={capitalizeWords(t("purposeInMalaysia"))}
                  />
                );
              }}
            />
            <Controller
              name="tempohDMalaysia"
              control={control}
              rules={{
                required: t("fieldRequired"),
              }}
              render={({ field }) => {
                const date = getValues("tempohDMalaysia");

                const formattedDate = Array.isArray(date)
                  ? `${date[0]}-${String(date[1]).padStart(2, "0")}-${String(
                      date[2]
                    ).padStart(2, "0")}`
                  : date;

                return (
                  <Input
                    disabled={view}
                    required
                    {...field}
                    helperText={errors.tempohDMalaysia?.message as string}
                    error={!!errors.tempohDMalaysia?.message}
                    label={capitalizeWords(t("durationInMalaysia"))}
                    type="date"
                    onChange={(newValue) =>
                      setValue("tempohDMalaysia", newValue.target.value)
                    }
                    value={
                      getValues("tempohDMalaysia")
                        ? dayjs(getValues("tempohDMalaysia")).format(
                            "DD-MM-YYYY"
                          )
                        : ""
                    }
                  />
                );
              }}
            />
            <Controller
              name="activeCommitteeId"
              rules={{
                required: t("fieldRequired"),
                validate: (value) =>
                  parseInt(value) !== 0 || t("fieldRequired"),
              }}
              defaultValue={parseInt(getValues("activeCommitteeId"))}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    required
                    {...field}
                    type="select"
                    label={t("position")}
                    isLoadingData={isLoadingPositionListRes}
                    options={positionList}
                    helperText={errors.activeCommitteeId?.message as string}
                    error={!!errors.activeCommitteeId?.message}
                    onChange={(selectedValue) => {
                      field.onChange(selectedValue);
                      const selectedOption = positionList?.find(
                        (opt) => Number(opt.value) === Number(selectedValue)
                      );
                      if (selectedOption?.designationCode) {
                        setValue(
                          "designationCode",
                          selectedOption.designationCode
                        );
                      }
                    }}
                  />
                );
              }}
            />
            <Controller
              name="otherDesignationCode"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    helperText={errors.tujuanDMalaysia?.message as string}
                    error={!!errors.otherDesignationCode?.message}
                    label={t("importanceOfPosition2")}
                  />
                );
              }}
            />
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                mt: 2,
                gap: 2,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              {!view ? (
                <>
                  <ButtonPrimary
                    disabled={
                      isCreatingAJK ||
                      isLoadingAJK ||
                      (watch("identificationType") === "4" &&
                        (watch("identificationNo")?.length < 12 ||
                          !userICCorrect ||
                          !userNameMatchIC))
                    }
                    type="submit"
                  >
                    {t("update")}
                  </ButtonPrimary>
                </>
              ) : null}
            </Box>
          </Box>

          {!view
            ? // <DocumentUpload
              //   disabled={!ajkId ? true : false}
              //   ajkId={ajkId ? ajkId : 0} // Pass in the AJK id here
              //   id={id ? parseInt(id) : 0} // Pass in the society id
              //   documentType={DocumentUploadType.NON_CITIZEN_COMMITTEE}
              // />
              watch("identificationNo") && (
                <FileUploader
                  title="ajkEligibilityCheck"
                  type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
                  validTypes={[
                    "application/pdf",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/msword",
                    "text/plain",
                  ]}
                  maxFileSize={25 * 1024 * 1024}
                  disabled={view}
                  societyId={parseInt(id ? id : "")}
                  icNo={watch("identificationNo")}
                />
              )
            : null}

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
              gap: 2,
            }}
          >
            {watch("identificationNo")
              ? !view && (
                  <ButtonOutline onClick={handleSenaraiAjkResets}>
                    {t("reset")}
                  </ButtonOutline>
                )
              : null}
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default CreateAjkBukanWn;
