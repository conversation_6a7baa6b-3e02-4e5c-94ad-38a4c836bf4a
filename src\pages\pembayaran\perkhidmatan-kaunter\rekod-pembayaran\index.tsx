import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Box, TextField, InputAdornment, Card } from "@mui/material";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { useForm } from "@refinedev/react-hook-form";
import DataTable, { IColumn } from "@/components/datatable";
import { formatDate, MALAYSIA, PaymentTypeList } from "@/helpers";
import FilterBar from "@/components/filter";
import { SearchIcon } from "@/components/icons";
import { useSelector } from "react-redux";

const PembayaranKaunter: React.FC = () => {
  const { t } = useTranslation();
  const [paymentList, setPaymentList] = useState<any>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const { watch, setValue } = useForm({
    defaultValues: {
      organizationName: "",
      page: 1,
      rowsPerPage: 10,
      jenis<PERSON><PERSON>bayaran: "",
      negeri: "",
    },
  });

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];

  const getStateName = (stateCode: any) => {
    const stateName = addressData?.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };

  const { mutate: fetchPaymentList, isLoading: paymentListDataIsLoading } =
    useCustomMutation();

  const handleFetchPaymentList = () => {
    fetchPaymentList(
      {
        url: `${API_URL}/society/payment/getPaymentList`,
        method: "post",
        values: {
          searchQuery: watch("organizationName"),
          paymentId: null,
          paymentStatus: 1, // 1 - PAID , 2 - UNPAID, 3 - CANCEL - 3, 4 - FAILED
          paymentMethod: "KAUNTER",
          page: watch("page"),
          size: watch("rowsPerPage"),
          paymentType: watch("jenisPembayaran"),
          stateCode: watch("negeri"),
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        },
      },
      {
        onSuccess: (data) => {
          const alldata = data?.data?.data;
          const total = alldata?.total;
          console.log(total);
          const transformData = alldata?.data?.map((item: any) => ({
            ...item,
            bayaranDiterima: false,
          }));

          setPaymentList(transformData);
          setTotalRecords(total);
        },
      }
    );
  };

  useEffect(() => {
    handleFetchPaymentList();
  }, []);

  const handleChangePage = (newPage: number) => {
    setValue("page", newPage);
    handleFetchPaymentList();
  };

  const handleRowPerPage = (newPageSize: number) => {
    setValue("rowsPerPage", newPageSize);
    handleFetchPaymentList();
  };

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    jenisPembayaran: "jenisPembayaran",
    negeri: "negeri",
  });

  const onFilterChange = (filter: string, value: any) => {
    setValue("page", 1);
    switch (filter) {
      case "jenisPembayaran":
        setValue("jenisPembayaran", value);
        break;
      case "negeri":
        setValue("negeri", value);
        break;
    }
    handleFetchPaymentList();
  };

  // @ts-ignore
  const addressDataRedux = useSelector((state) => state?.addressData?.data);
  const StateList = addressDataRedux
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ id: item.id, value: item.name }));

  const filterOptions = {
    jenisPembayaran: PaymentTypeList.map(({ id, value }) => ({
      value: value,
      label: value,
    })),
    // @ts-ignore
    negeri: StateList.map(({ id, value }) => ({
      value: id,
      label: value,
    })),
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      setValue("page", 1);
      const value = (event.target as HTMLInputElement).value;
      setValue("organizationName", value);
      handleFetchPaymentList();
    }
  };

  const columnsViewData: IColumn[] = [
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "center",
    },
    {
      field: "userName",
      headerName: t("namaPembayar"),
      align: "center",
      flex: 1,
    },
    {
      field: "societyName",
      headerName: t("negeriBayaranDiterima"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{getStateName(row?.stateCode)}</Box>;
      },
    },
    {
      field: "receiptNo",
      headerName: t("noResit1"),
      align: "center",
      flex: 1,
    },
    {
      field: "officerName",
      headerName: t("pegawaiBertugas"),
      align: "center",
      flex: 1,
    },
    {
      field: "paymentDate",
      headerName: t("tarikhBayar"),
      align: "center",
      flex: 1,
      renderCell: ({ row }: any) => {
        return row?.paymentDate
          ? formatDate(row.paymentDate, "D-M-YYYY", {
              parseFormat: "HH:mm DD:MM:YYYY",
            })
          : "-";
      },
    },
    {
      field: "paymentType",
      headerName: t("jenisPembayaran"),
      align: "center",
      flex: 1,
    },
  ];

  return (
    <>
      <Box
        sx={{
          display: "flex",
          gap: "20px",
          minHeight: "calc(100vh - 60px - 83px)",
        }}
      >
        <Box
          sx={{
            height: "100%",
            flex: 3,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Card
            sx={{
              p: 3,
              borderRadius: "15px",
              boxShadow: "none",
              height: "100%",
              width: "100%",
            }}
          >
            <TextField
              fullWidth
              variant="outlined"
              placeholder={t("namaPertubuhan")}
              sx={{
                display: "block",
                boxSizing: "border-box",
                maxWidth: 570,
                marginInline: "auto",
                height: "40px",
                background: "var(--border-grey)",
                opacity: 0.5,
                border: "1px solid var(--text-grey)",
                borderRadius: "10px",
                "& .MuiOutlinedInput-root": {
                  height: "40px",
                  "& fieldset": {
                    border: "none",
                  },
                },
              }}
              onKeyDown={onSearchKeyDown}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon
                      sx={{
                        color: "var(--text-grey-disabled)",
                        marginLeft: "8px",
                      }}
                    />
                  </InputAdornment>
                ),
              }}
            />

            <FilterBar
              filterOptions={filterOptions}
              onFilterChange={onFilterChange}
              selectedFilters={selectedFilters}
              onSelectedFiltersChange={handleSelectedFiltersChange}
            />
            <Box mt={3}>
              <DataTable
                columns={columnsViewData}
                rows={paymentList ? paymentList : []}
                isLoading={paymentListDataIsLoading}
                page={watch("page")}
                rowsPerPage={watch("rowsPerPage")}
                totalCount={totalRecords}
                onPageChange={handleChangePage}
                onPageSizeChange={(newPageSize) => {
                  setValue("page", 1);
                  handleRowPerPage(newPageSize);
                }}
              />
            </Box>
          </Card>
        </Box>
      </Box>
    </>
  );
};

export default PembayaranKaunter;
