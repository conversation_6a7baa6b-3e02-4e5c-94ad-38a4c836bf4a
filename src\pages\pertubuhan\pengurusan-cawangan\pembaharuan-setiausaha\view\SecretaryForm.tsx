import { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { handleNext } from "@/redux/secretaryBranchReformReducer";
import { useSecretaryBranchReformContext } from "../Provider";
import { getLocalStorage } from "@/helpers/utils";
import {
  MALAYSIA,
  ListGelaran,
  AddressOptions,
} from "@/helpers/enums";

import { Box, Grid, Typography } from "@mui/material";
import { PageButton, NavButton } from "@/components/button/ButtonPagination";
import FormFieldRow from "@/components/form-field-row";
import Label from "@/components/label/Label";
import TextFieldController from "@/components/input/TextFieldController";
import SelectFieldController from "@/components/input/select/SelectFieldController";
import DatePickerController from "@/components/input/DatePickerController";
import DisabledTextField from "@/components/input/DisabledTextField";
import dayjs from "dayjs";

const SecretaryForm: React.FC = () => {
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { control, watch, handleSubmit, setValue } = useFormContext();
  const { isViewOnly } = useSecretaryBranchReformContext();

  const occupationList = getLocalStorage("occupation_list", []);
  const currentLanguage = i18n.language;
  const isMalaysiaLanguage = currentLanguage === "my";

  const addressList = getLocalStorage("address_list", []);
  const employerAddressOptions = AddressOptions(t);

  const jobCode = watch("jobCode");
  const committeeEmployerAddressStatus = watch(
    "committeeEmployerAddressStatus"
  );

  const disabledState = isViewOnly;

  const disabledEmployer = ["Pesara", "Tidak Bekerja"].includes(jobCode);

  const stateOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === MALAYSIA)
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList]
  );
  const residenceDistrictOptions = useMemo(
    () =>
      addressList
        .filter(
          (item: any) =>
            item.pid === Number(watch("committeeResidenceStateCode"))
        )
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("residenceStateCode")]
  );

  const employerDistrictOptions = useMemo(
    () =>
      addressList
        .filter(
          (item: any) =>
            item.pid === Number(watch("committeeEmployerStateCode"))
        )
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("employerStateCode")]
  );

  const allCountryOptions = useMemo(
    () =>
      addressList
        ?.filter((i: any) => i.level === 0)
        ?.map((item: any) => ({
          value: item.id,
          label: item.name,
        }))
        ?.filter((i: any) => i.value !== MALAYSIA),
    [addressList]
  ); 

  const genderOptions = useMemo(
    () => [
      {
        value: "L",
        label: t("male"),
      },
      {
        value: "P",
        label: t("female"),
      },
    ],
    []
  );

  const onSubmit = () => dispatch(handleNext());

  const convertIcToDate = (data: string) => {
    if (!data) {
      return;
    }
    const yyMMdd = data.substring(0, 6);
    let parsedDate = dayjs(yyMMdd, "YYMMDD");
    const year = parsedDate.year();
    const currentYear = dayjs().year();
    if (year < 100) {
      const fullYear = year + (year > currentYear % 100 ? 1900 : 2000);
      parsedDate = parsedDate.year(fullYear);
    }
    const formattedDate = parsedDate.format("YYYY-MM-DD");
    return formattedDate;
  };

  useEffect(() => {
    if (!watch("identificationNo") || watch("identificationNo").length !== 12) {
      setValue("gender", null);
    } else {
      if (parseInt(watch("identificationNo")?.slice(-1)) % 2 === 0) {
        setValue("gender", "P");
      } else {
        setValue("gender", "L");
      }
    }
    // Remove auto-population of placeOfBirth - let user input manually
  }, [watch("identificationNo")]);

  useEffect(() => {
    const idNoString =
      typeof watch("identificationNo") === "number"
        ? String(watch("identificationNo"))
        : watch("identificationNo") ?? "";

    if (idNoString.length >= 6) {
      setValue("dateOfBirth", convertIcToDate(watch("identificationNo")));
    }
  }, [watch("identificationNo")]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {t("oldBranchSecretary")}
        </Typography>

        <Grid container spacing={2} pl={2} pt={2}>
          <FormFieldRow
            label={<Label text={t("name")} />}
            value={
              <TextFieldController
                control={control}
                name="oldSecretaryName"
                disabled
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("idNumber")} />}
            value={
              <TextFieldController
                control={control}
                name="oldSecretaryIdentificationNumber"
                disabled
              />
            }
          />
        </Grid>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {t("newBranchSecretary")}
        </Typography>

        <Grid container spacing={2} pl={2} pt={2}>
          <FormFieldRow
            label={<Label text={t("position")} />}
            value={<DisabledTextField value={t("secretary")} />}
          />
        </Grid>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {isMalaysiaLanguage
            ? "Maklumat Peribadi Setiausaha baru"
            : "Personal Information of the new Secretary"}
        </Typography>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Grid container spacing={2} pl={2} pt={2}>
            <FormFieldRow
              label={<Label text={t("idType")} />}
              value={<DisabledTextField value={t("mykad")} />}
            />

            <FormFieldRow
              label={<Label text={t("idNumber")} />}
              value={
                <TextFieldController
                  control={control}
                  name="identificationNo"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("gelaran")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="titleCode"
                  options={ListGelaran}
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("fullName")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeName"
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("gender")} />}
              value={
                <SelectFieldController
                  control={control}
                  name="gender"
                  options={genderOptions}
                  disabled 
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("citizenship")} required />}
              value={<DisabledTextField value="Malaysia" />}
            />

            <FormFieldRow
              label={<Label text={t("dateOfBirth")} required />}
              value={
                <DatePickerController
                  control={control}
                  name="dateOfBirth"
                  disabled 
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("placeOfBirth")} />}
              value={
                <TextFieldController
                  control={control}
                  name="placeOfBirth"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("pekerjaan")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="jobCode"
                  options={occupationList}
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              align="flex-start"
              label={<Label text={t("residentialAddress")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeResidenceAddress"
                  disabled={disabledState}
                  multiline
                  rows={3}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("state")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="committeeResidenceStateCode"
                  options={stateOptions}
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("district")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="committeeResidenceDistrictCode"
                  options={residenceDistrictOptions}
                  disabled={
                    !watch("committeeResidenceStateCode") || disabledState
                  }
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("city")} />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeResidenceCity"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("postcode")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeResidencePostcode"
                  disabled={disabledState}
                  type="number"
                  rules={{
                    validate: (value: string) => {
                      if (!/^\d{5}$/.test(value)) {
                        return t("postcodeValidation");
                      }
                      return true;
                    },
                  }}
                  isPostcode
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("email")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="email"
                  type="email"
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("mobilePhoneNumber")} required />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="hpNoCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 3 }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                    required
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="hpNo"
                    type="tel"
                    required
                  />
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("officePhoneNumber")} />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="workTelNoCode"
                    type="telCode"
                    inputProps={{ maxLength: 3 }}
                    sx={{ width: "80px" }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="workTelNo"
                    type="tel"
                  />
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("homePhoneNumber")} />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="homeTelNoCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 3 }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="homeTelNo"
                    type="tel"
                  />
                </Box>
              }
            />
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography
            sx={{
              color: "#FF0000",
              fontSize: "12px",
              fontWeight: "500 !important",
            }}
          >
            {t("peringatan")} :
          </Typography>

          <Typography
            sx={{
              color: "#666666",
              fontSize: "12px",
              fontWeight: "400 !important",
            }}
          >
            {isMalaysiaLanguage
              ? "Bagi ahli jawatankuasa yang TIDAK BEKERJA/ PESARA, maklumat majikan tidak perlu diisi"
              : "For committee members who are UNWORKED/RETIRED, employer information does not need to be filled in"}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {t("employerInfo")}
        </Typography>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Grid container spacing={2} pl={2} pt={2}>
            <FormFieldRow
              label={<Label text={t("employerName")} />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeEmployerName"
                  disabled={disabledState || disabledEmployer}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("employerAddress")} />}
              value={
                <SelectFieldController
                  control={control}
                  name="committeeEmployerAddressStatus"
                  options={employerAddressOptions}
                  disabled={disabledState || disabledEmployer}
                />
              }
            />

            {committeeEmployerAddressStatus === "0" && (
              <>
                <FormFieldRow
                  label={<Label text={t("country")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="committeeEmployerCountryCode"
                      options={allCountryOptions}
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={""} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="committeeEmployerAddress"
                      disabled={disabledState || disabledEmployer}
                      multiline
                      rows={3}
                    />
                  }
                />
              </>
            )}

            {committeeEmployerAddressStatus === "1" && (
              <>
                <FormFieldRow
                  label={<Label text={""} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="committeeEmployerAddress"
                      disabled={disabledState || disabledEmployer}
                      multiline
                      rows={3}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("state")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="committeeEmployerStateCode"
                      options={stateOptions}
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("district")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="committeeEmployerDistrictCode"
                      options={employerDistrictOptions}
                      disabled={
                        !watch("committeeEmployerStateCode") ||
                        disabledState ||
                        disabledEmployer
                      }
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("city")} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="committeeEmployerCity"
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("postcode")} />}
                  value={
                    <TextFieldController
                      isPostcode
                      control={control}
                      name="committeeEmployerPostcode"
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />
              </>
            )}
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          gap: 1,
          mt: 3,
          mb: 3,
        }}
      >
        <PageButton active>1</PageButton>
        <PageButton type="submit">2</PageButton>

        <NavButton type="submit">Next</NavButton>
      </Box>
    </form>
  );
};

export default SecretaryForm;
