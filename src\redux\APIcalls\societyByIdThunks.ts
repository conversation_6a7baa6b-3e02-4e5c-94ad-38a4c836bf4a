import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setSocietyByIdDataRedux, setSocietyByIdError, setSocietyByIdLoading } from '../societyByIdDataReducer';

export const fetchSocietyByIdData = createAsyncThunk(
  'societyById/fetchData',
  async ({ id }: { id: string },
    { dispatch }) => {
    dispatch(setSocietyByIdLoading(true));
    try {
      const response = await fetch(`${API_URL}/society/${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
      const data = await response.json();
      dispatch(setSocietyByIdDataRedux(data.data));
    } catch (error: any) {
      dispatch(setSocietyByIdError(error.message));
    } finally {
      dispatch(setSocietyByIdLoading(false));
    }
  }
);
