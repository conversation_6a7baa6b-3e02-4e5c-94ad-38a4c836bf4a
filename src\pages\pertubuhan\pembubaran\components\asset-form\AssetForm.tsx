import { Dispatch, SetStateAction, useEffect } from "react";
import { useNotification } from "@refinedev/core";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { usePembubaranContext } from "../../PembubaranProvider";
import { formatAndValidateNumber, useMutation } from "@/helpers";
import { useAssetSchema } from "@/schemas";

import { Box, Typography } from "@mui/material";
import {
  ButtonPrimary,
  TextFieldController,
  FormFieldRow,
  Label,
  ToggleButtonController,
  ButtonOutline,
} from "@/components";

interface AssetFormProps {
  activeAsset: number | null;
  setActiveAsset: Dispatch<SetStateAction<number | null>>;
  setActiveComp: Dispatch<SetStateAction<"list" | "form">>;
}

// eslint-disable-next-line react-refresh/only-export-components
export const reasonOptions = (language: string) => [
  {
    value: "donation",
    label:
      language === "my"
        ? "Didermakan kepada organisasi lain/ pertubuhan berdaftar yang mempunyai matlamat yang serupa."
        : "Donated to other organizations/registered societies that have similar goals.",
  },
  {
    value: "liability",
    label:
      language === "my"
        ? "Pembayaran liabiliti jangka panjang/pendek."
        : "Payment of long/short term liabilities.",
  },
  {
    value: "balance",
    label: language === "my" ? "Tiada baki." : "No Balance",
  },
  {
    value: "other",
    label:
      language === "my"
        ? "Lain-lain (Sila Nyatakan)"
        : "Others (Please Specify)",
  },
];

const AssetForm: React.FC<AssetFormProps> = ({
  setActiveComp,
  activeAsset,
  setActiveAsset,
}) => {
  const schema = useAssetSchema();
  const { t, i18n } = useTranslation();
  const { open } = useNotification();
  const { form, liquidationDetailData } = usePembubaranContext();
  const { setValue: setValuePembubaranForm, getValues } = form;
  const currentLanguage = i18n.language;

  const { fetch: updateLiquidation, isLoading: isLoadingUpdate } = useMutation({
    url: "society/liquidate/update",
    method: "put",
    onSuccess: (data) => {
      const assets = data?.data?.data?.assets;

      setValuePembubaranForm("assets", assets);
      setActiveComp("list");
      setActiveAsset(null);
    },
    onSuccessNotification: () => {
      open?.({
        type: "success",
        message:
          currentLanguage === "my"
            ? "Maklumat aset berjaya ditambah"
            : "Asset information added successfully.",
        // description:
      });
    },
  });

  const { control, handleSubmit, setValue, watch } = useForm({
    defaultValues: {
      reason: "",
      otherReason: "",
      assetType: "",
      assetValue: "",
    },
    resolver: yupResolver<FieldValues>(schema),
  });

  const { reason, otherReason, assetType, assetValue } = watch();
  const isFormValid =
    assetType?.trim() !== "" &&
    assetValue &&
    reason?.trim() !== "" &&
    (reason !== "other" || otherReason);

  const handleGoBack = () => {
    setActiveComp("list");
    setActiveAsset(null);
  };

  const onSubmit = (data: any) => {
    const liquidationFormValues = getValues();
    const { assets } = liquidationFormValues;

    const deletedAssets = assets
      ? assets.filter((asset: any) => asset.status === -1)
      : [];

    const reasonMapping: Record<any, any> = {
      liability: {
        liability: 1,
        donation: 0,
        balance: 0,
        other: 0,
        otherReason: null,
      },
      donation: {
        donation: 1,
        balance: 0,
        other: 0,
        liability: 0,
        otherReason: null,
      },
      balance: {
        balance: 1,
        donation: 0,
        other: 0,
        liability: 0,
        otherReason: null,
      },
      other: {
        other: 1,
        liability: 0,
        balance: 0,
        donation: 0,
        otherReason: data.otherReason,
      },
    };

    const updatedAssets =
      activeAsset !== null
        ? assets.map((asset: any, index: number) => {
            if (asset.id === activeAsset) {
              return {
                ...asset,
                assetType: data.assetType,
                assetValue: data.assetValue,
                otherReason: data.otherReason || "",
                ...reasonMapping[data.reason],
              };
            }

            if (index === activeAsset + deletedAssets.length) {
              return {
                ...asset,
                assetType: data.assetType,
                assetValue: data.assetValue,
                otherReason: data.otherReason || "",
                ...reasonMapping[data.reason],
              };
            }

            return asset;
          })
        : [
            ...(assets ?? []),
            {
              assetType: data.assetType,
              assetValue: data.assetValue,
              otherReason: data.otherReason || "",
              ...reasonMapping[data.reason],
            },
          ];

    liquidationFormValues.assets = updatedAssets;
    updateLiquidation({
      ...liquidationFormValues,
      applicationStatusCode: liquidationDetailData?.applicationStatusCode ?? 1,
    });
  };

  useEffect(() => {
    if (activeAsset === null) return;

    const assets = form.getValues("assets") || [];
    const deletedAssets =
      assets.filter((asset: any) => asset.status === -1) ?? [];
    const assetById = assets.find((asset: any) => asset.id === activeAsset);
    const asset = assetById || assets[activeAsset + deletedAssets.length];

    if (!asset) return;

    const getReasonValue = (asset: any) => {
      if (asset.liability === 1) return "liability";
      if (asset.donation === 1) return "donation";
      if (asset.balance === 1) return "balance";
      return "other";
    };

    setValue("assetType", asset.assetType);
    setValue("assetValue", asset.assetValue);
    setValue("otherReason", asset.otherReason);
    setValue("reason", getReasonValue(asset));
  }, [activeAsset, form, setValue]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "18px 16px",
          borderRadius: "14px",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: "10px",
            padding: "22px 27px",
            border: "0.5px solid #DADADA",
            marginBottom: 1,
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight={500}
            mb="20px"
          >
            {t("maklumatAsset")}
          </Typography>

          <FormFieldRow
            label={
              <Label
                text={
                  <>
                    {t("assetType")}
                    <br />
                    {currentLanguage === "my"
                      ? "(bangunan, tanah dan lain-lain)"
                      : "(buildings, land, etc.)"}
                  </>
                }
                required
              />
            }
            value={<TextFieldController control={control} name="assetType" />}
          />

          <FormFieldRow
            label={<Label text={`${t("value")} (RM)`} required />}
            value={
              <TextFieldController
                control={control}
                type="text"
                name="assetValue"
                isNumber
              />
            }
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text={t("solution")} required />}
            value={
              <>
                <ToggleButtonController
                  name="reason"
                  control={control}
                  options={reasonOptions(currentLanguage)}
                  sx={{
                    gap: 4,
                  }}
                />

                {reason === "other" && (
                  <TextFieldController
                    name="otherReason"
                    control={control}
                    multiline
                    rows={4}
                  />
                )}
              </>
            }
          />
        </Box>

        <Box sx={{ display: "flex", gap: "11px", justifyContent: "flex-end" }}>
          <ButtonOutline onClick={handleGoBack}>{t("back")}</ButtonOutline>

          <ButtonPrimary
            disabled={!isFormValid || isLoadingUpdate}
            type="submit"
          >
            {t("save")}
          </ButtonPrimary>
        </Box>
      </Box>
    </form>
  );
};

export default AssetForm;
