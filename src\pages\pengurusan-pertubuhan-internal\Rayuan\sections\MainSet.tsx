import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import { Box, Typography, IconButton, Grid } from "@mui/material";
import Input from "../../../../components/input/Input";
import { MALAYSIA, ROApprovalType } from "../../../../helpers/enums";
import { getLocalStorage } from "../../../../helpers/utils";
import { EditIcon } from "../../../../components/icons";
import { AppDispatch, RootState } from "../../../../redux/store";
import { useDispatch } from "react-redux";
import { fetchAppealData } from "../../../../redux/APIcalls/appealThunks";
import { useSelector } from "react-redux";
import {
  resetSearchParams,
  setAppealSearchParams,
} from "../../../../redux/appealDataReducer";
import { API_URL } from "../../../../api";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { DataTable } from "@/components";
import { FieldValues, useForm } from "react-hook-form";
import dayjs from "dayjs";

interface FormValues {
  category: string | null;
  subCategory: string | null;
  searchQuery: string | null;
}

interface SectionProps {
  title: string;
  total: number;
  idSebab: any;
  moduleType: string | null;
}

const MainSet: React.FC<SectionProps> = ({
  title,
  total,
  idSebab,
  moduleType,
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1) ?? [];
  const subCategories = categories.filter((cat: any) => cat.level === 2) ?? [];

  const mainCategoriesOptions = mainCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));
  const subCategoriesOptions = subCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  const dispatch: AppDispatch = useDispatch();

  useEffect(() => {
    dispatch(resetSearchParams());
  }, [dispatch]);

  const [formValues, setFormValues] = useState<FormValues>({
    category: null,
    subCategory: null,
    searchQuery: null,
  });

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const {
    data: appealData,
    loading: appealDataLoading,
    searchParams,
  } = useSelector((state: RootState) => state.appealData);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors: { [key in keyof FormValues]?: string } = {};
    setValue("page", 1);
    if (Object.keys(newErrors).length === 0) {
      const updatedFormValues = Object.fromEntries(
        Object.entries(formValues).map(([key, value]) => [
          key,
          typeof value === "string" && value.trim() === "" ? null : value,
        ])
      );

      const mergedSearchParams = { ...searchParams, ...updatedFormValues };
      dispatch(setAppealSearchParams(mergedSearchParams));
    }
  };

  const handleClearSearch = () => {
    setFormValues({
      category: null,
      subCategory: null,
      searchQuery: null,
    });

    const newErrors: { [key in keyof FormValues]?: string } = {};
    if (Object.keys(newErrors).length === 0) {
      const updatedFormValues = Object.fromEntries(
        Object.entries(formValues).map(([key, value]) => [
          key,
          typeof value === "string" && value.trim() === "" ? null : value,
        ])
      );

      const mergedSearchParams = {
        ...searchParams,
        ...{
          category: null,
          subCategory: null,
          searchQuery: null,
        },
      };
      dispatch(setAppealSearchParams(mergedSearchParams));
    }
  };

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  useEffect(() => {
    const defaultParams = { idSebab: idSebab };
    const mergedSearchParams = { ...defaultParams, ...searchParams };
    dispatch(
      fetchAppealData({
        searchParams,
        size: pageSize,
        page: page,
        body: mergedSearchParams,
      })
    );
  }, [searchParams, dispatch, pageSize, page]);

  // @ts-ignore
  const addressDataRedux = useSelector((state) => state?.addressData?.data);
  const StateList = addressDataRedux
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ value: item.id, label: item.name }));

  const columns = [
    {
      field: "societyNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            <Typography sx={{ fontSize: 13, fontWeight: "400 !important" }}>
              {row?.societyNo ? row.societyNo : row?.societyApplicationNo}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "applicantName",
      flex: 1,
      headerName: t("applicant"),
      align: "center",
      headerAlign: "center",
    },
    {
      field: "societyName",
      headerName: t("pertubuhan"),
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "submitDate",
      headerName: t("submissionDate"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        // let row = { submitDate: "07/04/2025" };
        const parsedDate = dayjs(
          row?.submitDate,
          ["YYYY-MM-DD", "DD/MM/YYYY"],
          true
        );
        const isExceeded =
          parsedDate.isValid() && dayjs().diff(parsedDate, "day") > 30;
        return (
          <Box>
            <Typography sx={{ fontSize: 13, fontWeight: "400 !important" }}>
              {parsedDate.isValid() ? parsedDate.format("DD-MM-YYYY") : ""}
            </Typography>
            {isExceeded && (
              <Typography
                sx={{
                  fontSize: 12,
                  color: "var(--error)",
                  fontWeight: "400 !important",
                }}
              >
                {t("Exceededtheperiod")}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: "societyStateCode",
      headerName: t("state"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const state = StateList.find(
          (item: any) => item.value === Number(row?.stateCode)
        );
        return (
          <p
            style={{ fontSize: 14, color: "var(--text-grey)", fontWeight: 400 }}
          >
            {state?.label ? state?.label : "-"}
          </p>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: t("action"),
      headerAlign: "right",
      align: "right",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        const encodedId = btoa(row?.id?.toString() || "");
        const encodedType = btoa(moduleType || "none");
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            <IconButton
              onClick={() => {
                // navigate(
                //   `/pertubuhan/kelulusan/penubuhan-induk/kemaskini/${row.id}/maklumat-am?parent=0&activeMenu=0`
                // );
                navigate(
                  `/pengurus-pertubuhan/keputusan-pertubuhan/rayuan/kelulusan/${encodedId}/${encodedType}`
                );
              }}
              sx={{ color: "black", minWidth: 0, p: 0.5 }}
            >
              <EditIcon
                sx={{
                  color: "var(--primary-color)",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          marginBottom: 1,
        }}
        component="form"
        onSubmit={handleSubmit}
      >
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("listOfApealApplications")}
          </Typography>

          <Input
            value={formValues.category ? formValues.category : ""}
            name="category"
            onChange={handleChange}
            label={t("organizationCategory")}
            options={mainCategoriesOptions}
            type="select"
          />
          <Input
            value={formValues.subCategory ? formValues.subCategory : ""}
            name="subCategory"
            onChange={handleChange}
            label={t("organizationSubCategory2")}
            options={subCategoriesOptions}
            type="select"
          />
          <Input
            value={formValues.searchQuery ? formValues.searchQuery : ""}
            name="searchQuery"
            onChange={handleChange}
            label={t("organizationName")}
          />
          <Grid container mt={3} spacing={2}>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrevious
                variant="outlined"
                sx={{
                  backgroundColor: "white",
                  "&:hover": { backgroundColor: "white" },
                }}
                onClick={handleClearSearch}
              >
                {t("previous")}
              </ButtonPrevious>
              <ButtonPrimary
                type="submit"
                variant="contained"
                sx={{
                  boxShadow: "none",
                }}
              >
                {t("search")}
              </ButtonPrimary>
            </Grid>
          </Grid>
        </Box>
      </Box>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          display: "grid",
          gap: 2,
        }}
      >
        <Box
          sx={{
            textAlign: "center",
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            py: 2,
          }}
        >
          <Typography variant="h5" gutterBottom>
            {total}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
            {title}
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
            overflowX: "auto",
          }}
        >
          <Typography className="title" sx={{ pb: 3 }}>
            {t("listOfApealApplications")}
          </Typography>
          {appealData?.data && (
            <DataTable
              rows={appealData?.data}
              isLoading={appealDataLoading}
              columns={columns as any}
              totalCount={appealData?.total || 0}
              page={page}
              rowsPerPage={pageSize}
              onPageChange={(newPage) => setValue("page", newPage)}
              onPageSizeChange={(newPageSize) =>
                setValue("pageSize", newPageSize)
              }
            />
          )}
        </Box>
      </Box>
    </>
  );
};

export default MainSet;
