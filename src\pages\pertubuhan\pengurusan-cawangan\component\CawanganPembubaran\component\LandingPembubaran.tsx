import { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import useQuery from "@/helpers/hooks/useQuery";
import { FieldValues, useForm } from "react-hook-form";
import { debounce, ApplicationStatusEnum, useQueryFilterForm } from "@/helpers";

import {
  Box,
  Button,
  Divider,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import DataTable, { IColumn } from "@/components/datatable";
import { ButtonPrimary } from "@/components/button";
import FilterBar, { FilterOption } from "@/components/filter";

import SearchIcon from "@mui/icons-material/Search";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { EditIcon, EyeIcon, FilterIcon } from "@/components/icons";
import { ILiquidationFeedbackPaging } from "@/types";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";

const actionIconMap: Record<number, React.ReactNode> = {
  1: <EditIcon />,
  2: <EyeIcon />,
  3: <EyeIcon />,
  4: <EyeIcon />,
  23: <EditIcon />,
  36: <EditIcon />,
};

const statusOptions = [
  { value: 2, label: "Menunggu Keputusan" },
  { value: 1, label: "Belum dihantar" },
  { value: 3, label: "Lulus" },
  { value: 4, label: "Tolak" },
  { value: 5, label: "MENUNGGU_BAYARAN_KAUNTER" },
  { value: 6, label: "MENUNGGU_BAYARAN_ONLINE" },
  { value: 11, label: "AKTIF" },
  { value: 41, label: "MENUNGGU_ULASAN_AGENSI_LUAR" },
];

const LandingPembubaran: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const [availableYear, setAvailableYear] = useState<FilterOption[]>([]);
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    dateApplied: "dateApplied",
    submissionDate: "submissionDate",
    decisionDate: "decisionDate",
    status: "status",
  });

  const formMethods = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      applicantName: undefined,
      createdYear: undefined,
      submissionYear: undefined,
      decisionYear: undefined,
      status: undefined,
    },
  });
  const { setValue, watch } = formMethods;

  watch("applicantName");
  const page = watch("page");
  const pageSize = watch("pageSize");

  const { buildFilters } = useQueryFilterForm({
    formMethods,
    baseFilters: [
      {
        field: "societyId",
        value: id,
        operator: "eq",
      },
      {
        field: "branchLiquidation",
        value: 1,
        operator: "eq",
      },
      {
        field: "branchId",
        value: location.state?.branchId,
        operator: "eq",
      },
    ],
  });

  const filterOptions = {
    dateApplied: availableYear,
    submissionDate: availableYear,
    decisionDate: availableYear,
    status: statusOptions,
  };

  const columns: IColumn[] = [
    { field: "applicantName", headerName: t("namePemohon"), flex: 1 },
    { field: "createdDate", headerName: t("dateApplied"), flex: 1 },
    { field: "submissionDate", headerName: t("submissionDate"), flex: 1 },
    { field: "decisionDate", headerName: t("decisionDate"), flex: 1 },
    {
      field: "applicationStatusCode",
      headerName: "Status",
      flex: 1,
      renderCell: ({ row }) => {
        return ApplicationStatusEnum[row.applicationStatusCode];
        // const isSecretary = row.isSecretary === 1;
        // const hasFeedback = row.feedback !== null;

        // if (isSecretary || hasFeedback) return "MENUNGGU KEPUTUSAN";
        // else return "MAKLUM BALAS";
      },
    },
    {
      field: "actions",
      headerName: "",
      renderCell: ({ row }) => {
        const isSecretary = row.isSecretary === 1;
        const hasFeedback = row.feedback !== null;

        return (
          <IconButton
            onClick={() => {
              if (isSecretary || hasFeedback) {
                navigate(`${row.liquidationId}`);
              } else {
                navigate(`${row.liquidationId}/feedback`);
              }
            }}
            sx={{
              padding: 0,
            }}
          >
            {row ? actionIconMap[row.applicationStatusCode] : null}
          </IconButton>
        );
      },
    },
  ];

  const { data: liquidationListResponse, isLoading: isLoadingLiquidationList } =
    useQuery({
      url: "society/liquidate/paging",
      filters: buildFilters(pageSize, page),
      onSuccess: (data) => {
        const availableYears: string[] =
          data?.data?.data?.availableCreatedYears ?? [];
        const availableYearsOptions = availableYears.map((year) => ({
          value: year,
          label: year,
        }));

        setAvailableYear(availableYearsOptions);
      },
    });

  const totalList = liquidationListResponse?.data?.data?.total ?? 0;
  const liquidationList: ILiquidationFeedbackPaging[] =
    liquidationListResponse?.data?.data?.data ?? [];

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const onFilterChange = (filter: string, value: number | string) => {
    setValue("page", 1);
    switch (filter) {
      case "submissionDate":
        setValue("submissionYear", value);
        break;
      case "dateApplied":
        setValue("createdYear", value);
        break;
      case "decisionDate":
        setValue("decisionYear", value);
        break;
      case "status":
        setValue("status", value);
        break;
    }
  };

  const handleChangePage = (newPage: number) => setValue("page", newPage);

  const handleChangePageSize = (newPageSize: number) =>
    setValue("pageSize", newPageSize);

  const handleSearchApplicantName = useCallback(
    debounce(
      ({
        target: { value },
      }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setValue("applicantName", value || undefined);
      },
      1000
    ),
    []
  );

  const isManager = useSelector(getUserPermission);

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "45px 35px",
          borderRadius: "15px",
          marginBottom: 1,
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        <TextField
          fullWidth
          variant="outlined"
          placeholder={t("namePemohon")}
          sx={{
            display: "block",
            boxSizing: "border-box",
            width: "90%",
            height: "40px",
            marginInline: "auto",
            marginTop: "12px",
            background: "rgba(132, 132, 132, 0.3)",
            opacity: 0.5,
            border: "1px solid rgba(102, 102, 102, 0.8)",
            borderRadius: "10px",
            "& .MuiOutlinedInput-root": {
              height: "40px",
              "& fieldset": {
                border: "none",
              },
            },
          }}
          onChange={handleSearchApplicantName}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: "#9CA3AF", marginLeft: "8px" }} />
              </InputAdornment>
            ),
          }}
        />

        <FilterBar
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          selectedFilters={selectedFilters}
          onSelectedFiltersChange={handleSelectedFiltersChange}
        />

        <DataTable
          columns={columns}
          rows={liquidationList}
          page={page}
          rowsPerPage={pageSize}
          totalCount={totalList}
          onPageChange={handleChangePage}
          onPageSizeChange={handleChangePageSize}
          isLoading={isLoadingLiquidationList}
        />
      </Box>
      {isManager ? (
        <Box
          sx={{
            backgroundColor: "white",
            padding: "13px 16px",
            borderRadius: "15px",
            marginBottom: 1,
            boxShadow: "0px 12px 12px 0px #EAE8E866",
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: "10px",
              border: "0.5px solid #DADADA",
              padding: "22px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("liquidation")}
            </Typography>

            <Box
              sx={{
                width: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Typography
                fontSize="14px"
                color="#666666"
                fontWeight="400 !important"
              >
                {t("applicationForDissolution")}
              </Typography>

              <ButtonPrimary
                onClick={() =>
                  navigate(`create`, {
                    state: location.state,
                  })
                }
                sx={{
                  display: "block",
                  backgroundColor: "var(--primary-color)",
                  width: "100px",
                  minWidth: "unset",
                  height: "32px",
                  color: "white",
                  "&:hover": { backgroundColor: "#19ADAD" },
                  textTransform: "none",
                  fontWeight: 400,
                  fontSize: "14px",
                }}
              >
                {t("mohon")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      ) : (
        <></>
      )}
    </>
  );
};

export default LandingPembubaran;
