import { object, string } from "yup";
import { toBase64, useQuery } from "@/helpers";
import { type CrudFilter, useNotification } from "@refinedev/core";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

import { Box } from "@mui/material"
import { Formik, type FormikHelpers } from "formik";
import { FormEnforcementLiabilityRestrictionCheckNameOrMyKad } from "@/components/form/enforcement";

export interface EnforcementLiabilityRestrictionRequestBodyCheck {
  name: string
  identificationNo: string
}

interface UserAuthValidateIdResponseBodyGet {
  name: boolean
  userExist: boolean
  status: "Y" | "N"
}

interface SocietyBlacklistIsUserRegisteredInSocietyResponseBodyGet {
  usernameInSocietyCommittee: string | null
  usernameInBranchCommittee: string | null
  existActiveUserInActiveSociety: boolean
  existActiveUserInActiveBranch: boolean
}

export const PenguatkuasaanInternalSekatanLiabilitiCreate = <
  RequestBody extends EnforcementLiabilityRestrictionRequestBodyCheck = EnforcementLiabilityRestrictionRequestBodyCheck,
  ValidateIdResponse extends UserAuthValidateIdResponseBodyGet = UserAuthValidateIdResponseBodyGet,
  BlackListResponse extends SocietyBlacklistIsUserRegisteredInSocietyResponseBodyGet = SocietyBlacklistIsUserRegisteredInSocietyResponseBodyGet
>() => {
  const { open } = useNotification();
  const { t } = useTranslation();
  const [requestBody, setRequestBody] = useState<RequestBody | null>(null);
  const navigate = useNavigate();

  const {
    refetch: checkUserRegisteredInSociety
   } = useQuery<{ data: BlackListResponse }>({
    url: 'society/blacklist/isUserRegisteredInSociety',
    autoFetch: false,
    onSuccess (data) {
      const registeredResponse = data.data.data
      /**
       * @todo for now it only check the society, the future will check both society and branch.
       */
      if (!registeredResponse.existActiveUserInActiveSociety) {
        open?.({
          type: "error",
          message: t("inputValidationErrorIdentityCardNumberAndNameAreNotInTheRecords")
        })
      } else {
        const id = Math.floor(Math.random() * 1000_000)
        const encodedId = toBase64(id.toString())
        const state = requestBody?.name
          ? requestBody
          : {
            ...requestBody,
            name: registeredResponse.usernameInSocietyCommittee
          }
        navigate(encodedId, { state, relative: "path" })
      }
    }
  })
  const {
    refetch: validateIdentity
  } = useQuery<{ data: ValidateIdResponse }>({
    url: "user/auth/validateId",
    autoFetch: false,
    withAuthHeaders: false,
    async onSuccess(data) {
      const validateIdData = data.data.data
      if (!(validateIdData.name && validateIdData.status === "Y")) {
        open?.({
          type: "error",
          message: t("inputValidationErrorIdentityCardNumberAndNameAreNotInTheRecords")
        })
      } else {
        await checkUserRegisteredInSociety({
          filters: [
            {
              field: "identificationNo",
              operator: "eq",
              value: requestBody?.identificationNo!
            }
          ]
        })
      }
    },
  })

  const handleSubmit = async (
    payload: RequestBody,
    { setSubmitting }: FormikHelpers<RequestBody>
  ) => {
    setSubmitting(true)
    try {
      setRequestBody(payload);
      if (payload.name) {
        const identificationNoFilter: CrudFilter = {
          field: "identificationNo",
          operator: "eq",
          value: payload.identificationNo
        }
        await validateIdentity({
          filters: [
            {
              field: "name",
              operator: "eq",
              value: payload.name.trim().toUpperCase()
            },
            identificationNoFilter
          ]})
      } else {
        await checkUserRegisteredInSociety({
          filters: [
            {
              field: "identificationNo",
              operator: "eq",
              value: payload.identificationNo
            }
          ]
        })
      }
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Box
      sx={{
        marginTop: "0.5rem",
        backgroundColor: "white",
        borderRadius: "1rem",
        padding: 2,
        boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)"
      }}
    >
      <Formik<RequestBody>
        initialValues={{ name: "", identificationNo: "" } as RequestBody}
        onSubmit={handleSubmit}
        validationSchema={object().shape({
          identificationNo: string().min(1).required(),
          name: string().notRequired(),
        }).required()}
        isInitialValid={false}
      >
        <FormEnforcementLiabilityRestrictionCheckNameOrMyKad />
      </Formik>
    </Box>
  )
}
