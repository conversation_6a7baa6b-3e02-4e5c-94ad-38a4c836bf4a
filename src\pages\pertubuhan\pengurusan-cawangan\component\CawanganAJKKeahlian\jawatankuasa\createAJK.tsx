/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import { Box, Typography, Grid, styled } from "@mui/material";
import { useTranslation } from "react-i18next";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import ButtonPrimary from "@/components/button/ButtonPrimary";
import { ButtonOutline } from "@/components/button";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import Input from "@/components/input/Input";
import { Controller, useForm } from "react-hook-form";
import useMutation from "@/helpers/hooks/useMutation";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
  OrganisationPositions,
} from "@/helpers/enums";
import { DocumentUploadType, getLocalStorage, useQuery } from "@/helpers";
import dayjs from "dayjs";
import FileUploader from "@/components/input/fileUpload";
import { useSelector } from "react-redux";

interface ICommitte {
  jobCode: string;
  branchId: number;
  branchNo: string;
  titleCode: string;
  committeeName: string;
  gender: string;
  nationalityStatus: string;
  identityType: string;
  committeeIcNo: string;
  dateOfBirth: string; // Format: YYYY-MM-DD
  placeOfBirth: string;
  designationCode: string;
  otherDesignationCode: string;
  committeeEmployerAddressStatus: string;
  committeeEmployerName: string;
  committeeEmployerAddress: string;
  committeeEmployerPostcode: string;
  employerCountryCode: string;
  committeeEmployerStateCode: string;
  committeeEmployerCity: string;
  committeeEmployerDistrict: string;
  committeeAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: string;
  residentialCountryCode: string;
  committeeStateCode: string;
  committeeDistrict: string;
  committeeCity: string;
  email: string;
  telephoneNumber: string;
  phoneNumber: string;
  noTelP: string;
  status: number;
  applicationStatusCode: string;
  pegHarta: string;
  tarikhTukarSu: string; // Format: YYYY-MM-DD
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: boolean;
  benarAjk: boolean;
  id: string;
}

export const CreateAjk: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [params] = useSearchParams();
  const encodedId = params.get("id");

  const occupationList = getLocalStorage("occupation_list", []);
  const { id } = useParams();

  const value = params.get("value")
    ? JSON.parse(params.get("value") || "")
    : {};

  const handleSenaraiAjk = () => {
    navigate(-1);
  };
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];

  const defaultValue = {
    jobCode: "",
    branchId: branchDataRedux.id,
    branchNo: branchDataRedux.branchNo,
    titleCode: "",
    committeeName: "",
    gender: "",
    nationalityStatus: "",
    identityType: "",
    committeeIcNo: "",
    dateOfBirth: "",
    placeOfBirth: "",
    designationCode: "",
    otherDesignationCode: "",
    committeeEmployerAddressStatus: "",
    committeeEmployerName: "",
    committeeEmployerAddress: "",
    employerCountryCode: "",
    committeeEmployerStateCode: "",
    committeeEmployerCity: "",
    committeeEmployerDistrict: "",
    committeeAddress: "",
    residentialPostcode: "",
    residentialAddressStatus: "",
    residentialCountryCode: "",
    committeeStateCode: "",
    committeeDistrict: "",
    committeeCity: "",
    email: "",
    telephoneNumber: "",
    phoneNumber: "",
    noTelP: "",
    status: "",
    applicationStatusCode: "",
    pegHarta: "",
    tarikhTukarSu: "",
    otherPosition: "",
    batalFlat: false,
    blacklistNotice: false,
    benarAjk: true,
  };

  const location = useLocation();
  const ajk: ICommitte = location.state?.ajk;
  const meetingData = location.state.meetingData; 
  const form = useForm<ICommitte | any>();
  const [ajkId, setAjkId] = useState<number>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    watch,
    setValue,
    getValues,
    reset,
  } = form;

  const mutate = useMutation({
    url: "society/committee/draft/create",
    onSuccess: (response) => {
      const ajkId = response.data.data.id;
      setAjkId(ajkId);
      // navigate(-1)
    },
  });

  const { mutate: updateAJK, isLoading: isUpdateAJK } = useCustomMutation();

  const view = location.state?.view;

  useEffect(() => {
    if (ajk) {
      setAjkId(parseInt(ajk.id));
      Object.entries(ajk).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, []);

  const onSubmit = (data: any) => {
    if (ajkId) {
      updateAJK(
        {
          url: `${API_URL}/society/committee/draft/update`,
          method: "put",
          values: {
            ...data,
            nationalityStatus: 1,
            identityType: "1",
            updateMeetingId: meetingData.id,
            appointedDate: meetingData.meetingDate,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            return {
              message: data?.data?.msg,
              type: "success",
            };
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            // navigate(-1)
          },
        }
      );
    } else {
      mutate.fetch({
        ...defaultValue,
        ...data,
        branchId: branchDataRedux?.id,
        nationalityStatus: 1,
        identityType: "1",
      });
    }
  };

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <form
          style={{
            borderRadius: "14px",
            width: "100%",
          }}
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box
            sx={{
              mb: 3,
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                fontWeight: "bold",
                fontSize: 14,
                color: "var(--primary-color)",
                pl: 2,
              }}
            >
              {t("positionInfo")}
            </Typography>

            <Box sx={{ pl: 2 }}>
              <Grid container>
                <Controller
                  name="designationCode"
                  rules={{
                    required: "Medan ini diperlukan",
                  }}
                  defaultValue={parseInt(getValues("designationCode"))}
                  control={control}
                  render={({ field }) => {
                    return (
                      <Input
                        disabled={view}
                        required
                        {...field}
                        type="select"
                        label={t("position")}
                        error={!!errors.designationCode?.message}
                        value={parseInt(getValues("designationCode"))}
                        options={OrganisationPositions.map((position) => ({
                          ...position,
                          label: t(position.label),
                        }))}
                      />
                    );
                  }}
                />
              </Grid>
            </Box>
          </Box>

          <Box
            sx={{
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Box
              sx={{
                px: 2,
                py: 1,
                mb: 3,
                borderRadius: "16px",
              }}
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("personalInfo")}
              </Typography>
            </Box>

            <Box sx={{ pl: 2 }}>
              <Controller
                name="identityType"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      error={!!errors.identityType?.message}
                      label={t("idType")}
                      options={IdTypes}
                      value={t(
                        `${
                          IdTypes.find(
                            (item) => item.value === watch("identityType")
                          )?.label || ""
                        }`
                      )}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                name="committeeIcNo"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      label={t("idNumber")}
                      error={!!errors.committeeIcNo?.message}
                    />
                  );
                }}
              />
              <Controller
                name="titleCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                control={control}
                defaultValue={getValues("titleCode")}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!errors.otherPosition?.message}
                      label={t("title")}
                      type="select"
                      options={ListGelaran}
                      value={getValues("titleCode")}
                    />
                  );
                }}
              />
              <Controller
                name="committeeName"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!errors.committeeName?.message}
                      label={t("fullName")}
                    />
                  );
                }}
              />
              <Controller
                name="gender"
                control={control}
                defaultValue={getValues("gender")}
                render={({ field }) => (
                  <Input
                    disabled={view}
                    {...field}
                    required
                    label={t("gender")}
                    type="select"
                    options={ListGender.map((gender) => ({
                      ...gender,
                      label: t(gender.label),
                    }))}
                  />
                )}
              />
              <Controller
                name="nationalityStatus"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      error={!!errors.nationalityStatus?.message}
                      value={Number(watch("nationalityStatus"))}
                      label={t("citizenship")}
                      options={CitizenshipStatus.map((item) => ({
                        ...item,
                        label: t(item.label),
                      }))}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                name="dateOfBirth"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      label={t("dateOfBirth")}
                      type="date"
                      onChange={(newValue) =>
                        setValue("dateOfBirth", newValue.target.value)
                      }
                      value={
                        getValues("dateOfBirth")
                          ? dayjs(getValues("dateOfBirth")).format("DD-MM-YYYY")
                          : ""
                      }
                    />
                  );
                }}
              />
              <Controller
                name="placeOfBirth"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      label={t("placeOfBirth")}
                    />
                  );
                }}
              />
              <Controller
                name="jobCode"
                defaultValue={getValues("jobCode")}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!errors.jobCode?.message}
                      label={t("occupation")}
                      type="select"
                      value={getValues("jobCode")}
                      options={occupationList}
                    />
                  );
                }}
              />
              <Controller
                name="committeeAddress"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!errors.committeeAddress?.message}
                      label={t("residentialAddress")}
                    />
                  );
                }}
              />

              <Controller
                name="committeeStateCode"
                control={control}
                defaultValue={getValues("committeeStateCode")}
                render={({ field }) => (
                  <Input
                    disabled={view}
                    required
                    {...field}
                    label={t("state")}
                    type="select"
                    onChange={(e) =>
                      setValue("committeeStateCode", e.target.value)
                    }
                    value={parseInt(getValues("committeeStateCode"))}
                    options={addressData
                      .filter((item: any) => item.pid === MALAYSIA)
                      .map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                  />
                )}
              />

              <Controller
                name="committeeDistrict"
                control={control}
                defaultValue={getValues("committeeDistrict")}
                render={({ field }) => (
                  <Input
                    disabled={view}
                    {...field}
                    required
                    label={t("district")}
                    type="select"
                    value={parseInt(getValues("committeeDistrict"))}
                    onChange={(e) =>
                      setValue("committeeDistrict", e.target.value)
                    }
                    options={addressData
                      .filter(
                        (item: any) =>
                          item.pid === parseInt(watch("committeeStateCode"))
                      )
                      .map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                  />
                )}
              />

              <Controller
                name="committeeCity"
                control={control}
                render={({ field }) => {
                  return <Input {...field} disabled={view} label={t("city")} />;
                }}
              /> 
              <Controller
                name="postcode"
                rules={{
                  required: t("idNumberRequired"),
                  maxLength: {
                    value: 5,
                    message: t("postcodeHelper"),
                  },
                  pattern: {
                    value: /^\d+$/,
                    message: t("numbersOnly"),
                  },
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      onChange={(e) => {
                        if (/^\d{0,5}$/.test(e.target.value)) {
                          field?.onChange(e.target.value);
                        }
                      }}
                      error={!!errors.postcode?.message}
                      label={t("postcode")}
                    />
                  );
                }}
              />

              <Controller
                name="email"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!errors.email?.message}
                      label={t("email")}
                      type="email"
                    />
                  );
                }}
              />

              <Controller
                name="phoneNumber"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      required
                      error={!!errors.phoneNumber?.message}
                      label={t("phoneNumber")}
                      inputMode={"numeric"}
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        input.value = input.value
                          .replace(/\D/g, "")
                          .slice(0, 12);
                      }}
                    />
                  );
                }}
              />

              <Controller
                name="telephoneNumber"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.telephoneNumber?.message}
                      label={t("homeNumber")}
                      inputMode={"numeric"}
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        input.value = input.value
                          .replace(/\D/g, "")
                          .slice(0, 12);
                      }}
                    />
                  );
                }}
              />

              <Controller
                name="noTelP"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.noTelP?.message}
                      label={t("officeNumber")}
                      inputMode={"numeric"}
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        input.value = input.value
                          .replace(/\D/g, "")
                          .slice(0, 12);
                      }}
                    />
                  );
                }}
              />
            </Box>
          </Box>

          <Box
            sx={{
              mt: 3,
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Box
              sx={{
                display: "flex",
                // justifyContent: "space-between",
                // backgroundColor: "#e0f2f1",
                px: 2,
                py: 1,
                mb: 3,
                borderRadius: "16px",
              }}
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("employerInfo")}
              </Typography>
            </Box>

            <Box sx={{ pl: 2 }}>
              <Controller
                name="committeeEmployerName"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      error={!!errors.committeeEmployerName?.message}
                      label={t("committeeEmployerName")}
                      disabled={
                        watch("jobCode") == "Pesara" ||
                        watch("jobCode") == "Tidak Bekerja" ||
                        view
                      }
                    />
                  );
                }}
              />

              <Controller
                name="committeeEmployerAddressStatus"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.committeeEmployerAddressStatus?.message}
                      label={t("employerAddress")}
                      type={"select"}
                      options={[
                        {
                          value: "dalam",
                          label: "Dalam Negara",
                        },
                        {
                          value: "luar",
                          label: "Luar Negara",
                        },
                      ]}
                    />
                  );
                }}
              />
              {watch("committeeEmployerAddressStatus") !== "dalam" ? (
                <Controller
                  name="employerCountryCode"
                  control={control}
                  render={({ field }) => {
                    return (
                      <Input
                        {...field}
                        error={!!errors.employerCountryCode?.message}
                        label={t("country")}
                        type="select"
                        value={parseInt(getValues("employerCountryCode"))}
                        disabled={
                          watch("jobCode") == "Pesara" ||
                          watch("jobCode") == "Tidak Bekerja" ||
                          view
                        }
                        options={addressData
                          .filter((item: any) => item.pid === 0)
                          .map((item: any) => ({
                            label:
                              item.name.charAt(0) +
                              item.name.slice(1).toLowerCase(),
                            value: item.id,
                          }))}
                      />
                    );
                  }}
                />
              ) : null}

              <Controller
                name="committeeEmployerAddress"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      error={!!errors.committeeEmployerAddress?.message}
                      multiline
                      rows={4}
                      disabled={
                        watch("jobCode") == "Pesara" ||
                        watch("jobCode") == "Tidak Bekerja" ||
                        view
                      }
                    />
                  );
                }}
              />

              {watch("committeeEmployerAddressStatus") === "dalam" && (
                <>
                  <Controller
                    name="committeeEmployerStateCode"
                    control={control}
                    render={({ field }) => {
                      return (
                        <Input
                          {...field}
                          disabled={
                            watch("jobCode") == "Pesara" ||
                            watch("jobCode") == "Tidak Bekerja" ||
                            view
                          }
                          error={!!errors.committeeEmployerStateCode?.message}
                          label={t("state")}
                          type="select"
                          fullWidth
                          multiline
                          rows={3}
                          options={addressData
                            ?.filter((item: any) => item.pid == MALAYSIA)
                            .map((item: any) => ({
                              label: item.name,
                              value: "" + item.id,
                            }))}
                        />
                      );
                    }}
                  />

                  <Controller
                    name="committeeEmployerDistrict"
                    control={control}
                    render={({ field }) => {
                      return (
                        <Input
                          {...field}
                          error={!!errors.committeeEmployerDistrict?.message}
                          label={t("district")}
                          disabled={
                            watch("jobCode") == "Pesara" ||
                            watch("jobCode") == "Tidak Bekerja" ||
                            view
                          }
                          type="select"
                          fullWidth
                          multiline
                          rows={3}
                          options={addressData
                            ?.filter(
                              (item: any) =>
                                item.pid == watch("committeeEmployerStateCode")
                            )
                            .map((item: any) => ({
                              label: item.name,
                              value: "" + item.id,
                            }))}
                        />
                      );
                    }}
                  />

                  <Controller
                    name="committeeEmployerCity"
                    control={control}
                    render={({ field }) => {
                      return (
                        <Input
                          {...field}
                          error={!!errors.committeeEmployerCity?.message}
                          label={t("city")}
                          disabled={
                            watch("jobCode") == "Pesara" ||
                            watch("jobCode") == "Tidak Bekerja" ||
                            view
                          }
                        />
                      );
                    }}
                  />

                  <Controller
                    name="committeeEmployerPostcode"
                    rules={{
                      maxLength: {
                        value: 5,
                        message: t("postcodeHelper"),
                      },
                      pattern: {
                        value: /^\d+$/,
                        message: t("numbersOnly"),
                      },
                    }}
                    control={control}
                    render={({ field }) => {
                      return (
                        <Input
                          disabled={view}
                          {...field}
                          onChange={(e) => {
                            if (/^\d{0,5}$/.test(e.target.value)) {
                              field?.onChange(e.target.value);
                            }
                          }}
                          error={!!errors.committeeEmployerPostcode?.message}
                          label={t("postcode")}
                        />
                      );
                    }}
                  />
                </>
              )}
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                mt: 2,
                gap: 2,
              }}
            >
              {!view ? (
                <ButtonPrimary
                  disabled={isUpdateAJK || mutate.isLoading}
                  type="submit"
                >
                  {t("save")}
                </ButtonPrimary>
              ) : null}
            </Box>
          </Box>

          {!view
            ? // <DocumentUpload
              //   ajkId={ajkId ? ajkId : 0} // Pass in the AJK id here
              //   id={id ? parseInt(id) : 0} // Pass in the society id
              //   documentType={DocumentUploadType.CITIZEN_COMMITTEE}
              // />
              watch("committeeIcNo") && (
                <FileUploader
                  title="ajkEligibilityCheck"
                  type={DocumentUploadType.CITIZEN_COMMITTEE}
                  societyId={parseInt(id ? id : "")}
                  icNo={watch("committeeIcNo")}
                  validTypes={[
                    "application/pdf",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/msword",
                    "text/plain",
                  ]}
                />
              )
            : null}

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
              gap: 2,
            }}
          >
            <ButtonOutline onClick={handleSenaraiAjk}>
              {t("back")}
            </ButtonOutline>
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default CreateAjk;
