import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setPositionListDataRedux, setPositionListError, setPositionListLoading } from '../positionListDataReducer';

export const fetchPositionListData = createAsyncThunk(
  'positionList/fetchData',
  async (_, { dispatch }) => {
    dispatch(setPositionListLoading(true));
    try {
      const response = await fetch(`${API_URL}/society/admin/jppmPostion/list`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();
      dispatch(setPositionListDataRedux(data.data));
    } catch (error: any) {
      dispatch(setPositionListError(error.message));
    } finally {
      dispatch(setPositionListLoading(false));
    }
  }
);
