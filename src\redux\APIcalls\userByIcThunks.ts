import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setUserByIcDataRedux, setUserByIcError, setUserByIcLoading } from '../userByIcDataReducer';

export const fetchUserByIcData = createAsyncThunk(
  'userByIc/fetchData',
  async ({ id }: { id: string },
    { dispatch }) => {
    dispatch(setUserByIcLoading(true));
    try {
      const response = await fetch(`${API_URL}/society/user/getUserByCriteria?identificationNo=${id}&userGroup=1`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
      const data = await response.json();
      dispatch(setUserByIcDataRedux(data?.body?.[0]));
    } catch (error: any) {
      dispatch(setUserByIcError(error.message));
    } finally {
      dispatch(setUserByIcLoading(false));
    }
  }
);
