import Box from "@mui/material/Box";
import { useTranslation } from "react-i18next";
import { Grid, Typography, Stack, Checkbox } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import FileUploader from "@/components/input/fileUpload";
import { ApplicationStatus, DocumentUploadType, useQuery } from "@/helpers";
import CreatePernyataanPendapatanMaklumatAm from "../pernyata-pendapatan/CreatePernyataPendapatanMaklumatAm";
import { useState } from "react";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import { ButtonPrimary } from "@/components";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CreateLaporanAktivitiCawangan = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();
  const location = useLocation();

  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const financialStatementId = location.state?.societyId;

  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const [statementComplete, setStatementComplete] = useState(false);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const [checked, setChecked] = useState(false);
  const [uploadedIds, setUploadedIds] = useState<string[]>([]);
  const [oldFiles, setOldFiles] = useState<string[]>([]);

  const handleNextActions = () => {
    handleNext();
    navigate(`../sumbangan`, {
      state: {
        societyId: societyId,
        statementId: statementId,
        financialStatementId: financialStatementId,
        year: year,
      },
    });
  };

  const downloadFile = () => {
    const url = "https://files.fm/SELAMAT4NGO/f/ess3ra46u";
    fetch(url)
      .then((res) => res.blob())
      .then((blob) => {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = "Templat Penyata Kewangan.pdf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      })
      .catch(console.error);
    window.open(url, "_blank", "noreferrer,noopener");
  };

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  const { data, isLoading, refetch } = useQuery({
    url: `society/statement/general/get`,
    filters: [
      {
        field: "statementId",
        value: statementId,
        operator: "eq",
      },
      {
        field: "societyId",
        value: societyId,
        operator: "eq",
      },
      {
        field: "branchId",
        value: branchDataRedux.id,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      setChecked(data?.data?.data?.akuanLaporanAktiviti);
    },
  });

  const { mutate: update, isLoading: isLoadingUpdate } = useCustomMutation();
  const Update = (data: any, checkedValue: boolean): void => {
    update(
      {
        url: `${API_URL}/society/statement/general/update`,
        method: "put",
        values: {
          statementId: data?.id,
          akuanLaporanAktiviti: checkedValue,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            refetch();
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleCheck = (checkboxValue: boolean) => {
    Update(data?.data?.data, checkboxValue);
    setChecked(checkboxValue);
  };

  const handleUploadComplete = (id: string) => {
    setUploadedIds((prev) => (prev.includes(id) ? prev : [...prev, id]));
  };
  const handleOldUploadedFiles = (files: any) => {
    setOldFiles(files);
  };

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <span style={{ fontSize: 12 }}>{t("incomeStatementInfo")}</span>
            <br />
            <br />
            <span style={{ fontSize: 12 }}>{t("incomeStatementInfo2")}</span>
          </Typography>
        </Box>
      </Box>

      <CreatePernyataanPendapatanMaklumatAm year={year} t={t} />

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Grid
            container
            spacing={10}
            alignItems="center"
            sx={{ mb: checked ? 1 : 0 }}
          >
            <Grid item sm={10}>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: "400 !important",
                }}
              >
                <Typography style={{ fontWeight: 400, fontSize: 14 }}>
                  {t("recordOfActivityAvailable")}
                </Typography>
              </Typography>
            </Grid>
            <Grid
              item
              sm={2}
              sx={{ display: "flex", justifyContent: "flex-end" }}
            >
              <Checkbox
                checked={checked}
                onChange={(e) => handleCheck(e.target.checked)}
              />
            </Grid>
          </Grid>
          {checked ? (
            <Typography sx={{ fontWeight: 400, fontSize: 14, color: "red" }}>
              {t("completeReportUpload")}
            </Typography>
          ) : null}
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            display: "grid",
            p: 3,
            py: 2,
            mb: 2,
            gap: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              borderRadius: "16px",
              fontSize: "16px",
              fontWeight: "500 !important",
            }}
          >
            {t("updateActivityReport")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ mt: 2 }}>
                <Typography sx={labelStyle}>
                  {t("organizationalActivitiesAppendix")}{" "}
                  {checked ? <span style={{ color: "red" }}>*</span> : null}
                </Typography>
                <Typography
                  sx={{
                    ...labelStyle,
                    mt: 3,
                    color: "var(--link)",
                    textDecoration: "link",
                    cursor: "pointer",
                  }}
                  onClick={downloadFile}
                >
                  {t("downloadReference")}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FileUploader
                // title="addSupportingDocument"
                type={DocumentUploadType.ACTIVITY_REPORT_STATEMENT}
                societyId={societyId}
                statementId={statementId}
                sxContainer={{
                  border: "2px dashed #ccc",
                  background: "#fff",
                  mb: 3,
                }}
                onUploadComplete={handleUploadComplete}
                hasOldFiles={handleOldUploadedFiles}
                disabled={isDisabled || statementComplete || !checked}
                required={checked}
                maxFileSize={20 * 1024 * 1024}
                validTypes={[
                  // "text/plain",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "application/pdf",
                ]}
              />
            </Grid>
          </Grid>
        </Box>

        <Stack
          direction="row"
          spacing={2}
          sx={{ pl: 1, mt: 2 }}
          justifyContent="flex-end"
        >
          <ButtonPrimary
            disabled={uploadedIds.length === 0 && oldFiles.length === 0}
            onClick={handleNextActions}
          >
            {t("next")}
          </ButtonPrimary>
        </Stack>
      </Box>
    </>
  );
};

export default CreateLaporanAktivitiCawangan;
