import { <PERSON>, Grid, TextField, Typography } from "@mui/material";
import { t } from "i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { allAjkListData } from "@/redux/ajkReducer";
import { CitizenshipStatus, designation, ListGender } from "@/helpers/enums";
import { formatDate, useQuery } from "@/helpers";
import { getSocietyDataRedux } from "@/redux/societyDataReducer";

const labelStyle = {
  fontWeight: "400!important",
  marginBottom: "8px",
  fontSize: "14px",
  color: "#666666",
};

function SenaraiAjkSemak() {
  const ajkData = useSelector(allAjkListData);
  const societyData = useSelector(getSocietyDataRedux);

  const { id } = useParams();
  const navigate = useNavigate();
  const goback = () => {
    navigate(-1);
  };

  const rowHeight = "80px";

  const {
    data: exportAjkData,
    isLoading: isLoadingExportAjk,
    refetch: exportAjkRefetch,
  } = useQuery({
    url: `society/committee/exportAjk`,
    autoFetch: false,
    onSuccess: (data) => {
      const link = data?.data?.data;
      downloadFile(link);
    },
  });

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  //
  const {
    data: exportAjkGeneral,
    isLoading: isLoadingExportAjkGeneral,
    refetch: exportAjkGeneralFetch,
  } = useQuery({
    url: `society/committee/exportAjkGeneral`,
    autoFetch: false,
    onSuccess: (data) => {
      const link = data?.data?.data;
      downloadFile(link);
    },
  });

  const exportUmum = () => {
    exportAjkGeneralFetch({
      filters: [
        {
          field: "societyId",
          value: id,
          operator: "eq",
        },
      ],
    });
  };

  const exportAjk = () => {
    exportAjkRefetch({
      filters: [
        {
          field: "societyId",
          value: id,
          operator: "eq",
        },
      ],
    });
  };

  const getGenderLabel = (val: string) => {
    if (!val) return "-";
    const genderItem = ListGender?.find((item) => item.value === val) ?? null;
    return genderItem?.label ? t(genderItem.label) : "-";
  }

  const getCitizenshipStatus = (val: string) => {
    if (!val) return "-";
    const citizenshipItem = CitizenshipStatus?.find((item) => item.value === parseInt(val)) ?? null;
    return citizenshipItem?.label ? t(citizenshipItem.label) : "-";
  }

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            px: 2,
            py: 4,
            display: "grid",
            gap: 1,
          }}
        >
          <Typography sx={{ fontWeight: "400!important" }}>
            {societyData?.societyName}
          </Typography>
          <Typography sx={{ fontWeight: "400!important" }}>
            {societyData?.societyNo ?? societyData?.applicationNo}
          </Typography>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            pl: 2,
            p: 3,
            mt: 1,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Box
            sx={{
              mb: 3,
            }}
          >
            <Typography color={"primary"} sx={{ fontWeight: "500!important" }}>
              {t("viewAjk")}
            </Typography>
          </Box>
          <Box>
            <Grid container spacing={2} sx={{ pb: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography sx={labelStyle}>{t("organizationName")}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  value={societyData?.societyName ?? "-"}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography sx={labelStyle}>
                  {t("organizationNumber2")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  value={societyData?.societyNo ?? societyData?.applicationNo ?? "-"}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>

        <Box
          sx={{
            pl: 2,
            p: 3,
            mt: 1,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Box
            sx={{
              mb: 3,
            }}
          >
            <Typography color={"primary"} sx={{ fontWeight: "500!important" }}>
              {t("viewAjk")}
            </Typography>
          </Box>
          <Box
            sx={{
              pl: 2,
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box sx={{ display: "flex", width: "100%", overflow: "scroll" }}>
              {/* Labels */}
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  textAlign: "center",
                  borderRight: "1px solid #DADADA",
                  flexShrink: 0,
                }}
              >
                {[
                  { label: t("jawatan"), key: "designationCode" },
                  { label: t("namaPenuh"), key: "fullName" },
                  { label: t("gender"), key: "gender" },
                  { label: t("idNumberPlaceholder"), key: "idNumber" },
                  { label: t("citizen"), key: "citizen" },
                  { label: t("dateAndBirthPlace"), key: "dobAndPob" },
                  { label: t("phone"), key: "phone" },
                  { label: t("pekerjaan"), key: "occupation" },
                  { label: t("residentialAddress"), key: "residentialAddress" },
                  { label: t("employerAddress"), key: "employerAddress" },
                ].map((row) => (
                  <Box
                    key={row.key}
                    sx={{
                      ...labelStyle,
                      fontWeight: "500!important",
                      fontSize: "14px",
                      color: "#666666",
                      minHeight: rowHeight,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: "0 20px",
                    }}
                  >
                    {row.label}
                  </Box>
                ))}
              </Box>

              {/* Data Columns */}
              <Box sx={{ display: "flex" }}>
                {ajkData?.length > 0 &&
                  ajkData.map((item: any, colIndex: number) => {
                    const {
                      id,
                      name,
                      gender,
                      identificationNo,
                      nationalityStatus,
                      dateOfBirth,
                      phoneNumber,
                      jobCode,
                      residentialAddress,
                      designationCode,
                      employerAddress,
                    } = item;

                    const rowData = [
                      designationCode
                        ? designation[
                            designationCode as keyof typeof designation
                          ]?.name ?? "-"
                        : "-",
                      name,
                      getGenderLabel(gender),
                      identificationNo,
                      getCitizenshipStatus(nationalityStatus),
                      formatDate(dateOfBirth),
                      phoneNumber,
                      jobCode,
                      residentialAddress,
                      employerAddress ?? "-",
                    ];

                    return (
                      <Box
                        key={id}
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          textAlign: "center",
                          borderRight:
                            colIndex === ajkData.length - 1
                              ? "none"
                              : "1px solid #DADADA",
                          flex: 1,
                          minWidth: 0,
                        }}
                      >
                        {rowData.map((value, index) => (
                          <Box
                            key={index}
                            sx={{
                              ...labelStyle,
                              padding: "0 30px",
                              minHeight: rowHeight,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              whiteSpace: index >= 8 ? "pre-wrap" : "nowrap",
                              wordBreak: "break-word",
                              color:
                                index === 0
                                  ? "var(--primary-color)"
                                  : "inherit",
                              width: "100%",
                              maxWidth: "100%",
                            }}
                          >
                            {value || ""}
                          </Box>
                        ))}
                      </Box>
                    );
                  })}
              </Box>
            </Box>
          </Box>
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 1 }}
          >
            <ButtonPrimary onClick={() => exportAjk()}>Cetak AJK</ButtonPrimary>
            <ButtonPrimary onClick={() => exportUmum()}>
              Cetak Umum
            </ButtonPrimary>
            <ButtonOutline onClick={goback}>{t("back")}</ButtonOutline>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default SenaraiAjkSemak;
