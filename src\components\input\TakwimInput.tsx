import React from 'react';
import { Grid, TextField, Typography } from '@mui/material';

interface SimpleInputProps {
  label: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>
  slotProps?: any;
}

export const TakwimInput: React.FC<SimpleInputProps> = ({
  label,
  value,
  onChange,
  onFocus,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  error = false,
  helperText,
  inputProps,
  slotProps
}) => {
  return (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{
            color: '#666666',
            fontSize: '14px',
          }}
        >
          {label}
          {required && <span style={{ color: 'red' }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        <TextField
          fullWidth
          size="small"
          value={value}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          placeholder={placeholder}
          disabled={disabled}
          error={error}
          helperText={helperText}
          inputProps={inputProps}
          sx={{
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'white',
              '& fieldset': {
                borderColor: '#DADADA',
              },
              '&:hover fieldset': {
                borderColor: '#DADADA',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#DADADA',
              },
            },
            '& .MuiInputBase-input': {
              fontSize: '14px',
              padding: '8.5px 14px',
            },
          }}
        />
      </Grid>
    </Grid>
  );
};

export default TakwimInput;


