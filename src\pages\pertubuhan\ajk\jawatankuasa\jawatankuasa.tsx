import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from "@mui/material";
import { ButtonPrimary } from "../../../../components/button";
import ALiranTugas from "../../AliranTugas";
import { Ajk } from "../../pernyata-tahunan/interface";
import { EyeIcon } from "../../../../components/icons";
import {
  COMMITTEE_TASK_TYPE,
  OrganisationPositionLabel,
} from "../../../../helpers/enums";
import { usejawatankuasaContext } from "./jawatankuasaProvider";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import { downloadFile, getLocalStorage, useQuery } from "@/helpers";
import { useDispatch } from "react-redux";
import { setJawatankuasaAJKFormValues } from "@/redux/slices/jawatankuasaAJKFormSlice";

const JawatankuasaComp: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const addressList = getLocalStorage("address_list", []);
  const location = useLocation();
  const disabled = location.state?.disabled ?? false;
  const dispatch = useDispatch();

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const {
    isAliranModuleAccess,
    ajkList,
    module,
    // addressList,
    fetchAjkList,
    fetchSociety,
    // fetchAddressList,
    fetchAliranTugas,
    fetchAliranTugasAccess,
    fetchAliranTugasStatus,
    setModule,
  } = usejawatankuasaContext();

  const isManager = useSelector(getUserPermission);
  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  useEffect(() => {
    fetchAjkList();
    // fetchAddressList();
    fetchSociety();
    fetchAliranTugasAccess(module);
    fetchAliranTugasStatus(module);
    fetchAliranTugas(module);
  }, [module, shouldFetch]);

  useEffect(() => {
    setModule(COMMITTEE_TASK_TYPE.PENGURUSAN_AJK);
  }, []);

  const handleViewAjk = (ajk: Ajk) => {
    navigate(`../jawatankuasa/create-ajk`, {
      state: {
        ajk: ajk,
        view: true,
      },
    });
  };

  const { data, isLoading, refetch } = useQuery({
    url: `society/document/exportPdf`,
    autoFetch: false,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      {
        field: "documentType",
        operator: "eq",
        value: "SENARAI_AHLI_JAWATAN_KUASA",
      },
    ],
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        const file = data?.data?.data as unknown as string;
        downloadFile({
          data: file,
          name: `SENARAI AHLI JAWATAN KUASA`,
        });
      }
    },
  });

  const handleCetak = async () => {
    refetch();
  };

  useEffect(() => {
    dispatch(
      setJawatankuasaAJKFormValues({
        type: 0,
        savedMeetingDate: null,
        savedMeetingDetail: null,
        appointmentDate: null,
        uploadedIds: [],
        citizenAJKs: [],
      })
    );
  }, []);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganAhliJawatankuasaTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {ajkList.length} Orang
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("ajkList")}
          </Typography>

          <Table
            sx={{
              backgroundColor: "white",
              borderRadius: "4px",
              overflow: "hidden",
              "& .MuiTableCell-root": { fontSize: "16px" },
            }}
          >
            <TableHead>
              <TableRow sx={{ backgroundColor: "white" }}>
                <TableCell sx={{ color: "#666666", textAlign: "center" }}>
                  {t("position")}
                </TableCell>
                <TableCell sx={{ color: "#666666", textAlign: "center" }}>
                  {t("name")}
                </TableCell>
                <TableCell sx={{ color: "#666666", textAlign: "center" }}>
                  {t("email")}
                </TableCell>
                <TableCell sx={{ color: "#666666", textAlign: "center" }}>
                  {t("noTelefon")}
                </TableCell>
                <TableCell sx={{ color: "#666666", textAlign: "center" }}>
                  {t("negeri")}
                </TableCell>
                <TableCell sx={{ color: "#666666" }}></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {ajkList.length > 0 ? (
                ajkList.map((ajk, index) => (
                  <TableRow key={index}>
                    <TableCell sx={{ textAlign: "center" }}>
                      {t(OrganisationPositionLabel[ajk.designationCode])}
                    </TableCell>
                    <TableCell sx={{ textAlign: "center" }}>
                      {ajk.name?.toUpperCase()}
                    </TableCell>
                    <TableCell sx={{ textAlign: "center" }}>
                      {ajk.email}
                    </TableCell>
                    <TableCell sx={{ textAlign: "center" }}>
                      {ajk.phoneNumber}
                    </TableCell>
                    <TableCell sx={{ textAlign: "center" }}>
                      {
                        addressList.find(
                          (item: any) =>
                            item.id.toString() === ajk.residentialStateCode
                        )?.name
                      }
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleViewAjk(ajk)}>
                        <EyeIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    sx={{ textAlign: "center", color: "#888" }}
                  >
                    {t("noData")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          {ajkList?.length > 0 ? (
            <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end" }}>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
            </Box>
          ) : null}
        </Box>
      </Box>
      {isManager || isAliranModuleAccess ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("update")} {t("ahliJawatanKuasa")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("update")} {t("ahliJawatanKuasa")}
                </Typography>

                <ButtonPrimary
                  disabled={disabled}
                  onClick={() => {
                    navigate(`update-ajk`);
                  }}
                >
                  {t("update")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>{" "}
        </>
      ) : (
        <></>
      )}

      {isManager ? (
        <ALiranTugas module={COMMITTEE_TASK_TYPE.PENGURUSAN_AJK} />
      ) : // Pass the setShouldFetch function/>
      null}
    </>
    // :
    //  <></>
    // }
    // </>
  );
};

export default JawatankuasaComp;
