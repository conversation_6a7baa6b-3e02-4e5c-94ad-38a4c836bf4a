import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  CircularProgress,
} from "@mui/material";
import { ButtonPrimary, ButtonText } from "@/components/button";
import { ApplicationSubmittedIcon } from "@/components/icons";
import { ErrorOutline } from "@mui/icons-material";

interface DialogActionFlowProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  confirmationText: string;
  successMessage: string;
  errorMessage?: string | null;
  icon?: React.ReactNode;
  hideOnError?: boolean;
}

type DialogState = "CONFIRM" | "LOADING" | "SUCCESS" | "ERROR";

export const DialogActionFlow: React.FC<DialogActionFlowProps> = ({
  open,
  onClose,
  onConfirm,
  confirmationText,
  successMessage,
  errorMessage = "An error occurred. Please try again.",
  icon = <ApplicationSubmittedIcon color="#0CA6A6" />,
  hideOnError = false,
}) => {
  const [dialogState, setDialogState] = useState<DialogState>("CONFIRM");
  const [error, setError] = useState<string | null>(null);

  // Reset state when errorMessage prop changes
  useEffect(() => {
    if (errorMessage && errorMessage !== "An error occurred. Please try again.") {
      setError(errorMessage);
      setDialogState("ERROR");
    }
  }, [errorMessage]);

  const handleConfirm = async () => {
    setDialogState("LOADING");
    try {
      await onConfirm();
      setDialogState("SUCCESS");
    } catch (error) {
      if (hideOnError) {
        onClose();
      } else {
        setError(errorMessage);
        setDialogState("ERROR");
      }
    }
  };

  const handleClose = () => {
    onClose();
    // Reset state after dialog closes
    setTimeout(() => {
      setDialogState("CONFIRM");
      setError(null);
    }, 300);
  };

  const renderContent = () => {
    switch (dialogState) {
      case "CONFIRM":
        return (
          <>
            <DialogContent sx={{ py: 2, px: 2.5, textAlign: "center" }}>
              <Typography variant="body1" sx={{ fontSize: 16 }}>
                {confirmationText}
              </Typography>
            </DialogContent>
            <DialogActions
              sx={{
                py: 2,
                px: 3,
                justifyContent: "center",
                flexDirection: "column",
                gap: 1,
              }}
            >
              <ButtonPrimary sx={{fontSize: "12px", fontWeight: "400"}} onClick={handleConfirm}>Ya</ButtonPrimary>
              <ButtonText sx={{fontSize: "12px", fontWeight: "400", textDecoration: "underline"}} onClick={handleClose}>Kembali</ButtonText>
            </DialogActions>
          </>
        );

      case "LOADING":
        return (
          <DialogContent>
            <CircularProgress
              size="2rem"
              sx={{
                display: "block",
                margin: "25px auto",
              }}
            />
          </DialogContent>
        );

      case "SUCCESS":
        return (
          <DialogContent>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: "100%",
                height: "100%",
                paddingBottom: "20px",
                position: "relative",
              }}
            >
              <Box
                sx={{
                  position: "absolute",
                  top: "50%",
                  marginInline: "auto",
                  transform: "translateY(-55%)",
                }}
              >
                {icon}
              </Box>

              <Typography
                fontSize="12px"
                lineHeight="18px"
                fontWeight="400"
                textAlign="center"
                sx={{
                  maxWidth: "237px",
                  marginInline: "auto",
                }}
              >
                {successMessage}
              </Typography>
            </Box>
          </DialogContent>
        );

      case "ERROR":
        return (
          <DialogContent>
            <Box
              sx={{
                display: "grid",
                alignItems: "center",
                justifyContent: "center",
                width: "100%",
                height: "100%",
                paddingBottom: "20px",
                position: "relative",
              }}
            >
              <Box
                sx={{
                  // position: "absolute",
                  // top: "90%",
                  marginInline: "auto",
                  // transform: "translateY(-55%)",
                }}
              >
                <ErrorOutline color="error" sx={{ fontSize: 40 }} />
              </Box>

              <Typography
                fontSize="12px"
                lineHeight="18px"
                fontWeight="400"
                textAlign="center"
                sx={{
                  maxWidth: "237px",
                  marginInline: "auto",
                }}
              >
                {error}
              </Typography>
            </Box>
          </DialogContent>
        );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      sx={{
        "& .MuiPaper-root": {
          width: dialogState === "CONFIRM" ? "60dvh" : "337px",
          height: dialogState === "CONFIRM" ? "auto" : "127px",
        },
      }}
    >
      {renderContent()}
    </Dialog>
  );
};

export default DialogActionFlow;




