import { ButtonOutline, ButtonPrimary, Label } from "@/components";
import { InputControllerFormik } from "@/components/input";
import FileUploader from "@/components/input/fileUpload";
import { DocumentUploadType, useQuery } from "@/helpers";
import {
  CitizenshipStatus,
  DurationOptions,
  GenderType,
  IdTypes,
  OrganisationPositions,
} from "@/helpers/enums";
import { capitalizeWords, getAddressList } from "@/helpers/utils";
import { useFormCommitteeNonCitizenBySocietyIdContext } from "@/pages/pertubuhan/ajk/jawatankuasa/createAJKBukanWn";
import { usejawatankuasaContext } from "@/pages/pertubuhan/ajk/jawatankuasa/jawatankuasaProvider";
import { Box, Grid, Typography } from "@mui/material";
import { useBack, useNotification } from "@refinedev/core";
import { Form, useFormikContext } from "formik";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useParams } from "react-router-dom";

export interface CommitteeNonCitizenBySocietyRequestBody {
  id: number | null;
  societyNo: string;
  societyName: string;
  name: string;
  citizenshipStatus: number;
  identificationType: string;
  identificationNo: number;
  applicantCountryCode: number;
  gender: string;
  residentialAddress: string;
  residentialCity?: string | null;
  visaNo?: string | null;
  visaExpirationDate: Date | null | string;
  permitNo?: string | null;
  permitExpirationDate: Date | null | string;
  tujuanDMalaysia: string;
  stayDurationDigit: string;
  stayDurationUnit: string;
  designationCode: number;
  activeCommitteeId: string;
  otherDesignationCode: string;
}

export interface FormCommitteeNonCitizenBySocietyIdInnerProps<
  RequestBody extends CommitteeNonCitizenBySocietyRequestBody = CommitteeNonCitizenBySocietyRequestBody
> {
  initialValue: RequestBody;
}

export const FormCommitteeNonCitizenBySocietyIdInner = <
  RequestBody extends CommitteeNonCitizenBySocietyRequestBody = CommitteeNonCitizenBySocietyRequestBody,
  PropType extends FormCommitteeNonCitizenBySocietyIdInnerProps<RequestBody> = FormCommitteeNonCitizenBySocietyIdInnerProps<RequestBody>
>({
  initialValue,
}: PropType) => {
  const { t } = useTranslation();
  const location = useLocation();
  const { open } = useNotification();
  const {
    values,
    setFieldValue,
    resetForm,
    isValid,
    isSubmitting: isSubmittingFormik,
  } = useFormikContext<RequestBody>();
  const {
    addressList: addressListFromNetwork,
    appointmentDateG,
    savedMeetingDate,
  } = usejawatankuasaContext();
  const [userICCorrect, setUserICCorrect] = useState(false);
  const [userNameMatchIC, setUserNameMatchIC] = useState(false);
  const [nameHelperText, setNameHelperText] = useState<string | undefined>(
    undefined
  );
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [idNoHelperText, setIdNoHelperText] = useState<string | undefined>(
    undefined
  );
  const back = useBack();
  const { id } = useParams();
  const {
    societyNonCitizenCommitteeId,
    isUploadingFilesAfterCreateAJK,
    setIsUploadingFilesAfterCreateAJK,
    redirectToAJKLists,
  } = useFormCommitteeNonCitizenBySocietyIdContext();

  const isSubmitting =
    isSubmittingFormik || (!initialValue?.id && isUploadingFilesAfterCreateAJK);
  const addressListFallback = getAddressList();
  const addressList =
    addressListFromNetwork.length > 0
      ? addressListFromNetwork
      : addressListFallback;

  const [positionList, setPositionList] = useState<
    { value: number; label: string; designationCode?: string }[]
  >([]);

  const { refetch: refetchValidateId } = useQuery<any>({
    url: `user/auth/validateId`,
    autoFetch: false,
    queryOptions: {
      cacheTime: 0,
    },
    onSuccessNotification(data) {
      //reset before new call
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      const { name, status, integrationOff } = data?.data?.data ?? {};

      if (!integrationOff) {
        if (status === "Y") {
          setUserICCorrect(true);
          if (name) {
            setUserNameMatchIC(true);
          }
        } else {
          setUserICCorrect(false);
          setUserNameMatchIC(true);
        }
      }

      return false;
    },
    onErrorNotification() {
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      return false;
    },
  });
  const viewMode: boolean = location.state?.view ?? false;
  const isInputDisabled = viewMode || isSubmitting;

  const handleSenaraiAjkResets = () => {
    resetForm({
      values: initialValue,
    });
  };
  const handleUploadAfterSubmitSuccessfully = () => {
    setIsUploadingFilesAfterCreateAJK(false);
    open?.({
      message: t("nonCitizenCommitteeSuccessfullyCreated"),
      type: "success",
    });
    redirectToAJKLists();
  };
  const validateId = async () => {
    await refetchValidateId({
      filters: [
        {
          field: "identificationNo",
          operator: "eq",
          value: values.identificationNo,
        },
        {
          field: "name",
          operator: "eq",
          value: values.name?.trim().toUpperCase(),
        },
        {
          field: "sessionIdentificationNo",
          operator: "eq",
          value: values.identificationNo,
        },
      ],
    });
  };

  useEffect(() => {
    if (
      values.identificationNo?.toString()?.length > 11 &&
      !!values.name &&
      values.identificationType === "4"
    ) {
      validateId();
    } else {
      setUserICCorrect(true);
      setUserNameMatchIC(true);
      setIdNoHelperText(undefined);
      setNameHelperText(undefined);
    }
  }, [values.identificationNo, values.name, values.identificationType]);

  useEffect(() => {
    if (values.identificationType === "4") {
      setIdNoHelperText(
        values.identificationNo.toString().length === 12 && !userICCorrect
          ? t("IcDoesNotExist")
          : undefined
      );
      setNameHelperText(
        values.identificationNo.toString()?.length === 12 &&
          values.name?.trim() !== undefined &&
          !userNameMatchIC
          ? t("invalidName")
          : undefined
      );
    }
  }, [
    values.identificationType,
    values.identificationNo,
    values.name,
    userICCorrect,
    userNameMatchIC,
  ]);

  const { isLoading: isLoadingPositionListRes } = useQuery({
    url: "society/nonCitizenCommittee/getPositionsList",
    filters: [
      {
        field: "societyId",
        value: parseInt(id ? id : ""),
        operator: "eq",
      },
      {
        field: "appointedDate",
        value: savedMeetingDate ? savedMeetingDate : appointmentDateG,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        const newList = data?.data?.data?.map((item: any) => {
          const position =
            OrganisationPositions?.find(
              (p) => p.value === Number(item.designationCode)
            ) ?? null;
          const label = position?.label
            ? `${t(position.label)}${
                item.positionHolder ? ` - ${item.positionHolder}` : ""
              }`
            : item.designationCode;

          return {
            label,
            value: Number(item?.activeCommitteeId),
            designationCode: item.designationCode,
          };
        });
        setPositionList(newList);
      }
    },
  });

  return (
    <Form style={{ display: "grid", gap: 16 }}>
      <Box
        sx={{
          px: 2,
          py: 1,
          mb: 3,
          borderRadius: "14px",
        }}
      >
        <Typography
          variant="h6"
          component="h2"
          sx={{
            fontWeight: "bold",
            fontSize: 14,
            color: "var(--primary-color)",
          }}
        >
          {t("maklumat")} {t("nonCitizenAJK")}
        </Typography>
      </Box>

      <Box sx={{ pl: 2 }}>
        <InputControllerFormik
          name="societyNo"
          label={t("organizationNumber")}
          disabled
        />
        <InputControllerFormik
          name="societyName"
          label={t("organizationName")}
          disabled
        />
        <InputControllerFormik
          name="name"
          label={t("name")}
          disabled={isInputDisabled}
          helperText={nameHelperText}
          required
        />
        <InputControllerFormik
          name="citizenshipStatus"
          label={t("citizenship")}
          disabled
          type="select"
          options={CitizenshipStatus.map((item) => ({
            ...item,
            label: t(item.label),
          }))}
        />
        <InputControllerFormik
          name="identificationType"
          required
          label={t("idType")}
          disabled={isInputDisabled}
          type="select"
          trimHelperText={false}
          options={IdTypes.filter((item) =>
            [4, 5].includes(Number(item.value))
          )}
          onValueChange={(val) => {
            if (val === "4") {
              setFieldValue("identificationNo", "");
            }
          }}
        />
        <InputControllerFormik
          name="identificationNo"
          required
          disabled={isInputDisabled}
          label={t("idNumber")}
          {...(values.identificationType === "4" && {
            digitsLimit: 12,
            inputProps: {
              inputMode: "numeric",
              pattern: "[0-9]*",
              maxLength: 12,
              minLength: 12,
            },
          })}
          helperText={idNoHelperText}
        />
        <InputControllerFormik
          name="applicantCountryCode"
          required
          disabled={isInputDisabled}
          label={capitalizeWords(t("originCountry"))}
          type="select"
          trimHelperText={false}
          options={
            addressList
              ?.filter((item) => typeof item.pid === "number" && item.pid === 0)
              ?.map((item) => ({
                label: capitalizeWords(item.name, null, true),
                value: parseInt(item.id as unknown as string),
              })) ?? []
          }
        />
        <InputControllerFormik
          name="gender"
          required
          disabled={isInputDisabled}
          label={t("gender")}
          type="select"
          trimHelperText={false}
          options={GenderType.map((item) => ({
            label: t(item.translateKey),
            value: item.code,
          }))}
        />
        <InputControllerFormik
          name="residentialAddress"
          required
          disabled={isInputDisabled}
          label={t("residentialAddress")}
        />
        <InputControllerFormik
          name="residentialCity"
          disabled={isInputDisabled}
          label={t("city")}
        />
        <InputControllerFormik
          name="visaNo"
          disabled={isInputDisabled}
          label={t("nomborVisa")}
        />
        <InputControllerFormik
          name="visaExpirationDate"
          disabled={isInputDisabled}
          type="date"
          label={t("visaExpiryDate")}
          dateInputProps={{ disablePast: true }}
        />
        <InputControllerFormik
          name="permitNo"
          disabled={isInputDisabled}
          label={t("permitNumber")}
        />
        <InputControllerFormik
          name="permitExpirationDate"
          disabled={isInputDisabled}
          type="date"
          label={t("permitExpiryDate")}
          dateInputProps={{ disablePast: true }}
        />
        <InputControllerFormik
          name="tujuanDMalaysia"
          required
          disabled={isInputDisabled}
          label={capitalizeWords(t("purposeInMalaysia"))}
        />
        <Grid
          container
          spacing={2}
          alignItems={"flex-start"}
          sx={{ alignItems: "flex-start" }}
        >
          <Grid item xs={12} sm={4} flexDirection="column" display="flex">
            <Label
              text={capitalizeWords(t("durationInMalaysia"))}
              required={true}
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            sx={{
              display: "flex",
              gap: 1,
              alignItems: "flex-start",
              mb: 1,
            }}
          >
            <InputControllerFormik
              name="stayDurationDigit"
              disabled={isInputDisabled}
              required
              isLabelNoSpace={false}
              isLabel={false}
              type="text"
              inputMode="numeric"
              onlyAcceptNumber={true}
            />
            <InputControllerFormik
              name="stayDurationUnit"
              disabled={isInputDisabled}
              required
              isLabelNoSpace={false}
              type="select"
              isLabel={false}
              trimHelperText={false}
              options={DurationOptions.map((position) => ({
                ...position,
                label: t(position.label),
              }))}
            />
          </Grid>
        </Grid>
        <InputControllerFormik
          name="activeCommitteeId"
          disabled={isInputDisabled}
          required
          type="select"
          label={t("position")}
          isLoadingData={isLoadingPositionListRes}
          options={positionList}
          onChange={(event) => {
            const { value } = event.target;
            const selectedOption =
              positionList?.find((opt) => opt.value === value) ?? null;
            if (selectedOption?.designationCode) {
              setFieldValue("designationCode", selectedOption.designationCode);
            }
            setFieldValue("activeCommitteeId", value);
          }}
        />
        <InputControllerFormik
          name="otherDesignationCode"
          required
          disabled={isInputDisabled}
          multiline
          rows={4}
          label={t("importanceOfPosition2")}
        />
      </Box>

      {!viewMode && values.identificationNo && (
        <FileUploader
          title="ajkEligibilityCheck"
          type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
          validTypes={[
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "text/plain",
          ]}
          maxFileSize={25 * 1024 * 1024}
          disabled={viewMode}
          societyId={parseInt(id ?? "")}
          icNo={values.identificationNo}
          societyNonCitizenCommitteeId={
            societyNonCitizenCommitteeId?.toString() ?? undefined
          }
          onUploadedFilesChanged={(files) => setUploadedFiles(files)}
          showSuccessUploadNotification={false}
          uploadAfterSubmit={
            societyNonCitizenCommitteeId === null ||
            isUploadingFilesAfterCreateAJK
          }
          uploadAfterSubmitIndicator={isUploadingFilesAfterCreateAJK}
          onUploadAfterSubmitSuccessfully={handleUploadAfterSubmitSuccessfully}
        />
      )}

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mt: 2,
          gap: 2,
        }}
      >
        <ButtonOutline onClick={back}>{t("back")}</ButtonOutline>
        {!viewMode ? (
          <>
            <ButtonOutline onClick={handleSenaraiAjkResets}>
              {t("reset")}
            </ButtonOutline>
            <ButtonPrimary
              disabled={
                (societyNonCitizenCommitteeId === null &&
                  uploadedFiles.length === 0) ||
                !isValid ||
                isSubmitting ||
                (values.identificationType === "4" &&
                  (values.identificationNo?.toString().length < 12 ||
                    !userICCorrect ||
                    !userNameMatchIC))
              }
              type="submit"
            >
              {t("update")}
            </ButtonPrimary>
          </>
        ) : null}
      </Box>
    </Form>
  );
};
