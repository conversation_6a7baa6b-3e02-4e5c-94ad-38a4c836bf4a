import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { SpeechRecognitionState, TextToSpeechState } from '@/components/chatbot/types';
import {
  createSpeechRecognition,
  isSpeechRecognitionSupported,
  isSpeechSynthesisSupported,
  speakText,
  startSpeechRecognition,
  stopSpeaking as stopSpeakingUtil,
  stopSpeechRecognition,
  initVoices
} from '@/components/chatbot/SpeechUtils';
import {
  initElevenLabs,
  isElevenLabsAvailable,
  createEnhancedSpeechRecognition,
  textToSpeechWithElevenLabs,
  playAudioBlob,
  getElevenLabsVoices
} from '@/components/chatbot/ElevenLabsUtils';
import { useTranslation } from 'react-i18next';

// Import the SpeechRecognitionInstance type
import type { SpeechRecognitionInstance } from '@/components/chatbot/SpeechUtils';

// Speech provider types
type SpeechProvider = 'elevenlabs' | 'browser';

// Environment variable configuration
// const SPEECH_ENABLED = import.meta.env.VITE_SPEECH_ENABLED === 'true';
// const SPEECH_PROVIDER = (import.meta.env.VITE_SPEECH_PROVIDER as SpeechProvider) || 'elevenlabs';
// const ELEVENLABS_API_KEY = import.meta.env.VITE_ELEVENLABS_API_KEY || '***************************************************';

const SPEECH_ENABLED = 'true' === 'true';
const SPEECH_PROVIDER = ('browser' as SpeechProvider);
const ELEVENLABS_API_KEY = '***************************************************';


interface SpeechContextType {
  // Speech recognition
  speechRecognition: SpeechRecognitionState & {
    provider: SpeechProvider;
    isElevenLabsAvailable: boolean;
    isElevenLabsReady: boolean;
  };
  startListening: (onResult?: (text: string) => void) => void;
  stopListening: () => void;
  initializeElevenLabs: (apiKey: string) => void;

  // Text-to-speech
  textToSpeech: TextToSpeechState & {
    provider: SpeechProvider;
    isElevenLabsReady: boolean;
  };
  speak: (text: string, messageId?: string, disableSpeech?: boolean) => void;
  stopSpeaking: () => void;

  // UI Control
  showSpeechControls: boolean;
  setShowSpeechControls: (show: boolean) => void;
}

const SpeechContext = createContext<SpeechContextType | null>(null);

export const SpeechProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { i18n } = useTranslation();

  // Get locale - ensure Malay is properly recognized
  const locale = ["bm", "my"].includes(i18n.language) ? "ms" : i18n.language;

  // ElevenLabs initialization state
  const [isElevenLabsReady, setIsElevenLabsReady] = useState(false);
  // Set showSpeechControls based on environment variable
  const [showSpeechControls, setShowSpeechControls] = useState(SPEECH_ENABLED);

  // User interaction tracking
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const userInteractionRef = useRef(false);

  // Speech recognition state
  const [speechRecognition, setSpeechRecognition] = useState<SpeechRecognitionState & {
    provider: SpeechProvider;
    isElevenLabsAvailable: boolean;
    isElevenLabsReady: boolean;
  }>({
    isListening: false,
    isSupported: SPEECH_ENABLED && SPEECH_PROVIDER === 'browser' ? isSpeechRecognitionSupported() : false,
    error: null,
    provider: SPEECH_PROVIDER,
    isElevenLabsAvailable: false,
    isElevenLabsReady: false
  });

  // Text-to-speech state
  const [textToSpeech, setTextToSpeech] = useState<TextToSpeechState & {
    provider: SpeechProvider;
    isElevenLabsReady: boolean;
  }>({
    isSpeaking: false,
    isSupported: SPEECH_ENABLED && SPEECH_PROVIDER === 'browser' ? isSpeechSynthesisSupported() : false,
    speakingMessageId: null,
    error: null,
    provider: SPEECH_PROVIDER,
    isElevenLabsReady: false
  });

  // Speech recognition instance ref
  const recognitionRef = useRef<SpeechRecognitionInstance | null>(null);
  
  // Enhanced speech recognition ref for ElevenLabs
  const enhancedRecognitionRef = useRef<{ start: () => void; stop: () => void; isRecording: () => boolean } | null>(null);

  // Audio element ref for ElevenLabs TTS
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Callback for when speech recognition completes
  const [speechResultCallback, setSpeechResultCallback] = useState<((text: string) => void) | null>(null);

  // Flag to track if speech recognition is already in progress
  const isListeningRef = useRef<boolean>(false);

  // Alice voice ID for ElevenLabs (you may need to get the actual ID from ElevenLabs)
  const ALICE_VOICE_ID = 'UcqZLa941Kkt8ZhEEybf'; // This is Alice's voice ID

  // Improved speech management with timeout protection
  const isSpeakingRef = useRef(false);
  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Queue for pending speech requests
  const pendingSpeechQueue = useRef<Array<{ text: string; messageId?: string; disableSpeech?: boolean }>>([]);

  /**
   * Reset speech flag with timeout protection
   */
  const resetSpeechFlag = useCallback(() => {
    isSpeakingRef.current = false;
    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
      speechTimeoutRef.current = null;
    }
    console.log('SpeechContext: Speech flag reset');
  }, []);

  /**
   * Set speech flag with automatic timeout protection
   */
  const setSpeechFlag = useCallback(() => {
    isSpeakingRef.current = true;
    
    // Clear any existing timeout
    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
    }
    
    // Set a timeout to automatically reset the flag after 30 seconds
    speechTimeoutRef.current = setTimeout(() => {
      console.log('SpeechContext: Speech timeout reached, forcing flag reset');
      resetSpeechFlag();
      setTextToSpeech(prev => ({
        ...prev,
        isSpeaking: false,
        speakingMessageId: null,
        error: 'Speech timeout - flag reset'
      }));
    }, 30000); // 30 second timeout
    
    console.log('SpeechContext: Speech flag set with timeout protection');
  }, [resetSpeechFlag]);

  /**
   * Handle user interaction events to enable audio playback
   */
  const handleUserInteraction = useCallback(() => {
    if (!userInteractionRef.current) {
      console.log('SpeechContext: User interaction detected, enabling audio playback');
      userInteractionRef.current = true;
      setHasUserInteracted(true);

      // Process any pending speech requests
      if (pendingSpeechQueue.current.length > 0) {
        console.log(`SpeechContext: Processing ${pendingSpeechQueue.current.length} pending speech requests`);
        const pendingRequests = [...pendingSpeechQueue.current];
        pendingSpeechQueue.current = [];
        
        // Process the most recent request only to avoid overwhelming the user
        const latestRequest = pendingRequests[pendingRequests.length - 1];
        if (latestRequest) {
          setTimeout(() => {
            speak(latestRequest.text, latestRequest.messageId, latestRequest.disableSpeech);
          }, 500);
        }
      }
    }
  }, []);

  /**
   * Set up user interaction listeners
   */
  useEffect(() => {
    const events = ['click', 'keydown', 'touchstart', 'mousedown'];
    
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true, passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, [handleUserInteraction]);

  /**
   * Initialize ElevenLabs with API key and proper state tracking
   */
  const initializeElevenLabs = useCallback(async (apiKey: string) => {
    if (!SPEECH_ENABLED) {
      console.log('SpeechContext: Speech is disabled via environment variable');
      return;
    }

    console.log('SpeechContext: Starting ElevenLabs initialization...');
    
    try {
      // Initialize ElevenLabs client
      initElevenLabs(apiKey);
      
      // Wait a bit for initialization to complete
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if initialization was successful
      const isAvailable = isElevenLabsAvailable();
      console.log('SpeechContext: ElevenLabs availability check:', isAvailable);
      
      if (isAvailable) {
        // Test ElevenLabs by getting voices
        try {
          const voices = await getElevenLabsVoices();
          console.log('SpeechContext: ElevenLabs voices loaded:', voices.length);
          
          const aliceVoice = voices.find(v => v.name.toLowerCase().includes('alice'));
          if (aliceVoice) {
            console.log('SpeechContext: Found Alice voice:', aliceVoice);
          }
          
          // Mark as ready only after successful voice loading
          setIsElevenLabsReady(true);
          console.log('SpeechContext: ElevenLabs fully initialized and ready');
          
          setSpeechRecognition(prev => ({
            ...prev,
            isElevenLabsAvailable: true,
            isElevenLabsReady: true,
            isSupported: true
          }));

          setTextToSpeech(prev => ({
            ...prev,
            isElevenLabsReady: true,
            isSupported: true
          }));
        } catch (voiceError) {
          console.warn('SpeechContext: Could not load ElevenLabs voices, but client is available:', voiceError);
          setIsElevenLabsReady(true); // Still mark as ready, voices might load later
        }
      } else {
        throw new Error('ElevenLabs initialization failed');
      }
    } catch (error) {
      console.error('SpeechContext: ElevenLabs initialization failed:', error);
      setIsElevenLabsReady(false);
      setSpeechRecognition(prev => ({
        ...prev,
        error: `ElevenLabs initialization failed: ${error}`
      }));
    }
  }, []);

  /**
   * Starts speech recognition for user input
   * @param onResult Optional callback function to handle the speech recognition result
   */
  const startListening = useCallback((onResult?: (text: string) => void) => {
    if (!SPEECH_ENABLED) {
      console.log('SpeechContext: Speech is disabled via environment variable');
      setSpeechRecognition(prev => ({
        ...prev,
        error: 'Speech functionality is disabled'
      }));
      return;
    }

    // Mark user interaction when they start speech recognition
    handleUserInteraction();

    // Always use browser speech recognition for STT
    if (!speechRecognition.isSupported) {
      setSpeechRecognition(prev => ({
        ...prev,
        error: 'Browser speech recognition is not supported'
      }));
      return;
    }

    // Check if speech recognition is already in progress
    if (isListeningRef.current) {
      console.log('SpeechContext: Speech recognition already in progress, ignoring duplicate call');
      return;
    }

    // Set the listening flag to prevent multiple instances
    isListeningRef.current = true;
    console.log('SpeechContext: Starting browser speech recognition');

    // Store the callback if provided
    if (onResult) {
      setSpeechResultCallback(() => onResult);
    }

    // Set listening state
    setSpeechRecognition(prev => ({
      ...prev,
      isListening: true,
      error: null
    }));

    // Create a unique ID for this recognition session to prevent duplicate processing
    const recognitionId = `recognition_${Date.now()}`;
    const hasProcessedRef = { processed: false };

    // Use Web Speech API
    console.log('SpeechContext: Using Web Speech API');
    
    // Create recognition instance if not already created
    if (!recognitionRef.current) {
      recognitionRef.current = createSpeechRecognition(locale);
    } else {
      // If we already have an instance, make sure it's stopped and recreate it
      try {
        stopSpeechRecognition(recognitionRef.current);
        recognitionRef.current = createSpeechRecognition(locale);
        console.log('SpeechContext: Recreated speech recognition instance');
      } catch (error) {
        console.error('SpeechContext: Error recreating speech recognition instance:', error);
      }
    }

    // Start speech recognition
    startSpeechRecognition(
      recognitionRef.current,
      // On result
      (text) => {
        // Check if we've already processed a result for this recognition session
        if (hasProcessedRef.processed) {
          console.log(`SpeechContext: Already processed a result for session ${recognitionId}, ignoring`);
          return;
        }

        // Mark this session as processed
        hasProcessedRef.processed = true;

        // Reset the listening flag
        isListeningRef.current = false;
        console.log('SpeechContext: Speech recognition result received, resetting isListening flag');

        // Call the callback if provided
        if (speechResultCallback) {
          speechResultCallback(text);
        } else if (onResult) {
          onResult(text);
        }

        setSpeechRecognition(prev => ({
          ...prev,
          isListening: false
        }));
      },
      // On error
      (error) => {
        // Reset the listening flag
        isListeningRef.current = false;
        console.log('SpeechContext: Speech recognition error, resetting isListening flag');

        setSpeechRecognition(prev => ({
          ...prev,
          isListening: false,
          error
        }));
      },
      // On end
      () => {
        // Reset the listening flag
        isListeningRef.current = false;
        console.log('SpeechContext: Speech recognition ended, resetting isListening flag');

        setSpeechRecognition(prev => ({
          ...prev,
          isListening: false
        }));
      }
    );
  }, [speechRecognition.isSupported, speechResultCallback, locale, handleUserInteraction]);

  /**
   * Stops speech recognition
   */
  const stopListening = useCallback(() => {
    // Stop Web Speech API recognition
    stopSpeechRecognition(recognitionRef.current);
    
    // Stop ElevenLabs recognition if active
    if (enhancedRecognitionRef.current) {
      enhancedRecognitionRef.current.stop();
      enhancedRecognitionRef.current = null;
    }

    // Reset the listening flag
    isListeningRef.current = false;
    console.log('SpeechContext: Manually stopped speech recognition, resetting isListening flag');

    setSpeechRecognition(prev => ({
      ...prev,
      isListening: false
    }));
  }, []);

  /**
   * Speaks a message using the configured speech provider with improved replay functionality
   * @param text The text to speak
   * @param messageId Optional ID of the message being spoken
   * @param disableSpeech Optional flag to disable speech (for specific pages)
   */
  const speak = useCallback(async (text: string, messageId?: string, disableSpeech?: boolean) => {
    if (!SPEECH_ENABLED) {
      console.log('SpeechContext: Speech is disabled via environment variable');
      return;
    }

    // If speech is disabled for this call, just log and return
    if (disableSpeech) {
      console.log('SpeechContext: Speech disabled for this call, skipping');
      return;
    }

    // Check if ElevenLabs is initialized
    if (!isElevenLabsAvailable()) {
      console.log('SpeechContext: ElevenLabs not initialized, initializing now...');
      await initializeElevenLabs(ELEVENLABS_API_KEY);
      
      // Check again after initialization attempt
      if (!isElevenLabsAvailable()) {
        console.error('SpeechContext: Failed to initialize ElevenLabs');
        setTextToSpeech(prev => ({
          ...prev,
          error: 'Failed to initialize ElevenLabs for TTS'
        }));
        return;
      }
    }

    // Improved speech blocking logic - allow replay of the same message
    if (isSpeakingRef.current) {
      const currentSpeakingId = textToSpeech.speakingMessageId;
      
      // If it's the same message being replayed, stop current and continue
      if (currentSpeakingId === messageId) {
        console.log('SpeechContext: Replaying the same message, stopping current speech');
        stopSpeaking();
        // Wait a bit for cleanup
        await new Promise(resolve => setTimeout(resolve, 100));
      } else {
        console.log('SpeechContext: Different speech in progress, skipping new request');
        return;
      }
    }

    // Stop any ongoing speech
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    stopSpeakingUtil();

    // Set speech flag with timeout protection
    setSpeechFlag();
    console.log('SpeechContext: Starting speech with ElevenLabs TTS');

    // Set speaking state
    setTextToSpeech(prev => ({
      ...prev,
      isSpeaking: true,
      speakingMessageId: messageId || null,
      error: null
    }));

    try {
      console.log('SpeechContext: Using ElevenLabs TTS with Alice voice');
      
      // Use ElevenLabs with Alice voice
      const audioBlob = await textToSpeechWithElevenLabs(text, ALICE_VOICE_ID, locale);
      
      if (audioBlob) {
        // Play the audio blob
        audioRef.current = await playAudioBlob(
          audioBlob,
          // On start
          () => {
            console.log('SpeechContext: ElevenLabs audio started playing');
            setTextToSpeech(prev => ({
              ...prev,
              isSpeaking: true
            }));
          },
          // On end
          () => {
            console.log('SpeechContext: ElevenLabs audio finished playing');
            // Reset the speech flag
            resetSpeechFlag();
            audioRef.current = null;

            setTextToSpeech(prev => ({
              ...prev,
              isSpeaking: false,
              speakingMessageId: null
            }));
          },
          // On error
          (error) => {
            console.error('SpeechContext: ElevenLabs audio playback error:', error);
            // Reset the speech flag
            resetSpeechFlag();
            audioRef.current = null;

            setTextToSpeech(prev => ({
              ...prev,
              isSpeaking: false,
              speakingMessageId: null,
              error: `ElevenLabs playback failed: ${error}`
            }));
          }
        );
      } else {
        console.error('SpeechContext: ElevenLabs returned null audio blob');
        resetSpeechFlag();
        setTextToSpeech(prev => ({
          ...prev,
          isSpeaking: false,
          speakingMessageId: null,
          error: 'ElevenLabs returned null audio blob'
        }));
      }
    } catch (error) {
      console.error('SpeechContext: ElevenLabs TTS error:', error);
      resetSpeechFlag();
      setTextToSpeech(prev => ({
        ...prev,
        isSpeaking: false,
        speakingMessageId: null,
        error: `ElevenLabs TTS failed: ${error}`
      }));
    }
  }, [textToSpeech.speakingMessageId, locale, setSpeechFlag, resetSpeechFlag]);

  /**
   * Stops text-to-speech
   */
  const stopSpeaking = useCallback(() => {
    // Stop ElevenLabs audio if playing
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }

    // Stop browser TTS
    stopSpeakingUtil();

    // Reset the speech flag
    resetSpeechFlag();
    console.log('SpeechContext: Manually stopped speech, resetting speech flag');

    setTextToSpeech(prev => ({
      ...prev,
      isSpeaking: false,
      speakingMessageId: null
    }));
  }, [resetSpeechFlag]);

  // Initialize the selected speech provider when component mounts
  useEffect(() => {
    if (!SPEECH_ENABLED) {
      console.log('SpeechContext: Speech functionality is disabled via environment variable');
      return;
    }

    console.log('SpeechContext: Initializing speech providers');

    // Initialize browser voices for STT
    initVoices().then(voices => {
      console.log(`Loaded ${voices.length} speech synthesis voices`);

      // Log available voices for debugging
      if (voices.length > 0) {
        const localePrefix = locale === 'ms' ? 'ms' : 'en';
        const localeVoices = voices.filter(voice =>
          voice.lang.startsWith(localePrefix) ||
          (locale === 'ms' && voice.lang.startsWith('id'))
        );

        console.log(`Available ${locale} voices:`,
          localeVoices.map(v => `${v.name} (${v.lang})`).join(', '));
      }
    });

    // Initialize ElevenLabs for TTS
    console.log('SpeechContext: Initializing ElevenLabs for TTS');
    const initElevenLabsAsync = async () => {
      try {
        await initializeElevenLabs(ELEVENLABS_API_KEY);
      } catch (error) {
        console.error('SpeechContext: Failed to initialize ElevenLabs:', error);
      }
    };
    initElevenLabsAsync();
  }, [locale, initializeElevenLabs]);

  // Clean up speech recognition and synthesis on unmount
  useEffect(() => {
    return () => {
      stopSpeechRecognition(recognitionRef.current);
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      stopSpeakingUtil();

      // Reset the speech flag and clear timeout
      resetSpeechFlag();
      console.log('SpeechContext: Component unmounted, resetting speech flag');
    };
  }, [resetSpeechFlag]);

  return (
    <SpeechContext.Provider
      value={{
        speechRecognition,
        startListening,
        stopListening,
        initializeElevenLabs,
        textToSpeech,
        speak,
        stopSpeaking,
        showSpeechControls,
        setShowSpeechControls
      }}
    >
      {children}
    </SpeechContext.Provider>
  );
};

export const useSpeech = () => {
  const context = useContext(SpeechContext);
  if (!context) {
    throw new Error('useSpeech must be used within a SpeechProvider');
  }
  return context;
};
