import { useTranslation } from "react-i18next";
import { useBack } from "@refinedev/core";
import { useNavigate } from "react-router-dom";

import { Box, Button, Grid, Typography, useTheme } from "@mui/material"
import { useState } from "react";
import { TextFieldControllerFormik } from "@/components/input";
import { Form, useFormikContext } from "formik";
import { DatePickerFormik } from "@/components/input/DatePickerFormik";
import { DialogConfirmation } from "@/components/dialog";

export interface FormEnforcementLiabilityRestrictionWhitelistingProps {}

export interface PenguatkuasaanSekatanLiabilitiWhitelistingRequestBodyCreate {
  name: string
  noticeReferenceNo: string
  referenceNo: string
  whitelistDate: string
  notes: string
}

export const FormEnforcementLiabilityRestrictionWhitelisting = <
  RequestBody extends PenguatkuasaanSekatanLiabilitiWhitelistingRequestBodyCreate = PenguatkuasaanSekatanLiabilitiWhitelistingRequestBodyCreate
>() => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isSubmitting, isValid, values, submitForm: handleSubmit } = useFormikContext<RequestBody>();
  const back = useBack();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionSuccess, setActionSuccess] = useState(false);
  const navigate = useNavigate();

  const primary = theme.palette.primary.main;
  const error = theme.palette.error.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const submitForm = async () => {
    try {
      await handleSubmit();
      setActionSuccess(true);
      setTimeout(() => {
        navigate("../..", { relative: "path" })
      }, 1000);
    } catch {
      setActionSuccess(false);
    }
  };
  const handleClose = () => {
    setDialogOpen(false);
    setActionSuccess(false);
  };

  return (
    <>
      <Form>
        <Box
          sx={{
            border: "0.5px solid #dadada",
            borderRadius: "10px",
            padding: "1.5rem"
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "1.5rem"
            }}
          >
            <Typography
              color={primary}
              sx={{
                fontSize: 14,
                fontWeight: "medium",
              }}
            >
              {t("whitelistingInformation")}
            </Typography>
          </div>
          <Grid container spacing={2}>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("instructionLetterReferenceNumber")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="noticeReferenceNo"
                helperTextComponentPlacement="INSIDE"
              />
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("whitelistDate")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <DatePickerFormik
                name="whitelistDate"
              />
            </Grid>
            <Grid item md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("ulasan")}
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <TextFieldControllerFormik
                name="notes"
                multiline
                required={false}
                rows={4}
                helperTextComponentPlacement="INSIDE"
              />
            </Grid>
          </Grid>
        </Box>
        <div style={{
          display: "flex",
          alignItems: "flex-end",
          justifyContent: "flex-end",
          columnGap: "0.5rem",
          marginTop: "1rem",
        }}>
          <Button
            type="button"
            sx={{
              textTransform: "capitalize",
              minWidth: "7.5rem"
            }}
            variant="outlined"
            onClick={back}
          >
            {t("back")}
          </Button>
          <div style={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "flex-end",
            columnGap: "0.5rem"
          }}>
            <Button
              type="button"
              sx={{
                textTransform: "capitalize",
                minWidth: "7.5rem"
              }}
              variant="contained"
              disabled={!isValid || isSubmitting}
              onClick={() => setDialogOpen(true)}
            >
              {t("simpan")}
            </Button>
          </div>
        </div>
      </Form>
      {values && (
        <DialogConfirmation
          isMutating={isSubmitting}
          open={dialogOpen}
          onClose={handleClose}
          onAction={submitForm}
          onConfirmationText={t("areYouSureContinueWhitelisting", {
            name: `<b>${values.name}?</b>`
          })}
          isSuccess={actionSuccess}
          onSuccessText={t("whitelistingSuccessMessage", {
            name: values.name
          })}
        />
      )}
    </>
  )
}
