import { type KeyboardEvent } from "react"

import { InputAdornment, styled, TextField, type TextFieldProps } from "@mui/material"
import { SearchIcon } from "../icons"

const StyledTextField = styled(TextField)`
  display: block;
  box-sizing: border-box;
  max-width: 570px;
  margin-inline: auto;
  height: 40px;
  background: var(--border-grey);
  opacity: 0.5;
  border: 1px solid var(--text-grey);
  border-radius: 10px;

  & .MuiOutlinedInput-root {
    height: 40px;

    & fieldset {
      border: none;
    }
  }
`

const StyledSearchIcon = styled(SearchIcon)`
  color: var(--text-grey-disabled);
  margin-left: 8px";
`

export type SearchGreyProps = TextFieldProps & {
  onSearchKeyDown(value: string): void
}

export const SearchGrey = <
  PropType extends SearchGreyProps = SearchGreyProps
>({
  fullWidth = true,
  variant = "outlined",
  onSearchKeyDown,
  ...otherProps
}: PropType) => {
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const value = (e.target as any).value
      console.log(value)
      onSearchKeyDown(value)
    }
  }
  return (
    <StyledTextField
      {...otherProps}
      fullWidth={fullWidth}
      variant={variant}
      onKeyDown={handleKeyDown}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <StyledSearchIcon />
          </InputAdornment>
        ),
      }}
    />
  )
}
