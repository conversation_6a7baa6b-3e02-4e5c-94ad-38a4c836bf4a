import React, { useState, useRef, useEffect } from "react";
import { Box, Typography, LinearProgress, IconButton } from "@mui/material";
import { Delete as DeleteIcon } from "@mui/icons-material";
import { DocumentUploadType, useQuery, useUploadPresignedUrl } from "@/helpers";
import { API_URL } from "@/api";
import { t } from "i18next";

interface UploadingFile {
  name: string;
  progress: number;
  size: string;
  isComplete?: boolean;
}

interface DocumentUploadProps {
  ajkId: number;
  id: number;
  documentType: number;
  disabled?: boolean;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  ajkId,
  id,
  documentType,
  disabled,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);

  const { upload } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      fetchDocuments();
    },
    onUploadProgress: (progress, fileName) => {
      setUploadingFiles((prev) =>
        prev.map((f) =>
          f.name === fileName ? { ...f, progress, isComplete: true } : f
        )
      );
    },
  });

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const { refetch: fetchDocuments } = useQuery({
    url: `society/document/documentByParam`,
    filters: [
      { field: "societyId", operator: "eq", value: id },
      documentType == DocumentUploadType.NON_CITIZEN_COMMITTEE
        ? { field: "societyNonCommitteeId", operator: "eq", value: ajkId }
        : { field: "societyCommitteeId", operator: "eq", value: ajkId },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setUploadedFiles(data?.data?.data || []);
    },
  });

  useEffect(() => {
    if (ajkId) fetchDocuments();
  }, [ajkId]);

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files) return;

    if (ajkId) {
      Array.from(files).forEach(async (file) => {
        setUploadingFiles((prev) => [
          ...prev,
          {
            name: file.name,
            progress: 0,
            size: formatFileSize(file.size),
          },
        ]);

        await upload({
          params: {
            type: documentType,
            societyId: id ? id : 0,
            note: "DOCUMENT",
            [documentType == DocumentUploadType.NON_CITIZEN_COMMITTEE
              ? "societyNonCommitteeId"
              : "societyCommitteeId"]: ajkId,
          },
          file,
        });
      });
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (!files) return;

    if (ajkId) {
      Array.from(files).forEach(async (file) => {
        setUploadingFiles((prev) => [
          ...prev,
          {
            name: file.name,
            progress: 0,
            size: formatFileSize(file.size),
          },
        ]);

        await upload({
          params: {
            type: documentType,
            societyId: id ? id : 0,
            note: "CITIZEN COMMITTEE DOCUMENT",
            societyCommitteeId: ajkId,
          },
          file,
        });
      });
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const response = await fetch(
        `${API_URL}/society/document/deleteDocument?id=${documentId}`,
        {
          method: "PUT",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );

      if (response.ok) {
        setUploadedFiles((prev) =>
          prev.filter((file) => file.id !== documentId)
        );
      }
    } catch (error) {
      console.error("Delete error:", error);
    }
  };

  return (
    <Box
      sx={{
        pl: 2,
        p: 3,
        borderRadius: "10px",
        border: "0.5px solid #dfdfdf",
        mt: 1,
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          mb: 2,
          gap: 2,
        }}
      >
        <Box>
          <Typography color="primary">{t("ajkEligibilityCheck")}</Typography>
        </Box>
      </Box>

      <Box
        sx={{
          border: "2px dashed #e0e0e0",
          borderRadius: 1,
          p: 4,
          mb: 2,
          textAlign: "center",
          cursor: "pointer",
          "&:hover": {
            backgroundColor: "#f5f5f5",
          },
        }}
        onClick={() => {
          !disabled ? fileInputRef.current?.click() : null;
        }}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: "none" }}
          onChange={handleFileUpload}
          multiple
          accept=".pdf,.docx,.txt"
        />
        <Typography
          sx={{
            fontFamily: "DM Sans",
            fontSize: "18px",
            fontWeight: 500,
            lineHeight: "18.23px",
            textAlign: "center",
            color: "#222222",
            marginBottom: "15px",
          }}
        >
          Click to upload
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: "var(--primary-color)",
            display: "flex",
            gap: 1,
            justifyContent: "center",
          }}
        >
          <Box
            component="span"
            sx={{ bgcolor: "#3276E81A", px: 1, py: 0.5, borderRadius: 1 }}
          >
            PDF
          </Box>
          <Box
            component="span"
            sx={{ bgcolor: "#3276E81A", px: 1, py: 0.5, borderRadius: 1 }}
          >
            DOCX
          </Box>
          <Box
            component="span"
            sx={{ bgcolor: "#3276E81A", px: 1, py: 0.5, borderRadius: 1 }}
          >
            TXT
          </Box>
          <Box
            component="span"
            sx={{ bgcolor: "#E3F2FD", px: 1, py: 0.5, borderRadius: 1 }}
          >
            {"<"}25 MB
          </Box>
        </Typography>
      </Box>

      <Box sx={{ textAlign: "left", mt: 2 }}>
        {/* Show files being uploaded */}
        {uploadingFiles.map(
          (file, index) =>
            !file.isComplete && (
              <Box
                key={index}
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "8px",
                  backgroundColor: "#fff",
                  p: 2,
                  mb: 1,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mb: 1,
                  }}
                >
                  <Box>
                    <Typography>{file.name}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "start", gap: 1 }}>
                    {file.isComplete && (
                      <Typography
                        sx={{
                          color: "var(--primary-color)",
                          fontSize: "0.875rem",
                          mt: 0.5,
                        }}
                      >
                        Upload complete
                      </Typography>
                    )}
                  </Box>
                </Box>
                {!file.isComplete && (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      gap: 2,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ color: "var(--primary-color)", minWidth: "180px" }}
                    >
                      {`${file.progress}%`} • Uploading • {file.size}
                    </Typography>

                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 2,
                        width: "300px",
                      }}
                    >
                      <LinearProgress
                        variant="determinate"
                        value={file.progress}
                        sx={{
                          flex: 1,
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: "#E0E0E0",
                          "& .MuiLinearProgress-bar": {
                            backgroundColor: "#00BCD4",
                            borderRadius: 3,
                          },
                        }}
                      />
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteDocument(file.name)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </Box>
                )}
              </Box>
            )
        )}

        {/* Show uploaded files */}
        {uploadedFiles.map((file) => (
          <Box
            key={file.id}
            sx={{
              border: "1px solid #E0E0E0",
              borderRadius: "8px",
              backgroundColor: "#fff",
              p: 2,
              mb: 1,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                mb: 1,
              }}
            >
              <Box>
                <Typography
                  onClick={() => window.open(file.url, "_blank")}
                  sx={{
                    cursor: "pointer",
                    "&:hover": {
                      color: "var(--primary-color)",
                      textDecoration: "underline",
                    },
                  }}
                >
                  {file.name}
                </Typography>
                <Typography
                  sx={{
                    color: "var(--primary-color)",
                    fontSize: "0.875rem",
                    mt: 0.5,
                  }}
                >
                  Upload complete
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <IconButton
                  size="small"
                  onClick={() => handleDeleteDocument(file.id)}
                  sx={{ color: "#9E9E9E" }}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default DocumentUpload;
