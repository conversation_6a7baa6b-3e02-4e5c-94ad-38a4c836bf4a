import { useParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/redux/store";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  setIsSecretaryReformSuccess,
  handleNext,
  reset,
} from "@/redux/secretaryBranchReformReducer";
import {
  useMutation,
  useQuery,
  omitKeysFromObject,
  filterEmptyValuesOnObject,
  MALAYSIA,
} from "@/helpers";
// import { useSecretaryBranchReformContext } from "../Provider";

import {
  Box,
  Fade,
  Typography,
  Grid,
  FormControl,
  Select,
  TextField,
  useMediaQuery,
  Theme,
  MenuItem,
} from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
// import MeetingForm from "../view/MeetingForm";
import { IApiResponse, ISecretaryBranch } from "@/types";
import CustomSkeleton from "@/components/custom-skeleton";
import { useEffect, useState } from "react";
import CustomPopover from "@/components/popover";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { MapContainer, Marker, TileLayer, useMap } from "react-leaflet";
import { useCawanganPindaanContext } from "../../component/CawanganPindaan/CawanganPindaanProvider";

interface MeetingMemberAttendance {
  id: number;
  name: string;
  position: string;
}

interface MeetingForm {
  meetingId: string;
  branchId: number | null;
  branchNo: string | null;
  city: string;
  closing: string;
  confirmBy: string;
  district: string;
  id: number;
  mattersDiscussed: string;
  meetingAddress: string;
  meetingContent: string;
  meetingDate: string;
  meetingMemberAttendances: MeetingMemberAttendance[];
  meetingMethod: string;
  meetingMinute: string;
  meetingPlace: string;
  meetingPurpose: string;
  meetingTime: string;
  meetingTimeDurationMinutes: string;
  meetingTimeTo: string;
  meetingType: string;
  openingRemarks: string;
  otherMatters: string;
  platformType: string;
  postcode: string;
  providedBy: string;
  societyId: number;
  societyNo: string | null;
  state: string;
  totalAttendees: number;
}

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const MeetingForm: React.FC = () => {
  // const dispatch = useDispatch();
  const { id, branchId } = useParams();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { branchAmendmentList } = useCawanganPindaanContext();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [disabledNext, setDisableNext] = useState(true);
  const [meetinglist, setMeetinglist] = useState([]);
  const [meetingForm, setMeetingForm] = useState<MeetingForm>({
    meetingId: "",
    branchId: null,
    branchNo: null,
    city: "",
    closing: "",
    confirmBy: "",
    district: "",
    id: 0,
    mattersDiscussed: "",
    meetingAddress: "",
    meetingContent: "",
    meetingDate: "",
    meetingMemberAttendances: [],
    meetingMethod: "",
    meetingMinute: "",
    meetingPlace: "",
    meetingPurpose: "",
    meetingTime: "",
    meetingTimeDurationMinutes: "",
    meetingTimeTo: "",
    meetingType: "",
    openingRemarks: "",
    otherMatters: "",
    platformType: "",
    postcode: "",
    providedBy: "",
    societyId: 0,
    societyNo: null,
    state: "",
    totalAttendees: 0,
  });

  function reset() {
    setMeetingForm({
      meetingId: "",
      branchId: null,
      branchNo: null,
      city: "",
      closing: "",
      confirmBy: "",
      district: "",
      id: 0,
      mattersDiscussed: "",
      meetingAddress: "",
      meetingContent: "",
      meetingDate: "",
      meetingMemberAttendances: [],
      meetingMethod: "",
      meetingMinute: "",
      meetingPlace: "",
      meetingPurpose: "",
      meetingTime: "",
      meetingTimeDurationMinutes: "",
      meetingTimeTo: "",
      meetingType: "",
      openingRemarks: "",
      otherMatters: "",
      platformType: "",
      postcode: "",
      providedBy: "",
      societyId: 0,
      societyNo: null,
      state: "",
      totalAttendees: 0,
    });
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setMeetingForm((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const { data: addressList } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressListData = addressList?.data?.data || [];

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/meeting/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const meetingMethodData = data?.data?.data || [];

  const {
    data: meetingData,
    isLoading: isLoadingMeetingData,
    refetch: refetchMeetingData,
  } = useCustom({
    url: `${API_URL}/society/meeting/search`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        meetingDate: meetingForm.meetingDate,
      },
    },
    queryOptions: {
      enabled: !!meetingForm.meetingDate,
      onSuccess: (data) => {
        const meetingSearchlist = data?.data?.data?.data || [];
        if (meetingSearchlist.length > 0) {
          setMeetinglist(meetingSearchlist);
        }
      },
    },
  });

  const {
    data: meetingSearchReuslt,
    isLoading: isLoadingSearchReuslt,
    refetch: refetchSearchReuslt,
  } = useCustom({
    url: `${API_URL}/society/meeting/${meetingForm.meetingId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!meetingForm.meetingId,
      onSuccess: (data) => {
        const meetingListById = data?.data?.data;
        if (meetingListById) {
          setMeetingForm((prevState) => ({
            ...prevState,
            ...meetingListById,
          }));
        }
      },
    },
  });

  const { mutate: updateMeetingData, isLoading: isLoadingUpdateMeetingData } =
    useCustomMutation();

  const updateMeeting = () => {
    updateMeetingData(
      {
        url: `${API_URL}/society/external/branchAmendment/update`,
        method: "patch",
        values: {
          id: branchId,
          meetingId: meetingForm.id,
          meetingType: meetingForm.meetingType,
          meetingDate: meetingForm.meetingDate,
          meetingPlace: meetingForm.meetingPlace,
          meetingTime: meetingForm.meetingTime,
          totalAttendees: meetingForm.totalAttendees,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanError"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setDisableNext(false);
        },
      }
    );
  };

  const RecenterAutomatically = ({
    lat,
    lng,
  }: {
    lat: number;
    lng: number;
  }) => {
    const map = useMap();
    useEffect(() => {
      map.setView([lat, lng]);
    }, [lat, lng]);
    return null;
  };

  const [meetingCoords, setMeetingCoords] = useState<[number, number]>([
    2.745564, 101.707021,
  ]);

  const goNext = () => {
    navigate(
      `/pertubuhan/society/${id}/senarai/cawangan/branch-Info/amend/${branchId}`
    );
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <Typography
              sx={{
                color: "#FF0000",
                fontSize: "12px",
                fontWeight: "500 !important",
              }}
            >
              {t("peringatan")} :
            </Typography>
            <Typography
              sx={{
                color: "#666666",
                fontSize: "12px",
                fontWeight: "400 !important",
              }}
            >
              {" "}
              Jika Senarai Mesyuarat tiada dalam pilihan, klik disini bagi
              kemasukan maklumat mesyuarat pindaan nama dan alamat cawangan.
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            Maklumat Mesyuarat meluluskan pindaan
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  {t("meetingDate")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextField
                size="small"
                fullWidth
                required
                name="meetingDate"
                value={meetingForm.meetingDate}
                onChange={(e) => {
                  setMeetingForm((prevState: any) => ({
                    ...prevState,
                    meetingDate: e.target.value,
                  }));
                }}
                inputProps={{
                  max: new Date().toISOString().split("T")[0],
                  onKeyDown: (e) => e.preventDefault(),
                }}
                type="date"
              />
            </Grid>

            {/* Senarai mesyuarat* */}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingList")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingForm.meetingId}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setMeetingForm((prevState: any) => ({
                      ...prevState,
                      meetingId: e.target.value,
                    }));
                  }}
                >
                  {meetinglist?.map((items: any, index) => {
                    return (
                      <MenuItem key={index} value={items.id}>
                        {items.meetingType} ({items.meetingDate})
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </Grid>
            {/* Kaedah Mesyuarat*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingMethod")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  disabled
                  value={meetingForm.meetingMethod}
                  name="meetingMethod"
                  displayEmpty
                  required
                  onChange={(e) => {
                    setMeetingForm((prevState: any) => ({
                      ...prevState,
                      meetingMethod: e.target.value,
                    }));
                  }}
                >
                  {!isLoading &&
                    meetingMethodData
                      .filter((item: any) => item.pid === 2)
                      .map((item: any) => (
                        <MenuItem key={item.id} value={item.id}>
                          {i18n.language === "en"
                            ? item.nameEn
                            : item.nameBm == "Hybrid"
                            ? "Hibrid"
                            : item.nameBm}
                        </MenuItem>
                      ))}
                </Select>
              </FormControl>
            </Grid>
            {/* Jenis Platform*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("platformType")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="platformType"
                  value={meetingForm.platformType}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>
            {/* Tujuan Mesyuarat*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingPurpose")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="meetingPurpose"
                  value={meetingForm.meetingPurpose}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>
            {/* Masa */}

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("Masa")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>

            <Grid item xs={12} sm={9} gap={1} sx={{ display: "flex" }}>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <TextField
                    size="small"
                    disabled
                    fullWidth
                    required
                    name="meetingTime"
                    value={meetingForm.meetingTime}
                    onChange={handleInputChange}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <TextField
                    size="small"
                    disabled
                    fullWidth
                    required
                    name="meetingTimeTo"
                    value={meetingForm.meetingTimeTo}
                    onChange={handleInputChange}
                  />
                </FormControl>
              </Grid>
            </Grid>

            {/* Nama tempat mesyuarat */}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("namaTempatMesyuarat")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="meetingAddress"
                  value={meetingForm.meetingAddress}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingLocation")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <Box>
                <MapContainer
                  center={meetingCoords}
                  zoom={13}
                  style={{
                    height: "150px",
                    width: "100%",
                    borderRadius: "8px",
                  }}
                >
                  <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                  <Marker position={meetingCoords} />
                  <RecenterAutomatically
                    lat={meetingCoords[0]}
                    lng={meetingCoords[1]}
                  />
                </MapContainer>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("alamatTempatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            {/* Alamat tempat mesyuarat*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingPlaceAddress")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="meetingAddress"
                  value={meetingForm.meetingAddress}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>
            {/* Negeri*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("negeri")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingForm.state}
                  displayEmpty
                  disabled
                  name="state"
                  required
                  onChange={(e) => {
                    setMeetingForm((prevState: any) => ({
                      ...prevState,
                      state: e.target.value,
                    }));
                  }}
                >
                  {addressListData
                    .filter((item: any) => item.pid === MALAYSIA)
                    .map((item: any) => (
                      <MenuItem key={item.id} value={item.id}>
                        {item.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            {/* Daerah*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("daerah")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingForm.district}
                  disabled
                  displayEmpty
                  name="district"
                  required
                  onChange={(e) => {
                    setMeetingForm((prevState: any) => ({
                      ...prevState,
                      district: e.target.value,
                    }));
                  }}
                >
                  {addressListData
                    .filter((item: any) => item.pid == meetingForm.state)
                    .map((item: any) => (
                      <MenuItem key={item.id} value={item.id}>
                        {item.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            {/* Bandar*/}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("bandar")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="city"
                  value={meetingForm.city}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>

            {/* Poskod */}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("postcode")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="postcode"
                  value={meetingForm.postcode}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("maklumatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("totalAttendMember")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="totalAttendees"
                  value={meetingForm.totalAttendees}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("isiMaklumatMesyuarat")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <FormControl fullWidth>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  name="meetingContent"
                  value={meetingForm.meetingContent}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("kehadiranAhliMesyuarat")}
          </Typography>
          <Grid container spacing={2}>
            <Grid
              item
              xs={3}
              sx={{
                display: "flex",
                justifyContent: "start",
                alignItems: "center",
                gap: 1,
              }}
            >
              <Typography sx={labelStyle}>{t("memberAttendance")}</Typography>
            </Grid>
            <Grid item xs={9}>
              <Grid container gap={1}>
                {meetingForm.meetingMemberAttendances &&
                meetingForm.meetingMemberAttendances.length > 0 ? (
                  meetingForm.meetingMemberAttendances?.map((member: any) => (
                    <Grid
                      key={member.id}
                      item
                      xs={12}
                      gap={1}
                      sx={{ display: "flex" }}
                    >
                      <Grid item xs={6}>
                        <TextField
                          size="small"
                          disabled
                          fullWidth
                          required
                          name="name"
                          value={member.name}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <TextField
                          size="small"
                          disabled
                          fullWidth
                          required
                          name="position"
                          value={member.position}
                        />
                      </Grid>
                    </Grid>
                  ))
                ) : (
                  <Grid item xs={12} gap={1} sx={{ display: "flex" }}>
                    <Grid item xs={6}>
                      <TextField
                        size="small"
                        disabled
                        fullWidth
                        required
                        name="name"
                        value={"-"}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        size="small"
                        disabled
                        fullWidth
                        required
                        name="position"
                        value={"-"}
                      />
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("meetingInformation")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("minitMesyuarat")}</Typography>
            </Grid>

            <Grid item xs={12} sm={9}></Grid>

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("ucapanAluanPengerusi")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <TextField
                size="small"
                disabled
                fullWidth
                multiline
                rows={4}
                name="openingRemarks"
                value={meetingForm.openingRemarks}
                onChange={handleInputChange}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("penutup")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <TextField
                size="small"
                disabled
                fullWidth
                multiline
                rows={4}
                name="closingRemarks"
                value={meetingForm.closing}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("disediakanOleh")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <TextField
                size="small"
                disabled
                fullWidth
                name="providedBy"
                value={meetingForm.providedBy}
                onChange={handleInputChange}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("disahkanOleh")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <TextField
                size="small"
                disabled
                fullWidth
                name="confirmBy"
                value={meetingForm.confirmBy}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        </Box>

        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => reset()}
            >
              {t("semula")}
            </ButtonOutline>

            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              onClick={updateMeeting}
              disabled={!meetingForm.meetingId || isLoadingUpdateMeetingData}
            >
              {t("update")}
            </ButtonPrimary>
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              disabled={disabledNext}
              onClick={() => goNext()}
            >
              {t("seterusnya")}
            </ButtonPrimary>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default MeetingForm;
