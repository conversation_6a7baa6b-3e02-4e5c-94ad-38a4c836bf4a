import { useTranslation } from "react-i18next";

import { Typography, Box } from "@mui/material";
import FormFieldRow from "../../../../components/form-field-row";
import Label from "../../../../components/label/Label";
import DisabledTextField from "../../../../components/input/DisabledTextField";
import {
  DocumentUploadType,
  IdTypes,
  OrganisationPositions,
  useQuery,
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { useState } from "react";
import dayjs from "@/helpers/dayjs";

type MaklumatPermohonanSectionProps = {
  societyDataById: any;
  NonCitizenCommitteeData: any;
};

const MaklumatPermohonanSection: React.FC<MaklumatPermohonanSectionProps> = ({
  societyDataById,
  NonCitizenCommitteeData,
}) => {
  const { t } = useTranslation();
  const [addressData, setAddressList] = useState<any>();

  useQuery({
    url: `society/admin/address/list`,
    autoFetch: true, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAddressList(data?.data?.data || []);
    },
  });

  return (
    <Box
      sx={{
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("pertubuhan")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("organization_name")} />}
          value={
            <DisabledTextField value={societyDataById?.societyName ?? "-"} />
          }
        />

        <FormFieldRow
          label={<Label text={t("organizationNumber")} />}
          value={
            <DisabledTextField value={NonCitizenCommitteeData?.societyNo ?? "-"} />
          }
        />

        <FormFieldRow
          label={<Label text={t("stateOrganization")} />}
          value={
            <DisabledTextField value={societyDataById?.createdDate ?? "-"} />
          }
        />
      </Box>

      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("nonCitizenSecretaryInformation")}
        </Typography>
        <FormFieldRow
          label={<Label text={t("fullNameCapitalizedOnlyFirstLetter")} />}
          value={<DisabledTextField value={NonCitizenCommitteeData?.name} />}
        />
        <FormFieldRow
          label={<Label text={t("citizenship")} />}
          value={
            <DisabledTextField
              value={
                NonCitizenCommitteeData?.citizenshipStatus ?? t("nonCitizen")
              }
            />
          }
        />
        <FormFieldRow
          label={<Label text={t("idTypeCapitalizedOnlyFirstLetter")} />}
          value={
            <DisabledTextField
              value={t(
                `${
                  IdTypes.find(
                    (item) =>
                      item.value === NonCitizenCommitteeData?.identificationType
                  )?.label || "-"
                }`
              )}
            />
          }
        />
        <FormFieldRow
          label={<Label text={t("idNumberCapitalizedOnlyFirstLetter")} />}
          value={
            <DisabledTextField
              value={NonCitizenCommitteeData?.identificationNo ?? "-"}
            />
          }
        />
        <FormFieldRow
          label={<Label text={t("originCountry")} />}
          value={
            <DisabledTextField
              value={
                addressData
                  ? addressData
                      .filter((item: any) => item.pid === 0)
                      .find(
                        (item: any) =>
                          item.id ===
                          Number(NonCitizenCommitteeData?.applicantCountryCode)
                      )?.name
                  : "-"
              }
            />
          }
        />
        <FormFieldRow
          label={<Label text={t("nomborVisa")} />}
          value={
            <DisabledTextField value={NonCitizenCommitteeData?.visaNo ?? "-"} />
          }
        />
        <FormFieldRow
          label={<Label text={t("visaExpiryDate")} />}
          value={
            <DisabledTextField
              value={
                NonCitizenCommitteeData.visaExpirationDate
                  ? dayjs(NonCitizenCommitteeData.visaExpirationDate, 'DD/MM/YY').format(
                      "DD-MM-YYYY"
                    )
                  : "-"
              }
            />
          }
        />
        <FormFieldRow
          label={<Label text={t("nomborPermit")} />}
          value={
            <DisabledTextField
              value={NonCitizenCommitteeData?.permitNo ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("permitExpiryDate")} />}
          value={
            <DisabledTextField
              value={
                NonCitizenCommitteeData.permitExpirationDate
                  ? dayjs(NonCitizenCommitteeData.permitExpirationDate, 'DD/MM/YY').format(
                      "DD-MM-YYYY"
                    )
                  : "-"
              }
            />
          }
        />
        <FormFieldRow
          label={<Label text={t("tujuanDiMalaysia")} />}
          value={
            <DisabledTextField
              value={NonCitizenCommitteeData?.tujuanDMalaysia}
            />
          }
        />
        <FormFieldRow
          label={<Label text={t("tempohDiMalaysia")} />}
          value={
            <DisabledTextField
              value={`${NonCitizenCommitteeData.stayDurationDigit} ${t(
                NonCitizenCommitteeData.stayDurationUnit)}`}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("position")} />}
          value={
            <DisabledTextField
              value={t(
                `${
                  OrganisationPositions.find(
                    (item) =>
                      item?.value ===
                      parseInt(NonCitizenCommitteeData?.designationCode)
                  )?.label || "-"
                }`
              )}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("importanceOfPosition2")} />}
          value={<DisabledTextField multiline row={4} value={NonCitizenCommitteeData?.summary ?? NonCitizenCommitteeData?.otherDesignationCode} />}
        />
      </Box>

      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
        }}
      >
        {NonCitizenCommitteeData?.identificationNo && (
          <FileUploader
            title={"ajkEligibilityCheck"}
            type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
            icNo={NonCitizenCommitteeData?.identificationNo}
            societyId={NonCitizenCommitteeData?.societyId}
            societyNonCitizenCommitteeId={NonCitizenCommitteeData?.id}
            disabled={true}
            validTypes={[
              "text/plain",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              "application/msword",
              "application/pdf",
            ]}
          />
        )}
      </Box>
    </Box>
  );
};

export default MaklumatPermohonanSection;
