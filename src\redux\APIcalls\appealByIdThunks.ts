import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setAppealByIdDataRedux, setAppealByIdError, setAppealByIdLoading } from '../appealByIdDataReducer';

export const fetchAppealByIdData = createAsyncThunk(
  'appealById/fetchData',
  async ({ id }: { id: string },
    { dispatch }) => {
    dispatch(setAppealByIdLoading(true));
    try {
      const response = await fetch(`${API_URL}/society/appeal/getById/${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
      const data = await response.json();
      // console.log('data from think', data.data)
      dispatch(setAppealByIdDataRedux(data?.data));
    } catch (error: any) {
      dispatch(setAppealByIdError(error.message));
    } finally {
      dispatch(setAppealByIdLoading(false));
    }
  }
);
