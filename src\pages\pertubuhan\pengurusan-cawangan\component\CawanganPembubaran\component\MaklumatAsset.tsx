import StepperComponent from "@/components/stepper/StepperComponent";
import { steps } from "../../../../pembubaran/add-pembubaran/constant";
import CampaignAlert from "@/components/alert/Campaign";
import { useTranslation } from "react-i18next";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import { DeleteButton, EditButton } from "@refinedev/mui";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useState } from "react";
import Modal from "@/components/dialog/modal/Moda";
import Input from "@/components/input/Input";
import InputCheckbox from "@/components/input/InputChekbox";

function MaklumatAsset() {
  const { t } = useTranslation();
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [addModalMaklumatAsset, setModalMaklumatAsset] = useState(false);
  const [checkedOther, setCheckedOther] = useState(false);
  return (
    <div>
      <StepperComponent activeStep={1} steps={steps} />
      <CampaignAlert>{t("maklumatAssetCampaign")}</CampaignAlert>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mb: 3,
        }}
      >
        <ButtonPrimary
          onClick={() => {
            setModalMaklumatAsset(true);
          }}
        >
          {t("add")}
        </ButtonPrimary>
      </Box>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: "none",
          border: "1px solid #e0e0e0",
          backgroundColor: "white",
          borderRadius: 2.5 * 1.5,
          p: 1,
          mb: 3,
        }}
      >
        <Table sx={{ width: "100%" }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell>Jenis Asset</TableCell>
              <TableCell>Nilai (RM)</TableCell>
              <TableCell>Cara Penyelesaian</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow
              sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
            >
              <TableCell width={"200"}>{"Warung Tunai"}</TableCell>
              <TableCell width={"200"}>{"1500.00"}</TableCell>
              <TableCell>
                Didermakan kepada organisasi lain/ pertubuhan berdaftar yang
                mempunyai matlamat yang serupa.
              </TableCell>
              <TableCell width={"50"}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <DeleteButton hideText />
                  <EditButton hideText />
                </Box>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mb: 3,
          mt: 3,
        }}
      >
        <ButtonPrimary
          onClick={() => {
            setDialogSaveOpen(true);
          }}
        >
          {t("next")}
        </ButtonPrimary>
      </Box>

      <ConfirmationDialog
        open={dialogSaveOpen}
        onClose={() => setDialogSaveOpen(false)}
        title="Keputusan Permohonan"
        message="Apakah anda pasti dengan keputusan permohonan ini?"
        onConfirm={() => {
          setDialogSaveOpen(false);
        }}
        onCancel={() => setDialogSaveOpen(false)}
        status={0}
      />

      <Modal
        open={addModalMaklumatAsset}
        onClose={() => setModalMaklumatAsset(false)}
        title={t("maklumatAsset")}
      >
        <Box
          sx={{
            borderRadius: "10px",
            border: "1px solid #D9D9D9",
            padding: "10px",
          }}
        >
          <Input
            subTitle="(Contoh: Bangunan, tanah dan lain-lain)"
            required
            label="Jenis Asset"
          />
          <Input required label="Nilai RM" />
          <InputCheckbox
            labelCheckbox="Didermakan kepada organisasi lain/ pertubuhan berdaftar yang mempunyai matlamat yang serupa"
            required
            label="Cara Penyelesaian"
          />
          <InputCheckbox labelCheckbox="Pembayaran liabiliti jangka panjang/pendek." />
          <InputCheckbox labelCheckbox="Tiada baki." />
          <InputCheckbox
            value={checkedOther}
            labelCheckbox="Lain-lain (terhad kepada 100 patah perkataan)"
            onChange={(e) => {
              setCheckedOther(e.target.checked);
            }}
          />
          <Input rows={4} multiline placeholder="Sila nyatakan" />
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            mb: 3,
            mt: 3,
          }}
        >
          <ButtonPrimary
            onClick={() => {
              setModalMaklumatAsset(false);
            }}
          >
            {t("save")}
          </ButtonPrimary>
        </Box>
      </Modal>
    </div>
  );
}

export default MaklumatAsset;
