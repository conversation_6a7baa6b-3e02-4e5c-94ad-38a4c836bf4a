import React from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { InputLabel, Grid, Stack, Typography } from "@mui/material";
import Checkbox from "@mui/material/Checkbox";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import SectionHeader from "@/components/header/section/SectionHeader";
import { OutlinedInput } from "@/components/input";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import Input from "@/components/input/Input";

export const CreatePernyataanPendapatanMaklumatAm: React.FC<{
  t: TFunction;
  year: number;
}> = ({ t, year }) => {
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("generalInformation")}
          </Typography>

          <Grid>
            <Input label={t("annualStatement")} value={year} disabled />
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default CreatePernyataanPendapatanMaklumatAm;
