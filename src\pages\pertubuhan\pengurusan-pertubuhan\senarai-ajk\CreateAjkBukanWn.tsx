import React, { useEffect, useState } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useSearchParams } from "react-router-dom";
import ButtonPrimary from "../../../../components/button/ButtonPrimary";
import { OrganizationStepper } from "../organization-stepper";
import { ButtonOutline } from "../../../../components/button";
import { Controller, useForm } from "react-hook-form";
import Input from "../../../../components/input/Input";
import useMutation from "../../../../helpers/hooks/useMutation";
import { API_URL } from "../../../../api";
import { useBack, useCustom, useCustomMutation } from "@refinedev/core";
import {
  ApplicationStatus,
  CitizenshipStatus,
  IdTypes,
  OrganisationPositions,
  DurationOptions,
  GenderType,
} from "../../../../helpers/enums";
import { useSelector } from "react-redux";
import InfoQACard from "../InfoQACard";
import FileUploader from "@/components/input/fileUpload";
import {
  capitalizeWords,
  DocumentUploadType,
  formatArrayDate,
  useQuery,
} from "@/helpers";
import dayjs from "dayjs";

interface IFormNonCommite {
  name: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  applicantCountryCode: string;
  visaExpirationDate: string; // ISO 8601 format
  permitExpirationDate: string; // ISO 8601 format
  visaNo: string;
  permitNo: string;
  positionCode: string;
  visaPermitNo: string;
  tujuanDMalaysia: string;
  tempohDMalaysia: string | undefined;
  stayDurationDigit: number;
  stayDurationUnit: string;
  durationType: string;
  summary: string;
  societyName: string;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  applicationStatusCode: number;
  status: number;
  ro: string; // Regional Office
  pembaharuanSu: string; // Renewal Required
  pemCaw: string; // Yes or No
  otherPosition: string;
  transferDate: string; // ISO 8601 format
  noteRo: string; // Note for Regional Office
  designationCode: string;
  activeCommitteeId: string;
  gender: string;
}

export const CreateAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const back = useBack();
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(3);

  const handleSenaraiAjk = () => {
    navigate(-1);
  };

  const form = useForm<IFormNonCommite>();
  const {
    control,
    formState: { errors },
    watch,
    handleSubmit,
    reset,
    getValues,
    clearErrors,
    setValue,
  } = form;

  const mutate = useMutation({
    url: "society/nonCitizenCommittee/create",
    onSuccess: () => {
      reset({});
    },
  });

  useEffect(() => {
    if (Number(watch("identificationType")) === 4) {
      clearErrors("tempohDMalaysia");
      clearErrors("stayDurationDigit");
      clearErrors("stayDurationUnit");
    }
  }, [
    watch("tempohDMalaysia"),
    watch("stayDurationDigit"),
    watch("stayDurationUnit"),
    watch("identificationType"),
  ]);

  const { mutateAsync: updateAJK, isLoading: isLoadingAJK } =
    useCustomMutation();

  const onSubmit = async (value: IFormNonCommite) => {
    const values = getValues() as any;
    if (isEdit) {
      await updateAJK({
        url: `${API_URL}/society/nonCitizenCommittee/${values.id}/edit`,
        method: "put",
        values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      });
    } else {
      await mutate.fetchAsync({ ...values });
    }
    back();
  };
  const [params] = useSearchParams();
  const encodedId = params.get("id");
  const societyId = atob(encodedId ?? "");
  const [isEdit, setIsEdit] = useState(false);
  const [userICCorrect, setUserICCorrect] = useState(false);
  const [userNameMatchIC, setUserNameMatchIC] = useState(false);
  const [positionList, setPositionList] = useState<
    { value: number; label: string; designationCode?: string }[]
  >([]);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    // reset({});
    if (encodedId && societyDataRedux) {
      const decodedId = atob(encodedId);

      reset((prev) => ({
        ...prev,
        societyId: +decodedId,
        societyName: societyDataRedux.societyName,
        societyNo: societyDataRedux.applicationNo || "GOS-2022",
      }));
    }
  }, [isEdit]);

  useEffect(() => {
    if (params.get("value")) {
      try {
        const encodedValue = params.get("value") || "";
        const decodedValue = decodeURIComponent(encodedValue);
        const parseValue = JSON.parse(decodedValue);
        parseValue.designationCode = parseInt(parseValue.designationCode);
        reset((prev) => ({
          ...prev,
          ...parseValue,
          ...(parseValue?.visaExpirationDate && {
            visaExpirationDate: formatArrayDate(
              parseValue?.visaExpirationDate,
              "YYYY-MM-DD"
            ),
          }),
          ...(parseValue?.permitExpirationDate && {
            permitExpirationDate: formatArrayDate(
              parseValue?.permitExpirationDate,
              "YYYY-MM-DD"
            ),
          }),
        }));
        setIsEdit(true);
      } catch (error) {
        console.error("Error parsing JSON:", error);
      }
    }
  }, []);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  // @ts-ignore
  const addressData = useSelector((state) => state.addressData.data);

  const value = watch();
  const readOnly = value.applicationStatusCode === 2;

  const { data, isLoading } = useCustom<any>({
    url: `${API_URL}/user/auth/validateId`,
    method: "get",
    config: {
      query: {
        identificationNo: watch("identificationNo"),
        name: watch("name")?.trim().toUpperCase(),
        sessionIdentificationNo: watch("identificationNo"),
      },
    },
    queryOptions: {
      enabled:
        watch("identificationNo")?.length > 11 &&
        !!watch("name") &&
        watch("identificationType") === "4",
      retry: false,
      cacheTime: 0,
    },
    successNotification(data) {
      //reset before new call
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      const { name, message, status, userExist, integrationOff } =
        data?.data?.data || {};

      if (!integrationOff) {
        if (status === "Y") {
          setUserICCorrect(true);
          if (name) {
            setUserNameMatchIC(true);
          }
        } else {
          setUserICCorrect(false);
          setUserNameMatchIC(true);
        }
      }

      return false;
    },
    errorNotification(error) {
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      return false;
    },
  });

  let identificationNoHelperText: string | undefined = undefined;
  if (watch("identificationType") === "4") {
    if (errors.identificationNo?.message) {
      identificationNoHelperText = errors.identificationNo.message;
    } else if (watch("identificationNo")?.length === 12 && !userICCorrect) {
      identificationNoHelperText = t("IcDoesNotExist");
    }
  } else if (errors.identificationNo?.message) {
    identificationNoHelperText = errors.identificationNo.message;
  }

  let nameHelperText: string | undefined = undefined;
  if (watch("identificationType") === "4") {
    if (errors.name?.message) {
      nameHelperText = errors.name.message;
    } else if (
      watch("identificationNo")?.length === 12 &&
      watch("name")?.trim() !== undefined &&
      !userNameMatchIC
    ) {
      nameHelperText = t("invalidName");
    }
  } else if (errors.name?.message) {
    nameHelperText = errors.name.message;
  }

  const {
    data: positionListRes,
    isLoading: isLoadingPositionListRes,
    refetch: refetchSocietyList,
  } = useQuery({
    url: "society/nonCitizenCommittee/getPositionsList",
    filters: [
      {
        field: "societyId",
        value: societyId,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        console.log("list", data?.data?.data);
        const newList = data?.data?.data?.map((item: any) => {
          const position = OrganisationPositions.find(
            (p) => p.value === Number(item.designationCode)
          );
          const label = position?.label
            ? `${t(position.label)}${
                item.positionHolder ? ` - ${item.positionHolder}` : ""
              }`
            : item.designationCode;

          return {
            label,
            value: Number(item?.activeCommitteeId),
            designationCode: item.designationCode,
          };
        });

        setPositionList(newList);
      }
    },
  });

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#fff",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              px: 2,
              py: 1,
              mb: 3,
              borderRadius: "14px",
            }}
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                fontWeight: "bold",
                fontSize: 14,
                color: "var(--primary-color)",
              }}
            >
              {t("nonCitizenAJKInfo")}
            </Typography>
          </Box>

          <Box sx={{ pl: 2 }}>
            <Controller
              name="societyNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.societyNo?.message}
                    helperText={errors.societyNo?.message}
                    label={capitalizeWords(t("organizationNumber"))}
                    disabled
                  />
                );
              }}
            />
            <Controller
              name="societyName"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.societyName?.message}
                    helperText={errors.societyName?.message}
                    label={t("organizationName")}
                    disabled
                  />
                );
              }}
            />
            <Controller
              name="name"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!nameHelperText}
                    helperText={nameHelperText}
                    label={t("name")}
                    disabled={readOnly}
                  />
                );
              }}
            />
            <Controller
              name="citizenshipStatus"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    label={t("citizenship")}
                    value={2}
                    options={CitizenshipStatus.map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                    type="select"
                    disabled
                  />
                );
              }}
            />
            <Controller
              name="identificationType"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.identificationType?.message}
                    helperText={errors.identificationType?.message}
                    label={t("idType")}
                    disabled={readOnly}
                    value={t(getValues("identificationType"))}
                    type="select"
                    options={IdTypes.filter(
                      (item) =>
                        Number(item.value) === 4 || Number(item.value) === 5
                    ).map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;
                      if (inputType !== "4" && value === "4") {
                        setValue("identificationNo", "");
                      }

                      setValue(field.name, value);
                    }}
                  />
                );
              }}
            />
            <Controller
              name="identificationNo"
              rules={{
                required: t("fieldRequired"),
                validate: (value) => {
                  const type = getValues("identificationType");
                  if (type === "4" && value.length !== 12) {
                    return t("fieldRequired");
                  }
                  return true;
                },
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!identificationNoHelperText}
                    helperText={identificationNoHelperText}
                    label={t("idNumber")}
                    inputProps={
                      getValues("identificationType") === "4"
                        ? {
                            inputMode: "numeric",
                            pattern: "[0-9]*",
                            maxLength: 12,
                            minLength: 12,
                          }
                        : undefined
                    }
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;

                      if (inputType === "4") {
                        value = value.replace(/\D/g, "").slice(0, 12);
                      }

                      setValue(field.name, value);
                    }}
                    disabled={readOnly}
                  />
                );
              }}
            />
            <Controller
              name="gender"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.applicantCountryCode?.message}
                    helperText={errors.applicantCountryCode?.message}
                    label={t("gender")}
                    type="select"
                    disabled={readOnly}
                    options={GenderType.map((item) => ({
                      label: t(item.translateKey),
                      value: item.code,
                    }))}
                  />
                );
              }}
            />
            <Controller
              name="applicantCountryCode"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.applicantCountryCode?.message}
                    helperText={errors.applicantCountryCode?.message}
                    label={capitalizeWords(t("originCountry"))}
                    type="select"
                    disabled={readOnly}
                    options={addressData
                      .filter((item: any) => item.pid === 0)
                      .map((item: any) => ({
                        label: capitalizeWords(item.name),
                        value: item.id.toString(),
                      }))}
                  />
                );
              }}
            />
            <Controller
              name="visaNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    disabled={readOnly}
                    error={!!errors.visaNo?.message}
                    helperText={errors.visaNo?.message}
                    label={capitalizeWords(t("visaNumber"))}
                  />
                );
              }}
            />
            <Controller
              name="visaExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.visaExpirationDate?.message}
                    helperText={errors.visaExpirationDate?.message}
                    label={capitalizeWords(t("visaExpiryDate"))}
                    disabled={readOnly}
                    type="date"
                  />
                );
              }}
            />
            <Controller
              name="permitNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.permitNo?.message}
                    helperText={errors.permitNo?.message}
                    label={t("permitNumber")}
                    disabled={readOnly}
                  />
                );
              }}
            />
            <Controller
              name="permitExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    value={
                      getValues("permitExpirationDate")
                        ? dayjs(getValues("permitExpirationDate")).format(
                            "DD-MM-YYYY"
                          )
                        : "-"
                    }
                    error={!!errors.permitExpirationDate?.message}
                    helperText={errors.permitExpirationDate?.message}
                    type="date"
                    disabled={readOnly}
                    label={t("permitExpiryDate")}
                  />
                );
              }}
            />
            <Controller
              name="tujuanDMalaysia"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.tujuanDMalaysia?.message}
                    helperText={errors.tujuanDMalaysia?.message}
                    label={capitalizeWords(t("purposeInMalaysia"))}
                    disabled={readOnly}
                  />
                );
              }}
            />
            {Number(watch("identificationType")) !== 4 && (
              <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    sx={{
                      color: "#666666",
                      fontWeight: "400 !important",
                      fontSize: "14px",
                    }}
                  >
                    {capitalizeWords(t("durationInMalaysia"))}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Controller
                        name="stayDurationDigit"
                        control={control}
                        rules={{
                          validate: (value) => {
                            // Skip validation if identificationType is 4 (MyPR)
                            if (Number(watch("identificationType")) === 4)
                              return true;
                            return value ? true : t("fieldRequired");
                          },
                        }}
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              {...field}
                              isLabelNoSpace={false}
                              isLabel={false}
                              type="text"
                              inputMode="numeric"
                              error={!!errors.stayDurationDigit?.message}
                              helperText={errors.stayDurationDigit?.message}
                              onChange={(newValue) => {
                                const value = newValue.target.value;
                                if (/^\d*$/.test(value)) {
                                  setValue(
                                    "stayDurationDigit",
                                    parseInt(value) || 0
                                  );
                                }
                              }}
                              disabled={readOnly}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Controller
                        name="stayDurationUnit"
                        control={control}
                        rules={{
                          validate: (value) => {
                            // Skip validation if identificationType is 4 (MyPR)
                            if (Number(watch("identificationType")) === 4)
                              return true;
                            return value ? true : t("fieldRequired");
                          },
                        }}
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              isLabelNoSpace={false}
                              {...field}
                              type="select"
                              error={!!errors.stayDurationUnit?.message}
                              helperText={errors.stayDurationUnit?.message}
                              onChange={(newValue) =>
                                setValue(
                                  "stayDurationUnit",
                                  newValue.target.value
                                )
                              }
                              isLabel={false}
                              value={getValues("stayDurationUnit")}
                              options={DurationOptions.map((duration) => ({
                                ...duration,
                                label: t(duration.label),
                              }))}
                              disabled={readOnly}
                            />
                          );
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            )}
            <Controller
              name="activeCommitteeId"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              disabled={readOnly}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.activeCommitteeId?.message}
                    helperText={errors.activeCommitteeId?.message}
                    label={t("position")}
                    type="select"
                    // options={OrganisationPositions.map((position) => ({
                    //   ...position,
                    //   label: t(position.label),
                    // }))}
                    isLoadingData={isLoadingPositionListRes}
                    options={positionList}
                    onChange={(selectedValue) => {
                      field.onChange(selectedValue); // updates activeCommitteeId
                      console.log("selectedValue", selectedValue);
                      if (positionList) {
                        console.log("positionList", positionList);
                        const selectedOption = positionList.find(
                          (opt) =>
                            Number(opt.value) ===
                            Number(getValues("activeCommitteeId"))
                        );

                        console.log("selectedOption", selectedOption);
                        if (selectedOption?.designationCode) {
                          setValue(
                            "designationCode",
                            selectedOption.designationCode
                          );
                        }
                      }
                    }}
                  />
                );
              }}
            />
            <Controller
              name="summary"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field, fieldState }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={fieldState.invalid}
                    helperText={fieldState.error?.message}
                    label={t("importanceOfPosition")}
                    disabled={readOnly}
                  />
                );
              }}
            />
          </Box>
          <Box sx={{ mt: 3 }}>
            {watch("identificationNo") && (
              <FileUploader
                title="ajkEligibilityCheck"
                type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
                info="nonCitizenDocumentInfo"
                validTypes={[
                  "application/pdf",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "text/plain",
                ]}
                maxFileSize={25 * 1024 * 1024}
                disabled={readOnly}
                societyId={parseInt(societyId)}
                // societyNo={societyDataRedux?.societyNo}
                icNo={watch("identificationNo")}
              />
            )}
          </Box>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
              gap: 2,
            }}
          >
            <ButtonOutline onClick={handleSenaraiAjk}>
              {t("back")}
            </ButtonOutline>
            {!readOnly ? (
              <ButtonPrimary
                disabled={
                  watch("identificationType") === "4" &&
                  (watch("identificationNo")?.length < 12 ||
                    !userICCorrect ||
                    !userNameMatchIC)
                }
                type="submit"
              >
                {t("update")}
              </ButtonPrimary>
            ) : null}
          </Box>
        </Box>
      </form>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />
        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}
        <InfoQACard />
      </Box>
    </Box>
  );
};

export default CreateAjkBukanWn;
