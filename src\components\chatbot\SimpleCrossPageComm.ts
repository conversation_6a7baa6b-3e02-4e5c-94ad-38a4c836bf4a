/**
 * Minimal cross-page communication for speech input
 * Only handles speech input → chatbot page communication
 * API remains the single source of truth for all message data
 */

// Storage key for speech input communication
const SPEECH_INPUT_KEY = 'chatbot_speech_input';

// Interface for speech input message
export interface SpeechInputMessage {
  text: string;
  timestamp: number;
  messageId: string;
  processed: boolean;
}

/**
 * Generates a unique message ID
 */
function generateMessageId(): string {
  return `speech_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`;
}

/**
 * Sends speech input to the chatbot page
 * @param text The speech input text
 * @returns The generated message ID
 */
export function sendSpeechInputToChatbot(text: string): string {
  const messageId = generateMessageId();
  
  const message: SpeechInputMessage = {
    text: text.trim(),
    timestamp: Date.now(),
    messageId,
    processed: false,
  };

  try {
    localStorage.setItem(SPEECH_INPUT_KEY, JSON.stringify(message));
    // console.log('SpeechInput: Sent to chatbot page:', messageId, text.substring(0, 50) + '...');
    return messageId;
  } catch (error) {
    // console.error('SpeechInput: Error sending to chatbot page:', error);
    return messageId;
  }
}

/**
 * Listens for speech input from the speech input page
 * @param onSpeechInput Callback when speech input is received
 * @returns Cleanup function
 */
export function listenForSpeechInput(
  onSpeechInput: (text: string, messageId: string) => void
): () => void {
  const handleStorageChange = (event: StorageEvent) => {
    if (event.key !== SPEECH_INPUT_KEY || !event.newValue) {
      return;
    }

    try {
      const message: SpeechInputMessage = JSON.parse(event.newValue);
      
      // Only process unprocessed messages
      if (message.processed) {
        return;
      }

      // Validate message
      if (!message.text || !message.text.trim()) {
        // console.warn('SpeechInput: Received empty message');
        return;
      }

      // console.log('Chatbot: Received speech input:', message.messageId, message.text.substring(0, 50) + '...');
      
      // Mark as processed
      const processedMessage = { ...message, processed: true };
      localStorage.setItem(SPEECH_INPUT_KEY, JSON.stringify(processedMessage));
      
      // Call the handler
      onSpeechInput(message.text, message.messageId);
      
    } catch (error) {
      // console.error('Chatbot: Error processing speech input:', error);
    }
  };

  // Add event listener for storage changes
  window.addEventListener('storage', handleStorageChange);
  
  // console.log('Chatbot: Listening for speech input');

  // Return cleanup function
  return () => {
    window.removeEventListener('storage', handleStorageChange);
    // console.log('Chatbot: Stopped listening for speech input');
  };
}

/**
 * Clears any pending speech input messages
 */
export function clearSpeechInput(): void {
  try {
    localStorage.removeItem(SPEECH_INPUT_KEY);
    // console.log('SpeechInput: Cleared pending messages');
  } catch (error) {
    // console.error('SpeechInput: Error clearing messages:', error);
  }
}

/**
 * Checks if there's a pending speech input message
 * @returns The pending message or null
 */
export function getPendingSpeechInput(): SpeechInputMessage | null {
  try {
    const stored = localStorage.getItem(SPEECH_INPUT_KEY);
    if (!stored) return null;
    
    const message: SpeechInputMessage = JSON.parse(stored);
    return message.processed ? null : message;
  } catch (error) {
    // console.error('SpeechInput: Error getting pending message:', error);
    return null;
  }
}

// Legacy exports for backward compatibility
export const sendSpeechInputCrossPage = sendSpeechInputToChatbot;
export const setupCrossPageListener = (handlers: { onUserMessage?: (text: string, messageId: string) => void }) => {
  if (handlers.onUserMessage) {
    return listenForSpeechInput(handlers.onUserMessage);
  }
  return () => {};
};
export const clearCrossPageStorage = clearSpeechInput;
