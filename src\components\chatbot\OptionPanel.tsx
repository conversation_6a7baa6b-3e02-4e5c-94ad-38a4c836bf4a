import { Avatar, <PERSON>, Button, Typography, IconButton, Collapse } from "@mui/material";
import { Close } from "@mui/icons-material";
import { OptionGroup } from "./types";
import { memo, useState, useEffect } from "react";

interface OptionPanelProps {
  showOptionPanel: boolean;
  loading: boolean;
  currentBotMessage: string | null;
  selectedParentKey: string | null;
  optionGroups: OptionGroup[];
  locale: string;
  onOptionSelect: (option: string) => void;
  onBackToOptions: () => void;
}

/**
 * Component for displaying chatbot option panels
 */
export const OptionPanel = memo(({
  showOptionPanel,
  loading,
  currentBotMessage,
  selectedParentKey,
  optionGroups,
  locale,
  onOptionSelect,
  onBackToOptions,
}: OptionPanelProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Reset expanded state when option panel is shown again
  useEffect(() => {
    if (showOptionPanel && !loading && currentBotMessage === null) {
      setIsExpanded(false);
    }
  }, [showOptionPanel, loading, currentBotMessage]);

  if (!showOptionPanel || loading || currentBotMessage !== null) {
    return null;
  }

  const handleExpand = () => {
    setIsExpanded(true);
  };

  const handleCollapse = () => {
    setIsExpanded(false);
    // Reset to parent options when collapsing
    if (selectedParentKey !== null) {
      onBackToOptions();
    }
  };

  /**
   * Renders the compact FAQ button
   */
  const renderFaqButton = () => (
    <Box sx={{ textAlign: "center" }}>
      <Button
        variant="contained"
        onClick={handleExpand}
        sx={{
          borderRadius: 2,
          px: 3,
          py: 1.5,
          fontSize: "0.9rem",
          fontWeight: "medium",
          bgcolor: "primary.main",
          color: "white",
          boxShadow: 2,
          "&:hover": {
            bgcolor: "primary.dark",
            boxShadow: 4,
            transform: "translateY(-1px)",
          },
          transition: "all 0.2s ease-in-out",
        }}
      >
        {locale === "ms" ? "FAQ" : "FAQ"}
      </Button>
    </Box>
  );

  /**
   * Renders the parent category options
   */
  const renderParentGroups = () => (
    <Box sx={{ textAlign: "center", width: "100%" }}>
      <Box sx={{ 
        display: "flex", 
        justifyContent: "space-between", 
        alignItems: "center", 
        mb: 2,
        gap: 1
      }}>
        <Typography 
          variant="subtitle1" 
          sx={{ 
            fontWeight: "bold", 
            flex: 1,
            fontSize: { xs: "0.875rem", sm: "1rem" },
            wordBreak: "break-word"
          }}
        >
          {locale === "ms" ? "Pilih Kategori" : "Choose a Category"}
        </Typography>
        <IconButton
          onClick={handleCollapse}
          size="small"
          sx={{ 
            flexShrink: 0,
            color: "grey.600",
            "&:hover": {
              bgcolor: "grey.200",
              color: "grey.800",
            },
            transition: "all 0.2s ease-in-out",
          }}
          aria-label={locale === "ms" ? "Tutup" : "Close"}
        >
          <Close />
        </IconButton>
      </Box>
      <Box sx={{ 
        display: "flex", 
        flexDirection: "column", 
        gap: 1.5,
        width: "100%"
      }}>
        {optionGroups.map((group) => (
          <Button
            key={group.key}
            variant="contained"
            onClick={() => onOptionSelect(group.key)}
            sx={{ 
              borderRadius: 1,
              py: { xs: 0.8, sm: 1.2 },
              px: { xs: 1, sm: 2 },
              fontSize: { xs: "0.75rem", sm: "0.8rem", md: "0.9rem" },
              whiteSpace: "normal",
              wordBreak: "break-word",
              textAlign: "center",
              minHeight: "auto",
              lineHeight: 1.2,
              maxWidth: "100%",
              "&:hover": {
                transform: "translateY(-1px)",
                boxShadow: 3,
              },
              transition: "all 0.2s ease-in-out",
            }}
          >
            {group.label}
          </Button>
        ))}
      </Box>
    </Box>
  );

  /**
   * Renders the child options for a selected parent category
   */
  const renderChildOptions = () => {
    const group = optionGroups.find(
      (g) => g.key.toLowerCase() === selectedParentKey?.toLowerCase()
    );
    
    if (!group) return null;
    
    return (
      <Box sx={{ textAlign: "center", width: "100%" }}>
        <Box sx={{ 
          display: "flex", 
          justifyContent: "space-between", 
          alignItems: "center", 
          mb: 2,
          gap: 1
        }}>
          <Typography 
            variant="subtitle1" 
            sx={{ 
              fontWeight: "bold", 
              flex: 1,
              fontSize: { xs: "0.875rem", sm: "1rem" },
              wordBreak: "break-word"
            }}
          >
            {group.label}
          </Typography>
          <IconButton
            onClick={handleCollapse}
            size="small"
            sx={{ 
              flexShrink: 0,
              color: "grey.600",
              "&:hover": {
                bgcolor: "grey.200",
                color: "grey.800",
              },
              transition: "all 0.2s ease-in-out",
            }}
            aria-label={locale === "ms" ? "Tutup" : "Close"}
          >
            <Close />
          </IconButton>
        </Box>
        <Box sx={{ 
          display: "flex", 
          flexDirection: "column", 
          gap: 1.5,
          width: "100%"
        }}>
          {group.children.map((option, index) => (
            <Button
              key={index}
              variant="contained"
              onClick={() => onOptionSelect(option)}
              sx={{ 
                borderRadius: 1,
                py: { xs: 0.8, sm: 1.2 },
                px: { xs: 1, sm: 2 },
                fontSize: { xs: "0.75rem", sm: "0.8rem", md: "0.9rem" },
                whiteSpace: "normal",
                wordBreak: "break-word",
                textAlign: "center",
                minHeight: "auto",
                lineHeight: 1.2,
                maxWidth: "100%",
                "&:hover": {
                  transform: "translateY(-1px)",
                  boxShadow: 3,
                },
                transition: "all 0.2s ease-in-out",
              }}
            >
              {option}
            </Button>
          ))}
        </Box>
        <Button 
          variant="outlined" 
          onClick={onBackToOptions} 
          sx={{ 
            mt: { xs: 1.5, sm: 2 }, 
            borderRadius: 1,
            py: { xs: 0.8, sm: 1 },
            px: { xs: 1, sm: 2 },
            fontSize: { xs: "0.75rem", sm: "0.8rem", md: "0.9rem" },
            maxWidth: "100%",
            "&:hover": {
              transform: "translateY(-1px)",
              boxShadow: 2,
            },
            transition: "all 0.2s ease-in-out",
          }}
        >
          {locale === "ms" ? "Kembali" : "Back"}
        </Button>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "flex-end",
        width: "100%",
        maxWidth: "100vw",
        overflow: "hidden",
      }}
    >
      <Avatar
        src="/rosie-bot.png"
        alt="Bot Avatar"
        variant="square"
        sx={{ 
          width: { xs: 32, sm: 40, md: 48 }, 
          height: { xs: 32, sm: 40, md: 48 }, 
          mr: { xs: 0.5, sm: 1 },
          flexShrink: 0
        }}
      />
      <Box
        sx={{
          p: isExpanded ? { xs: 1, sm: 1.5, md: 2 } : { xs: 0.75, sm: 1, md: 1.5 },
          borderRadius: { xs: 2, sm: 3 },
          bgcolor: "grey.100",
          color: "text.primary",
          maxWidth: { xs: "calc(100vw - 80px)", sm: "75%", md: "70%" },
          width: "fit-content",
          minWidth: { xs: "120px", sm: "180px" },
          maxHeight: { xs: "60vh", sm: "70vh" },
          boxShadow: isExpanded ? 4 : 3,
          border: "1px solid",
          borderColor: "grey.200",
          transition: "all 0.3s ease-in-out",
          boxSizing: "border-box",
          ml: 0,
          mr: "auto",
          overflow: "hidden",
          position: "relative",
        }}
      >
        <Collapse in={!isExpanded}>
          {renderFaqButton()}
        </Collapse>
        <Collapse in={isExpanded}>
          <Box sx={{ 
            maxHeight: { xs: "50vh", sm: "60vh" },
            overflowY: "auto",
            overflowX: "hidden"
          }}>
            {selectedParentKey === null
              ? renderParentGroups()
              : renderChildOptions()}
          </Box>
        </Collapse>
      </Box>
    </Box>
  );
});

OptionPanel.displayName = "OptionPanel";
