import {
  Box,
  Grid,
  Menu<PERSON><PERSON>,
  <PERSON>ack,
  TextField,
  Typography,
  Button,
  Fade,
  Select,
  FormHelperText,
  FormControl,
  Theme,
  useMediaQuery,
  Table,
  TableHead,
  TableCell,
  TableBody,
  TableRow,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CloseIcon from "@mui/icons-material/Close";
import { useForm } from "@refinedev/react-hook-form";
import { useNavigate } from "react-router-dom";
import i18n from "../../i18n";
import Menu from "@mui/material/Menu";
import { t } from "i18next";
import { ButtonPrimary } from "../../components/button";
import { API_URL } from "../../api";
import { useCustom } from "@refinedev/core";
import CustomDataGrid from "../../components/datagrid";
import { Edit } from "@refinedev/mui";
import { GridColDef } from "@mui/x-data-grid";
import { urlParams } from "../../helpers/utils";
import { FeedbackStatus, IdTypes, ListGelaran } from "../../helpers/enums";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const inputStyle = {};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  display: "flex",
  alignItems: "center",
};

enum TypeEnum {
  MAKLUM_BALAS = "jenis_MaklumBalas",
  ADUAN = "jenis_Aduan",
  ISU_SISTEM = "jenis_IsuSistem",
  KEPUASAN_PELANGAN = "jenis_KepuasanPelangan",
}

function FeedbackSemak() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const navigate = useNavigate();

  const [idType, setIdType] = useState("");

  const getLanguageDisplay = (lang: string) => {
    switch (lang.toLowerCase()) {
      case "bm":
      case "ms":
      case "my":
        return "BM";
      case "en":
        return "EN";
      default:
        return lang.toUpperCase();
    }
  };

  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] =
    useState<null | HTMLElement>(null);

  const [languageAnchorEl, setLanguageAnchorEl] = useState<null | HTMLElement>(
    null
  );

  const handleLanguageMenuClose = () => {
    setLanguageAnchorEl(null);
  };

  const handleLanguageMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMobileMenuAnchorEl(event.currentTarget);
  };

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    handleLanguageMenuClose();
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [showData, setShowData] = useState(false);

  const [formData, setFormData] = useState({
    refNo: "",
    idType: "",
    idNo: "",
    phoneNumber: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleSave = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    const data = { ...formData };

    setLoading(true);
    setShowData(false);

    const url = urlParams(`${API_URL}/society/feedback/search`, {
      pageSize: 10,
      pageNo: 1,
      identificationNo: data.idNo,
      id: data.refNo,
      phoneNumber: data.phoneNumber,
    });

    try {
      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      const responseData = await response.json();
      if (responseData?.status === "SUCCESS") {
        setTableData(responseData?.data?.data);
        console.log(responseData?.data?.data);
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
    setShowData(true);
  };

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (!formData.idType) errors.idType = t("requiredValidation");
    if (!formData.idNo) errors.idNo = t("requiredValidation");

    return errors;
  };

  const getTitleName = (title = "") => {
    if (!title) return "Unknown Title"; // Handle null or undefined titleId
    const gelaran = ListGelaran.find((item) => item.value === String(title));
    return gelaran ? gelaran.label : "Unknown Title"; // Fallback for unknown IDs
  };

  const goBack = () => {
    navigate(-1);
  };

  function getTypeEnumValue(key: string): string | undefined {
    const result = TypeEnum[key as keyof typeof TypeEnum];
    return t(result);
  }

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const addressData = addressList?.data?.data || [];

  const getStateName = (stateCode: any) => {
    const stateName = addressData.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };

  const getStatusName = (status: any) => {
    const statusName = FeedbackStatus.filter((i: any) => i.value == status);
    return t(statusName[0]?.label);
  };

  const columns: GridColDef[] = [
    {
      field: "namaPemohon",
      headerName: t("namePemohon"),
      flex: 1,
      renderCell: ({ row }) => {
        return (
          row?.branchCommitteeGetOnePendingResponses?.[0]?.committeeName || "-"
        );
      },
    },
    {
      field: "namaCawangan",
      headerName: t("namaCawangan"),
      flex: 1,
      renderCell: ({ row }) => {
        return row?.branchGetOnePendingResponses?.name || "-";
      },
    },
    {
      field: "namaPertubuhan",
      headerName: t("namaPertubuhan"),
      flex: 1,
      renderCell: ({ row }) => {
        return row?.branchGetOnePendingResponses?.branchApplicationNo || "-";
      },
    },
    {
      field: "tarikhMohon",
      headerName: t("tarikhMohon"),
      flex: 1,
      renderCell: ({ row }) => {
        const createdDate = row?.branchGetOnePendingResponses?.createdDate;
        if (createdDate) {
          return `${createdDate[2]}/${createdDate[1]}/${createdDate[0]}`;
        }
        return "-";
      },
    },
    {
      field: "roBertanggungjawab",
      headerName: t("roBertanggungjawab"),
      flex: 1,
      renderCell: ({ row }) => {
        return row?.branchGetOnePendingResponses?.ro || "-";
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 0.5,
      renderCell: (params: any) => {
        const row = params.row;
        const branchId = row.branchGetOnePendingResponses?.id;
        const encodedId = btoa(branchId?.toString() || "");
        return (
          <Button
            onClick={() => {
              navigate(
                `/pertubuhan/kelulusan/penubuhan-cawangan/update/${encodedId}`
              );
            }}
            sx={{ color: "black" }}
          >
            <Edit />
          </Button>
        );
      },
    },
  ];

  const [total, setTotal] = useState<number>(0);

  return (
    <>
      <Box
        sx={{
          backgroundImage: 'url("/bg-feedback.jpg")',
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "center center",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            background: "rgba(0, 0, 0, 0.3)", 
          },
          "& > *": {
            position: "relative",
            zIndex: 2,
          },
          display: "flex",
          justifyContent: "center",
          alignContent: "center",
          pt: 4,
          height: "100vh",
          overflowY: "auto",
        }}
      >
        <Box>
          <Box
            sx={{
              maxWidth: "1000px",
              width: "100%",
              flex: 1,
              p: { xs: 3, sm: 0 },
            }}
          >
            {/* menu toggle */}
            <Box
              sx={{
                mb: { xs: "15px", sm: "50px" },
                display: "flex",
                alignContent: "center",
                gap: 2,
              }}
            >
              <Box
                sx={{
                  color: "#fff",
                  cursor: "pointer",
                  display: "flex",
                  alignItems: "center",
                }}
                onClick={goBack}
              >
                <ChevronLeftIcon />
              </Box>
              <Typography
                onClick={handleLanguageMenuClick}
                sx={{
                  color: "#fff",
                  cursor: "pointer",
                  fontWeight: 700,
                  fontSize: "16px",
                  fontFamily: "Inter, sans-serif",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                {getLanguageDisplay(i18n.language)}
              </Typography>
            </Box>
            <Fade in={true} timeout={500}>
              <Box
                sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}
              >
                <Box
                  sx={{
                    p: { xs: 1, sm: 2, md: 3 },
                    border: "1px solid #D9D9D9",
                    borderRadius: "14px",
                    mb: 2,
                  }}
                >
                  <Typography variant="subtitle1" sx={sectionStyle}>
                    {t("checkFeedbacks")}
                  </Typography>

                  <Grid container spacing={2} sx={{ pl: 2, mb: 6, mt: 2 }}>
                    {/* Reference Number */}
                    <Grid item xs={12} sm={4} sx={labelStyle}>
                      <Typography>{t("reference_number")}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextField
                        sx={inputStyle}
                        fullWidth
                        name="refNo"
                        value={formData.refNo}
                        onChange={handleInputChange}
                        error={!!formErrors.refNo}
                        helperText={formErrors.refNo}
                      />
                    </Grid>
                    {/* Identification type */}
                    <Grid item xs={12} sm={4} sx={labelStyle}>
                      <Typography>
                        {t("idType")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <FormControl
                        sx={inputStyle}
                        fullWidth
                        required
                        error={!!formErrors.idType}
                      >
                        <Select
                          value={idType}
                          displayEmpty
                          required
                          onChange={(e) => {
                            setIdType(e.target.value);
                            setFormData((prevState) => ({
                              ...prevState,
                              idType: e.target.value,
                            }));
                            setFormErrors((prev) => ({ ...prev, idType: "" }));
                          }}
                        >
                          <MenuItem value="" disabled>
                            {t("pleaseSelect")}
                          </MenuItem>
                          {IdTypes.map((item, index) => {
                            return (
                              <MenuItem value={item?.value} key={index}>
                                {t(item?.label)}
                              </MenuItem>
                            );
                          })}
                        </Select>

                        {formErrors.idType && (
                          <FormHelperText>{formErrors.idType}</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>
                    {/* ID Number */}
                    <Grid item xs={12} sm={4} sx={labelStyle}>
                      <Typography>
                        {t("idNumber")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextField
                        sx={inputStyle}
                        fullWidth
                        required
                        name="idNo"
                        value={formData.idNo}
                        onChange={handleInputChange}
                        error={!!formErrors.idNo}
                        helperText={formErrors.idNo}
                      />
                    </Grid>
                    {/* Phone number */}
                    <Grid item xs={12} sm={4} sx={labelStyle}>
                      <Typography>{t("phoneNumber")}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextField
                        sx={inputStyle}
                        fullWidth
                        required
                        name="phoneNumber"
                        value={formData.phoneNumber}
                        inputProps={{ inputMode: "numeric", pattern: "[0-9]*" }}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          const value = e.target.value;
                          if (/^\d*$/.test(value)) {
                            handleInputChange(e);
                          }
                        }}
                        error={!!formErrors.phoneNumber}
                        helperText={formErrors.phoneNumber}
                      />
                    </Grid>
                  </Grid>
                </Box>

                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrimary
                    variant="contained"
                    sx={{
                      width: isMobile ? "100%" : "auto",
                    }}
                    onClick={handleSave}
                    disabled={loading}
                  >
                    {t("semak")}
                  </ButtonPrimary>
                </Grid>
              </Box>
            </Fade>

            {showData ? (
              <Box
                sx={{
                  backgroundColor: "white",
                  p: 3,
                  borderRadius: "15px",
                  mt: 3,
                }}
              >
                <Box
                  sx={{
                    p: { xs: 1, sm: 2, md: 3 },
                    border: "1px solid #D9D9D9",
                    borderRadius: "14px",
                    mb: 2,
                  }}
                >
                  <Typography variant="subtitle1" sx={sectionStyle}>
                    {t("feedbackList")}
                  </Typography>

                  <Box sx={{ overflowX: "auto", width: "100%" }}>
                    {tableData?.length == 0 ? (
                      <Typography
                        fontSize="16px"
                        fontWeight={600}
                        textAlign="center"
                        lineHeight="24px"
                        marginTop="18px"
                        color={"#000"}
                      >
                        {t("noData")}
                      </Typography>
                    ) : null}
                    {tableData?.length > 0 ? (
                      <Table sx={{ mt: "20px" }}>
                        <TableHead
                          sx={{
                            "& th": {
                              textAlign: "center",
                            },
                          }}
                        >
                          <TableRow sx={{ backgroundColor: "#fff" }}>
                            <TableCell sx={{ fontWeight: "bold" }}>
                              {t("idNumberPlaceholder")}
                            </TableCell>
                            <TableCell sx={{ fontWeight: "bold" }}>
                              {t("jenisCadanganMaklumBalas")}
                            </TableCell>
                            <TableCell sx={{ fontWeight: "bold" }}>
                              {t("tajukButiran")}
                            </TableCell>
                            <TableCell sx={{ fontWeight: "bold" }}>
                              {t("lokasi")}
                            </TableCell>
                            <TableCell sx={{ fontWeight: "bold" }}>
                              {t("status")}
                            </TableCell>
                          </TableRow>
                        </TableHead>

                        {tableData?.length > 0
                          ? tableData.map((item, index) => {
                              const {
                                id,
                                identificationNo,
                                type,
                                title,
                                stateCode,
                                status,
                              } = item;
                              return (
                                <TableBody
                                  sx={{
                                    background: "#fff",
                                    "& td": {
                                      textAlign: "center",
                                    },
                                  }}
                                  key={index + id}
                                >
                                  <TableRow>
                                    <TableCell>{identificationNo}</TableCell>
                                    <TableCell>
                                      {getTypeEnumValue(type)}
                                    </TableCell>
                                    <TableCell
                                      sx={{ textTransform: "uppercase" }}
                                    >
                                      {title}
                                    </TableCell>
                                    <TableCell>
                                      {getStateName(stateCode)}
                                    </TableCell>
                                    <TableCell>
                                      {getStatusName(status)}
                                    </TableCell>
                                  </TableRow>
                                </TableBody>
                              );
                            })
                          : null}
                      </Table>
                    ) : null}
                    {/* <CustomDataGrid
                    url={`${API_URL}/society/feedback/search`}
                    columns={columns}
                    filters={[
                      { field: "pageNo", operator: "eq", value: "1" },
                      { field: "pageSize", operator: "eq", value: "10" },
                    ]}
                    noResultMessage={t("noOrganizationFound")}
                    isFiltered
                    type={2}
                    onDataReceived={(data) => setTotal(data?.data?.total || 0)}
                  /> */}
                  </Box>
                </Box>
              </Box>
            ) : null}
          </Box>
        </Box>
      </Box>

      <Menu
        anchorEl={languageAnchorEl}
        open={Boolean(languageAnchorEl)}
        onClose={handleLanguageMenuClose}
      >
        <MenuItem onClick={() => changeLanguage("en")}>English</MenuItem>
        <MenuItem onClick={() => changeLanguage("bm")}>Bahasa Melayu</MenuItem>
      </Menu>
    </>
  );
}

export default FeedbackSemak;
