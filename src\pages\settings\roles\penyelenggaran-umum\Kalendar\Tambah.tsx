import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { FieldValues, useForm } from "react-hook-form";
import { useMutation, getMalaysiaAddressList } from "@/helpers";

import {
  Box,
  Theme,
  Typography,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  ButtonPrimary,
  DatePickerController,
  SelectFieldController,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";

import { IApiResponse } from "@/types";

const Tambah = () => {
  const navigate = useNavigate();

  const malaysiaAddressList = getMalaysiaAddressList() ?? [];
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const stateOptions = useMemo(() => {
    return malaysiaAddressList.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  }, []);

  const { fetch: createCalendar, isLoading: isCreatingDistrict } = useMutation<
    IApiResponse<number>
  >({
    url: "society/lookup/calendar/create",
    onSuccess: (res) => {
      const resCode = res.data.code;

      if (resCode === 200) navigate("..");
    },
  });

  const { control, handleSubmit, setValue } = useForm<FieldValues>({
    defaultValues: {
      holidayName: "",
      startDate: "",
      endDate: "",
      stateIds: [],
      status: 1,
    },
  });

  const onSubmit = (data: FieldValues) => createCalendar(data);

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("penambahanKalendar")}
          </Typography>

          <Box
            component="form"
            onSubmit={handleSubmit(onSubmit)}
            sx={{ display: "grid" }}
          >
            <FormFieldRow
              label={<Label text={t("namaCuti")} />}
              value={
                <TextFieldController control={control} name="holidayName" />
              }
            />

            <FormFieldRow
              label={<Label text={t("date")} />}
              value={
                <DatePickerController
                  control={control}
                  name="startDate"
                  formatValue="DD-MM-YYYY"
                  onChange={(date) => setValue("endDate", date)}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("negeri")} />}
              value={
                <SelectFieldController
                  control={control}
                  name="stateIds"
                  options={stateOptions}
                  multiple
                />
              }
            />

            <Box
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrevious
                variant="outlined"
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={() => navigate(-1)}
              >
                {t("back")}
              </ButtonPrevious>
              <ButtonPrimary
                type="submit"
                disabled={isCreatingDistrict}
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
              >
                {isCreatingDistrict && <CircularProgress size={15} />}

                {t("add")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Tambah;
