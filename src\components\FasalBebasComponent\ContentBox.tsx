import React from "react";
import { Box, Grid, Typography } from "@mui/material";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function ContentBox({ clauseContent }: { clauseContent: string }) {
  const { id, clauseId } = useParams();
  const { t } = useTranslation();
  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        border: "1px solid #D9D9D9",
        backgroundColor: "#FFFFFF",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Typography sx={{ mb: 1, ...sectionStyle }}>
        {t("clauseContent")} {clauseId}
      </Typography>
      <Box
        sx={{
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box
              sx={{
                p: { xs: 1 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Box
                sx={{
                  whiteSpace: "pre-wrap",
                  wordWrap: "break-word",
                  py: 2,
                  height: "100%",
                }}
              >
                <Typography
                  sx={{ fontWeight: "400 !important" }}
                  dangerouslySetInnerHTML={{
                    __html: `<div class="ql-editor">${clauseContent}</div>`,
                  }}
                ></Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default ContentBox;
