import { Button as InitialButton, styled, type ButtonProps } from "@mui/material"

import { type ReactNode } from "react"

export interface ButtonSmallWithIconProps extends ButtonProps {
  icon: ReactNode
}

const StyledButton = styled(InitialButton)`
  font-size: 7px;
  color: #666666;
  text-transform: capitalize;
`

export const ButtonSmallWithIcon = <
  Props extends ButtonSmallWithIconProps = ButtonSmallWithIconProps
>({ icon, onClick, ...otherProps }: Props) => (
  <div
    style={{
      display: "flex",
      columnGap: "0.5rem"
    }}>
    <InitialButton
      sx={{
        flexGrow: 1,
        minWidth: "2rem !important",
        px: "0 !important"
      }}
      variant="contained"
      disableRipple={true}
      size="small"
      {...{ onClick }}
    >
      {icon}
    </InitialButton>
    <StyledButton
      sx={{
        flexGrow: 2,
      }}
      variant="outlined"
      {...{ onClick }}
      {...otherProps}
    />
  </div>
)
