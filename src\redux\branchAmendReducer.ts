import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface branchProp {
  data: branchPropItems;
  isView:boolean;
}

interface branchPropItems {
  id: string | number;
  branchId: string | number;
  branchNo:string | number;
  currentBranchName: string;
  currentBranchAddress: string; 
  registerDateTime?:string;
}

const initialState: branchProp = {
  data: {
    id: "",
    branchId: "",
    currentBranchName: "",
    currentBranchAddress: "", 
    branchNo:"",
    registerDateTime:""
  },
  isView:false,
};

export const branchAmendSlice = createSlice({
  name: "branchAmend",
  initialState,
  reducers: {
    setBranchAmendInfo: (state, action: PayloadAction<branchPropItems>) => {
      state.data = action.payload;
    }, 
    resetBranchAmendInfo: (state) => {
      state.data = { 
        ...initialState.data, 
        // isView: state.data.isView  
      };
    },
    setIsView: (state, action: PayloadAction<boolean>) => {
      state.isView = action.payload;
    }, 
  },
});

export const {setBranchAmendInfo, setIsView, resetBranchAmendInfo} = branchAmendSlice.actions;
export default branchAmendSlice.reducer;
