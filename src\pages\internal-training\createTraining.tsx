import React, {useEffect, useState} from "react";
import InternalTrainingHeader from "@/pages/internal-training/trainingHeader";
import {Box, FormControl, FormHelperText, Grid, MenuItem, Select, TextField, Typography} from "@mui/material";
import CreateStepOne from "@/pages/internal-training/createStepOne";
import CreateStepTwo from "@/pages/internal-training/createStepTwo";
import CreateStepThree from "@/pages/internal-training/createStepThree";
import {useParams} from "react-router-dom";
import {borderStyle, headerStyle, labelStyle} from "@/pages/internal-training/trainingConstant";

interface InternalTrainingCreateProps {
  isUpdate?: boolean
}

const InternalTrainingCreate: React.FC<InternalTrainingCreateProps> = ({isUpdate=false}) => {

  const [tempPage, setTempPage] = useState("");
  const [page, setPage] = useState("tajuk");

  const { id } = useParams<{ id: string }>()
  const [courseId, setCourseId] = useState<number>(0);
  console.log("id",id);
  useEffect(() => {
    if(id) setCourseId(parseInt(id));
  }, []);

  const tabs = [
    {label: "Tajuk Latihan", page: "tajuk"},
    {label: "Bahan Pelajaran", page: "pelajaran"},
    {label: "Soalan Quiz", page: "quiz"},
  ];

  const handleNext = (id: number, page: string) => {
    console.log("handleNext", id);
    setCourseId(id);
    setPage(page);
    //setTempPage(page);
  }

  /*useEffect(() => {
    setPage(tempPage);
  }, [tempPage]);*/


  return (<>
    <InternalTrainingHeader />
    <Box
      sx={{
        width:"85%",
        display: "flex",
        alignItems: "center",
        backgroundColor: "white",
        paddingBottom: "8px",
        justifyContent: "space-evenly",
        borderRadius: "10px",
        px: 2,
        py: 1,
        mb: 1,
      }}
    >
      {tabs.map((tab, index) => {
        // Tentukan apakah tab saat ini aktif berdasarkan URL
        const isActive = page === tab.page;
        return (
          <React.Fragment key={index}>
            <Box
              sx={{
                flex: 1,
                backgroundColor: isActive ? "#0CA6A6" : "#FFFFFF",
                p: 1,
                //mx:1,
                borderRadius: "5px",
              }}>
              <Box
                key={index}
                onClick={() => {
                  setPage(tab.page);
                }}
                sx={{
                  cursor: "pointer",
                  color: isActive ? "#FFFFFF" : "#666666",
                  transition: "color 0.3s, border-bottom 0.3s",
                }}
              >
                <Typography sx={{fontWeight: "400 !important", textAlign: "center", fontSize: "14px"}}>
                  {tab.label}
                </Typography>
              </Box>
            </Box>
          </React.Fragment>
        );
      })}
    </Box>
      {page === "tajuk" ?
      <CreateStepOne headerStyle={headerStyle} borderStyle={borderStyle} labelStyle={labelStyle} handleNext={handleNext} courseId={courseId} isUpdate={isUpdate} />
        :<></>}
      {page === "pelajaran" ?
        <CreateStepTwo headerStyle={headerStyle} borderStyle={borderStyle} labelStyle={labelStyle} handleNext={handleNext} courseId={courseId} isUpdate={isUpdate} />
        :<></>}
      {page === "quiz" ?
        <CreateStepThree headerStyle={headerStyle} borderStyle={borderStyle} labelStyle={labelStyle} handleNext={handleNext} courseId={courseId} isUpdate={isUpdate} />
        :<></>}
  </>);
}

export default InternalTrainingCreate;
