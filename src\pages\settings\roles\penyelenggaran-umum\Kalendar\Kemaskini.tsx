import { useMemo } from "react";
import { useParams } from "react-router-dom";
import { FieldValues, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { useMutation, useQuery, getMalaysiaAddressList } from "@/helpers";

import {
  Box,
  Theme,
  Typography,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import {
  ButtonPrimary,
  FormFieldRow,
  TextFieldController,
  Label,
  CustomSkeleton,
  DatePickerController,
  SelectFieldController,
  DisabledTextField,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";

import { IApiResponse, ILookupCalendar } from "@/types";

const Kemaskini = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const malaysiaAddressList = getMalaysiaAddressList() ?? [];

  const stateOptions = useMemo(() => {
    return malaysiaAddressList.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  }, []);

  const { control, setValue, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      holidayName: "",
      startDate: "",
      endDate: "",
      stateIds: [],
      status: 1,
    },
  });

  const { fetch: updateCalendar, isLoading: isUpdatingCalendar } = useMutation<
    IApiResponse<ILookupCalendar>
  >({
    url: `society/lookup/calendar/${id}/edit`,
    method: "put",
    onSuccess: (res) => {
      const resCode = res.data.code;

      if (resCode === 200) navigate("..");
    },
  });

  const { isLoading: isLoadingCalendarDetail } = useQuery<
    IApiResponse<ILookupCalendar>
  >({
    url: `society/lookup/calendar/${id}`,
    onSuccess: (res) => {
      const resCode = res?.data?.code ?? null;
      const detail = res?.data?.data ?? null;

      if (resCode && resCode === 200) {
        setValue("holidayName", detail?.holidayName);
        setValue("startDate", detail?.startDate);
        setValue("endDate", detail?.endDate);
        setValue("stateIds", detail?.stateIds);
      }
    },
  });

  const onSubmit = (data: FieldValues) => updateCalendar(data);

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("penambahanKalendar")}
          </Typography>

          {isLoadingCalendarDetail ? (
            <CustomSkeleton height={50} />
          ) : (
            <Box
              component="form"
              onSubmit={handleSubmit(onSubmit)}
              sx={{ display: "grid" }}
            >
              <FormFieldRow
                label={<Label text="ID" />}
                value={<DisabledTextField value={id ?? ""} />}
              />

              <FormFieldRow
                label={<Label text={t("namaCuti")} />}
                value={
                  <TextFieldController control={control} name="holidayName" />
                }
              />

              <FormFieldRow
                label={<Label text={t("date")} />}
                value={
                  <DatePickerController
                    control={control}
                    name="startDate"
                    formatValue="DD-MM-YYYY"
                    onChange={(date) => setValue("endDate", date)}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("negeri")} />}
                value={
                  <SelectFieldController
                    control={control}
                    name="stateIds"
                    options={stateOptions}
                    multiple
                  />
                }
              />

              <Box
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: isMobile ? "100%" : "auto",
                  }}
                  onClick={() => navigate(-1)}
                >
                  {t("back")}
                </ButtonPrevious>
                <ButtonPrimary
                  type="submit"
                  disabled={isUpdatingCalendar}
                  variant="contained"
                  sx={{
                    width: isMobile ? "100%" : "auto",
                    display: "flex",
                    alignItems: "center",
                    gap: "5px",
                  }}
                >
                  {isUpdatingCalendar && <CircularProgress size={15} />}
                  {t("kemaskini")}
                </ButtonPrimary>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Kemaskini;
