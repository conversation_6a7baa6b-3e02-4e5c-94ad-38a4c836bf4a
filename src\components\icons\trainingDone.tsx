import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const TrainingDoneIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"
         style={{ color, ...sx }}
         {...props}
    >
      <path d="M12.5 0.5C5.625 0.5 0 6.125 0 13C0 19.875 5.625 25.5 12.5 25.5C19.375 25.5 25 19.875 25 13C25 6.125 19.375 0.5 12.5 0.5ZM12.5 23C6.9875 23 2.5 18.5125 2.5 13C2.5 7.4875 6.9875 3 12.5 3C18.0125 3 22.5 7.4875 22.5 13C22.5 18.5125 18.0125 23 12.5 23ZM18.2375 7.475L10 15.7125L6.7625 12.4875L5 14.25L10 19.25L20 9.25L18.2375 7.475Z" fill="#0CA6A6"/>
    </svg>

  );
});
