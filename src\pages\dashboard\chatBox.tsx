import { LanguageRounded } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import AddIcon from "@mui/icons-material/Add";
import { RobotHeadSVG } from "@/components/icons/robotHead";
import { PlayArrowNoSpacSVG } from "@/components/icons/playNoSpace";

export const DashboardChatBox = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const clickableBoxStyles = {
    display: "flex",
    justifyItems: "center",
    alignItems: "center",
    border: "1px solid var(--text-grey-disabled)",
    borderRadius: 50,
    cursor: "pointer",
    transition: "background 0.2s ease-in-out",
    "&:hover": {
      background: "rgba(0, 0, 0, 0.1)",
    },
    "&:active": {
      background: "rgba(0, 0, 0, 0.2)",
    },
  };

  return (
    <Box
      sx={{
        bgcolor: "white",
        borderRadius: "15px",
        display: "flex",
        flexDirection: "column",
        gap: 1,
        padding: 2,
        height: "100%",
      }}
    >
      <Typography sx={{ mb: 3 }} className="sub-step-login-disabled">
        {t("chatBox")}
      </Typography>

      {/*CHAT BOX*/}
      <Box
        sx={{
          border: "1px solid var(--border-grey)",
          bgcolor: "var(--border-grey)",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center", //remove when there is chat msgs so that they stack
          alignItems: "center",
          borderRadius: "10px",
          p: 1,
          flex: 1, // Make the chatbox take the remaining space
          minHeight: 0, // Prevents overflow issues
        }}
      >
        <img
          height={70}
          src={`data:image/svg+xml;utf8,${encodeURIComponent(RobotHeadSVG)}`}
          alt="Robot Head"
        />
        <Typography
          sx={{
            textTransform: "capitalize",
            p: 1,
            bgcolor: "var(--primary-color)",
            color: "white",
            borderRadius: 1,
          }}
          className="step-header-login"
        >
          {t("chatWithKakRos")}
        </Typography>
      </Box>

      {/* Chat Input */}
      <Box
        sx={{
          display: "flex",
          gap: 1,
          alignItems: "center",
          justifyContent: "space-between",
          bgcolor: "var(--border-grey)",
          borderRadius: "10px",
          p: "0.5rem 1rem",
          maxHeight: 70,
        }}
      >
        <Box
          sx={{
            display: "grid",
            gap: { lg: 0, xl: 2 },
          }}
        >
          <Typography
            className="step-header-login"
            sx={{ fontSize: { lg: "9px !importnat", xl: 13 } }}
          >
            {t("chatWithKakRos")}
          </Typography>
          <Box
            sx={{
              display: "flex",
              gap: { lg: 0.5, xl: 1 },
              justifyItems: "flex-end",
              alignItems: "center",
            }}
          >
            <Box sx={{ ...clickableBoxStyles, p: 0.3 }}>
              <AddIcon
                sx={{ fontSize: 13, color: "var(--text-grey-disabled)" }}
              />
            </Box>

            <Box sx={{ ...clickableBoxStyles, gap: 1, pl: 0.5, pr: 0.5 }}>
              <LanguageRounded
                sx={{ fontSize: 13, color: "var(--text-grey-disabled)" }}
              />
              <Typography className="step-header-login-disabled">
                {t("search")}
              </Typography>
            </Box>
          </Box>
        </Box>
        <Box sx={{ pt: 1 }}>
          <img
            src={`data:image/svg+xml;utf8,${encodeURIComponent(
              PlayArrowNoSpacSVG
            )}`}
            alt="Robot Head"
          />
        </Box>
      </Box>
    </Box>
  );
};
