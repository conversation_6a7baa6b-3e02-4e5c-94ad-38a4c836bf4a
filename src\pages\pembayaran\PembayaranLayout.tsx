import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Button } from "@mui/material";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { getNavItem, MENU_INTERNAL, NavItem } from "@/helpers/menuConfig";

type LayoutProps = {
  children: React.ReactNode;
};

export const PembayaranLayout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const { id } = useParams();

  const [tabsOption, setTabsOption] = useState<NavItem[]>([]);
  const [selectedTab, setSelectedTab] = useState(0);

  const hasKaunterPermission = AuthHelper.hasAuthority([
    `${PermissionNames.MEJA_BANTUAN1.label}:${pageAccessEnum.Read}`,
    `${PermissionNames.MEJA_BANTUAN2.label}:${pageAccessEnum.Read}`,
    `${PermissionNames.MEJA_BANTUAN3.label}:${pageAccessEnum.Read}`,
  ]);

  useEffect(() => {
    if (!hasKaunterPermission) {
      navigate("/internal-user");
    }
  }, [hasKaunterPermission]);

  useEffect(() => {
    if (location.pathname?.includes("rekod-pembayaran")) {
      setSelectedTab(1);
    } else {
      setSelectedTab(0);
    }
  }, [location]);

  // menu permission
  const menus = getNavItem(MENU_INTERNAL(), "pembayaran");
  const [allowedMenuItems, setAllowedMenuItems] = useState<NavItem[]>(menus);

  useEffect(() => {
    const finalMenuItems = allowedMenuItems.filter((item) => {
      if (item.permissions.length == 0) {
        // If the permissions (length = 0), it means no permission is required.
        return true;
      } else {
        return AuthHelper.hasAuthority(item.permissions);
      }
    });

    if (JSON.stringify(finalMenuItems) !== JSON.stringify(allowedMenuItems)) {
      setAllowedMenuItems(finalMenuItems);
    }

    const activeSection = allowedMenuItems.findIndex((section) =>
      location.pathname.includes(section.path)
    );
    console.log("activeSection", activeSection);
    const result = allowedMenuItems[activeSection];

    if (activeSection !== -1) {
      setSelectedTab(0);
      if (result.subItems) navigate(result.subItems[0].path);
    }
    setTabsOption(allowedMenuItems || []);
  }, [allowedMenuItems]);

  return (
    <>
      <Box>
        <Box
          sx={{
            display: "flex",
            background: "#fff",
            borderRadius: "10px",
            p: 1,
            gap: 1,
            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
            overflowX: "auto",
          }}
        >
          {tabsOption?.map((tab, index) => (
            <Button
              key={index}
              onClick={() => {
                setSelectedTab(index);
                navigate(tab.path);
              }}
              sx={{
                textTransform: "none",
                fontWeight: selectedTab === index ? 600 : 400,
                color: selectedTab === index ? "#fff" : "#333",
                background:
                  selectedTab === index
                    ? "var(--primary-color)"
                    : "transparent",
                borderRadius: "5px",
                px: 3,
                py: 1,
                width: "100%",
                transition: "all 0.3s ease",
                "&:hover": {
                  background:
                    selectedTab === index ? "var(--primary-color)" : "#F1F4FA",
                },
              }}
            >
              {t(tab.label)}
            </Button>
          ))}
        </Box>
        {hasKaunterPermission && (
          <Box sx={{ flexGrow: 1, pt: 1 }}>{children}</Box>
        )}
      </Box>
    </>
  );
};

export default PembayaranLayout;
