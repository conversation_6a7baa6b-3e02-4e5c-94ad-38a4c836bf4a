import React from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, Button, IconButton, Typography } from "@mui/material";
import { TextFieldController } from "@/components";
import { AddIcon, TrashIcon } from "@/components/icons";

const Form: React.FC = () => {
  const { control } = useFormContext();
  const classes = globalStyles();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "perbelanjaan",
  });

  return (
    <Box className={classes.sectionBox}>
      <Typography className="title" mb={2}>
        Perbel<PERSON><PERSON><PERSON> bahagian A
      </Typography>

      <Box
        sx={{ display: "flex", gap: "4px", justifyContent: "flex-end" }}
        mb={3}
      >
        <IconButton
          onClick={() =>
            append({
              perkara: "",
              bilangan: "",
              anggaran_harga: "",
              jumlah: "",
            })
          }
          sx={{
            padding: "9px 10px",
            borderRadius: "5px",
            backgroundColor: "var(--primary-color)",
          }}
        >
          <AddIcon color="#fff" />
        </IconButton>

        <Button
          className={classes.btnOutline}
          sx={{ fontSize: "7px !important" }}
        >
          Tambah Perbelanjaan A
        </Button>
      </Box>

      {fields.map((field, index) => (
        <Box
          key={field.id}
          display="flex"
          alignItems="center"
          gap={1}
          position="relative"
          mb={2}
        >
          <Box sx={{ flex: 4 }}>
            <TextFieldController
              control={control}
              name={`perbelanjaan[${index}].perkara`}
              placeholder="Perkara"
            />
          </Box>

          <Box sx={{ flex: 1 }}>
            <TextFieldController
              control={control}
              name={`perbelanjaan[${index}].bilangan`}
              placeholder="Bilangan"
            />
          </Box>

          <Box sx={{ flex: 2 }}>
            <TextFieldController
              control={control}
              name={`perbelanjaan[${index}].anggaran_harga`}
              placeholder="Anggaran Harga"
            />
          </Box>

          <Box sx={{ flex: 2 }}>
            <TextFieldController
              control={control}
              name={`perbelanjaan[${index}].jumlah`}
              placeholder="Jumlah"
            />
          </Box>

          {index !== 0 && (
            <IconButton
              sx={{
                position: "absolute",
                right: "-15px",
                top: "50%",
                transform: "translateY(-50%)",
                padding: 0,
              }}
              onClick={() => remove(index)}
            >
              <TrashIcon color="red" />
            </IconButton>
          )}
        </Box>
      ))}
    </Box>
  );
};

const PerbelanjaanAForm = React.memo(Form);
export default PerbelanjaanAForm;
