import { Close, Refresh, OpenInFull, Minimize } from "@mui/icons-material";
import {
  Box,
  IconButton,
  Slide,
  Typography,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
} from "@mui/material";
import { Form } from "../form";
import { object, string } from "yup";
import { useSelector } from "react-redux";
import {
  getChatbotButtonPosition,
  getChatbotOpened,
} from "@/redux/chatbotReducer";
import { useChatbotContext } from "@/contexts/chatbot";
import { useCallback, useLayoutEffect, useRef, useState } from "react";
import { useGetIdentity } from "@refinedev/core";
import { IUser } from "@/types";
import { useTranslation } from "react-i18next";
import { useQuery } from "@/helpers";
import { ChatFormInner } from "./ChatFormInner";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm, FormProvider } from "react-hook-form";
import { ChatMessageOnlyText, ChatbotChatAreaProps } from "./types";
import { MessageDisplay } from "./MessageDisplay";
import { OptionPanel } from "./OptionPanel";
import { useChatbot } from "./useChatbot";
import { getOptionGroups } from "./utils";
import {
  animationKeyframes,
  chatBoxStyles,
  closeButtonStyles,
  getContainerStyles,
  headerStyles,
  headerTitleStyles,
} from "./styles";
import { useNavigate } from "react-router-dom";
import { deleteChatHistory } from "./api";

export const BetaBadgeTopRight = () => (
  <Box
    sx={{
      position: "absolute",
      top: 0,
      right: 0,
      width: 100,
      height: 100,
      zIndex: 20,
      pointerEvents: "none",
    }}
  >
    <Box
      sx={{
        position: "absolute",
        width: 160,
        transform: "rotate(45deg)",
        top: 10,
        // right: -40,
        backgroundColor: "red",
        textAlign: "center",
        py: 0.4,
        boxShadow: 2,
      }}
    >
      <Typography
        variant="caption"
        sx={{
          color: "white",
          fontWeight: "bold",
          fontSize: 16,
          // letterSpacing: 1,
        }}
      >
        BETA
      </Typography>
    </Box>
  </Box>
);

export const BetaBadgeTopLeft = () => (
  <Box
    sx={{
      position: "absolute",
      top: 0,
      left: 0,
      width: 100,
      height: 100,
      zIndex: 20,
      pointerEvents: "none",
    }}
  >
    <Box
      sx={{
        position: "absolute",
        width: 160,
        transform: "rotate(-45deg)",
        top: 10,
        left: -40,
        backgroundColor: "red",
        textAlign: "center",
        py: 0.5,
        boxShadow: 2,
      }}
    >
      <Typography
        variant="caption"
        sx={{
          color: "white",
          // fontWeight: "bold",
          // fontSize: 1,
          letterSpacing: 0.5,
        }}
      >
        BETA
      </Typography>
    </Box>
  </Box>
);

export const BetaBannerBottomHeader = () => (
  <Box
    sx={{
      position: "absolute",
      bottom: 0,
      right: 0, // Ganti ke 'left: 0' atau 'left: 50%' jika mau tengah
      bgcolor: "red",
      color: "white",
      px: 2,
      py: 0.5,
      borderTopLeftRadius: "8px",
      fontSize: "12px",
      fontWeight: "bold",
      zIndex: 1000,
    }}
  >
    BETA
  </Box>
);

/**
 * Main chatbot component
 */
export const ChatbotChatArea = ({ isMobile = false }: ChatbotChatAreaProps) => {
  const { i18n } = useTranslation();
  const positionState = useSelector(getChatbotButtonPosition);
  const isOpen = useSelector(getChatbotOpened);
  const { toggleChat } = useChatbotContext();
  const { data: user } = useGetIdentity<IUser>();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatFormRef = useRef<{ focus: () => void }>(null);
  const navigate = useNavigate();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Get locale
  const locale = ["bm", "my"].includes(i18n.language) ? "ms" : i18n.language;

  // Fetch society list data
  const { data: societyListDataResponse, refetch: refetchSocietyList } =
    useQuery({
      autoFetch: false,
      url: "society/getUserSociety",
      filters: [
        { field: "pageSize", value: 5, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
      ],
    });

  // Get option groups based on user status
  const optionGroups = getOptionGroups(locale, !!user?.identificationNo);

  // Use the chatbot hook for state management
  const {
    messages,
    currentBotMessage,
    displayedBotText,
    showOptionPanel,
    selectedParentKey,
    loading,
    error,
    handleOptionSelect,
    handleBackToOptions,
    handleSubmitMessage,
    resetChatbot,
  } = useChatbot({
    user,
    locale,
    isOpen,
    refetchSocietyList,
    societyListDataResponse,
    isWidget: true,
  });

  // Focus input when chatbot opens
  useLayoutEffect(() => {
    if (isOpen && !loading) {
      const timer = setTimeout(() => {
        if (chatFormRef.current) {
          chatFormRef.current.focus();
        }
      }, 300); // Slightly longer delay to ensure the slide animation completes
      return () => clearTimeout(timer);
    }
  }, [isOpen, loading]);

  // Focus input after bot finishes typing
  useLayoutEffect(() => {
    if (currentBotMessage === null && !loading) {
      const timer = setTimeout(() => {
        if (chatFormRef.current) {
          chatFormRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [currentBotMessage, loading]);

  // Focus input when option panel is shown
  useLayoutEffect(() => {
    if (showOptionPanel && !loading) {
      const timer = setTimeout(() => {
        if (chatFormRef.current) {
          chatFormRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [showOptionPanel, loading]);

  // Form setup
  const formMethods = useForm<ChatMessageOnlyText>({
    defaultValues: { text: "" },
    resolver: yupResolver(object({ text: string().required() })),
    mode: "onChange",
  });

  // Handle form submission
  const handleSend = async (data: ChatMessageOnlyText) => {
    if (!data.text.trim()) return;
    formMethods.reset({ text: "" });
    await handleSubmitMessage(data.text);
  };

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  // Reset form when bot is typing
  useLayoutEffect(() => {
    if (currentBotMessage !== null) {
      formMethods.reset({ text: "" });
    }
  }, [currentBotMessage, formMethods]);

  // Scroll to bottom when messages change
  useLayoutEffect(() => {
    scrollToBottom();
  }, [messages, currentBotMessage, loading, scrollToBottom]);

  // Scroll to bottom when chat opens
  useLayoutEffect(() => {
    if (isOpen) {
      scrollToBottom();
    }
  }, [isOpen, scrollToBottom]);

  const handleDeleteChat = async () => {
    if (!user?.identificationNo) return;

    try {
      await deleteChatHistory(user.identificationNo);
      resetChatbot();
      setShowDeleteConfirm(false);
      // console.log("Chat history deleted successfully");
    } catch (error) {
      // console.error("Failed to delete chat history:", error);
    }
  };

  const handleFullscreen = () => {
    window.open("/chatbot", "_blank");
  };

  if (isOpen) {
    return (
      <>
        <Box sx={getContainerStyles(isMobile, positionState)}>
          <Slide in={isOpen} direction="left" mountOnEnter unmountOnExit>
            <Box
              sx={{
                ...(isMobile
                  ? {
                      width: 320,
                      height: 480,
                    }
                  : {
                      width: "inherit",
                      height: "inherit",
                    }),
                ...chatBoxStyles,
                display: "flex",
                flexDirection: "column",
                overflow: "hidden",
              }}
            >
              {/* Header */}
              <Box sx={headerStyles}>
                {/* <BetaBadgeTopLeft /> */}
                <BetaBadgeTopRight />
                <Typography variant="h6" sx={headerTitleStyles}>
                  <img
                    src="/rosie-bot.png"
                    alt="Chatbot"
                    style={{
                      width: "48px",
                      height: "48px",
                      objectFit: "cover",
                    }}
                  />
                  ROSIE
                </Typography>

                <Box sx={{ display: "flex", gap: 1 }}>
                  <IconButton
                    onClick={handleFullscreen}
                    size="medium"
                    sx={{ color: "primary.main" }}
                    title="Open in fullscreen"
                  >
                    <OpenInFull />
                  </IconButton>
                  {user?.identificationNo && (
                    <Tooltip
                      title={
                        locale === "ms"
                          ? "Padam Sejarah Chat"
                          : "Clear chat history"
                      }
                      placement="bottom"
                    >
                      <IconButton
                        onClick={() => setShowDeleteConfirm(true)}
                        size="medium"
                        sx={{ color: "error.main" }}
                      >
                        <Refresh />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Tooltip
                    title={locale === "ms" ? "Kecilkan" : "Minimize"}
                    placement="bottom"
                  >
                    <IconButton
                      onClick={toggleChat}
                      size="medium"
                      sx={closeButtonStyles}
                    >
                      <Minimize color="primary" />
                    </IconButton>
                  </Tooltip>
                </Box>
                {/* <BetaBannerBottomHeader/> */}
              </Box>
              {/* <Box
              sx={{
                backgroundColor: 'red',
                color: 'white',
                textAlign: 'center',
                fontWeight: 'bold',
                fontSize: '12px',
                py: 0.5,
                borderBottom: '1px solid #ccc',
                boxShadow: 1,
              }}
            >
              BETA VERSION
            </Box> */}

              {/* Messages */}
              <Box
                sx={{
                  flex: 1,
                  display: "flex",
                  flexDirection: "column",
                  overflow: "hidden",
                  minHeight: 0, // Important for flex child
                }}
              >
                <MessageDisplay
                  messages={messages}
                  currentBotMessage={currentBotMessage}
                  displayedBotText={displayedBotText}
                  loading={loading}
                  error={error}
                  userProfilePicture={user?.profilePicture || ""}
                  messagesEndRef={messagesEndRef}
                  locale={locale}
                  isWidget={true}
                />
              </Box>

              {/* Options Panel */}
              <OptionPanel
                showOptionPanel={showOptionPanel}
                loading={loading}
                currentBotMessage={currentBotMessage}
                selectedParentKey={selectedParentKey}
                optionGroups={optionGroups}
                locale={locale}
                onOptionSelect={handleOptionSelect}
                onBackToOptions={handleBackToOptions}
              />

              {/* Input Form */}
              <FormProvider {...formMethods}>
                <Form<ChatMessageOnlyText>
                  onSubmit={handleSend}
                  defaultValues={{ text: "" }}
                  key={currentBotMessage || "default"}
                >
                  <ChatFormInner isWidget={true} ref={chatFormRef} />
                </Form>
              </FormProvider>
            </Box>
          </Slide>
        </Box>
        <style>{animationKeyframes}</style>
        <Dialog
          open={showDeleteConfirm}
          onClose={() => setShowDeleteConfirm(false)}
          PaperProps={{
            style: {
              borderRadius: "8px",
              padding: "16px",
            },
          }}
        >
          <DialogTitle sx={{ textAlign: "center" }}>
            {locale === "ms" ? "Pengesahan Pemadaman" : "Delete Confirmation"}
          </DialogTitle>
          <DialogContent>
            <Typography>
              {locale === "ms"
                ? "Adakah anda pasti untuk memadam sejarah chat ini?"
                : "Are you sure you want to delete this chat history?"}
            </Typography>
          </DialogContent>
          <DialogActions sx={{ justifyContent: "center", gap: 2 }}>
            <Button
              variant="outlined"
              onClick={() => setShowDeleteConfirm(false)}
            >
              {locale === "ms" ? "Batal" : "Cancel"}
            </Button>
            <Button
              variant="contained"
              onClick={handleDeleteChat}
              sx={{
                bgcolor: "#E53E3E",
                "&:hover": { bgcolor: "#C53030" },
              }}
            >
              {locale === "ms" ? "Padam" : "Delete"}
            </Button>
          </DialogActions>
        </Dialog>
      </>
    );
  } else {
    return (
      <Box
        sx={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          zIndex: 50,
        }}
      >
        <Fab
          onClick={toggleChat}
          color="primary"
          size="medium"
          sx={{
            boxShadow: 3,
            "&:hover": {
              boxShadow: 6,
            },
          }}
        >
          <OpenInFull />
        </Fab>
      </Box>
    );
  }
};
