import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

import LandingPembubaran from "./component/LandingPembubaran";
import DialogNotice from "../../../pembubaran/DialogNotice";

const CawanganPembubaran: React.FC = () => {
  const navigate = useNavigate();

  const [isDialogOpen, setIsDialogOpen] = useState(true);

  const handleDialogClose = () => navigate(-1);
  const handleDialogConfirm = () => setIsDialogOpen(false);

  return (
    <>
      <LandingPembubaran />

      <DialogNotice
        open={isDialogOpen}
        onClose={handleDialogClose}
        handleConfirm={handleDialogConfirm}
      />
    </>
  );
};

export default CawanganPembubaran;
