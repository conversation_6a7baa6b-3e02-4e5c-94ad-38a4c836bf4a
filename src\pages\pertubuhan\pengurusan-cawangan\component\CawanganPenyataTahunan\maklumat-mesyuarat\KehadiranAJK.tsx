import { Box, Grid, TextField, Typography } from '@mui/material'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next';
import { MeetingAttendee } from '../interface';

type KehadiranAJKProps = {
  ahli: MeetingAttendee[] | undefined; // List of 'ahli' (members)
};

const KehadiranAJK: React.FC<KehadiranAJKProps> = ({ ahli }) => {
  const { t } = useTranslation();
  if(ahli && ahli?.length <= 0 ){
    return
  }
  return (
    <Box sx={{
      background: "white",
      border: "1px solid rgba(0, 0, 0, 0.12)",
      borderRadius: "14px",
      p: 3,
      mb: 2,
    }}>
      <Grid container spacing={2} alignItems="flex-start" sx={{ mb: 1 }}>

        <Grid item xs={12} sm={4}>
          <Typography
            variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}
          >
            {t("kehadiranAjk")}
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Grid container spacing={2}>
            {ahli?.map((item, index) => (
              <React.Fragment key={index}>
                <Grid item xs={6}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    placeholder="Name"
                    value={item.name}
                    disabled
                    sx={{
                      background: "#E8E9E8"
                    }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    placeholder="Jawatan"
                    value={item.position}
                    disabled
                    sx={{
                      background: "#E8E9E8"
                    }}
                  />
                </Grid>
              </React.Fragment>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Box>
  )
}

export default KehadiranAJK
