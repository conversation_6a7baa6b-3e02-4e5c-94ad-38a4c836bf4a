import { useEffect, useMemo } from "react";
import { NavLink, useNavigate, useOutlet, Outlet } from "react-router-dom";
import { Box } from "@mui/material";
import WrapContent from "../View/WrapContent";
import Menu from "./Menu";
import useQuery from "../../../helpers/hooks/useQuery";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

const KeputusanCawangan = () => {
  const navigate = useNavigate();

  const hasKaunterPermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Read
  );

  useEffect(() => {
    if (!hasKaunterPermission) {
      navigate("/pengurus-pertubuhan/maklumat-pertubuhan");
    }
  }, [hasKaunterPermission, navigate]);

  const { data: roDecisionPendingBranchCount, refetch: fetchSociety } =
    useQuery({
      url: `society/roDecision/getAllPendingCount/branch`,
      autoFetch: false,
    });

  const pendingBranchCount = roDecisionPendingBranchCount?.data?.data;

  useEffect(() => {
    fetchSociety();
  }, []);

  const tab = useMemo(
    () => [
      {
        name: "Pendaftaran Cawangan",
        slug: "pendaftaran",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchRegistrationPendingCount
            )
          : 0,
      },
      {
        name: "Lanjut Masa",
        slug: "lanjut-masa",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchExtensionTimePendingCount
            )
          : 0,
      },
      {
        name: "Pindaan Nama dan Alamat",
        slug: "pindaan-nama-dan-alamat",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data.branchAmendmentPendingCount
            )
          : 0,
      },
      {
        name: "Pembubaran",
        slug: "pembubaran",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchLiquidationPendingCount
            )
          : 0,
      },
      {
        name: "Permohonan bukan Warganegara",
        slug: "permohonan-bukan-warganegara",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchNonCitizenPendingCount
            )
          : 0,
      },
      {
        name: "Pegawai Awam",
        slug: "pegawai-awam",
        number: 21,
      },
      {
        name: "Pegawai Harta",
        slug: "pegawai-harta",
        number: 23,
      },
    ],
    [roDecisionPendingBranchCount]
  );

  if (hasKaunterPermission) {
    return (
      <Box>
        <WrapContent title="Keputusan Cawangan">
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
              gap: "1rem",
            }}
          >
            {tab.map((data, index) => {
              return (
                <NavLink
                  key={index}
                  to={`/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan${
                    data.slug === "pendaftaran" ? "" : `/${data.slug}`
                  }`}
                  end={index === 0}
                  style={{ textDecoration: "none" }}
                >
                  {({ isActive }) => (
                    <Menu data={data} isActive={isActive} onClick={() => {}} />
                  )}
                </NavLink>
              );
            })}
          </Box>
        </WrapContent>

        <Outlet context={{ pendingBranchCount }} />
      </Box>
    );
  }
};

export default KeputusanCawangan;
