export interface IPendingCount {
  societyRegistrationPendingCount: number;
  societyLiquidationPendingCount: number;
  societyNonCitizenPendingCount: number;
  societyPublicOfficerPendingCount: number;
  societyPropertyOfficerPendingCount: number;
  societyExternalAgencyReviewPendingCount: number;
  societyAmendmentPendingCount: number;
  branchRegistrationPendingCount: number;
  branchLiquidationPendingCount: number;
  branchNonCitizenPendingCount: number;
  branchExtensionTimePendingCount: number;
  branchPublicOfficerPendingCount: number;
  branchPropertyOfficerPendingCount: number;
  branchAmendmentPendingCount: number;
  societyRegistrationQueryPendingCount: number;
  branchRegistrationQueryPendingCount: number;
  societyAmendmentQueryPendingCount: number;
  societyAppealQueryPendingCount: number;
  societyPrincipalSecretaryPendingCount: number;
}
