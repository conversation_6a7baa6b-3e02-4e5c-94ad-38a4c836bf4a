import { createSlice } from '@reduxjs/toolkit';

interface UserCommitteeBySocietyIdStore {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: UserCommitteeBySocietyIdStore = {
  data: null,
  loading: false,
  error: null,
};

export const userCommitteeBySocietyIdDataSlice = createSlice({
  name: 'userCommitteeBySocietyIdData',
  initialState,
  reducers: {
    setUserCommitteeBySocietyIdDataRedux(state, action) {
      state.data = action.payload;
    },
    setUserCommitteeBySocietyIdLoading(state, action) {
      state.loading = action.payload;
    },
    setUserCommitteeBySocietyIdError(state, action) {
      state.error = action.payload;
    },
  },
});

export const { setUserCommitteeBySocietyIdDataRedux, setUserCommitteeBySocietyIdLoading, setUserCommitteeBySocietyIdError } = userCommitteeBySocietyIdDataSlice.actions;
export default userCommitteeBySocietyIdDataSlice.reducer;
