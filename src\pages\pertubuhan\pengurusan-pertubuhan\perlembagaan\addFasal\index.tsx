import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { useSelector } from "react-redux";
import { handleSaveContent } from "../helper/handleSaveContent";
import InfoQACard from "../../InfoQACard";
import { OrganizationStepper } from "../../organization-stepper";
import { ApplicationStatus, ConstitutionType } from "@/helpers/enums";

export const FasalContentAdd = () => {
  const clause = "";
  const asalData = "";
  const name = "";
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(2);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [matlamatPertubuhan, setMatlamatPertubuhan] = useState("");
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [dataId, setDataId] = useState<number | null>(null);
  const [clauseContentId, setClauseContentId] = useState("");
  const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [fasalNo, setFasalNo] = useState("");
  const [fasalTajuk, setFasalTajuk] = useState("");
  const [checked, setChecked] = useState(false);
  const [constitutionType, setConstitutionType] = useState<string | null>(null);
  const [constitutionTypeInt, setConstitutionTypeInt] = useState<
    string | number
  >(0);
  console.log(constitutionTypeInt);
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  const handleFasalNo = (e: any) => {
    const value = e.target.value;
    setFasalNo(value);
  };

  const handleTajukFasal = (e: any) => {
    const value = e.target.value;
    setFasalTajuk(value);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId, currentNo } = useParams();

  // const { data } = useCustom({
  //   url: `${API_URL}/society/${id}/basic`,
  //   method: "get",
  //   config: {
  //     headers: {
  //       portal: localStorage.getItem("portal"),
  //       authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
  //     },
  //   },
  //   queryOptions: {
  //     onSuccess: (responseData) => {
  //       const { societyName, address, mailingAddress } =
  //         responseData?.data?.data;
  //       setNamaPertubuhan(societyName);
  //     },
  //   },
  // });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // useEffect(() => {
  //   if (clause) {
  //   }
  // }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!fasalNo) {
      errors.fasalNo = t("fieldRequired");
    }

    if (!fasalTajuk) {
      errors.fasalTajuk = t("fieldRequired");
    }
    if (!matlamatPertubuhan) {
      errors.matlamatPertubuhan = t("fieldRequired");
    }

    return errors;
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  useEffect(() => {
    if (societyDataRedux) {
      setConstitutionType(societyDataRedux.constitutionType);
      for (const [key, value] of Object.entries(ConstitutionType)) {
        if (value[1] === societyDataRedux.constitutionType) {
          setConstitutionTypeInt(value[0]);
        }
      }
    }
  }, []);
  // const amendmentId = getLocalStorage("amendmentId", null);
  // const isViewMode = getLocalStorage("isViewMode", false);

  return (
    <>
      <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
        <Box
          sx={{
            width: "55vw",
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
          }}
        >
          <Grid container gap={1} sx={{ mb: 2 }}>
            <Grid xs={2}>
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Typography sx={{ mb: 1, ...labelStyle }}>
                  {t("noFasal")}
                </Typography>
                <TextField
                  size="small"
                  value={fasalNo}
                  onChange={handleFasalNo}
                  placeholder={t("noFasal")}
                  type="number"
                  fullWidth
                  required
                  sx={{ background: "#fff" }}
                  error={!!formErrors.fasalNo}
                  helperText={formErrors.fasalNo}
                />
              </Box>
            </Grid>
            <Grid xs={4}>
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Typography sx={{ mb: 1, ...labelStyle }}>
                  {t("tajukfasal")}
                </Typography>
                <TextField
                  size="small"
                  value={fasalTajuk}
                  onChange={handleTajukFasal}
                  placeholder={t("tajukfasal")}
                  fullWidth
                  required
                  sx={{ background: "#fff" }}
                  error={!!formErrors.fasalTajuk}
                  helperText={formErrors.fasalTajuk}
                />
              </Box>
            </Grid>
          </Grid>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              background: "#fff",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("clause")} {fasalNo} : {fasalTajuk}
            </Typography>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
              <Typography sx={labelStyle}> {t("kandungan")}</Typography>
            </Box>
            <TextField
              fullWidth
              value={matlamatPertubuhan}
              variant="outlined"
              multiline
              minRows={3}
              onChange={(e) => {
                setMatlamatPertubuhan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  matlamatPertubuhan: "",
                }));
              }}
              error={!!formErrors.matlamatPertubuhan}
              helperText={formErrors.matlamatPertubuhan}
              sx={{
                "& fieldset": { borderRadius: "12px" },
                "& .MuiInputBase-input": { color: "black" },
              }}
            />
          </Box>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("clauseContent")} {clauseId}
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={12}>
                <Box
                  sx={{
                    p: { xs: 1, sm: 2, md: 3 },
                    border: "1px solid #D9D9D9",
                    backgroundColor: "#FFFFFF",
                    borderRadius: "14px",
                  }}
                >
                  <Box
                    sx={{
                      whiteSpace: "pre-wrap",
                      wordWrap: "break-word",
                      py: 2,
                      height: "100%",
                    }}
                  >
                    <Typography
                      sx={{ fontWeight: "400 !important" }}
                      dangerouslySetInnerHTML={{ __html: matlamatPertubuhan }}
                    ></Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              px: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Grid item xs={12}>
              <FormControlLabel
                sx={{
                  color: "#666666",
                  "&.MuiFormControlLabel-label": {
                    fontWeight: "400 !important",
                  },
                }}
                control={
                  <Checkbox checked={checked} onChange={handleChangeCheckbox} />
                }
                label={`${t("checkBox")}`}
              />
              <span style={{ color: "red" }}>*</span>
            </Grid>
          </Box>

          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonOutline>

            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              onClick={() => {
                const errors = validateForm();
                if (Object.keys(errors).length > 0) {
                  setFormErrors(errors);
                  return;
                }
                handleSaveContent({
                  i18n,
                  societyId: societyDataRedux.id,
                  societyName: societyDataRedux.societyName,
                  // amendmentId: amendmentId,
                  // clauseContentId,
                  constitutionTypeId: constitutionTypeInt,
                  dataId,
                  isEdit,
                  createClauseContent,
                  editClauseContent,
                  clauseNo: fasalNo,
                  clauseName: fasalTajuk,
                  description: matlamatPertubuhan,
                  constitutionValues: [
                    {
                      constitutionContentId: null,
                      societyName: namaPertubuhan,
                      definitionName: matlamatPertubuhan,
                      titleName: "Matlamat Pertubuhan",
                    },
                  ],
                  clause: `clause${id}`,
                  clauseCount: id,
                });
              }}
              disabled={isCreatingContent || isEditingContent || !checked}
            >
              {isCreatingContent || isEditingContent ? t("saving") : t("save")}
            </ButtonPrimary>
          </Grid>
        </Box>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <OrganizationStepper
            activeStep={activeStep}
            hidePayment={
              societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
            }
          />

          {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
            <Box
              sx={{
                padding: 3,
                backgroundColor: "white",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("kuiri")}
              </Typography>
              <Box
                sx={{
                  padding: 3,
                  backgroundColor: "#DADADA",
                  borderRadius: "15px",
                  maxHeight: "60vh",
                  maxWidth: "18vw",
                }}
              >
                <Typography
                  sx={{
                    mb: 8,
                    fontSize: "12px",
                    color: "#666666",
                    fontWeight: "500 !important",
                  }}
                >
                  {societyDataRedux.queryText}
                </Typography>
              </Box>
            </Box>
          ) : null}

          <InfoQACard />
        </Box>
      </Box>
    </>
  );
};

export default FasalContentAdd;
