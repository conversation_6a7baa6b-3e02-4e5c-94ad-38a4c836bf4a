import {TrainingFormProps} from "@/pages/internal-training/createStepOne";
import React, {useEffect, useState} from "react";
import {Box, Grid, IconButton, TextField, Typography} from "@mui/material";
import {useTranslation} from "react-i18next";
import DurationComponent from "@/pages/internal-training/durationComponent";
import ChapterFragment from "@/pages/internal-training/chapterFragment";
import {ButtonOutline, ButtonPrimary} from "@/components";
import {TrashIcon} from "@/components/icons";
import {DeleteIcon} from "@/components/icons/delete";
import {TrainingAddIcon} from "@/components/icons/trainingAdd";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import ConfirmationDialog from "@/pages/internal-training/confirmDialog";
import {DocumentUploadType, useUploadPresignedUrl} from "@/helpers";


export interface TrainingChapter {
  trainingCourseId: number,
  id: number,
  title: string,
  description: string,
  duration: number,
  youtubeLink: string,
  media: File | null,
}

const CreateStepTwo: React.FC<TrainingFormProps> = ({
                                                      headerStyle,
                                                      labelStyle,
                                                      borderStyle,
                                                      handleNext,
                                                      courseId,
                                                      isUpdate
                                                    }) => {

  const {t, i18n} = useTranslation();

  const [openModal, setOpenModal] = useState(false);

  const [noOfUploads, setNoOfUploads] = useState(0);

  const [totalChapter, setTotalChapter] = useState<TrainingChapter[]>([{
    trainingCourseId: courseId,
    id: 0,
    title: "",
    description: "",
    duration: 0,
    youtubeLink: "",
    media: null,
  }])

  const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {

  }

  const handleSave = (e: React.MouseEvent<HTMLButtonElement>) => {
    totalChapter.map((e, i) => {
      if (isUpdate) Edit(i);
      else Create(i);
    });
  }

  console.log("courseId",courseId);
  const handleAddChapter = (e: React.MouseEvent<HTMLButtonElement>) => {
    const temp = {
      trainingCourseId: courseId,
      id: 0,
      title: "",
      description: "",
      duration: 0,
      youtubeLink: "",
      media: null,
    }
    totalChapter.push(temp)
    const newArray = totalChapter.slice();
    setTotalChapter(newArray)
  }

  const handleDeleteChapter = (i: number) => {
    totalChapter.splice(i, 1);
    const newArray = totalChapter.slice();
    setTotalChapter(newArray)
  }

  const { upload: uploadFile, isLoading: isUploading } = useUploadPresignedUrl({
    onSuccessUpload: () => {
      setNoOfUploads((prevState) => prevState+1);
      if(noOfUploads == totalChapter.length){
        handleNext(courseId, "quiz");
      }
    },
  });


  const {mutate: create, isLoading: isLoadingCreate} = useCustomMutation();
  const Create = (i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    create(
      {
        url: `${API_URL}/society/admin/training/materials`,
        method: "post",
        values: {
          trainingCourseId: courseId,
          title: totalChapter[i].title ?? "",
          contentText: totalChapter[i].description ?? "",
          materialType: "VIDEO",
          youtubeLink: totalChapter[i].youtubeLink ?? "",
          description: totalChapter[i].description ?? "",
          duration: totalChapter[i].duration ?? 60,
          sequenceOrder: i + 1
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            // Handle file upload
            if (totalChapter[i].media) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_MATERIAL,
                  trainingMaterialId: data?.data?.data
                },
                file: totalChapter[i].media,
              });
            }
            else{
              handleNext(courseId, "quiz");
            }
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {mutate: edit, isLoading: isLoadingEdit} = useCustomMutation();
  const Edit = (i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    edit(
      {
        url: `${API_URL}/society/admin/training/materials`,
        method: "put",
        values: {
          id: totalChapter[i].id,
          trainingCourseId: courseId,
          title: totalChapter[i].title ?? "",
          contentText: totalChapter[i].description ?? "",
          materialType: "VIDEO",
          youtubeLink: totalChapter[i].youtubeLink ?? "",
          description: totalChapter[i].description ?? "",
          duration: totalChapter[i].duration ?? 60,
          sequenceOrder: i + 1
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            // Handle file upload
            if (totalChapter[i].media) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_MATERIAL,
                  trainingId: data?.data?.data
                },
                file: totalChapter[i].media,
              });
            }
            else{
              handleNext(courseId, "quiz");
            }
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {data: trainingData, isLoading: isTrainingLoading} = useCustom({
    url: `${API_URL}/society/admin/training/courses/${courseId}/materials`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: isUpdate && courseId!=0,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingDetails = trainingData?.data?.data || [];
  console.log("trainingMaterial", trainingDetails)

  useEffect(() => {
    if(isUpdate && (trainingDetails).length > 0){
      const tempArr : TrainingChapter[] = trainingDetails.map((e: TrainingChapter) => {
        const temp = {
          trainingCourseId: e.trainingCourseId,
          id: e.id,
          title: e.title || "",
          description: e.description || "",
          duration: e.duration || 60,
          youtubeLink: e.youtubeLink || "",
          media: "",
        }
        return temp;
      })
      setTotalChapter(tempArr);
    }
  }, [trainingDetails]);

  const handleDataChange = (i: number, data: TrainingChapter) => {
    //totalChapter[i].id = data.id;
    totalChapter[i].title = data.title;
    totalChapter[i].description = data.description;
    totalChapter[i].youtubeLink = data.youtubeLink;
    totalChapter[i].duration = data.duration;
    totalChapter[i].media = data.media;
    setTotalChapter(totalChapter)
  }
  return(<>
      <Box sx={{display: "flex"}}>
        <Box sx={{width: "85%",}}>
          {totalChapter.map((e: TrainingChapter, index) => {
              return (<Box
                  key={index}
                  sx={{
                    borderRadius: 2.5,
                    backgroundColor: "#fff",
                    //display: "inline",
                    px: 2,
                    py: 2,
                    mb: 1,
                  }}
                >
                  <ChapterFragment no={index + 1} headerStyle={headerStyle} borderStyle={borderStyle}
                                   labelStyle={labelStyle} handleDataChange={handleDataChange} data={e} />
                  {index === totalChapter.length - 1 ?
                    <Grid
                      item
                      xs={12}
                      sx={{
                        mt: 2,
                        display: "flex",
                        flexDirection: "row",
                        justifyContent: "flex-end",
                        gap: 1,
                      }}
                    >
                      <ButtonOutline
                        sx={{
                          bgcolor: "white",
                          "&:hover": {bgcolor: "white"},
                          width: "auto",
                        }}
                        onClick={handleSaveDraft}
                      >
                        {t("save")}
                      </ButtonOutline>
                      <ButtonPrimary
                        variant="contained"
                        sx={{
                          width: "auto",
                        }}
                        onClick={() => setOpenModal(true)}
                        //disabled={true}
                      >
                        {t("next")}
                      </ButtonPrimary>
                    </Grid> : <></>} </Box>
              )
            }
          )}
        </Box>
        <Box sx={{width: "15%"}}>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
              ml: 2,
            }}
          >
            {totalChapter.map((e, i) => {
              return <Grid container spacing={1} key={i}>
                <Grid item xs={10}>
                  <Box
                    sx={borderStyle}
                  >
                    <Typography key={i} sx={{
                      fontSize: 14,
                      color: "#666666",
                      fontWeight: "400 !important",
                    }}>
                      {`${t("chapter")} ${i + 1}`}
                    </Typography>
                  </Box></Grid>
                {i > 0 ?
                  <Grid item xs={2}>
                    <IconButton
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignContent: "center",
                        mt: 1
                      }}
                      onClick={() => {
                        handleDeleteChapter(i)
                      }}
                    >
                      <DeleteIcon
                        sx={{
                          color: "#FF0000",
                        }}
                      />
                    </IconButton></Grid> : <Grid item xs={2}></Grid>}
              </Grid>
            })}
            <Box>
              <Grid container sx={{mt: 1}}>
                <Grid item xs={3}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 2.5,
                      backgroundColor: "#0CA6A6",
                      width: 36,
                      height: 36,
                    }}
                  >
                    <TrainingAddIcon sx={{color: "#FFF",}}/>
                  </Box>
                </Grid>
                <Grid item xs={9}>
                  <ButtonOutline
                    sx={{
                      bgcolor: "white",
                      "&:hover": {bgcolor: "white"},
                      width: "75%",
                      minWidth: "75%"
                    }}
                    onClick={handleAddChapter}
                  >
                    {t("addChapter")}
                  </ButtonOutline>
                </Grid>
              </Grid>
            </Box>
          </Box>
        </Box>
      </Box>
      <ConfirmationDialog text={"Adakah anda pasti untuk mencipta latihan ini"} handleSave={handleSave}
                          labelStyle={labelStyle} openModal={openModal} setOpenModal={setOpenModal}/>
    </>
  );
}

export default CreateStepTwo;
