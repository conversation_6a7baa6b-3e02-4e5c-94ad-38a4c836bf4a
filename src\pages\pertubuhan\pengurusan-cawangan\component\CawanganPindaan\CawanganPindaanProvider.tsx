import React, {
  createContext,
  useContext,
  PropsWithChildren,
  useState,
  ReactNode,
} from "react";
import { useLocation, useParams } from "react-router-dom";
import {
  AddressList,
  Ajk,
  AjkNonCiizen,
  AliranTugas,
  Auditor,
  Document,
  PropertyOfficer,
  PublicOfficer,
  Society,
  Trustee,
} from "@/pages/pertubuhan/pernyata-tahunan/interface";
import useQuery from "@/helpers/hooks/useQuery";
import { ApplicationStatusList } from "@/helpers";
import { useSelector } from "react-redux";

interface BranchPindaanContextProps {
  branchAmendmentList: any[];
  setBranchAmendmentList: React.Dispatch<React.SetStateAction<any[]>>;
}

const branchPindaanContext = createContext<
  BranchPindaanContextProps | undefined
>(undefined);

export const useCawanganPindaanContext = (): BranchPindaanContextProps => {
  const context = useContext(branchPindaanContext);

  if (!context) {
    throw new Error(
      "usebranchPindaanContext must be used within a CawanganPindaanProvider"
    );
  }
  return context;
};

const CawanganPindaanProvider = ({ children }: { children: ReactNode }) => {
  const { id: societyId } = useParams();
  const location = useLocation();

  const [branchAmendmentList, setBranchAmendmentList] = useState<any[]>([]);

  return (
    <branchPindaanContext.Provider
      value={{
        branchAmendmentList,
        setBranchAmendmentList,
      }}
    >
      {children}
    </branchPindaanContext.Provider>
  );
};

export default CawanganPindaanProvider;
