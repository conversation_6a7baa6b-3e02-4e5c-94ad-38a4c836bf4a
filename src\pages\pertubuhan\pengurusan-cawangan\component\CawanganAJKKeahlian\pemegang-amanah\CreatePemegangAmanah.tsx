import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormControl,
  Select,
  MenuItem,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { API_URL } from "@/api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
} from "@/helpers/enums";
import { useEffect, useState } from "react";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { Trustee } from "../../CawanganPenyataTahunan/interface";
import { Controller, useForm } from "react-hook-form";
import Input from "@/components/input/Input";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import { useSelector } from "react-redux";
import { getLocalStorage } from "@/helpers";

export const CreatePemegangAmanah: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { id: societyId } = useParams();
  const location = useLocation();
  const trusteeId = location.state?.trusteeId;
  const view = location.state?.view;

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const occupationList = getLocalStorage("occupation_list", []);
  const handleBack = () => {
    navigate(-1);
  };

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = data?.data?.data || [];

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { trustee, fetchTrustee } = usejawatankuasaContext();

  useEffect(() => {
    if (trusteeId) {
      fetchTrustee();
      if (trustee) {
        Object.entries(trustee).forEach(([key, value]) => {
          setValue(key, value);
        });
      }
    }
  }, [fetchTrustee]);

  const form = useForm<Trustee | any>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    reset,
  } = form;

  const { mutate: saveTrustee, isLoading: isSaveTrustee } = useCustomMutation();

  const onSubmit = (data: any) => {
    saveTrustee(
      {
        url: trusteeId
          ? `${API_URL}/society/trustee/${trusteeId}`
          : `${API_URL}/society/trustee/create`,
        method: trusteeId ? "put" : "post",
        values: {
          ...data,
          societyId: societyId,
          branchId: branchDataRedux.id,
          branchNo: branchDataRedux.branchNo,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: () => {
          navigate(-1);
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("trusteeInformation")}
          </Typography>

          <Controller
            name="appointmentDate"
            control={control}
            defaultValue={getValues("appointmentDate")}
            render={({ field }) => {
              const date = getValues("appointmentDate");

              const formattedDate = Array.isArray(date)
                ? `${date[0]}-${String(date[1]).padStart(2, "0")}-${String(
                    date[2]
                  ).padStart(2, "0")}`
                : date;

              return (
                <Input
                  disabled={view}
                  required
                  {...field}
                  label={t("tarikhLantik")}
                  type="date"
                  value={formattedDate}
                />
              );
            }}
          />

          <Controller
            name="identificationType"
            defaultValue={"1"}
            control={control}
            render={({ field }) => (
              <Input
                disabled
                {...field}
                label={t("idType")}
                type="select"
                onChange={(e) => {
                  // Pass the event back to onChange in React Hook Form
                  field.onChange(e);
                }}
                options={IdTypes}
                value={"1"}
              />
            )}
          />
          <Controller
            name="identificationNo"
            control={control}
            defaultValue={getValues("identificationNo")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("idNumberPlaceholder")}
                value={getValues("identificationNo")}
              />
            )}
          />

          <Controller
            name="titleCode"
            control={control}
            defaultValue={getValues("titleCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("title")}
                type="select"
                options={ListGelaran}
                value={getValues("titleCode")}
              />
            )}
          />

          <Controller
            name="name"
            control={control}
            defaultValue={getValues("name")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("fullName")}
                options={ListGelaran}
              />
            )}
          />

          <Controller
            name="gender"
            control={control}
            defaultValue={getValues("gender")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("gender")}
                type="select"
                options={ListGender.map((gender) => ({
                  ...gender,
                  label: t(gender.label),
                }))}
              />
            )}
          />

          <Controller
            name="citizenshipStatus"
            control={control}
            defaultValue={1}
            render={({ field }) => (
              <Input
                {...field}
                label={t("citizenship")}
                type="select"
                options={CitizenshipStatus.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
                disabled
              />
            )}
          />

          <Controller
            name="dateOfBirth"
            control={control}
            defaultValue={getValues("dateOfBirth")}
            render={({ field }) => {
              const date = getValues("dateOfBirth");

              const formattedDate = Array.isArray(date)
                ? `${date[0]}-${String(date[1]).padStart(2, "0")}-${String(
                    date[2]
                  ).padStart(2, "0")}`
                : date;

              return (
                <Input
                  disabled={view}
                  required
                  {...field}
                  label={t("dateOfBirth")}
                  type="date"
                  value={formattedDate}
                />
              );
            }}
          />

          <Controller
            name="placeOfBirth"
            control={control}
            defaultValue={getValues("placeOfBirth")}
            render={({ field }) => (
              <Input disabled={view} {...field} label={t("placeOfBirth")} />
            )}
          />

          <Controller
            name="occupationCode"
            defaultValue={getValues("occupationCode")}
            control={control}
            render={({ field }) => {
              return (
                <Input
                  disabled={view}
                  required
                  {...field}
                  error={!!errors.occupationCode?.message}
                  label={t("occupation")}
                  type="select"
                  value={getValues("occupationCode")}
                  options={occupationList}
                />
              );
            }}
          />

          <Controller
            name="address"
            control={control}
            defaultValue={getValues("address")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("residentialAddress")}
              />
            )}
          />

          <Controller
            name="stateCode"
            control={control}
            defaultValue={getValues("stateCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("state")}
                type="select"
                onChange={(e) => setValue("stateCode", e.target.value)}
                value={parseInt(getValues("stateCode"))}
                options={addressData
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => ({
                    label: item.name,
                    value: item.id,
                  }))}
              />
            )}
          />

          <Controller
            name="districtCode"
            control={control}
            defaultValue={getValues("districtCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("district")}
                type="select"
                value={parseInt(getValues("districtCode"))}
                onChange={(e) => setValue("districtCode", e.target.value)}
                options={addressData
                  .filter(
                    (item: any) => item.pid === parseInt(watch("stateCode"))
                  )
                  .map((item: any) => ({ label: item.name, value: item.id }))}
              />
            )}
          />

          <Controller
            name="city"
            control={control}
            defaultValue={getValues("city")}
            render={({ field }) => (
              <Input disabled={view} {...field} label={t("city")} type="text" />
            )}
          />

          <Controller
            name="postalCode"
            control={control}
            defaultValue={getValues("postalCode")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("poskod")}
                type="text"
              />
            )}
          />

          <Controller
            name="email"
            control={control}
            defaultValue={getValues("email")}
            rules={{
              required: t("emailRequired"),
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: "Sila masukkan e-mel yang betul.",
              },
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("email")}
                type="email"
                error={!!error}
                helperText={error?.message}
              />
            )}
          />

          <Controller
            name="mobilePhoneNumber"
            control={control}
            defaultValue={getValues("mobilePhoneNumber")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("phoneNumber")}
                type="text"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  form.setValue(field.name, value);
                }}
              />
            )}
          />

          <Controller
            name="homePhoneNumber"
            control={control}
            defaultValue={getValues("homePhoneNumber")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                label={t("homeNumber")}
                type="text"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  form.setValue(field.name, value);
                }}
              />
            )}
          />

          <Controller
            name="officePhoneNumber"
            control={control}
            defaultValue={getValues("officePhoneNumber")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                label={t("officeNumber")}
                type="text"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  form.setValue(field.name, value);
                }}
              />
            )}
          />
        </Box>

        <Box
          sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}
        >
          <ButtonOutline onClick={handleBack}>{t("semula")}</ButtonOutline>
          {!view ? (
            <ButtonPrimary type="submit">{t("update")}</ButtonPrimary>
          ) : null}
        </Box>
      </Box>
    </form>
  );
};

export default CreatePemegangAmanah;
