import {
  G<PERSON>,
  <PERSON>,
  Typography,
  IconButton,
  Radio,
  RadioGroup,
  FormControlLabel,
  Button,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import FeedbackOutlinedIcon from "@mui/icons-material/FeedbackOutlined";
import WorkspacePremiumOutlinedIcon from "@mui/icons-material/WorkspacePremiumOutlined";
import { useEffect, useState } from "react";
import { DialogActionFlow } from "../../../components/dialog/confirm/DialogActionFlow";
import { eventService } from "@/services/eventService";
import {
  EventFeedbackAnswer,
  EventFeedbackAnswer2,
  FeedbackScale,
  IEventFeedbackQuestion,
} from "@/types/eventFeedbackQuestion";
import { ApiResponse } from "@/types/event";
import { IEventAttendee } from "@/types/eventAttendee";
import { IUser } from "@/types";
import { useGetIdentity, useNotification } from "@refinedev/core";
import { useTakwim } from "@/contexts/takwimProvider";
import dayjs from "dayjs";
import PriorityHighIcon from "@mui/icons-material/PriorityHigh";
import FeedIcon from "@mui/icons-material/Feed";
import ErrorIcon from "@mui/icons-material/Error";
import { t } from "i18next";
import AuthHelper from "@/helpers/authHelper";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import { ExclamationIcon } from "@/components/icons/exclamation";
import { ApplicationSubmittedIcon } from "@/components/icons";

interface FeedbackItem {
  id: number;
  perkara: string;
  hasMaklumbalas?: boolean;
  hasSertifikasi?: boolean;
}

interface Question {
  id: number;
  question: string;
}

interface AnswerOption {
  id: number;
  code: number;
  name: string;
}

interface MaklumBalasContentProps {
  feedbacks?: FeedbackItem[];
  feedbackName?: string;
  eventNo?: string;
  eventEndDate?: Date | null | undefined;
  isEventEnded?: boolean;
}

const MaklumBalasContent = ({
  feedbacks = [],
  feedbackName,
  eventNo,
  eventEndDate,
  isEventEnded,
}: MaklumBalasContentProps) => {
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedValues, setSelectedValues] = useState<{
    [key: number]: string;
  }>({});
  const [errors, setErrors] = useState<{ [key: number]: boolean }>({});
  const [eventFeedBackQuestion, setEventFeedBackQuestion] = useState<
    IEventFeedbackQuestion[]
  >([]);
  const [feedbackScale, setFeedbackScale] = useState<FeedbackScale[]>([]);
  const { data: user } = useGetIdentity<IUser>();
  const { userAttendance, setUserAttendance } = useTakwim();
  const { open: openNotification } = useNotification();
  const [attendanceNo, setAttendanceNo] = useState("");
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);
  const [isGeneratingCertificate, setIsGeneratingCertificate] = useState(false);
  const [isLoadingListSubmitters, setIsloadingSubmitters] = useState(false);
  const [customDialogMessage, setCustomDialogMessage] = useState<string | null>(
    null
  );
  const [isErrorSubmittingFeedback, setIsErrorSubmittingFeedback] =
    useState<boolean>();
  const [
    isLoadingIndividualFeedbackAnswers,
    setIsLoadingIndividualFeedbackAnswers,
  ] = useState(false);
  const [loadingAttendanceNo, setLoadingAttendanceNo] = useState<string | null>(
    null
  );
  const [feedbackSubmitters, setFeedbackSubmitters] =
    useState<IEventAttendee[]>();

  // Dummy questions that would come from API
  const questions: Question[] = [
    { id: 1, question: "Bagaimanakah pemilihan penceramah?" },
    { id: 2, question: "Bagaimanakah penyampaian penceramah?" },
    { id: 3, question: "Bagaimanakah pengurusan masa program?" },
    { id: 4, question: "Bagaimanakah kemudahan yang disediakan?" },
    { id: 5, question: "Bagaimanakah keberkesanan program?" },
    { id: 6, question: "Bagaimanakah kepuasan keseluruhan program?" },
  ];

  // Standard answer options that would come from API
  const answerOptions: AnswerOption[] = [
    { id: 1, code: 1, name: "Buruk" },
    { id: 2, code: 2, name: "Biasa" },
    { id: 3, code: 3, name: "Baik" },
    { id: 4, code: 4, name: "Cemerlang" },
    { id: 5, code: 5, name: "Mantap" },
  ];

  const defaultFeedbacks: FeedbackItem[] = [
    {
      id: 1,
      perkara: "Tiada maklum balas",
      hasMaklumbalas: true,
      hasSertifikasi: true,
    },
  ];

  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      PermissionNames.TAKWIM?.label || "TAKWIM-AKT",
      accessType
    );

    // 2 is internal user
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }

  const hasEditEventPermission = checkPermissionAndUserGroup(pageAccessEnum.Update);

  const hasCreateEventPermission = checkPermissionAndUserGroup(pageAccessEnum.Create);

  const hasEventManagementPermission =
    hasCreateEventPermission && hasEditEventPermission;

  useEffect(() => {
    if (eventNo) {
      fetchEventFeedbackQuestion(eventNo);
      if (user && userAttendance != null) {
        // ONLY FETCH IF userAttendance is null
        // call it from detail page
        fetchAttendeeDetail(user.identificationNo, eventNo);
      }
      if (hasEventManagementPermission) {
        fetchListAttendeesSubmitedFeedback(eventNo);
      }
    }
  }, [eventNo, user]);
  const displayFeedbacks = feedbacks.length > 0 ? feedbacks : defaultFeedbacks;

  const handleFeedbackClick = async (attendanceNo: string) => {
    if (attendanceNo == "") {
      setShowFeedbackForm(true);
      setErrors({});
      return;
    }
    try {
      // Fetch feedback answers first
      setLoadingAttendanceNo(attendanceNo);
      await fetchFeedbackAsnwerByAttendanceNo(attendanceNo);

      // Then show the form
      setShowFeedbackForm(true);
      // Reset errors when opening form
      setErrors({});
    } catch (error) {
      console.error("Error fetching feedback answers:", error);
      openNotification?.({
        type: "error",
        message: "Error",
        description: "Failed to fetch feedback answers",
      });
    } finally {
      setLoadingAttendanceNo(null);
    }
  };
  function handleBackToList(): void {
    setShowFeedbackForm(false);
  }

  const fetchEventFeedbackQuestion = async (eventNo: string | undefined) => {
    try {
      // setIsLoading(true);
      const feedbackQuestions: ApiResponse<IEventFeedbackQuestion[]> =
        await eventService.getEventFeedbackQuestionByEventNo(eventNo);
      if (feedbackQuestions.code == 200) {
        setEventFeedBackQuestion(feedbackQuestions.data);
        const feedbackScaleRes: ApiResponse<FeedbackScale[]> =
          await eventService.getFeedbackScale();
        setFeedbackScale(feedbackScaleRes.data);
      }

      // setEventAttedees(attendees); // Set the organizer state with the returned data
    } catch (error) {
      console.log(
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions"
      );
    } finally {
      // setIsLoading(false);
    }
  };

  const fetchAttendeeDetail = async (
    identificationNo: string | undefined,
    eventNo: string | undefined
  ) => {
    try {
      // setIsLoading(true);
      const attendeeDetail: ApiResponse<IEventAttendee> =
        await eventService.getAttendeesDetailByIdentificationNo(
          identificationNo,
          eventNo
        );
      setUserAttendance(attendeeDetail.data);

      // setEventAttedees(attendees); // Set the organizer state with the returned data
    } catch (error) {
      console.log(
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions"
      );
    } finally {
      // setIsLoading(false);
    }
  };

  const fetchListAttendeesSubmitedFeedback = async (
    eventNo: string | undefined
  ) => {
    try {
      setIsloadingSubmitters(true);
      const submitters: ApiResponse<IEventAttendee[]> =
        await eventService.getListAttendeesSubmittedFeedback(eventNo);
      if (submitters.code == 200) {
        setFeedbackSubmitters(submitters?.data);
      }
    } catch (error) {
      console.log(
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions"
      );
    } finally {
      setIsloadingSubmitters(false);
    }
  };

  const fetchFeedbackAsnwerByAttendanceNo = async (
    attendanceNo: string | undefined
  ) => {
    try {
      // setIsLoading(true);
      const answersRes: ApiResponse<EventFeedbackAnswer2[]> =
        await eventService.getFeedbackAnswerByAttendanceNo(attendanceNo);
      if (answersRes.code == 200) {
        // setFeedbackSubmitters(submitters?.data);
        interface AnswersObj {
          [key: number]: string;
        }

        const answersObj: AnswersObj = answersRes.data.reduce<AnswersObj>(
          (acc: AnswersObj, answer: EventFeedbackAnswer2): AnswersObj => ({
            ...acc,
            [answer.feedbackQuestionId]: answer?.eventFeedbackScaleCode, // Remove leading zeros
          }),
          {}
        );
        setSelectedValues(answersObj);
      }
    } catch (error) {
      console.log(
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions"
      );
    } finally {
      // setIsLoading(false);
    }
  };

  const validateAnswers = (): boolean => {
    const newErrors: { [key: number]: boolean } = {};
    let isValid = true;

    eventFeedBackQuestion.forEach((question) => {
      if (!selectedValues[question.feedbackQuestionId]) {
        newErrors[question.feedbackQuestionId] = true;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleOpenDialog = () => {
    setCustomDialogMessage(null);
    setFeedbackSubmitted(false);
    if (validateAnswers()) {
      setOpenDialog(true);
    }
  };

  const handleRadioChange = (questionNumber: number, value: string) => {
    setSelectedValues((prev) => ({
      ...prev,
      [questionNumber]: value,
    }));
  };

  const handleSubmit = async () => {
    try {
      // await new Promise((resolve) => setTimeout(resolve, 1000));

      const formattedAnswers: EventFeedbackAnswer[] = Object.entries(
        selectedValues
      ).map(
        ([questionId, value]): EventFeedbackAnswer => ({
          feedbackScaleCode: value.padStart(2, "0"),
          feedbackQuestionId: parseInt(questionId),
        })
      );

      if (!userAttendance) return;
      const result = await eventService.createEventFeedbackAnswer(
        userAttendance?.attendanceNo,
        formattedAnswers
      );

      if (result.code === 201 || result.code === 202) {
        setIsErrorSubmittingFeedback(false);

        setCustomDialogMessage(
          result?.msg || "Maklumbalas anda telah berjaya dihantar"
        );
        // throw new Error(result.msg || "Failed to submit event feedback");
      }
      fetchAttendeeDetail(user?.identificationNo, eventNo);
      setFeedbackSubmitted(true);
      // setOpenDialog(false);
      // setShowFeedbackForm(false);
    } catch (error) {
      // setOpenDialog(false);
      setIsErrorSubmittingFeedback(true);
      openNotification?.({
        type: "error",
        message: "Error",
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
      });
      setCustomDialogMessage(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
      console.error("Error submitting feedback:", error);
    }
  };

  async function handleCertificateClick() {
    try {
      if (!eventNo || !user?.identificationNo) return;

      setIsGeneratingCertificate(true);

      const result = await eventService.generateCertificate(
        eventNo,
        user.identificationNo
      );

      // console.log(result.data, "RESULT CERTIFICATE");

      if (result.code == 200) {
        // Ensure the URL is valid
        if (!result.data) {
          throw new Error("No certificate URL returned");
        }

        // Try to open in a new tab with specific options
        const newWindow = window.open(
          result.data,
          "_blank",
          "noopener,noreferrer"
        );

        // Check if window was blocked by popup blocker
        if (
          !newWindow ||
          newWindow.closed ||
          typeof newWindow.closed === "undefined"
        ) {
          console.warn(
            "Certificate window may have been blocked by popup blocker"
          );

          // Fallback: Create a temporary link and click it
          const link = document.createElement("a");
          link.href = result.data;
          link.target = "_blank";
          link.rel = "noopener noreferrer";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Show a notification to help the user
          openNotification?.({
            type: "progress",
            message: "Sijil Dihasilkan",
            description:
              "Jika sijil tidak dibuka secara automatik, sila cuba lagi atau semak tetapan penghalang popup anda. ",
          });
        }
      } else {
        openNotification?.({
          type: "error",
          message: "Error",
          description: result.msg || "Failed to generate certificate",
        });
      }
    } catch (error) {
      console.error("Error generating certificate:", error);
      openNotification?.({
        type: "error",
        message: "Error",
        description: "Failed to generate certificate",
      });
    } finally {
      setIsGeneratingCertificate(false);
    }
  }

  if (showFeedbackForm) {
    return (
      <Box sx={{ width: "100%", p: 2 }}>
        <Typography variant="h6" sx={{ mb: 3, color: "#0CA6A6" }}>
          Maklumbalas {hasEventManagementPermission ? "Peserta" : ""}
        </Typography>

        {eventFeedBackQuestion.map((q, idx) => (
          <Box key={q.feedbackQuestionId} sx={{ mb: 3 }}>
            <Typography
              sx={{
                color: errors[q.feedbackQuestionId] ? "error.main" : "#0CA6A6",
                mb: 1,
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              {idx + 1}. {q.question}
              <Typography
                component="span"
                color="error"
                sx={{
                  fontSize: "0.75rem",
                  visibility: errors[q.feedbackQuestionId]
                    ? "visible"
                    : "hidden",
                }}
              >
                *Wajib diisi
              </Typography>
            </Typography>
            {!hasEventManagementPermission ? (
              <RadioGroup
                value={selectedValues[q.feedbackQuestionId] || ""}
                onChange={(e) => {
                  handleRadioChange(q.feedbackQuestionId, e.target.value);
                  // Clear error when answering
                  if (errors[q.feedbackQuestionId]) {
                    setErrors((prev) => ({
                      ...prev,
                      [q.feedbackQuestionId]: false,
                    }));
                  }
                }}
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  gap: 2,
                }}
              >
                {feedbackScale.map((option) => (
                  <FormControlLabel
                    key={option.code}
                    value={option.code.toString()}
                    control={
                      <Radio
                        size="small"
                        sx={{
                          color: errors[q.feedbackQuestionId]
                            ? "error.main"
                            : undefined,
                          "&.Mui-checked": {
                            color: errors[q.feedbackQuestionId]
                              ? "error.main"
                              : undefined,
                          },
                        }}
                      />
                    }
                    label={option.label}
                  />
                ))}
              </RadioGroup>
            ) : (
              <RadioGroup
                value={selectedValues[q.feedbackQuestionId] || ""}
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  gap: 2,
                }}
              >
                {feedbackScale.map((option) => (
                  <FormControlLabel
                    key={option.code}
                    value={option.code.toString()}
                    control={
                      <Radio
                        // disabled
                        size="small"
                        sx={{
                          color: errors[q.feedbackQuestionId]
                            ? "error.main"
                            : undefined,
                          "&.Mui-checked": {
                            color: errors[q.feedbackQuestionId]
                              ? "error.main"
                              : undefined,
                          },
                        }}
                      />
                    }
                    label={option.label}
                  />
                ))}
              </RadioGroup>
            )}
          </Box>
        ))}

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          {!hasEditEventPermission ? (
            <Button
              variant="contained"
              onClick={handleOpenDialog}
              sx={{
                bgcolor: "#0CA6A6",
                textTransform: "none",
                "&:hover": {
                  bgcolor: "#3BB1B1",
                },
              }}
            >
              Hantar Maklumbalas
            </Button>
          ) : (
            <Button
              variant="contained"
              onClick={handleBackToList}
              sx={{
                bgcolor: "#0CA6A6",
                textTransform: "none",
                "&:hover": {
                  bgcolor: "#3BB1B1",
                },
              }}
            >
              Kembali
            </Button>
          )}
        </Box>

        <DialogActionFlow
          open={openDialog}
          onClose={() => {
            setOpenDialog(false);
            if (feedbackSubmitted) {
              setShowFeedbackForm(false); // <-- Hide form after dialog closes
              setFeedbackSubmitted(false); // Reset flag for next time
            }
          }}
          onConfirm={handleSubmit}
          confirmationText="Adakah anda pasti untuk menghantar maklumbalas ini?"
          successMessage={
            customDialogMessage || "Maklumbalas anda telah berjaya dihantar"
          }
          icon={isErrorSubmittingFeedback ? <ApplicationSubmittedIcon color="#FF0000" /> : <ApplicationSubmittedIcon color="#0CA6A6" />}
        />
      </Box>
    );
  }

  return (
    <Box sx={{ width: "100%" }}>
      <Typography variant="h6" sx={{ mb: 3, color: "#0CA6A6" }}>
        {hasEventManagementPermission
          ? "Maklumbalas Peserta"
          : "Maklumbalas dan Sertifikasi"}
      </Typography>

      {/* Header */}
      {!hasEventManagementPermission ? (
        <Grid
          container
          sx={{
            pb: 1,
            mb: 1,
            color: "#666666",
          }}
        >
          <Grid item textAlign={"center"} xs={6}>
            <Typography>Perkara</Typography>
          </Grid>
          <Grid
            item
            xs={3}
            sx={{
              textAlign: "center",
              justifyContent: "center",
              display: "flex",
              gap: 1,
            }}
          >
            <Typography>Maklum balas</Typography>
            <Tooltip
              title={t("Maklum balas akan tersedia selepas acara tamat")}
              arrow
            >
              <ErrorIcon fontSize="small" />
            </Tooltip>
          </Grid>
          <Grid
            item
            xs={3}
            sx={{
              textAlign: "center",
              justifyContent: "center",
              display: "flex",
              gap: 1,
            }}
          >
            <Typography>Sertifikasi</Typography>
            <Tooltip
              title={t("Lengkapkan maklum balas untuk menjana sijil")}
              arrow
            >
              <ErrorIcon fontSize="small" />
            </Tooltip>
          </Grid>
        </Grid>
      ) : (
        <Grid
          container
          sx={{
            // backgroundColor: "#f8f9fa",
            borderBottom: "1px solid #dee2e6",
            py: 2,
            px: 2,
          }}
        >
          <Grid item xs={1}>
            <Typography sx={{ color: "#666666", fontWeight: 500 }}>
              No.
            </Typography>
          </Grid>
          <Grid item xs={8}>
            <Typography sx={{ color: "#666666", fontWeight: 500 }}>
              Nama
            </Typography>
          </Grid>
          <Grid item xs={3} sx={{ textAlign: "center" }}>
            <Typography sx={{ color: "#666666", fontWeight: 500 }}>
              Maklumbalas
            </Typography>
          </Grid>
        </Grid>
      )}
      {/* Content */}
      {!hasEventManagementPermission ? (
        // for non admin
        displayFeedbacks.map((item) => (
          <Grid
            container
            key={item.id}
            sx={{
              py: 2,
              borderBottom: "1px solid #97979780",
              borderTop: "1px solid #97979780",
              "&:hover": {
                backgroundColor: "#F8F9FA",
              },
            }}
            alignItems="center"
          >
            <Grid item textAlign={"center"} xs={6}>
              <Typography>{feedbackName}</Typography>
            </Grid>
            <Grid item xs={3} sx={{ textAlign: "center" }}>
              <IconButton
                disabled={!isEventEnded}
                onClick={() => handleFeedbackClick("")}
                color={item.hasMaklumbalas ? "primary" : "default"}
                // sx={{ opacity: item.hasMaklumbalas ? 1 : 0.5 }}
              >
                <FeedbackOutlinedIcon />
              </IconButton>
            </Grid>
            <Grid item xs={3} sx={{ textAlign: "center" }}>
              {isGeneratingCertificate ? (
                <CircularProgress size={24} sx={{ color: "#0CA6A6" }} />
              ) : (
                <IconButton
                  disabled={!userAttendance?.isFeedbackCompleted}
                  color={item.hasSertifikasi ? "primary" : "default"}
                  onClick={handleCertificateClick}
                >
                  <WorkspacePremiumOutlinedIcon />
                </IconButton>
              )}
            </Grid>
          </Grid>
        ))
      ) : // for admin
      isLoadingListSubmitters ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "200px",
          }}
        >
          <CircularProgress sx={{ color: "#0CA6A6" }} />
        </Box>
      ) : !feedbackSubmitters || feedbackSubmitters.length === 0 ? (
        // if no submitters
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            minHeight: "200px",
            gap: 2,
          }}
        >
          <FeedbackOutlinedIcon
            sx={{
              fontSize: 64,
              color: "#666666",
              opacity: 0.5,
            }}
          />
          <Typography
            sx={{
              color: "#666666",
              textAlign: "center",
            }}
          >
            Tiada maklumbalas buat masa ini
          </Typography>
        </Box>
      ) : (
        // if there are submitters
        feedbackSubmitters?.map((item, index) => (
          <Grid
            container
            key={item.attendanceNo}
            sx={{
              py: 2,
              px: 2,
              borderBottom: "1px solid #dee2e6",
              "&:hover": {
                backgroundColor: "#f8f9fa",
              },
            }}
            alignItems="center"
          >
            <Grid item xs={1}>
              <Typography sx={{ color: "#666666" }}>{index + 1}.</Typography>
            </Grid>
            <Grid item xs={8}>
              <Typography sx={{ color: "#666666" }}>
                {item?.fullName}
              </Typography>
            </Grid>
            <Grid item xs={3} sx={{ textAlign: "center" }}>
              {loadingAttendanceNo === item?.attendanceNo ? (
                <CircularProgress size={20} sx={{ color: "#0CA6A6" }} />
              ) : (
                <IconButton
                  onClick={() => handleFeedbackClick(item?.attendanceNo)}
                >
                  <FeedIcon
                    sx={{
                      fontSize: "20px",
                    }}
                  />
                </IconButton>
              )}
            </Grid>
          </Grid>
        ))
      )}

      {/* {displayFeedbacks.length === 0 && (
        <Typography
          variant="body1"
          sx={{
            textAlign: "center",
            color: "#666666",
            py: 3
          }}
        >
          Tiada maklum balas setakat ini
        </Typography>
      )} */}
    </Box>
  );
};

export default MaklumBalasContent;

