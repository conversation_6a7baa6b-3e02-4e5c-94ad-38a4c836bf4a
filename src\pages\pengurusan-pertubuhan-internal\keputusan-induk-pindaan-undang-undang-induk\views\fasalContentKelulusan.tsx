import React, { useEffect } from "react";
import { useN<PERSON><PERSON>, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button, useMediaQuery, useTheme } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";

import { DokumenIcon } from "../../../../components/icons";
import DisabledTextField from "../../../../components/input/DisabledTextField";
import FasalContent from "../../../../components/FasalContent";
import { useCustom } from "@refinedev/core";
import useMutation from "../../../../helpers/hooks/useMutation";
import { setLocalStorage } from "../../../../helpers/utils";
import { API_URL } from "../../../../api";
import { useState } from "react";
import { ConstitutionType, ApplicationStatus } from "../../../../helpers/enums";
import CustomPopover from "../../../../components/popover";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { useDownloadAndExportConstitutions } from "@/helpers/hooks/useDownloadConstitutions";

function FasalContentKelulusan(item: any) {
  const { societyId } = item;

  const labelStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { t } = useTranslation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));

  // const [clauseContentId, setClauseContentId] = useState(1);
  const [currentFasal, setCurrentFasal] = useState(0);
  const [beforeFasal, setBeforeFasal] = useState([]);
  const [afterFasal, setAfterFasal] = useState([]);
  //  const [baseFasal,setBaseFasal] = useState([])
  const { amendmentId } = useParams();
  const fasalRedux = useSelector((state: { fasal: any }) => state.fasal.data);
  const { data: clauseContentBefore, isLoading: loadingBeforeData } = useCustom(
    {
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          status: ApplicationStatus["AKTIF"],
          // amendmentId:amendmentId
        },
      },
      queryOptions: {
        enabled: !!societyId,
      },
    }
  );

  const { data: clauseContentAfter, isLoading: loadingAfterData } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyId,
        // status: ApplicationStatus["AKTIF"],
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!societyId,
    },
  });

  const clauseContentDataBefore = clauseContentBefore?.data?.data?.data || [];
  const clauseContentDataAfter = clauseContentAfter?.data?.data?.data || [];

  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyId}/`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const allConstitutions = constitutionData?.data?.data || [];
  const constitutionContentRegisterRequest = getLocalStorage(
    "constitutionContentRegisterRequest",
    null
  );

  const constitutionType =
    fasalRedux.currentAmendmentConstitutionType || ConstitutionType.IndukNGO[1];

  const generatePerlembagaanBefore = () => {
    if (constitutionType) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContentDataBefore.find(
              (item: any) =>  item.clauseNo ? Number(item.clauseNo) === Number(clause.id) :item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                content: existingItem?.description,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          setBeforeFasal(item.clauseContents);
        }
      });
    } else {
      setBeforeFasal([]);
    }
  };

  const generatePerlembagaanAfter = () => {
    if (constitutionType) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContentDataAfter.find(
              (item: any) => item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          setAfterFasal(item.clauseContents);
        }
      });
    } else {
      setAfterFasal([]);
    }
  };

  useEffect(() => {
    if (clauseContentDataBefore) {
      generatePerlembagaanBefore();
    }
    if (clauseContentAfter) {
      generatePerlembagaanAfter();
    }
  }, [
    constitutionType,
    constitutionData,
    clauseContentAfter,
    clauseContentBefore,
  ]);

  const isLoading =
    loadingAfterData || loadingBeforeData || isConstitutionLoading;

  const { getConstitutionsFile, isLoadingDownloadConstitutions } =
    useDownloadAndExportConstitutions();

  return (
    <>
      {!isLoading && afterFasal.length > 0 && beforeFasal.length > 0 && (
        <FasalContent
          scrollable
          fasalContent={beforeFasal}
          fasalContentActions={() => (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                rowGap: "0.5rem",
                marginTop: "1rem",
              }}
            >
              <Button
                startIcon={<DokumenIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() => societyId && getConstitutionsFile(societyId,2)}
              >
                Papar Perlembagaan Asal
              </Button>
              <Button
                startIcon={<DownloadIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() => societyId && getConstitutionsFile(societyId,1)}
              >
                Muat Turun Perlembagaan Asal
              </Button>
            </div>
          )}
          compareData={afterFasal}
          compareDataActions={() => (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                rowGap: "0.5rem",
                marginTop: "1rem",
              }}
            >
              <Button
                startIcon={<DokumenIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() => societyId && getConstitutionsFile(societyId,2,amendmentId)}
              >
                Papar Perlembagaan Pindaan
              </Button>
              <Button
                startIcon={<DownloadIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() => societyId && getConstitutionsFile(societyId,1,amendmentId)}
              >
                Muat Turun Perlembagaan Pindaan
              </Button>
            </div>
          )}
        />
      )}
    </>
  );
}

export default FasalContentKelulusan;
