import React, {useEffect, useState} from "react";
import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  SxProps,
  TextField,
  Theme,
  Typography
} from "@mui/material";
import DurationComponent from "@/pages/internal-training/durationComponent";
import {ButtonOutline, ButtonPrimary} from "@/components";
import {useTranslation} from "react-i18next";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import {useNavigate} from "react-router-dom";
import ConfirmationDialog from "@/pages/internal-training/confirmDialog";
import {DocumentUploadType, useUploadPresignedUrl} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";


export interface TrainingFormProps {
  headerStyle: SxProps<Theme>,
  labelStyle: SxProps<Theme>,
  borderStyle: SxProps<Theme>,
  handleNext: (id: number, page: string) => void,
  courseId: number,
  isUpdate: boolean,
}

const CreateStepOne: React.FC<TrainingFormProps> = ({
                                                      headerStyle,
                                                      labelStyle,
                                                      borderStyle,
                                                      handleNext,
                                                      courseId,
                                                      isUpdate
                                                    }) => {

  const {t, i18n} = useTranslation();

  const [openModal, setOpenModal] = useState(false);
  const [level, setLevel] = useState("")
  const [hour, setHour] = useState(1)
  const [minute, setMinute] = useState(0)

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    level: "",
    duration: 0,
    status: 0,
    explanation: "",
    objective: "",
    thumbnailUrl: "",
  });

  const navigate = useNavigate();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (!file) return;

    setSelectedFile(file);
    setFormData((prevState) => ({
      ...prevState,
      thumbnailUrl: file.name,
    }));
  };

  const hourCallback = (e: number) => {
    setHour(e);
  }

  const minuteCallback = (e: number) => {
    setMinute(e);
  }

  useEffect(() => {
    setFormData((prevState) => ({
      ...prevState,
      duration: hour * 60 + minute,
    }));
  }, [hour, minute]);

  const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {
    Create(0);
  }

  const handleSave = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (isUpdate) Edit(formData.status);
    else Create(formData.status);
  }

  const trainingLevels = [
    {
      id: "Asas",
      name: "Asas",
    },
    {
      id: "Sederhana",
      name: "Sederhana",
    },
    {
      id: "Mahir",
      name: "Mahir",
    },
  ]

  const trainingStatuses = [
    {
      id: 1,
      name: "Publish",
    },
    {
      id: 2,
      name: "Hidden",
    },
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const {name, value} = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({...prev, [name]: ""}));
  };

  const { upload: uploadFile, isLoading: isUploading } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      console.log("uploadFile",data)
      setSelectedFile(null);
      handleNext(data?.data?.data?.trainingId, "pelajaran");
    },
  });

  const {mutate: create, isLoading: isLoadingCreate} = useCustomMutation();
  const Create = (status: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    create(
      {
        url: `${API_URL}/society/admin/training/courses`,
        method: "post",
        values: {
          title: formData.title,
          description: formData.description,
          status: status,
          difficultyLevel: formData.level,
          startDate: formattedDate,
          endDate: formattedDate,
          maxParticipants: 50,
          passingCriteria: 70,
          duration: formData.duration,
          explanation: formData.explanation,
          objective: formData.objective,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            console.log("Create", data?.data?.data);
            setOpenModal(false)
            // Handle file upload
            if (selectedFile) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_POSTER,
                  trainingId: data?.data?.data
                },
                file: selectedFile,
              });
            }
            else{
              handleNext(data?.data?.data, "pelajaran");
            }
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {mutate: edit, isLoading: isLoadingEdit} = useCustomMutation();
  const Edit = (status: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    edit(
      {
        url: `${API_URL}/society/admin/training/courses`,
        method: "put",
        values: {
          id: courseId,
          title: formData.title,
          description: formData.description,
          difficultyLevel: formData.level,
          status: status,
          startDate: formattedDate,
          endDate: formattedDate,
          maxParticipants: 50,
          passingCriteria: 70,
          duration: formData.duration,
          explanation: formData.explanation,
          objective: formData.objective,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            // Handle file upload
            if (selectedFile) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_POSTER,
                  trainingId: data?.data?.data
                },
                file: selectedFile,
              });
            }else{
              handleNext(courseId, "pelajaran");
            }
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {data: trainingData, isLoading: isTrainingLoading} = useCustom({
    url: `${API_URL}/society/admin/training/courses/${courseId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: isUpdate && courseId!=0,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingDetails = trainingData?.data?.data || {};
  console.log("trainingCourse", trainingData, isUpdate)

  const {data: trainingDocData, isLoading: isTrainingDocLoading} = useCustom({
    url: `${API_URL}/society/document/documentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        trainingId: courseId,
        type: DocumentUploadType.TRAINING_POSTER
      },
    },
    queryOptions: {
      enabled: isUpdate && courseId!=0,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingDoc = trainingDocData?.data?.data || {};
  console.log("trainingDoc", trainingDocData, isUpdate)

  useEffect(() => {
    if(isUpdate && Object.keys(trainingDetails).length > 0){
      const temp = {
        title: trainingDetails.title || "",
        description: trainingDetails.description || "",
        level: trainingDetails.difficultyLevel || "",
        duration: trainingDetails.duration,
        status: trainingDetails.status || "",
        explanation: trainingDetails.explanation || "",
        objective: trainingDetails.objective || "",
        thumbnailUrl: trainingDoc.url || "",
      }
      setFormData(temp);
    }
  }, [trainingDetails]);

  return (<>
    <Box
      sx={{
        width: "85%",
        borderRadius: 2.5,
        backgroundColor: "#fff",
        //display: "inline",
        px: 2,
        py: 2,
        mb: 1,
      }}
    >
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Nama Latihan
        </Typography>
        <Grid container spacing={2} sx={{mt: 1}}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingName")} <span style={{color: "red"}}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              name="title"
              value={formData.title}
              error={!!formErrors.title}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingDescription")} <span style={{color: "red"}}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              multiline
              rows={4}
              name="description"
              value={formData.description}
              error={!!formErrors.description}
              onChange={handleInputChange}
            />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Kategori Latihan
        </Typography>
        <Grid container spacing={2} sx={{mt: 1}}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingLevel")} <span style={{color: "red"}}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.level}
            >
              <Select
                value={formData.level}
                displayEmpty
                required
                name="level"
                size={"small"}
                //disabled={isLoadingAddress}
                onChange={(e) => {
                  setLevel(e.target.value);
                  setFormData((prevState) => ({
                    ...prevState,
                    level: e.target.value,
                  }));
                  setFormErrors((prev) => ({
                    ...prev,
                    level: "",
                  }));
                }}
                variant={'outlined'}>
                <MenuItem value="" disabled>
                  {t("pleaseSelect")}
                </MenuItem>
                {trainingLevels
                  .map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.level && (
                <FormHelperText>{formErrors.level}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <DurationComponent setHour={setHour} setMinute={setMinute}
                             labelStyle={labelStyle} hour={hour} minute={minute}/>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingStatus")} <span style={{color: "red"}}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.status}
            >
              <Select
                value={formData.status}
                displayEmpty
                required
                name="status"
                size={"small"}
                variant={"outlined"}
                //disabled={isLoadingAddress}
                onChange={(e) => {
                  // @ts-ignore
                  setFormData((prevState) => ({
                    ...prevState,
                    status: e.target.value,
                  }));
                  setFormErrors((prev) => ({
                    ...prev,
                    status: "",
                  }));
                }}
              >
                <MenuItem value="" disabled>
                  {t("pleaseSelect")}
                </MenuItem>
                {trainingStatuses
                  .map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.status && (
                <FormHelperText>{formErrors.status}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Keterangan Latihan & Objektif
        </Typography>
        <Grid container spacing={2} sx={{mt: 1}}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingExplanation")} <span style={{color: "red"}}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              multiline
              rows={4}
              name="explanation"
              value={formData.explanation}
              error={!!formErrors.explanation}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingObjective")} <span style={{color: "red"}}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              multiline
              rows={4}
              name="objective"
              value={formData.objective}
              error={!!formErrors.objective}
              onChange={handleInputChange}
            />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Poster Banner
        </Typography>
        <Grid container spacing={2} sx={{mt: 1}}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingPoster")} <span style={{color: "red"}}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <Box
              sx={{
                border: "2px solid #DADADA",
                borderRadius: "8px",
                p: 2,
                gap: 2,
                textAlign: "center",
                cursor: "pointer",
                height: "200px",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
              }}
              onClick={() => {
                const element = document.getElementById("thumbnailBanner");
                if (element) {
                  element.click();
                }
              }}
            >
              {selectedFile || uploadedFiles?.length ? (
                <Typography sx={{color: "#147C7C", mb: 1}}>
                  {selectedFile
                    ? selectedFile.name
                    : uploadedFiles[0]?.name}
                </Typography>
              ) : (
                <>
                  <Box
                    sx={{
                      width: 50,
                      aspectRatio: "1/1",
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                      textAlign: "center",
                      borderRadius: 20,
                      mb: 2,
                      // p: 5,
                      bgColor: "#F2F4F7",
                    }}
                  >
                    <img
                      width={30}
                      src={"/uploadFileIcon.svg"}
                      alt={"view"}
                    />
                  </Box>

                  <Typography
                    sx={{
                      color: "var(--primary-color)",
                      fontWeight: "500",
                      fontSize: "14px",
                    }}
                  >
                    {t("muatNaik")}
                  </Typography>
                  <Typography
                    sx={{
                      color: "#667085",
                      fontWeight: "400",
                      fontSize: "12px",
                    }}
                  >
                    {`SVG, PNG, JPG or GIF (max. 800x400px)`}
                  </Typography>
                </>
              )}
              <input
                id="thumbnailBanner"
                type="file"
                hidden
                onChange={handleFileChange}
                accept=".svg,.png,.jpg,.jpeg,.gif"
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline
          sx={{
            bgcolor: "white",
            "&:hover": {bgcolor: "white"},
            width: "auto",
          }}
          onClick={handleSaveDraft}
        >
          {t("save")}
        </ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{
            width: "auto",
          }}
          onClick={() => setOpenModal(true)}
          //disabled={true}
        >
          {t("next")}
        </ButtonPrimary>
      </Grid>
    </Box>
    <ConfirmationDialog text={"Adakah anda pasti untuk mencipta latihan ini"} handleSave={handleSave}
                        labelStyle={labelStyle} openModal={openModal} setOpenModal={setOpenModal}/>
  </>);
}

export default CreateStepOne;
