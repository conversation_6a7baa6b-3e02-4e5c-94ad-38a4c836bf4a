import React from "react";
import {
  Stepper,
  Step,
  StepLabel,
  useMediaQuery,
  Theme,
  StepConnector,
  Box,
  Typography,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import CheckIcon from "@mui/icons-material/Check";

interface StepperPegawaiProps {
  jenisPegawai: string;
  activeStep: number;
  handleClick?: (step: number) => void;
}

export const StepperPegawai: React.FC<StepperPegawaiProps> = ({
  jenisPegawai,
  activeStep,
  handleClick,
}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const steps =
    i18n?.language === "en"
      ? [`${jenisPegawai} Official Information`, "payment"]
      : [`Maklumat Pegawai ${jenisPegawai}`, "payment"];

  const handleStepClick = (step: number) => {
    handleClick?.(step);

    const routes = [
      `/pertubuhan/society/${id}/senarai/rayuan/pegawai/${
        jenisPegawai == "Awam" ? "create-awam" : "create-harta"
      }`,
      `/pertubuhan/society/${id}/senarai/rayuan/pegawai/${
        jenisPegawai == "Awam" ? "create-awam" : "create-harta"
      }/bayaran`,
    ];
    if (routes[step]) {
      navigate(routes[step]);
    }
  };

  return (
    <Box
      sx={{
        mt: 3,
        padding: 3,
        backgroundColor: "white",
        borderRadius: "15px",
        maxHeight: {
          lg: "700px",
          md: "800px",
          xs: "800px",
        },
        minWidth: {
          lg: "25%",
        },
        maxWidth: {
          xs: "70px",
          md: "300px",
        },
      }}
    >
      <Typography
        sx={{
          mb: 8,
          fontSize: "16px",
          color: "var(--primary-color)",
          fontWeight: "500 !important",
        }}
      >
        Langkah Daftar Pegawai {jenisPegawai}
      </Typography>
      <Stepper
        sx={{ cursor: "pointer" }}
        activeStep={activeStep}
        orientation="vertical"
        connector={null}
      >
        {steps.map((stepKey, index) => (
          <Step key={stepKey} onClick={() => handleStepClick(index)}>
            <StepLabel
              icon={
                <Box
                  sx={{
                    width: 20,
                    height: 20,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: "4px",
                    border: `2px solid ${
                      index < activeStep
                        ? "var(--primary-color)"
                        : index === activeStep
                        ? "var(--primary-color)"
                        : "#DADADA"
                    }`,
                    backgroundColor:
                      index < activeStep
                        ? "var(--primary-color)"
                        : "transparent",
                  }}
                />
              }
            >
              <Typography
                sx={{
                  fontWeight: "500 !important",
                  color:
                    index < activeStep || index === activeStep
                      ? "var(--primary-color)"
                      : "#DADADA",
                }}
              >
                {t(stepKey)}
              </Typography>
            </StepLabel>
          </Step>
        ))}
      </Stepper>
    </Box>
  );
};
