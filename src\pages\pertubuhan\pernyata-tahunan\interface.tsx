import { OrganisationPositionLabel } from "@/helpers";

export type Bank = {
  id: number;
  societyNo: string;
  branchNo: string;
  statementId: number;
  bankName: string;
  accountNo: string;
  applicationStatusCode: number;
  createdBy: string;
  createdDate: [];
  modifiedBy: string;
  modifiedDate: [];
};

export type BankType = {
  bankCode: string;
  bankName: string;
  id: number;
  status: number;
};

export type Meeting = {
  createdBy: number;
  createdDate: string;
  modifiedBy: number;
  modifiedDate: string;
  id: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  statementId: number;
  meetingType: string;
  meetingPurpose: string;
  meetingPlace: string;
  meetingMethod: "8" | "9" | "10";
  platformType: string;
  meetingDate: string;
  meetingTimeTo: string;
  meetingTime: string;
  meetingAddress: string;
  state: string;
  district: string;
  city: string;
  postcode: string;
  totalAttendees: number;
  openingRemarks: string;
  meetingContent: string;
  mattersDiscussed: string;
  otherMatters: string;
  closing: string;
  providedBy: string;
  confirmBy: string;
  meetingMinute: string;
  status: string;
  meetingMemberAttendances: MeetingAttendee[];
  GISInformation: string;
};

export type AddressList = {
  code: string;
  id: number;
  lovel: number;
  name: string;
  pid: number;
  shortCode: string;
  status: number;
};

export type MeetingAttendee = {
  createdBy: number;
  createdDate: string;
  modifiedBy: number;
  modifiedDate: string;
  id: number;
  meetingId: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  meetingDate: [];
  icNo: number;
  present: number;
  name: string;
  position: string;
  status: number;
};

export type Society = {
  categoryCodeJppm: string;
  subCategoryCode: string;
  committeeTaskEnabled: boolean;
  societyNo: number;
  societyName: string;
  approvedDate?: string | undefined;
};

export type Trustee = {
  id: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  titleCode: string;
  name: string;
  gender: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  occupationCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  subDistrictCode: string;
  city: string;
  postalCode: string;
  email: string;
  officePhoneNumber: string;
  homePhoneNumber: string;
  mobilePhoneNumber: string;
  appointmentDate: number[];
  status: number;
  visaExpirationDate: string; // ISO 8601 format
  permitExpirationDate: string; // ISO 8601 format
  visaNo: string;
  permitNo: string;
  branchTrustee: string;
};

export type PublicOfficer = {
  id: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  titleCode: string;
  name: string;
  gender: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  occupationCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  subDistrictCode: string;
  city: string;
  postalCode: string;
  email: string;
  officePhoneNumber: string;
  homePhoneNumber: string;
  mobilePhoneNumber: string;
  appointmentDate: number[];
  status: number;
  applicationStatusCode: string;
  branchTrustee: string;
};

export type PropertyOfficer = {
  id: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  titleCode: string;
  name: string;
  gender: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  occupationCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  subDistrictCode: string;
  city: string;
  postalCode: string;
  email: string;
  officePhoneNumber: string;
  homePhoneNumber: string;
  mobilePhoneNumber: string;
  appointmentDate: number[];
  status: number;
  applicationStatusCode: string;
  branchTrustee: string;
  societyCommitteeId: number;
  propertyOfficerApplicationId: number;
};

export type Ajk = {
  id: number;
  jobCode: string;
  societyId: number;
  societyNo: string;
  titleCode: string;
  committeeName: string;
  committeeStateCode: string;
  name: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  designationCode: keyof typeof OrganisationPositionLabel;
  otherDesignationCode: string;
  employerAddressStatus: string;
  employerName: string;
  employerAddress: string;
  employerPostcode: string;
  employerCountryCode: string;
  employerStateCode: string;
  employerCity: string;
  employerDistrict: string;
  residentialAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: string;
  residentialCountryCode: string;
  residentialStateCode: string;
  residentialDistrictCode: string;
  residentialCity: string;
  email: string;
  telephoneNumber: string;
  phoneNumber: string;
  noTelP: number;
  status: string;
  applicationStatusCode: string;
  pegHarta: string;
  tarikhTukarSu: [];
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: boolean;
  benarAjk: boolean;
  createdBy: string;
  createdDate: [];
  modifiedBy: string;
  modifiedDate: [];
};

export type AjkNonCiizen = {
  id: number;
  name: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  gender: string;
  applicantCountryCode: string;
  visaExpirationDate: string; // ISO 8601 format
  permitExpirationDate: string; // ISO 8601 format
  visaNo: string;
  permitNo: string;
  designationCode: keyof typeof OrganisationPositionLabel;
  activeCommitteeId: string;
  visaPermitNo: string;
  tujuanDMalaysia: string;
  tempohDMalaysia: string;
  durationType: string;
  summary: string;
  societyName: string;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  applicationStatusCode: string;
  status: number;
  ro: string; // Regional Office
  pembaharuanSu: string; // Renewal Required
  pemCaw: string; // Yes or No
  otherDesignationCode: string;
  stayDurationDigit: number;
  stayDurationUnit: string;
  transferDate: string; // ISO 8601 format
  noteRo: string; // Note for Regional Office
};

export type Auditor = {
  id: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  statementId: number;
  auditorType: "L" | "D";
  titleCode: string;
  name: string;
  licenseNo: string;
  companyName: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  employmentCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  smallDistrictCode: string;
  city: string;
  postcode: string;
  email: string;
  telephoneNo: string;
  phoneNo: string;
  appointmentDate: number[];
  createdBy: string;
  createdDate: string[];
  modifiedBy: string;
  modifiedDate: number[];
  status: string;
  deleteStatus: string;
  pemCaw: string;
};

export interface Sumbangan {
  id: number;
  statementId: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  contributionCode: string;
  contribution: string;
  countryOrigin: string;
  value: number;
  applicationStatusCode: string;
  createdBy: string;
  createdDate: number[];
  modifiedBy: string;
  modifiedDate: number[];
}

export interface AliranTugas {
  id: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  societyCommitteeId: number;
  identificationNo: string;
  designationCode: keyof typeof OrganisationPositionLabel;
  statementId: number;
  name: string;
  status: string;
  taskActivateDate: [] | null;
  taskDeactivateDate: [] | null;
  createdBy: number;
  createdDate: [];
  modifiedBy: number;
  modifiedDate: [];
}

export interface Document {
  createdBy: number;
  createdDate: string;
  modifiedBy: number;
  modifiedDate: string;
  id: number;
  type: number;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  meetingId: number;
  societyCommitteeId: number;
  societyNonCitizenCommitteeId: number;
  appealId: number;
  statementId: number;
  amendmentId: number;
  liquidationId: number;
  feedbackId: number;
  icNo: number;
  name: string;
  note: string;
  url: string;
  doc: string;
  status: number;
}
