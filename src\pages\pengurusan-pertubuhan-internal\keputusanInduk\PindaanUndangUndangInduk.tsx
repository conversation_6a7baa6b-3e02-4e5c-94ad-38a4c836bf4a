import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { formatArrayDate, getLocalStorage } from "../../../helpers/utils";
import { MALAYSIA } from "../../../helpers/enums";
import {
  Box,
  Grid,
  Typography,
  IconButton,
  TextField,
  Select,
  MenuItem,
  Button,
  debounce,
} from "@mui/material";
import { EditIcon } from "../../../components/icons";
import DataTable, { IColumn } from "../../../components/datatable";
import { CrudFilter } from "@refinedev/core";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../api";
import { useCallback, useEffect, useState } from "react";
import useQuery from "../../../helpers/hooks/useQuery";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};
interface PindaanUndangUndangIndukTabProps {
  number: number;
}

const PindaanUndangUndangIndukTab: React.FC<
  PindaanUndangUndangIndukTabProps
> = ({ number }) => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language;
  const addressList = getLocalStorage("address_list", null);
  const malaysiaList =
    addressList.filter((address: any) => address.pid === MALAYSIA) ?? [];
  const getStateName = (id: string) =>
    malaysiaList.find((state: any) => state.id === id)?.name || "-";

  const checkIsMoreThan60Days = (
    dateMeeting?: string,
    datePayment?: string
  ): boolean => {
    if (!dateMeeting || !datePayment) {
      return false;
    }

    const [dayA, monthA, yearA] = dateMeeting.split("-").map(Number);
    const [dayB, monthB, yearB] = datePayment.split("-").map(Number);

    const parsedDateA = new Date(yearA, monthA - 1, dayA);
    const parsedDateB = new Date(yearB, monthB - 1, dayB);

    // Check if date valid
    if (isNaN(parsedDateA.getTime()) || isNaN(parsedDateB.getTime())) {
      return false;
    }

    const diffInMs = parsedDateB.getTime() - parsedDateA.getTime();
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

    return diffInDays > 60;
  };

  const columns: IColumn[] = [
    {
      field: "societyNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.societyNo ? row?.societyNo : "-";
      },
    },
    {
      field: "societyName",
      align: "center",
      headerName: t("pertubuhan"),
      flex: 1,
    },
    {
      field: "roName",
      headerName: "RO",
      flex: 0.5,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.ro ? row?.ro : "-";
      },
    },
    {
      field: "createdDate",
      align: "center",
      headerName: t("tarikhAlir"),
      flex: 1,
    },
    {
      field: "meetingDate",
      headerName: t("tarikhMesyuarat"),
      flex: 0.5,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.meetingDate ? row?.meetingDate : "-";
      },
    },
    {
      field: "paymentDate",
      headerName: t("tarikhBayar"),
      flex: 0.5,
      align: "center",
      renderCell: ({ row }: any) => {
        const isMoreThan60Days = checkIsMoreThan60Days(
          row?.meetingDate,
          row?.paymentDate
        );
        return (
          <Box sx={{ display: "flex", flexDirection: "column" }} gap={1}>
            <Box>{row?.paymentDate ? row?.paymentDate : "-"}</Box>
            {isMoreThan60Days ? (
              <Box sx={{ fontSize: "12px", color: "#ED1111" }}>
                {t("Exceededtheperiod")}
              </Box>
            ) : null}
          </Box>
        );
      },
    },
    {
      field: "amendmentType",
      headerName: t("amendmentType"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.amendmentType ? row?.amendmentType : "-";
      },
    },
    {
      field: "stateCode",
      headerName: "Negeri",
      align: "center",
      flex: 1,
      renderCell: ({ row }: any) => getStateName(row.stateCode),
    },
    {
      field: "actions",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }: any) => {
        const societyId = btoa(row.societyId);
        const amendmentId = row.id;
        return (
          <IconButton
            onClick={() =>
              navigate(
                `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/pindaan-perlembagaan/${amendmentId}/${societyId}`
              )
            }
            sx={{ minHeight: "3rem", minWidth: "3rem" }}
          >
            <EditIcon color="var(--primary-color)" />
          </IconButton>
        );
      },
    },
  ];

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1) ?? [];
  const subCategories = categories.filter((cat: any) => cat.level === 2) ?? [];

  const mainCategoriesOptions = mainCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));
  const subCategoriesOptions = subCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const [formData, setFormData] = useState({
    organizationName: "",
    subOrganizationCategory: "",
    organizationCategory: "",
  });

  const request = debounce((value) => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "categoryCode",
          value: formData.organizationCategory || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: formData.subOrganizationCategory || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: value,
          operator: "eq",
        },
      ],
    });
  }, 800);

  const debouceRequest = useCallback((value: any) => request(value), []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      organizationName: value,
    }));
    debouceRequest(value);
    setFormErrors((prev) => ({ ...prev, organizationName: "" }));
  };

  const {
    data: searchResult,
    isLoading: isSearchLoading,
    refetch: fetchPendingResult,
  } = useQuery({
    url: `society/roDecision/getAllPending/amendment`,
    autoFetch: false,
  });

  let datAmendment = [];
  if (!isSearchLoading) {
    datAmendment = searchResult?.data?.data?.data || 0;
  }

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
    ];
    setPage(newPage);
    fetchPendingResult({ filters });
  };

  useEffect(() => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "categoryCode",
          value: formData.organizationCategory || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: formData.subOrganizationCategory || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: formData.organizationName || "",
          operator: "eq",
        },
      ],
    });
  }, [formData.organizationCategory, formData.subOrganizationCategory]);

  const totalList = searchResult?.data?.data?.total ?? 0;

  return (
    <>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("amendmentList")}
          </Typography>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("organization_category")}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <Select
                fullWidth
                displayEmpty
                defaultValue=""
                value={formData.organizationCategory}
                renderValue={(selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: "#aaa" }}>{t("pleaseSelect")}</span>
                    );
                  }
                  const selectedOption = mainCategoriesOptions.find(
                    (item: any) => item.value === selected
                  );
                  return selectedOption ? selectedOption.label : selected;
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      "& .MuiMenuItem-root": {
                        fontSize: "14px",
                      },
                    },
                  },
                }}
                sx={{ height: "37px", fontSize: "14px" }}
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    organizationCategory: e.target.value,
                  }));
                  setFormData((prevState) => ({
                    ...prevState,
                    subOrganizationCategory: "",
                  }));
                  // setFormErrors((prev) => ({
                  //   ...prev,
                  //   organizationCategory: "",
                  // }));
                }}
              >
                {mainCategoriesOptions.map((item: any) => (
                  <MenuItem key={item.value} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("organizationSubCategory2")}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <Select
                fullWidth
                displayEmpty
                disabled={!formData.organizationCategory}
                value={formData.subOrganizationCategory}
                defaultValue=""
                renderValue={(selected) => {
                  if (!selected) {
                    return (
                      <span style={{ color: "#aaa" }}>{t("pleaseSelect")}</span>
                    );
                  }
                  const selectedOption = subCategoriesOptions.find(
                    (item: any) => item.value === selected
                  );
                  return selectedOption ? selectedOption.label : selected;
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      "& .MuiMenuItem-root": {
                        fontSize: "14px",
                      },
                    },
                  },
                }}
                sx={{ height: "37px", fontSize: "14px" }}
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    subOrganizationCategory: e.target.value,
                  }));
                  // setFormErrors((prev) => ({
                  //   ...prev,
                  //   organizationCategory: "",
                  // }));
                }}
              >
                {subCategoriesOptions.map((item: any) => (
                  <MenuItem key={item.value} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("namaPertubuhan")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <TextField
                size="small"
                fullWidth
                required
                name="organizationName"
                value={formData.organizationName}
                onChange={handleInputChange}
                error={!!formErrors.organizationName}
                helperText={formErrors.organizationName}
              />
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: "13px",
            padding: "15px 0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            marginBottom: 1,
            backgroundColor: "var(--primary-color)",
          }}
        >
          <Typography
            fontWeight="500 !important"
            fontSize="36px"
            color="#FFF"
            textAlign="center"
            lineHeight="30px"
            sx={{
              "& span": {
                fontSize: "20px",
              },
            }}
          >
            {number}
            <br />
            <span>{t("pindaanApproverTitle")}</span>
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="37px"
          >
            {t("amendmentList")}
          </Typography>

          <DataTable
            columns={columns}
            rows={datAmendment}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            onPageChange={handleChangePage}
          />
        </Box>
      </Box>
    </>
  );
};

export default PindaanUndangUndangIndukTab;
