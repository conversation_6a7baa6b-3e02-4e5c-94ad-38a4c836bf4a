import React, {
  createContext,
  useContext,
  PropsWithChildren,
  useState,
  SetStateAction,
  Dispatch,
} from "react";
import { useLocation, useParams } from "react-router-dom";
import {
  AddressList,
  Ajk,
  AjkNonCiizen,
  AliranTugas,
  Auditor,
  Document,
  Meeting,
  PropertyOfficer,
  PublicOfficer,
  Society,
  Trustee,
} from "../../CawanganPenyataTahunan/interface";
import useQuery from "@/helpers/hooks/useQuery";
import { ApplicationStatusList } from "@/helpers";
import {
  setAliranTugasAccessRedux,
  setUserPermissionRedux,
} from "@/redux/userReducer";
import { useDispatch, useSelector } from "react-redux";

interface jawatankuasaContextProps {
  society: Society | undefined;
  ajkList: Ajk[];
  activeMembers: any;
  members: any;
  nonCitizenAjkList: AjkNonCiizen[];
  addressList: AddressList[];
  ajkCount: number;
  trusteeList: Trustee[];
  trustee: Trustee | undefined;
  auditorList: Auditor[];
  auditor: Auditor | undefined;
  module: string;
  isAliranModuleAccess: boolean;
  publicOfficers: PublicOfficer[];
  publicOfficer: PublicOfficer | undefined;

  propertyOfficers: PropertyOfficer[];

  aliranTugas: AliranTugas[];

  documents: Document[];

  isAliranModuleStatus: boolean;

  savedMeetingDate: string;

  savedMeetingDetail: Meeting | undefined;

  isSavedStatus: boolean; 
  isFetchActiveMembersLoading:boolean;

  fetchSociety: () => void; // Function to manually trigger ajkList query

  fetchAjkList: () => void; // Function to manually trigger ajkList query
  setAjkList: Dispatch<SetStateAction<Ajk[]>>;
  setModule: Dispatch<SetStateAction<string>>;

  fetchMembers: () => void;
  fetchActiveMembers: () => void;

  fetchNonCitizenAjkList: () => void; // Function to manually trigger nonCitizenAjkList query
  fetchAddressList: () => void; // Function to manually trigger addressList query
  fetchAjkCount: () => void; // Function to manually trigger ajkCount query

  fetchTrusteeList: () => void; // Function to manually trigger trustee query
  fetchTrustee: () => void; // Function to manually trigger trustee query

  fetchAuditorList: () => void; // Function to manually trigger auditorList query
  fetchAuditor: () => void;

  fetchPublicOfficers: () => void; // Function to manually trigger auditorList query
  fetchPublicOfficer: () => void;

  fetchPropertyOfficers: () => void; // Function to manually trigger auditorList query

  fetchAliranTugas: (module: string) => void;
  fetchDocuments: () => void; // Function to manually trigger auditorList query
  fetchAliranTugasAccess: (module: string) => void;
  fetchAliranTugasStatus: (module: string) => void;

  setSavedMeetingDate: (date: string) => void;
  setSavedMeetingDetail: (meeting: Meeting | undefined) => void;
  setIsSavedStatus: (status: boolean) => void;

  //noncitizen AJK use
  meetingId: number | null | undefined;
  setMeetingId: (id: number | null | undefined) => void;

  documentIds: string[] | null;
  setDocumentIds: (ids: string[] | null) => void;

  appointmentDateG: string | null;
  setAppointmentDateG: (date: string | null) => void;
}

const jawatankuasaContext = createContext<jawatankuasaContextProps | undefined>(
  undefined
);

export const usejawatankuasaContext = (): jawatankuasaContextProps => {
  const context = useContext(jawatankuasaContext);

  if (!context) {
    throw new Error(
      "usejawatankuasaContext must be used within a JawatankuasaProvider"
    );
  }
  return context;
};

const JawatankuasaProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { id: societyId } = useParams();
  const location = useLocation();
  const auditorId = location.state?.auditorId;
  const trusteeId = location.state?.trusteeId;
  const officerId = location.state?.officerId;

  const dispatch = useDispatch();

  // State initialization
  const [society, setSociety] = useState<Society>();

  const [ajkList, setAjkList] = useState<Ajk[]>([]);
  const [activeMembers, setActiveMembers] = useState<Ajk[]>([]);
  const [members, setMembers] = useState<any[]>([]);

  const [nonCitizenAjkList, setNonCitizenAjkList] = useState<AjkNonCiizen[]>(
    []
  );
  const [addressList, setAddressList] = useState<AddressList[]>([]);
  const [ajkCount, setAjkCount] = useState<number>(0);
  const [trusteeList, setTrustees] = useState<Trustee[]>([]);
  const [trustee, setTrustee] = useState<Trustee>();

  const [auditorList, setAuditors] = useState<Auditor[]>([]);
  const [auditor, setAuditor] = useState<Auditor>();

  const [publicOfficers, setPublicOfficers] = useState<PublicOfficer[]>([]);
  const [publicOfficer, setPublicOfficer] = useState<PublicOfficer>();

  const [propertyOfficers, setPropertyOfficers] = useState<PropertyOfficer[]>(
    []
  );

  const [aliranTugas, setAliranTugas] = useState<AliranTugas[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [module, setModule] = useState<string>(""); // Set default to empty string or any initial value
  const [isAliranModuleAccess, setIsAliranModuleAccess] =
    useState<boolean>(false); // Set default to empty string or any initial value
  const [isAliranModuleStatus, setIsAliranModuleStatus] =
    useState<boolean>(false); // Set default to empty string or any initial value
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const [savedMeetingDate, setSavedMeetingDate] = useState("");
  const [savedMeetingDetail, setSavedMeetingDetail] = useState<Meeting>();
  const [isSavedStatus, setIsSavedStatus] = useState(false);

  //noncitizen AJK use
  const [meetingId, setMeetingId] = useState<number | null | undefined>(null);
  const [documentIds, setDocumentIds] = useState<string[] | null>(null);
  const [appointmentDateG, setAppointmentDateG] = useState<string | null>(null);

  // Initialize useQuery hooks with enabled=false
  const { refetch: fetchAjkList } = useQuery({
    url: `society/branch/committee/listAjk`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "status", operator: "eq", value: "001" },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAjkList(data?.data?.data?.data || []);
    },
  });

  // Initialize useQuery hooks with enabled=false
  const { refetch: fetchMembers } = useQuery({
    url: `society/committee/listMember`,
    autoFetch: false, // Disable auto-fetch on mount
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      setMembers(data?.data?.data || []);
    },
  });

  // Initialize useQuery hooks with enabled=false
  const { refetch: fetchActiveMembers, isLoading:isFetchActiveMembersLoading} = useQuery({
    url: `society/committee/listActive`,
    autoFetch: false, // Disable auto-fetch on mount
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      setActiveMembers(data?.data?.data || []);
    },
  });

  const { refetch: fetchNonCitizenAjkList } = useQuery({
    url: `society/nonCitizenCommittee/getAll`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setNonCitizenAjkList(data?.data?.data?.data || []);
    },
  });

  const { refetch: fetchAddressList } = useQuery({
    url: `society/admin/address/list`,
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAddressList(data?.data?.data || []);
    },
  });

  const { refetch: fetchAjkCount } = useQuery({
    url: `society/committee/countAjk`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAjkCount(data?.data?.data.count || 0);
    },
  });

  const { refetch: fetchTrusteeList } = useQuery({
    url: `society/trustee/getAll`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setTrustees(data?.data?.data?.data || []);
    },
  });

  const { refetch: fetchTrustee } = useQuery({
    url: `society/trustee/${trusteeId}`,
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setTrustee(data?.data?.data || undefined);
    },
  });

  const { refetch: fetchAuditorList } = useQuery({
    url: `society/statement/auditor/list`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAuditors(data?.data?.data?.data || []);
    },
  });

  const { refetch: fetchAuditor } = useQuery({
    url: `society/statement/auditor/${auditorId}/get`,
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAuditor(data?.data?.data || undefined);
    },
  });

  const { refetch: fetchPublicOfficers } = useQuery({
    url: `society/public_officer/getAll/`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setPublicOfficers(data?.data?.data?.data || undefined);
    },
  });

  const { refetch: fetchPublicOfficer } = useQuery({
    url: `society/public_officer/${officerId}`,
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setPublicOfficer(data?.data?.data || undefined);
    },
  });

  const { refetch: fetchPropertyOfficers } = useQuery({
    url: `society/property_officer/getAllActive/`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setPropertyOfficers(data?.data?.data || undefined);
    },
  });

  const { refetch: fetchAliranTugas } = useQuery({
    url: `society/committee-task/list`,
    filters: [
      { field: "module", operator: "eq", value: module },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAliranTugas(data?.data?.data || undefined);
    },
  });

  const fetchAliranTugasTriggerRefetch = () => {
    fetchAliranTugas();
  };

  // Update the fetchAliranTugas to accept module as a parameter
  const fetchAliranTugasWithModule = (module: string) => {
    fetchAliranTugas({
      filters: [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: branchDataRedux.id },
        { field: "module", operator: "eq", value: module }, // Pass the dynamic module here
      ],
    });
  };

  const { refetch: fetchSociety } = useQuery({
    url: `society/${societyId}`,
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setSociety(data?.data?.data || null);
    },
  });

  const { refetch: fetchDocuments } = useQuery({
    url: `society/document/documentByParam`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setSociety(data?.data?.data || null);
    },
  });

  const { refetch: fetchAliranTugasAccess } = useQuery({
    url: `society/committee-task/me`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
      { field: "module", operator: "eq", value: "" },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      dispatch(setAliranTugasAccessRedux(data?.data?.data?.enabled || false));
      setIsAliranModuleAccess(data?.data?.data?.enabled || false);
    },
  });

  const fetchAliranTugasAccessWithModule = (module: string) => {
    fetchAliranTugasAccess({
      filters: [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: branchDataRedux.id },
        { field: "module", operator: "eq", value: module }, // Pass the dynamic module here
      ],
    });
  };

  const { refetch: fetchAliranTugasStatus } = useQuery({
    url: `society/${societyId}/committee_task/status`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
      { field: "module", operator: "eq", value: "PENGURUSAN_AJK" },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setIsAliranModuleStatus(data?.data?.data.enabled || false);
    },
  });

  const fetchAliranTugasStatusWithModule = (module: string) => {
    fetchAliranTugasStatus({
      filters: [
        { field: "module", operator: "eq", value: module }, // Pass the dynamic module here
      ],
    });
  };

  return (
    <jawatankuasaContext.Provider
      value={{
        documentIds,
        setDocumentIds,
        appointmentDateG,
        setAppointmentDateG,
        meetingId,
        setMeetingId,
        isSavedStatus,
        setIsSavedStatus,
        savedMeetingDetail,
        setSavedMeetingDetail,
        savedMeetingDate,
        setSavedMeetingDate,
        society,
        module,
        ajkList,
        isAliranModuleAccess,
        setAjkList,
        activeMembers,
        members,
        nonCitizenAjkList,
        addressList,
        ajkCount,
        trusteeList,
        trustee,
        auditorList,
        auditor,
        publicOfficers,
        publicOfficer,
        propertyOfficers,
        aliranTugas,
        documents,
        isAliranModuleStatus,
        isFetchActiveMembersLoading, 
        fetchSociety,
        fetchAjkList,
        fetchActiveMembers,
        fetchMembers,
        fetchNonCitizenAjkList,
        fetchAddressList,
        fetchAjkCount,
        fetchTrusteeList,
        fetchTrustee,
        fetchAuditorList,
        fetchAuditor,
        fetchPublicOfficers,
        fetchPublicOfficer,
        fetchPropertyOfficers,
        fetchAliranTugas: fetchAliranTugasWithModule,
        fetchDocuments,
        setModule,
        fetchAliranTugasAccess: fetchAliranTugasAccessWithModule,
        fetchAliranTugasStatus: fetchAliranTugasStatusWithModule,
      }}
    >
      {children}
    </jawatankuasaContext.Provider>
  );
};

export default JawatankuasaProvider;
