import { useEffect, useMemo } from "react";
import { useForm, FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  globalStyles,
  useQuery,
  getSectionByCode,
  useQueryFilterForm,
  SectionOptions,
  ApplicationStatusEnum,
} from "@/helpers";

import { Box, Typography, IconButton, Button } from "@mui/material";
import {
  ButtonPrimary,
  FormFieldRow,
  Label,
  DataTable,
  IColumn,
  TextFieldController,
  DatePickerController,
  SelectFieldController,
} from "@/components";

import { EditIcon } from "@/components/icons";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";

import {
  IApiPaginatedResponse,
  IApiResponse,
  ICancelledSociety,
} from "@/types";

const SenaraiPembatalan: React.FC = () => {
  const { t, i18n } = useTranslation();
  const classes = globalStyles();
  const navigate = useNavigate();

  const isMyLanguage = i18n.language === "my";

  const sectionOptions = useMemo(() => {
    return (
      SectionOptions.filter((section) => section.section === "13").map(
        (section) => ({
          label: section.description as string,
          value: section.code as string,
        })
      ) ?? []
    );
  }, []);

  const columns: IColumn<ICancelledSociety>[] = [
    {
      field: "",
      headerName: "No",
      flex: 1,
      align: "center",
      renderCell: ({ rowIndex }) => {
        const number = page * pageSize + rowIndex + 1 - pageSize;
        return <>{number}</>;
      },
    },
    {
      field: "cancelledDate",
      headerName: t("cancellationDate"),
      flex: 1,
      align: "center",
    },
    {
      field: "societyNo",
      headerName: t("ppmNumber"),
      flex: 1,
      align: "center",
    },
    {
      field: "societyName",
      headerName: "Pertubuhan",
      flex: 1,
      align: "center",
    },
    {
      field: "",
      headerName: t("status"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Typography
            className={classes.statusBadge}
            sx={{
              border:
                row.societyStatusCode === "008"
                  ? "1px solid var(--error)"
                  : "1px solid var(--primary-color)",
            }}
          >
            {ApplicationStatusEnum[row.societyStatusCode]}
          </Typography>
        );
      },
    },
    {
      field: "section",
      headerName: t("section"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) =>
        getSectionByCode(row.section)?.description ?? "-",
    },
    {
      field: "",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton
              onClick={() => {
                navigate(`senarai-pembatalan/${row.id}`);
              }}
            >
              <EditIcon color="#1DC1C1" />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const formMethods = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
      societyName: "",
      societyNo: "",
      section: "",
      cancelledDateFrom: "",
      cancelledDateTo: "",
    },
  });

  const { control, handleSubmit, watch, setValue } = formMethods;
  const { buildFilters } = useQueryFilterForm({
    formMethods,
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data: cancelledSocietyListRes,
    refetch: fetchCancelledSociety,
    isLoading: isLoadingCancelledSociety,
  } = useQuery<IApiPaginatedResponse<ICancelledSociety>>({
    url: "society/cancellation/getAllCancelledSociety",
    autoFetch: false,
  });

  const { data: countRes } = useQuery<IApiResponse<number>>({
    url: "society/cancellation/getAllCancelledSocietyCount",
  });

  const cancelledSocietyList = cancelledSocietyListRes?.data?.data?.data ?? [];
  const count = countRes?.data?.data ?? 0;

  const applyFilters = (pageSize: number, page: number) => {
    const filters = buildFilters(pageSize, page);

    fetchCancelledSociety({ filters });
  };

  const handleSearchSociety = () => {
    setValue("page", 1);
    applyFilters(pageSize, 1);
  };

  const handleChangePage = (newPage: number) => {
    setValue("page", newPage);
    applyFilters(pageSize, newPage);
  };

  const handleChangePageSize = (newPageSize: number) => {
    setValue("pageSize", newPageSize);
    setValue("page", 1);
    applyFilters(newPageSize, 1);
  };

  useEffect(() => {
    applyFilters(pageSize, 1);
  }, []);

  return (
    <>
      <form onSubmit={handleSubmit(handleSearchSociety)}>
        <Box className={classes.section} mb={2}>
          <Box className={classes.sectionBox} mb={2}>
            <Typography className="title" mb={1}>
              {isMyLanguage ? "Carian Pembatalan" : "Cancellation Search"}
            </Typography>

            <FormFieldRow
              label={<Label text={t("organizationName")} />}
              value={
                <TextFieldController control={control} name="societyName" />
              }
            />

            <FormFieldRow
              label={<Label text={t("ppmNumber")} />}
              value={<TextFieldController control={control} name="societyNo" />}
            />

            <FormFieldRow
              label={<Label text={t("section")} />}
              value={
                <SelectFieldController
                  control={control}
                  options={sectionOptions}
                  name="section"
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("cancellationDate")} />}
              value={
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                    color: "var(--text-grey)",
                  }}
                >
                  <DatePickerController
                    control={control}
                    name="cancelledDateFrom"
                    sx={{ maxWidth: "160px" }}
                  />
                  {isMyLanguage ? "Hingga" : "Until"}

                  <DatePickerController
                    control={control}
                    name="cancelledDateTo"
                    sx={{ maxWidth: "160px" }}
                  />
                </Box>
              }
            />
          </Box>

          <ButtonPrimary
            type="submit"
            sx={{ marginLeft: "auto" }}
            className={classes.btnSubmit}
          >
            {isMyLanguage ? "Cari Pertubuhan" : "Find an organization"}
          </ButtonPrimary>
        </Box>
      </form>

      <Box
        className={classes.section}
        sx={{
          background: "var(--primary-color) !important",
          display: "flex",
          justifyContent: "center",
        }}
        mb={2}
      >
        <Typography
          fontWeight="500 !important"
          fontSize="36px"
          color="#FFF"
          textAlign="center"
          lineHeight="30px"
          sx={{
            "& span": {
              fontSize: "20px",
            },
          }}
        >
          {count}
          <br />
          <span>
            {isMyLanguage
              ? "Jumlah Keseluruhan Pembatalan"
              : "Total Cancellation Amount"}
          </span>
        </Typography>
      </Box>

      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography className="title" mb={1}>
            {isMyLanguage ? "Senarai Pembatalan" : "Cancellation List"}
          </Typography>

          <Box sx={{ width: "80%", marginInline: "auto" }}>
            <DataTable
              columns={columns}
              rows={cancelledSocietyList}
              page={page}
              rowsPerPage={pageSize}
              totalCount={10}
              pagination={pageSize > 5}
              isLoading={isLoadingCancelledSociety}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
            />

            {!(pageSize > 5) && (
              <Button
                onClick={() => handleChangePageSize(10)}
                className={classes.btnOutline}
                sx={{
                  mt: 3,
                  marginInline: "auto",
                }}
              >
                {t("seeFullView")}
                <ArrowRightAltIcon sx={{ color: "#666666B2" }} />
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default SenaraiPembatalan;
