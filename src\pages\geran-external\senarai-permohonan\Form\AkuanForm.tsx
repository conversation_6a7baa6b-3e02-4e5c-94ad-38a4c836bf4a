import React from "react";
import { useTranslation } from "react-i18next";
import { useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, Typography, Checkbox } from "@mui/material";
import { FormFieldRow, Label, TextFieldController } from "@/components";

const Form = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const { control } = useFormContext();

  return (
    <>
      <Box className={classes.sectionBox} mb={2}>
        <Typography className="title" mb={2}>
          <PERSON><PERSON><PERSON> persetujuan <PERSON>
        </Typography>

        <FormFieldRow
          label={<Label text="Perbincangan AJK" required />}
          value={<TextFieldController control={control} name="test" />}
        />

        <FormFieldRow
          label={<Label text="Tarikh" required />}
          value={<TextFieldController control={control} name="test" />}
        />

        <FormFieldRow
          label={<Label text="Tempat" required />}
          value={<TextFieldController control={control} name="test" />}
        />
      </Box>

      <Box className={classes.sectionBox}>
        <Typography className="title" mb={2}>
          Akuan
        </Typography>

        <Typography className="label" mb={3}>
          Dengan ini saya mengaku bahawa maklumat pertubuhan yang diberikan
          dalam borang ini adalah benar, betul dan lengkap. Sekiranya saya
          didapati memberikan maklumat tidak benar atau palsu, saya boleh
          disabitkan kesalahan dibawah Seksyen 193 Kanun Keseksaan (Akta 574)
          dan boleh dikenakan hukuman penjara selama tempoh yang boleh sampai
          tiga (3) tahun dan boleh juga dikenakan denda
        </Typography>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "20px",
          }}
        >
          <Checkbox
            icon={
              <Box
                sx={{
                  width: "12px",
                  height: "12px",
                  border: "0.5px solid #848484",
                  background: "none",
                }}
              />
            }
            checkedIcon={
              <Box
                sx={{
                  width: "12px",
                  height: "12px",
                  border: "0.5px solid var(--primary-color)",
                  background: "var(--primary-color)",
                }}
              />
            }
            sx={{
              padding: 0,
            }}
          />

          <Label text="Akuan setuju terima." required />
        </Box>
      </Box>
    </>
  );
};

const AkuanForm = React.memo(Form);
export default AkuanForm;
