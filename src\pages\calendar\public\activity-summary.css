.content {
  max-width: 1280px;
  margin: 6rem auto 6.25rem auto;
}

.calendars-view {
  display: flex;
  box-shadow: 0 0.75rem 0.75rem 0 #EAE8E866;
  border-radius: 1.25rem;
  padding: 0.5rem 1rem 2.25rem 1rem;
  flex-direction: column;
  min-height: 6rem;
}

.all-activities-this-year-calendar {
  margin: 0 !important;
  border: 1px solid #dadada99;
  width: 100% !important;
}

.calendar-activities-this-month {
  margin: 0 !important;
  border: 1px solid #dadada99 !important;
  width: 100% !important;
  border-radius: 0.5rem !important;
  margin-bottom: 1.5rem !important;
}

.calendar-activities-this-month .MuiDayCalendar-header {
  justify-content: space-around;
}

.calendar-activities-this-month .MuiDayCalendar-header > span {
  font-size: 1rem !important;
  font-weight: 400;
}

.calendar-activities-this-month .MuiPickersSlideTransition-root {
  overflow-x: unset !important;
}

.calendar-activities-this-month-date {
  min-height: 3rem;
  min-width: 4rem;
  font-size: 1rem !important;
  font-weight: 200 !important;
  display: flex !important;
  align-items: flex-start;
}

.calendar-activities-this-month-date:hover {
  background-color: rgba(65, 195, 195, 0.5) !important;
}

@media screen and (min-width: 1280px) {
  .all-activities-this-year-calendar {
    width: 33.33% !important;
  }

  .calendar-activities-this-month {
    min-height: 50rem !important;
  }

  .calendar-activities-this-month .MuiDayCalendar-header {
    margin-bottom: 0.5rem;
  }

  .calendar-activities-this-month .MuiDayCalendar-header > span {
    font-size: 1.25rem !important;
  }

  .calendar-activities-this-month-days {
    justify-content: space-around;
    font-size: 1.25rem !important;
  }

  .calendar-activities-this-month-date {
    min-width: 6rem;
    min-height: 6rem;
    margin: 1rem !important;
    font-size: 1.25rem !important;
    align-items: center !important;
  }

  .calendars-view {
    min-height: 60rem;
  }
}
