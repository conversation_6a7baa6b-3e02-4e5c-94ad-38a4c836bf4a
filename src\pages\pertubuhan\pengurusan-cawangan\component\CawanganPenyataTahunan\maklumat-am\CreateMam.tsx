import React, { useState } from "react";
import {
  Box,
  Grid,
  <PERSON>pography,
  Button,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { EditIcon } from "@/components/icons";
import { TrashIcon } from "@/components/icons";
import Input from "@/components/input/Input";
import { ButtonPrimary } from "@/components/button";
import useQuery from "@/helpers/hooks/useQuery";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { Bank, Society } from "../interface";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import { ApplicationStatus } from "@/helpers";

export const CreateMam: React.FC = () => {
  type GeneralInfo = {
    categoryCodeJppm: string;
    subCategoryCode: string;
    phoneNumber: string;
    financialYearStart: number;
    regMemberCount: number;
    positionHolderNo: number;
    branchCount: number;
    federation: boolean;
  };

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [bankId, setBankId] = useState<number>(0);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const [society, setSociety] = useState<Society>(societyDataRedux);
  const [generalInfo, setGeneralInfo] = useState<GeneralInfo>();
  const { handleNextPenyataTahunan: handleNext } = useSenaraiContext();
  const { t } = useTranslation();

  // @ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const branchId = branchDataRedux.branchId;
  const navigate = useNavigate();
  const [statementComplete, setStatementComplete] = useState(false);
  const [bankAccounts, setBankAccounts] = useState<Bank[]>([]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
      { field: "branchId", operator: "eq", value: branchId },
    ],
    onSuccess: (data) => {
      const generalInfo = data?.data?.data || [];
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
      setGeneralInfo(generalInfo);
      setValue("regMemberCount", generalInfo?.regMemberCount);
      setValue("positionHolderNo", generalInfo?.positionHolderNo);
      setValue("federation", generalInfo?.federation ? "ya" : "tidak");
    },
  });

  const { refetch: refreshBank } = useQuery({
    url: `society/statement/bankInfo/list`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "branchId", operator: "eq", value: branchId },
    ],
    onSuccess: (data) => {
      const bank = data?.data?.data?.data || [];
      setBankAccounts(bank);
    },
  });

  const handleConfirmDelete = (id: number) => {
    setBankId(id);
    setOpenConfirm(true);
  };

  const { mutate: deleteBank } = useCustomMutation();

  const handleDeleteBank = () => {
    deleteBank(
      {
        url: `${API_URL}/society/statement/bankInfo/${bankId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          refreshBank();
          setOpenConfirm(false);
        },
      }
    );
  };

  const defaultFormValues = {
    regMemberCount: 0,
    positionHolderNo: 0,
    federation: true,
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    reset: resetForm,
  } = useForm<FieldValues>({
    defaultValues: defaultFormValues,
  });

  const { mutate: saveGeneralInfo, isLoading: isLoadingSaveBank } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = {
      regMemberCount: parseInt(data.regMemberCount, 10), // Convert to integer
      federation: data.federation == "ya" ? true : false,
    };
    saveGeneralInfo(
      {
        url: `${API_URL}/society/statement/societyInfo/update`,
        method: "put",
        values: {
          ...payload,
          societyId: societyId,
          statementId: statementId,
          branchId: branchId,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          handleNext();
          navigate(
            `/pertubuhan/society/${societyId}/senarai/cawangan/view/penyataTahunan/ajk`,
            {
              state: {
                societyId: societyId,
                statementId: statementId,
                year: year,
                branchId: branchId,
              },
            }
          );
        },
      }
    );
  };

  const { data: categoryData, isLoading: isCategoryLoading } = useCustom({
    url: `${API_URL}/society/admin/category/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const categories = categoryData?.data?.data || [];

  const categoryCode = society?.categoryCodeJppm
    ? categories.find(
        (cat: any) => cat.id === parseInt(society?.categoryCodeJppm)
      )?.categoryNameBm
    : "-";

  const subCategoryCode = society?.subCategoryCode
    ? categories.find(
        (cat: any) => cat.id === parseInt(society?.subCategoryCode)
      )?.categoryNameBm
    : "-";

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("tambahPenyataTahunan")}
          </Typography>
          <Grid>
            <Input
              label={t("organization_category")}
              value={categoryCode ? categoryCode : "-"}
              disabled
            />
            <Input
              label={t("sub_category")}
              disabled
              value={subCategoryCode ? subCategoryCode : "-"}
            />

            <Input
              label={t("organization_phone")}
              disabled
              value={generalInfo?.phoneNumber}
            />
            <Input label={t("financial_year_start")} disabled value={year} />

            <Controller
              name="regMemberCount"
              control={control}
              defaultValue={getValues("regMemberCount")} // Default value or empty string
              render={({ field }) => (
                <Input
                  {...field}
                  label={t("registered_members_count")}
                  type="number"
                  required
                  disabled
                  defaultValue={generalInfo?.regMemberCount ?? 0}
                />
              )}
            />

            {/* positionHolderNo */}

            <Controller
              name="positionHolderNo"
              control={control}
              defaultValue={getValues("positionHolderNo")} // Default value or empty string
              render={({ field }) => (
                <Input
                  {...field}
                  label={t("office_bearers_count")}
                  type="number"
                  required
                  disabled
                  defaultValue={generalInfo?.positionHolderNo}
                />
              )}
            />

            <Input
              label={t("branches_count")}
              disabled
              value={
                generalInfo?.branchCount == 0
                  ? t("none")
                  : generalInfo?.branchCount
              }
            />

            <Controller
              name="federation"
              control={control}
              defaultValue={getValues("federation") === true ? "ya" : "tidak"} // Default value or empty string
              render={({ field }) => (
                <Input
                  {...field}
                  type="select"
                  label={t("affiliations")}
                  required
                  disabled={isDisabled || statementComplete}
                  options={[
                    { value: "tidak", label: t("no") },
                    { value: "ya", label: t("yes") },
                  ]}
                  // value={getValues("federation") === true ? "ya" : "tidak"}

                  onChange={(e) => setValue("federation", e.target.value)}
                />
              )}
            />
          </Grid>
        </Box>
      </Box>

      {/* <CreateAccountBankDialog
          open={dialogSaveOpen}
          onClose={() => setDialogSaveOpen(false)}
        /> */}
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{ ...sectionStyle, borderRadius: "10px" }}
          >
            {t("organizationBankAccountInfo")}
          </Typography>

          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            {isDisabled || statementComplete ? null : (
              <ButtonPrimary
                type="button"
                sx={{
                  bgcolor: "transparent",
                  color: "#666666",
                  boxShadow: "none",
                  border: "1px solid #67D1D1",
                  fontSize: "12px",
                  fontWeight: "500 !important",
                }}
                onClick={() =>
                  navigate(
                    `/pertubuhan/society/${societyId}/senarai/cawangan/view/penyataTahunan/bank`,
                    {
                      state: {
                        statementId: statementId,
                        societyId: societyId,
                        branchId: branchId,
                      },
                    }
                  )
                }
              >
                {t("registerBankAccount")}
              </ButtonPrimary>
            )}
          </Box>

          <Box
            sx={{
              mt: 2,
              mb: 2,
              border: "1px solid #e0e0e0",
              borderRadius: "4px",
              overflow: "hidden",
            }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ color: "#666666" }}>
                    {t("bankName")}
                  </TableCell>
                  <TableCell sx={{ color: "#666666" }}>
                    {t("accountNumber")}
                  </TableCell>
                  <TableCell></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bankAccounts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} align="center">
                      {t("tiadaMaklumat")}
                    </TableCell>
                  </TableRow>
                ) : (
                  bankAccounts.map((account, index) => (
                    <TableRow key={index}>
                      <TableCell>{account.bankName}</TableCell>
                      <TableCell>{account.accountNo}</TableCell>
                      <TableCell
                        align="right"
                        sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                      >
                        {isDisabled || statementComplete ? null : (
                          <>
                            <IconButton
                              onClick={() =>
                                navigate("../bank", {
                                  state: {
                                    statementId: statementId,
                                    societyId: societyId,
                                    bankId: account.id,
                                  },
                                })
                              }
                            >
                              <EditIcon
                                sx={{ color: "var(--primary-color)" }}
                              />
                            </IconButton>
                            <IconButton
                              onClick={() => handleConfirmDelete(account.id)}
                            >
                              <TrashIcon sx={{ color: "red" }} />
                            </IconButton>
                          </>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </Box>

          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 4 }}>
            <Button
              type="submit"
              variant="contained"
              sx={{
                backgroundColor: "#8DC6C6",
                color: "white",
                "&:hover": { backgroundColor: "#7AB5B5" },
              }}
            >
              {t("next")}
            </Button>
          </Box>
        </Box>
        <ConfirmationDialog
          status={1}
          open={openConfirm}
          onClose={() => setOpenConfirm(false)}
          title={t("confirmDelete")}
          message={`${t("deleteConfirmationMessage")}`}
          onConfirm={handleDeleteBank}
          onCancel={() => setOpenConfirm(false)}
        />
      </Box>
    </form>
  );
};

export default CreateMam;
