import {
  Box,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import useQuery from "@/helpers/hooks/useQuery";
import { Auditor } from "../interface";
import { ApplicationStatus, ApplicationStatusEnum } from "@/helpers/enums";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import Input from "@/components/input/Input";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import { CrudFilter, useCustomMutation } from "@refinedev/core";
import dayjs from "dayjs";
import {
  <PERSON>,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { API_URL } from "@/api";

const JuruAudit: React.FC = () => {
  const { t } = useTranslation();
  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const isviewStatement = useSelector(
    // @ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  const [savedAppointmentDate, setSavedAppointmentDate] = useState("");
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const [statementComplete, setStatementComplete] = useState(false);
  const [auditDate, setAuditDate] = useState("");

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  // statement/auditor/countAuditors?societyId=406&statementId=3&branchId=1
  const [auditorCount, setAuditorCount] = useState<number>(0);
  useQuery({
    url: `society/statement/auditor/countAuditors`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const auditorCount = data?.data?.data || [];
      console.log(auditorCount);
      setAuditorCount(auditorCount.auditorCount);
    },
  });

  const [auditorList, setAuditorList] = useState<Auditor[]>([]);
  const { data, isLoading, refetch } = useQuery({
    url: `society/statement/auditor/list`,
    // filters: [
    //   { field: "societyId", operator: "eq", value: societyId },
    //   { field: "statementId", operator: "eq", value: statementId },
    //   { field: "branchId", operator: "eq", value: branchDataRedux.id },
    // ],
    autoFetch: false,
    onSuccess: (data) => {
      const auditorLst = data?.data?.data?.data || [];
      setAuditorList(auditorLst);
    },
  });

  const handleDateChange = (date: string) => {
    const filters: CrudFilter[] = [
      { field: "appointmentDate", value: date, operator: "eq" },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ];
    setSavedAppointmentDate(date);
    refetch({ filters });
  };

  useEffect(() => {
    if (societyId) {
      const filters: CrudFilter[] = [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: branchDataRedux.id },
      ];

      refetch({ filters });
    }
  }, [societyId]);

  const handleClearSearch = () => {
    const filters: CrudFilter[] = [
      { field: "appointmentDate", value: "", operator: "eq" },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ];

    refetch({ filters });
  };

  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const handleDaftarJuruaudit = () => {
    navigate("../../ajk/juruaudit/create");
  };

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  // const handleNextActions = () => {
  //   handleNext();
  //   navigate("../pendapatan", {
  //     state: {
  //       societyId: societyId,
  //       statementId: statementId,
  //       year: year,
  //     },
  //   });
  // };

  const { mutate: saveGeneralInfo, isLoading: isLoadingSaveBank } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    if (isDisabled || statementComplete) {
      navigate("../pendapatan", {
        state: {
          societyId: societyId,
          statementId: statementId,
          branchId: branchDataRedux.id,
          year: year,
        },
      });
    } else {
      saveGeneralInfo(
        {
          url: `${API_URL}/society/statement/general/update`,
          method: "put",
          values: {
            societyId: societyId,
            statementId: statementId,
            branchId: branchDataRedux.id,
            ...(savedAppointmentDate && {
              juruauditAppointedDate: savedAppointmentDate,
            }),
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess: () => {
            handleNext();
            navigate("../pendapatan", {
              state: {
                societyId: societyId,
                statementId: statementId,
                branchId: branchDataRedux.id,
                year: year,
              },
            });
          },
        }
      );
    }
  };

  useQuery({
    url: `society/statement/general/get`,
    filters: [
      {
        field: "statementId",
        value: statementId,
        operator: "eq",
      },
      {
        field: "societyId",
        value: societyId,
        operator: "eq",
      },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const date = data?.data?.data?.juruauditAppointedDate;
      setAuditDate(date);
      setSavedAppointmentDate(date);
    },
  });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          sx={{
            color: "#666666",
            fontSize: 14,
            fontWeight: "400 !important",
          }}
        >
          <span style={{ color: "red", fontWeight: "bold" }}>
            {t("peringatan")} :
          </span>{" "}
          Sila pastikan bilangan Juruaudit yang berstatus aktif mengikut
          bilangan di dalam perlembagaan.
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          textAlign: "center",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "var(--primary-color)",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {t("bilanganJuruauditTerkini")}
        </Typography>
        <Typography
          sx={{
            color: "#666666",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {auditorCount} Orang
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          gap: 3,
          mb: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: 14,
              fontWeight: "500 !important",
            }}
          >
            Jenis Juruaudit di dalam perlembagaan:{" "}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "600 !important",
            }}
          >
            Dalaman
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: 14,
              fontWeight: "500 !important",
            }}
          >
            Bilangan Juruaudit di dalam perlembagaan:{" "}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "600 !important",
            }}
          >
            {auditorCount} Orang
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("auditorAppointmentDate")}
        </Typography>
        <Input
          //textColor="#666666"
          value={auditDate ? dayjs(auditDate).format("DD-MM-YYYY") : ""}
          onChange={(e) => handleDateChange(e.target.value)}
          // disabled={!isAllow}
          type="date"
          label={t("appointmentDateAudit")}
        />
        <Box sx={{ display: "grid", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrevious
            variant="outlined"
            sx={{
              bgcolor: "white",
              "&:hover": { bgcolor: "white" },
            }}
            onClick={handleClearSearch}
          >
            {t("previous")}
          </ButtonPrevious>
        </Box>
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("auditorList")}
        </Typography>
        <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
          {isDisabled || statementComplete ? null : (
            <ButtonOutline onClick={handleDaftarJuruaudit}>
              {t("registerAuditor")}
            </ButtonOutline>
          )}
        </Box>
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: "none",
            border: "1px solid #e0e0e0",
            backgroundColor: "white",
            borderRadius: 2.5 * 1.5,
            p: 1,
            mb: 3,
          }}
        >
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("name")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("idNumber")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("email")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("tarikhLantik")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("status")}
                </TableCell>
                {/* <TableCell
                  align="right"
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                ></TableCell> */}
              </TableRow>
            </TableHead>
            <TableBody>
              {auditorList.map((row, index) => (
                <TableRow key={row.id}>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.name}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.identificationNo ? row.identificationNo : "-"}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.email ? row.email : "-"}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.appointmentDate
                      .map((part, index) =>
                        index > 0 ? part.toString().padStart(2, "0") : part
                      ) // Pad month and day to 2 digits
                      .join("-")}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {t(ApplicationStatusEnum[row.status])}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline
          sx={{
            bgcolor: "white",
            "&:hover": { bgcolor: "white" },
            width: isMobile ? "100%" : "auto",
          }}
          onClick={handleBackActions}
        >
          {t("kembali")}
        </ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{
            width: isMobile ? "100%" : "auto",
          }}
          onClick={onSubmit}
          // disabled={createFeedBackIsloading}
        >
          {t("next")}
        </ButtonPrimary>
      </Grid>
    </Box>
  );
};

export default JuruAudit;
