import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import { globalStyles } from "@/helpers";

import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { DataTable, IColumn } from "@/components";
import FilterBar from "@/components/filter";

import { SearchIcon, EditIcon } from "@/components/icons";

const dummy = [
  {
    namaPertubuhan:
      "PERSATUAN KEBAJIKAN PENDUDUK CINA TAMAN DELIMA, JALAN GANGSA, ALOR SETAR, KEDAH",
    nomborPertubuhan: "PPM-005-02-04082004",
    tarikhHantar: "2023-10-31 21:27:57",
    status: "Diluluskan",
    negeri: "Kedah",
  },
];

const LulusPenilaian: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();

  const columns: IColumn[] = [
    {
      field: "",
      headerName: "No",
      flex: 1,
      align: "center",
      renderCell: ({ rowIndex }) => {
        const number =
          watch("page") * watch("pageSize") + rowIndex + 1 - watch("pageSize");
        return <>{number}</>;
      },
    },
    { field: "namaPertubuhan", headerName: "Nama Pertubuhan", flex: 1 },
    { field: "nomborPertubuhan", headerName: "Nombor Pertubuhan", flex: 1 },
    { field: "tarikhHantar", headerName: "Tarikh Syor", flex: 1 },
    { field: "negeri", headerName: t("state"), flex: 1 },
    {
      field: "actions",
      headerName: "Tindakan",
      align: "center",
      renderCell: ({ row }) => {
        return (
          <IconButton
            sx={{
              padding: 0,
            }}
          >
            <EditIcon />
          </IconButton>
        );
      },
    },
  ];

  const statusOptions = [
    { value: 2, label: "Menunggu Keputusan" },
    { value: 1, label: "Belum dihantar" },
    { value: 3, label: "Lulus" },
    { value: 4, label: "Tolak" },
    { value: 5, label: "MENUNGGU_BAYARAN_KAUNTER" },
    { value: 6, label: "MENUNGGU_BAYARAN_ONLINE" },
    { value: 11, label: "AKTIF" },
    { value: 41, label: "MENUNGGU_ULASAN_AGENSI_LUAR" },
  ];

  const filterOptions = {
    status: statusOptions,
  };

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    status: "status",
  });

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      status: undefined,
    },
  });

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const onFilterChange = (filter: string, value: number | string) => {
    setValue("page", 1);
    switch (filter) {
      case "status":
        setValue("status", value);
        break;
    }
  };

  return (
    <Box className={classes.section}>
      <Box className={classes.sectionBox}>
        <Typography className="title" mb={8}>
          Senarai Lulus Penilaian
        </Typography>

        <TextField
          fullWidth
          variant="outlined"
          placeholder={t("state")}
          sx={{
            display: "block",
            boxSizing: "border-box",
            width: "90%",
            height: "40px",
            marginInline: "auto",
            marginTop: "12px",
            background: "rgba(132, 132, 132, 0.3)",
            opacity: 0.5,
            border: "1px solid rgba(102, 102, 102, 0.8)",
            borderRadius: "10px",
            "& .MuiOutlinedInput-root": {
              height: "40px",
              "& fieldset": {
                border: "none",
              },
            },
          }}
          // onChange={handleSearchSecretaryName}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: "#9CA3AF", marginLeft: "8px" }} />
              </InputAdornment>
            ),
          }}
        />

        <FilterBar
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          selectedFilters={selectedFilters}
          onSelectedFiltersChange={handleSelectedFiltersChange}
        />

        <DataTable
          columns={columns}
          rows={dummy}
          page={watch("page")}
          rowsPerPage={watch("pageSize")}
          totalCount={dummy.length}
          // onPageChange={handleChangePage}
          // onPageSizeChange={handleChangePageSize}
          // isLoading={isLoadingLiquidationList}
        />
      </Box>
    </Box>
  );
};

export default LulusPenilaian;
