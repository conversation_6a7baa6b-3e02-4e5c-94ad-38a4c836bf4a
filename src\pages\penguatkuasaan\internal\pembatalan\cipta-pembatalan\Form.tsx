import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import {
  globalStyles,
  useQuery,
  useMutation,
  getMainCategories,
  getSubCategories,
  getStateNameById,
  OrganisationPositionLabel,
  SectionOptions,
} from "@/helpers";

import {
  DisabledTextField,
  FormFieldRow,
  Label,
  TextFieldController,
  ButtonPrimary,
  DataTable,
  IColumn,
  CustomSkeleton,
  SelectFieldController,
  DialogConfirmation,
  DatePickerController,
} from "@/components";
import { Box, Typography, IconButton, CircularProgress } from "@mui/material";

import { EditIcon } from "@/components/icons";

import {
  IApiPaginatedResponse,
  IApiResponse,
  ICommittee,
  ISocietyDetail,
} from "@/types";
import { useMemo } from "react";

const historyDummy = [
  {
    date: "01/02/2024",
    notisType: "13(1)",
    description: "Notis Pembatalan",
  },
];

const CiptaPembatalanForm: React.FC = () => {
  const { id } = useParams();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const classes = globalStyles();
  const isMyLanguage = i18n.language === "my";

  const [dialogOpen, setDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const sectionOptions = useMemo(() => {
    return (
      SectionOptions.filter((section) => section.section === "13").map(
        (section) => ({
          label: section.description as string,
          value: section.code as string,
        })
      ) ?? []
    );
  }, []);

  const { data: societyDetailResponse, isLoading: isLoadingSocietyDetail } =
    useQuery<IApiResponse<ISocietyDetail>>({
      url: `society/${id}`,
    });

  const { data: ajkListResponse, isLoading: isLoadingAjkList } = useQuery<
    IApiPaginatedResponse<ICommittee>
  >({
    url: "society/committee/listAjk",
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: id,
      },
    ],
  });

  const {
    fetch: createSocietyCancellation,
    isLoading: isCreatingSocietyCancellation,
  } = useMutation<IApiResponse<number>>({
    url: "society/cancellation/create",
    onSuccess: (res) => {
      const responseCode = res?.data?.code;

      if (responseCode === 200) {
        setIsSuccess(true);
        setTimeout(
          () =>
            navigate("..", {
              state: {
                tab: "senarai-pembatalan",
              },
            }),
          2000
        );
      }
    },
  });

  const societyDetail = societyDetailResponse?.data?.data;
  const ajkList = ajkListResponse?.data?.data?.data ?? [];
  const mainCategory = getMainCategories(
    Number(societyDetail?.categoryCodeJppm)
  );
  const subCategory = getSubCategories(Number(societyDetail?.subCategoryCode));

  const historyColumn: IColumn[] = [
    { field: "date", headerName: "Tarikh", flex: 1, align: "center" },
    {
      field: "notisType",
      headerName: t("typeOfNotice"),
      flex: 1,
      align: "center",
    },
    {
      field: "description",
      headerName: t("descriptionNotice"),
      flex: 1,
      align: "center",
    },
    {
      field: "status",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton>
              <EditIcon color="#1DC1C1" />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const committeeColumn: IColumn<ICommittee>[] = [
    {
      field: "titleCode",
      headerName: t("position"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
      renderCell: ({ row }) => {
        return t(OrganisationPositionLabel[Number(row?.designationCode)]);
      },
    },
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
    },
    {
      field: "",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      background: "#E8E9E8",
      renderCell: ({ row }) => {
        return getStateNameById(row?.residentialStateCode ?? "");
      },
    },
  ];

  const { control, handleSubmit, getValues, setValue } = useForm<FieldValues>({
    defaultValues: {
      societyId: id,
      societyNo: "",
      cancelledDate: "",
      section: "",
      reason: "",
    },
  });

  const onSubmit = () => setDialogOpen(true);

  const createCancellationSociety = () => {
    const formValues = getValues();

    createSocietyCancellation(formValues);
  };

  useEffect(() => {
    if (societyDetail) {
      setValue("societyNo", societyDetail?.societyNo ?? "");
    }
  }, [societyDetail]);

  if (isLoadingSocietyDetail || isLoadingAjkList)
    return <CustomSkeleton height={50} number={4} />;

  return (
    <>
      <Box className={classes.section} mb={1}>
        <Box className={classes.sectionBox}>
          <Typography
            sx={{
              color: "#FF0000",
              fontWeight: "500",
              fontSize: "12px !important",
              "& span": { color: "#666666", fontWeight: "400" },
            }}
          >
            {t("peringatan")}
            <span>
              : Sila pastikan senarai AJK adalah yang terkini sebelum melakukan
              pembatalan pertubuhan
            </span>
          </Typography>
        </Box>
      </Box>

      <Box className={classes.section} mb={1}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            {isMyLanguage ? "Info Pertubuhan" : "Organization Info"}
          </Typography>

          <FormFieldRow
            label={<Label text={t("ppmNumber")} />}
            value={
              <DisabledTextField value={societyDetail?.societyNo ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationName")} />}
            value={
              <DisabledTextField value={societyDetail?.societyName ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationCategory")} />}
            value={
              <DisabledTextField value={mainCategory?.categoryNameBm ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationSubCategory2")} />}
            value={
              <DisabledTextField value={subCategory?.categoryNameBm ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("tarikhDaftar")} />}
            value={
              <DisabledTextField value={societyDetail?.registeredDate ?? "-"} />
            }
          />

          <FormFieldRow
            label={<Label text={t("branchedStatus")} />}
            value={
              <DisabledTextField
                value={
                  societyDetail?.hasBranch ? "BERCAWANGAN" : "TIDAK BERCAWANGN"
                }
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("alamatPertubuhan")} />}
            value={<DisabledTextField value={societyDetail?.address ?? "-"} />}
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text={t("organizationCommittee")} />}
            value={
              <Box
                sx={{
                  background: "#E8E9E8",
                  borderRadius: "5px",
                  overflow: "hidden",
                }}
              >
                <DataTable
                  columns={committeeColumn}
                  rows={ajkList}
                  page={1}
                  rowsPerPage={10}
                  totalCount={10}
                  pagination={false}
                  isLoading={isLoadingAjkList}
                />
              </Box>
            }
          />
        </Box>
      </Box>

      {/* SEJARAH NOTICE HIDE */}
      {/* <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            {isMyLanguage ? "Sejarah Notis" : "Notice History"}
          </Typography>

          <DataTable
            columns={historyColumn}
            rows={historyDummy}
            page={1}
            rowsPerPage={10}
            totalCount={10}
          />
        </Box>
      </Box> */}

      <form onSubmit={handleSubmit(onSubmit)}>
        <Box className={classes.section} mb={2}>
          <Box className={classes.sectionBox} mb={2}>
            <Typography className="title" mb={1}>
              {isMyLanguage
                ? " Keterangan Pembatalan"
                : "Cancellation Information"}
            </Typography>

            <FormFieldRow
              label={<Label text={t("cancellationDate")} required />}
              value={
                <DatePickerController
                  formatValue="YYYY-MM-DD"
                  control={control}
                  name="cancelledDate"
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("section")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="section"
                  options={sectionOptions}
                  required
                />
              }
            />

            <FormFieldRow
              align="flex-start"
              label={<Label text={t("ulasan")} />}
              value={
                <TextFieldController
                  control={control}
                  name="reason"
                  multiline
                  rows={3}
                />
              }
            />
          </Box>

          <ButtonPrimary
            type="submit"
            sx={{ marginLeft: "auto" }}
            className={classes.btnSubmit}
            disabled={isCreatingSocietyCancellation}
          >
            {isCreatingSocietyCancellation && <CircularProgress size={15} />}
            {t("next")}
          </ButtonPrimary>
        </Box>
      </form>

      <DialogConfirmation
        isMutating={isCreatingSocietyCancellation}
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onAction={createCancellationSociety}
        onConfirmationText={
          isMyLanguage
            ? "Adakah anda pasti untuk meneruskan Pembatalan ?"
            : "Are you sure you want to proceed with Cancellation?"
        }
        isSuccess={isSuccess}
        onSuccessText={
          isMyLanguage
            ? "Pembatalan Pertubuhan ini telah berjaya !"
            : "The cancellation of this Organization has been successful!"
        }
      />
    </>
  );
};

export default CiptaPembatalanForm;
