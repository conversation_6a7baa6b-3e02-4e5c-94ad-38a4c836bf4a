// Update the custom theme with Poppins font
const theme = createTheme({
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
          font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif !important;
        }
      `,
    },
  },
  typography: {
    fontFamily: "Poppins, Arial, sans-serif",
    h6: {
      fontSize: "16px",
      fontWeight: "bold",
    },
    body1: {
      fontSize: "14px",
    },
  },
});

import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { DataGrid, useGridApiRef } from "@mui/x-data-grid";
import { FieldValues } from "react-hook-form";
import {
  Box,
  Typography,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  ThemeProvider,
  createTheme,
  CssBaseline,
  MenuItem,
  Button,
  Divider,
  Menu,
} from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import { useForm } from "@refinedev/react-hook-form";
import useQuery from "@/helpers/hooks/useQuery";
import { Switch as CustomSwitch } from "@/components/switch";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { EyeIcon } from "@/components/icons";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { ApplicationStatusEnum, MeetingTypeOption } from "@/helpers/enums";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const CawanganMesyuarat: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const apiRef = useGridApiRef();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);

  const [isAliranTugas, setIsAliranTugas] = useState(false);

  //@ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [taskList, setTaskList] = useState([
    {
      id: 1,
      nama: "Atiqah",
      jawatan: "AJK",
      statusAliranTugas: "Aktif",
      tarikhAliranTugas: "23/9/2024",
      tarikhNyahAktif: "20/10/2024",
      isActive: true,
    },
    {
      id: 2,
      nama: "Aiman",
      jawatan: "AJK",
      statusAliranTugas: "Tidak aktif",
      tarikhAliranTugas: "23/9/2024",
      tarikhNyahAktif: "20/10/2024",
      isActive: false,
    },
  ]);

  const { watch, setValue } = useForm<FieldValues>({
    defaultValues: {
      searchQuery: "",
      page: 1,
      rowsPerPage: 10,
    },
  });

  const {
    data,
    isLoading: isMeetingLoading,
    refetch,
  } = useCustom({
    url: `${API_URL}/society/meeting/findByBranchId/${branchDataRedux.id}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      // filters: [
      //   // { field: "meetingType", operator: "eq", value: watch("searchQuery") },
      // ],
    },
  });

  const { data: meetingList } = useQuery({
    url: "society/admin/meeting/list",
  });

  const columns = [
    {
      field: "meetingType",
      headerName: t("meetingType"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params: any) => {
        return MeetingTypeOption.find(
          (option) => option.value === parseInt(params.row.meetingType)
        )?.label;
      },
    },
    {
      field: "meetingPlace",
      headerName: t("meetingPlace"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params: any) => params.row.meetingPlace,
    },
    {
      field: "meetingDate",
      headerName: t("meetingDate"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params: any) =>
        dayjs(params.row.meetingDate).format("DD/MM/YYYY"),
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      renderCell: (params: any) => {
        const isActive =
          params.row.status === "11" || params.row.status === "001";
        return (
          <Typography
            className="status-pertubuhan-text"
            sx={{
              backgroundColor: "#fff",
              border: `2px solid ${
                isActive ? "var(--success)" : "var(--error)"
              }`,
              textAlign: "center",
              padding: "4px 8px",
              borderRadius: "20px",
              fontSize: "14px",
              color: "#666666",
              fontWeight: "normal",
            }}
          >
            {isActive
              ? "Selesai"
              : t(
                  ApplicationStatusEnum[
                    (params.row.status as keyof typeof ApplicationStatusEnum) || "0"
                  ]
                )}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      align: "right",
      renderCell: (params: any) => {
        const row = params.row;

        return (
          <Link to={`${row.id}`}>
            <EyeIcon />
          </Link>
        );
      },
    },
  ];

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, id: number) => {
    setAnchorEl(event.currentTarget);
    setOpenMenuId(id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };

  const listData = data?.data?.data || [];

  const handleDaftarMesyuarat = () => {
    navigate("create");
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  useEffect(() => {
    refetch();
  }, [watch("searchQuery")]);

  const location = useLocation();
  const disabled = location.state?.disabled ?? false;

  const isManager = useSelector(getUserPermission);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        {/* <TextField
          fullWidth
          variant="outlined"
          placeholder={t("meeting")}
          sx={{
            display: "block",
            boxSizing: "border-box",
            width: "90%",
            height: "40px",
            marginInline: "auto",
            marginTop: "12px",
            background: "rgba(132, 132, 132, 0.3)",
            opacity: 0.5,
            border: "1px solid rgba(102, 102, 102, 0.8)",
            borderRadius: "10px",
            "& .MuiOutlinedInput-root": {
              height: "40px",
              "& fieldset": {
                border: "none",
              },
            },
          }}
          onChange={(e) => setValue("searchQuery", e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: "#9CA3AF", marginLeft: "8px" }} />
              </InputAdornment>
            ),
          }}
        /> */}

        <Box
          sx={{
            width: "80%",
            display: "flex",
            alignItems: "center",
            borderRadius: "8px",
            backgroundColor: "#FFFFFF",
            marginInline: "auto",
            marginTop: "12px",
            marginBottom: "12px",
            boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.1)",
            padding: "4px",
            gap: "4px",
          }}
        >
          <Button
            variant="text"
            startIcon={
              <img
                src={"/filter-icon.svg"}
                alt="Filter Icon"
                width="18"
                height="18"
              />
            }
            sx={{
              flex: 1,
              height: "36px",
              color: "#6B7280",
              textTransform: "none",
              textWrap: "nowrap",
              "&:hover": {
                backgroundColor: "#F9FAFB",
              },
            }}
          >
            {t("filterBy")}
          </Button>

          {[
            t("meetingType"),
            t("meetingPlace"),
            t("meetingDate"),
            t("status"),
          ].map((text, index) => (
            <React.Fragment key={text}>
              <Divider
                orientation="vertical"
                flexItem
                sx={{ backgroundColor: "#E5E7EB" }}
              />
              <Button
                variant="text"
                endIcon={<KeyboardArrowDownIcon />}
                sx={{
                  flex: 2,
                  height: "36px",
                  color: "#6B7280",
                  textTransform: "none",
                  width: "150px",
                  justifyContent: "space-between",
                  "&:hover": {
                    backgroundColor: "#F9FAFB",
                  },
                }}
              >
                {text}
              </Button>
            </React.Fragment>
          ))}
        </Box>

        <DataGrid
          autoHeight
          apiRef={apiRef}
          rows={listData}
          columns={columns as any}
          initialState={{
            pagination: { paginationModel: { pageSize: 5 } },
          }}
          rowSelection={false}
          pageSizeOptions={[5, 10, 15]}
          sx={{
            backgroundColor: "white",
            borderRadius: "4px",
            fontFamily: "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
            "& .MuiDataGrid-columnHeaders": {
              fontWeight: "bold",
              fontSize: "16px",
            },
            "& .MuiDataGrid-cell": {
              fontSize: "16px",
            },
            minHeight: "100px",
          }}
          getRowId={(row) => row.id}
          loading={isMeetingLoading}
          slots={{
            noRowsOverlay: () => (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  aligntItems: "center",
                  pt: 3,
                }}
              ></Box>
            ),
            noResultsOverlay: () => (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  aligntItems: "center",
                  pt: 3,
                }}
              ></Box>
            ),
          }}
          localeText={{
            MuiTablePagination: {
              labelRowsPerPage: t("rowsPerPage"),
            },
          }}
        />
      </Box>
      {isManager || isAliranTugasAccess ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("organizationMeeting")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("tambahMesyuarat")}
                </Typography>

                <ButtonPrimary
                  disabled={disabled}
                  onClick={handleDaftarMesyuarat}
                >
                  {t("add")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
                mb: 2,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 3,
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    color: "var(--primary-color)",
                    borderRadius: "16px",
                    fontSize: "14px",
                  }}
                >
                  {t("taskFlow")}
                </Typography>

                <CustomSwitch
                  disabled={disabled}
                  checked={isAliranTugas}
                  onChange={(e) => setIsAliranTugas(e.target.checked)}
                />
              </Box>

              {isAliranTugas && (
                <Table
                  sx={{
                    backgroundColor: "white",
                    borderRadius: "4px",
                    overflow: "hidden",
                    "& .MuiTableCell-root": { fontSize: "16px" },
                  }}
                >
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "white" }}>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("name")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("position")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("status")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("AKTIF")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("deactivationDate")}
                      </TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {taskList
                      .filter((e) => {
                        return e.statusAliranTugas === "Aktif";
                      })
                      .map((task, index) => (
                        <TableRow key={index}>
                          <TableCell>{task.nama}</TableCell>
                          <TableCell>{task.jawatan}</TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                p: 1,
                                border:
                                  task?.statusAliranTugas == "Aktif"
                                    ? "0.5px solid #00B69B"
                                    : "0.5px solid #FF0000",
                                backgroundColor:
                                  task?.statusAliranTugas == "Aktif"
                                    ? "#00B69B66"
                                    : "#FF000080",
                                textAlign: "center",
                                borderRadius: "30px",
                                px: 2,
                                color: "white",
                              }}
                            >
                              {t(task.statusAliranTugas)}
                            </Box>
                          </TableCell>
                          <TableCell>{task.tarikhAliranTugas}</TableCell>
                          <TableCell>{task.tarikhNyahAktif}</TableCell>
                          <TableCell>
                            <IconButton
                              onClick={(event) =>
                                handleMenuOpen(event, task.id)
                              }
                            >
                              <MoreVertIcon sx={{ color: "black" }} />
                            </IconButton>
                            <Menu
                              anchorEl={anchorEl}
                              open={openMenuId === task.id}
                              onClose={handleMenuClose}
                              slotProps={{
                                paper: {
                                  elevation: 0,
                                  sx: {
                                    backgroundColor: "white",
                                    color: "black",
                                    boxShadow: "0 4px 4px rgba(0, 0, 0, 0.25)",
                                    "& .MuiMenuItem-root": {
                                      color: "black",
                                      "& .MuiSvgIcon-root": {
                                        color: "black",
                                      },
                                    },
                                    "& .MuiDivider-root": {
                                      my: 0.5,
                                    },
                                  },
                                },
                              }}
                              transformOrigin={{
                                horizontal: "right",
                                vertical: "top",
                              }}
                              anchorOrigin={{
                                horizontal: "right",
                                vertical: "bottom",
                              }}
                            >
                              <MenuItem onClick={handleOpenModal}>
                                {t("deactivate")}
                              </MenuItem>
                            </Menu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              )}
            </Box>

            {isAliranTugas && (
              <Box
                sx={{
                  border: "1px solid rgba(0, 0, 0, 0.12)",
                  borderRadius: "14px",
                  p: 3,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("taskFlowList")}
                </Typography>

                <Table
                  sx={{
                    backgroundColor: "white",
                    borderRadius: "4px",
                    overflow: "hidden",
                    "& .MuiTableCell-root": { fontSize: "16px" },
                  }}
                >
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "white" }}>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("name")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("position")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("status")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("AKTIF")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("deactivationDate")}
                      </TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {taskList
                      .filter((e) => {
                        return e.statusAliranTugas !== "Aktif";
                      })
                      .map((task, index) => (
                        <TableRow key={index}>
                          <TableCell>{task.nama}</TableCell>
                          <TableCell>{task.jawatan}</TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                p: 1,
                                border:
                                  task?.statusAliranTugas == "Aktif"
                                    ? "0.5px solid #00B69B"
                                    : "0.5px solid #FF0000",
                                backgroundColor:
                                  task?.statusAliranTugas == "Aktif"
                                    ? "#00B69B66"
                                    : "#FF000080",
                                textAlign: "center",
                                borderRadius: "30px",
                                px: 2,
                                color: "white",
                              }}
                            >
                              {t(task.statusAliranTugas)}
                            </Box>
                          </TableCell>
                          <TableCell>{task.tarikhAliranTugas}</TableCell>
                          <TableCell>{task.tarikhNyahAktif}</TableCell>
                          <TableCell>
                            <IconButton
                              onClick={(event) =>
                                handleMenuOpen(event, task.id)
                              }
                            >
                              <MoreVertIcon sx={{ color: "black" }} />
                            </IconButton>
                            <Menu
                              anchorEl={anchorEl}
                              open={openMenuId === task.id}
                              onClose={handleMenuClose}
                              slotProps={{
                                paper: {
                                  elevation: 0,
                                  sx: {
                                    backgroundColor: "white",
                                    color: "black",
                                    boxShadow: "0 4px 4px rgba(0, 0, 0, 0.25)",
                                    "& .MuiMenuItem-root": {
                                      color: "black",
                                      "& .MuiSvgIcon-root": {
                                        color: "black",
                                      },
                                    },
                                    "& .MuiDivider-root": {
                                      my: 0.5,
                                    },
                                  },
                                },
                              }}
                              transformOrigin={{
                                horizontal: "right",
                                vertical: "top",
                              }}
                              anchorOrigin={{
                                horizontal: "right",
                                vertical: "bottom",
                              }}
                            >
                              <MenuItem onClick={handleOpenModal}>
                                {t("AKTIF")}
                              </MenuItem>
                            </Menu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </Box>
            )}
          </Box>{" "}
        </>
      ) : (
        <></>
      )}
    </ThemeProvider>
  );
};

export default CawanganMesyuarat;
