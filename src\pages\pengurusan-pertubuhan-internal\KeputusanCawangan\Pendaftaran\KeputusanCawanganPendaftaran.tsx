import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Card,
  CircularProgress,
  Dialog,
  DialogContent,
  Grid,
  Typography,
  useMediaQuery,
  useTheme,
  Theme,
} from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import AccordionComp from "../../View/Accordion";
import { filterEmptyValuesOnObject } from "../../../../helpers/utils";
import useMutation from "../../../../helpers/hooks/useMutation";
import MaklumatAmSection from "./views/MaklumatAmSection";
import { useKeputusanCawanganPendaftaranContext } from "./KeputusanCawanganPendaftaranProvider";
import MaklumatMesyuaratSection from "./views/MaklumatMesyuaratSection";
import SenaraiAJK from "./views/SenaraiAJK";
import DokumenSection from "./views/DokumenSection";
import MaklumatPermohonanPenubuhanCawangan from "./views/MaklumatPermohonanPenubuhanCawangan";
import Input from "../../../../components/input/Input";
import { CheckedIcon } from "../../../../components/input/customRadio";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { API_URL } from "../../../../api";
import { number, object, ObjectSchema, string } from "yup";
import { DecisionFormInner } from "./DecisionFormInner";
import { Formik, FormikHelpers } from "formik";
import { useNavigate } from "react-router-dom";
import { ArrowBackground2SVG } from "@/components/icons/arrowBackground2";
import { SelectFieldController, TextFieldController } from "@/components";
import { FieldValues, useForm } from "react-hook-form";
import { useCustomMutation } from "@refinedev/core";
import { fetchBranchByIdData } from "@/redux/APIcalls/branchByIdThunks";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

export interface DecisionBranchRegistrationPayload {
  /**
   * possible values
   * - 3 --> **LULUS**
   * - 4 --> **TOLAK**
   * - 36 --> **KUIRI**
   */
  applicationStatusCode: number;
  branchId: number;
  societyId: number;

  /**
   * this attribute is required if {@link applicationStatusCode} is 4 / **TOLAK**
   */
  rejectReason?: string | null;

  /**
   * this attribute is required if {@link applicationStatusCode} is 3 / **LULUS** or 36 / **KUIRI**
   */
  note?: string | null;

  roApprovalType: "BRANCH_REGISTRATION";
}

function KeputusanCawanganPendaftaranComp<
  Payload extends DecisionBranchRegistrationPayload = DecisionBranchRegistrationPayload
>() {
  const { branchDataById, societyDataById } =
    useKeputusanCawanganPendaftaranContext();
  const { t } = useTranslation();
  const [dialogSejarahKuiriSaveOpen, setDialogSejarahKuiriSaveOpen] =
    useState(false);
  const navigate = useNavigate();
  const [isSuccess, setIsSuccess] = useState(false);
  const dispatch: AppDispatch = useDispatch();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [roList, setRoList] = useState([]);
  const resolver = object()
    .shape({
      roApprovalType: string().required(),
      applicationStatusCode: number().label(t("statusPermohonan")).required(),
      societyId: number().required(),
      branchId: number().required(),
      note: string()
        .label(t("remarks"))
        .test({
          name: "test_note",
          exclusive: true,
          message: ({ label }) =>
            t("inputValidationErrorStringExceedsLimitWord", {
              label,
              value: 100,
            }),
          test: (note, context) => {
            const body = context.parent as Payload;
            console.log(note, body);
            if (body.applicationStatusCode === 36) {
              if (!(note?.length && note.length > 0)) {
                return context.createError({ message: t("fieldRequired") });
              }
            }
            return true;
          },
        }),
    })
    .required() as unknown as ObjectSchema<Payload>;

  const { fetchAsync: updateDecision } = useMutation({
    url: "society/roDecision/updateApprovalStatus",
    method: "patch",
    onSuccess: () => {
      setIsSuccess(true);
    },
  });

  const [ROQuery, setROQuery] = useState(null);

  const getRoQuery = async ({
    branchId,
    roApprovalType,
  }: {
    branchId: any;
    roApprovalType: string;
  }) => {
    try {
      if (branchId && roApprovalType) {
        const response = await fetch(`${API_URL}/society/roQuery/getQuery`, {
          method: "post",
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
          body: JSON.stringify({ branchId, roApprovalType }),
        });
        const data = await response.json();
        if (data.status === "SUCCESS") {
          setROQuery(data.data);
        }
      }
    } catch (error) {
      console.error("error:", error);
      return null;
    }
  };

  const defaultValues = {
    applicationStatusCode: 3,
    rejectReason: null,
    note: null,
    roApprovalType: "BRANCH_REGISTRATION",
    societyId: societyDataById?.id ?? 0,
    branchId: branchDataById?.id ?? 0,
  } as Payload;
  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);
  const theme = useTheme();
  const [readStatus, setReadStatus] = useState<ReadStatusType>({});
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const sectionItems = [
    {
      subTitle: t("generalInformation"),
      component: <MaklumatAmSection />,
    },
    {
      subTitle: t("meetingInformation"),
      component: <MaklumatMesyuaratSection />,
    },
    {
      subTitle: t("ajkList"),
      component: <SenaraiAJK />,
    },
    {
      subTitle: t("dokumen"),
      component: <DokumenSection />,
    },
    {
      subTitle: t("keputusanCawangan_MaklumatPermohonanPenubuhanCawangan"),
      component: <MaklumatPermohonanPenubuhanCawangan />,
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const onSubmit = async (
    payload: Payload,
    { setSubmitting }: FormikHelpers<Payload>
  ) => {
    setSubmitting(true);
    try {
      if (payload.applicationStatusCode === 4) {
        payload.rejectReason = payload.note;
        payload.note = null;
      }
      const filterPayload = filterEmptyValuesOnObject(payload);
      await updateDecision(filterPayload);
    } finally {
      setSubmitting(false);
    }
  };
  const handleQueryOpen = async () => {
    if (societyDataById?.id) {
      setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen);
    }
  };

  useEffect(() => {
    if (societyDataById?.id) {
      const payload = {
        branchId: branchDataById?.id,
        roApprovalType: "BRANCH_REGISTRATION",
      };

      getRoQuery(payload);
    }
  }, [societyDataById]);

  //

  const { control, handleSubmit, setValue, setError } = useForm<FieldValues>({
    defaultValues: {
      societyId: societyDataById?.id,
      roId: "",
      noteRo: "",
      branchId: branchDataById?.id,
      roApprovalType: "BRANCH_REGISTRATION",
    },
  });

  useEffect(() => {
    if (societyDataById?.id && branchDataById?.id) {
      fetchRoList();
    }
  }, [societyDataById, branchDataById]);

  useEffect(() => {
    if (branchDataById?.stateCode) {
      if (branchDataById?.ro) {
        setValue("roId", Number(branchDataById?.ro));
      }
      setValue("noteRo", branchDataById?.noteRo);
    }
  }, [branchDataById]);

  const fetchRoList = async () => {
    try {
      const response = await fetch(
        `${API_URL}/society/user/getRoList?societyId=${societyDataById?.id}&branchId=${branchDataById?.id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      setRoList(data?.data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return null;
    }
  };

  const roListOptions =
    roList?.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const { mutate: updateRo, isLoading } = useCustomMutation();

  const countWords = (str: string) =>
    str.trim().split(/\s+/).filter(Boolean).length;

  const onSubmitActionRo = (data: FieldValues) => { 
    if (data.noteRo && countWords(data.noteRo) > 100) {
      setError("noteRo", {
        type: "manual",
        message: t("inputValidationErrorStringExceedsLimitWord", {
          label: "Note",
          value: 100,
        }),
      });
      return;
    }
    updateRo({
      url: `${API_URL}/society/roDecision/updateRo`,
      method: "patch",
      values: data,
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      successNotification: (data) => {
        dispatch(fetchBranchByIdData({ id: branchDataById?.id }));
        return {
          message: data?.data?.msg,
          type: "success",
        };
      },
      errorNotification: (data) => {
        return {
          message: data?.response?.data?.msg,
          type: "error",
        };
      },
    });
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",

              display: "flex",
              justifyContent: "space-between",
              minHeight: 120,
            }}
          >
            <Box
              sx={{
                display: "grid",
                px: 5,
                py: 2,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography
                sx={{
                  fontFamily: "Poppins",
                  fontSize: 18,
                  fontWeight: "500!important",
                  color: "white",
                }}
              >
                {branchDataById?.name}
              </Typography>
              <Typography
                sx={{
                  fontFamily: "Poppins",
                  fontSize: 18,
                  fontWeight: "500!important",
                  color: "white",
                }}
              >
                {branchDataById?.branchNo || branchDataById?.branchApplicationNo
                  ? `${t("ppmBranchNumber")}: ${
                      branchDataById?.branchNo
                        ? branchDataById?.branchNo
                        : branchDataById?.branchApplicationNo
                    }`
                  : ""}
              </Typography>
            </Box>
            <img
              src={`data:image/svg+xml;utf8,${encodeURIComponent(
                ArrowBackground2SVG
              )}`}
              alt="Arrow"
              style={{
                width: "120px",
                height: "auto",
                marginRight: 50,
                marginTop: 30,
                display: "block",
              }}
            />
          </Box>
        </Box>
        <Box sx={{ mt: 4 }}>
          {sectionItems.map((item, index) => {
            return (
              <AccordionComp
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </AccordionComp>
            );
          })}
        </Box>
        {ROQuery?.[0] ? (
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
                display: "grid",
                gap: 2,
              }}
            >
              <Box>
                <Typography className="label" color={"primary"}>
                  {t("kuiri")}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "flex-end",
                }}
              >
                <ButtonOutline
                  sx={{
                    width: "20%",
                    gap: 2,
                  }}
                  //@ts-ignore
                  disabled={ROQuery?.length > 0 ? false : true}
                  onClick={() => handleQueryOpen()}
                >
                  <img height={16} width={15} src="/addDocument.png" />
                  {t("queryHistory")}
                </ButtonOutline>
              </Box>
              <Input
                value={
                  //@ts-ignore
                  ROQuery?.[0]?.note ? ROQuery[0].note : ""
                }
                name="name"
                rows={3}
                multiline
                disabled
                label={t("catatanKuiri")}
              />
            </Box>
          </Box>
        ) : null}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form onSubmit={handleSubmit(onSubmitActionRo)}>
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("responsibleRO")} 
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="roId"
                    control={control}
                    options={roListOptions} 
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    name="noteRo"
                    multiline
                    sx={{
                      minHeight: "126px",
                    }}
                    sxInput={{
                      minHeight: "126px",
                    }}
                  />
                </Grid>
              </Grid>

              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrimary type="submit" disabled={isLoading} sx={{}}>
                  {isLoading ? <CircularProgress size={24} /> : t("update")}
                </ButtonPrimary>
              </Grid>
            </Box>
          </form>

          <Formik<Payload>
            initialValues={defaultValues}
            onSubmit={onSubmit}
            validationSchema={resolver}
            enableReinitialize
            isInitialValid={false}
          >
            <DecisionFormInner<Payload> />
          </Formik>
        </Box>
      </Box>

      <Dialog
        open={dialogSejarahKuiriSaveOpen}
        onClose={() => setDialogSejarahKuiriSaveOpen(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 4 }}>
          <Box
            sx={{
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                pb: 4,
                color: "var(--primary-color)",
              }}
              gap={2}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen)
                }
              />
              <Typography sx={{ fontWeight: "4001important" }}>
                {t("queryHistory")}
              </Typography>
            </Box>
            {/* @ts-ignore */}
            {ROQuery?.length ? (
              // @ts-ignore
              ROQuery?.map((item, index) => (
                <Box sx={{ display: "flex", mb: 4 }} key={item.id}>
                  <Box sx={{ mr: 2 }}>
                    <Box
                      sx={{
                        width: 35,
                        height: 35,
                        borderRadius: "50%",
                        border: `1px solid ${
                          item.finished ? "var(--primary-color)" : "#FF0000"
                        }`,
                        backgroundColor: item.finished
                          ? "var(--primary-color)80"
                          : "#FF000080",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {item.finished ? <CheckedIcon /> : null}
                    </Box>
                    {/* @ts-ignore */}
                    {index !== ROQuery?.length - 1 && !item.finished && (
                      <Box
                        sx={{
                          width: 2,
                          height: "100%",
                          backgroundColor: "#DADADA",
                          ml: 2,
                        }}
                      />
                    )}
                  </Box>
                  <Box sx={{ width: "100%" }}>
                    <Box sx={{ display: "flex", gap: 3 }}>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #5088FF",
                          background: "#5088FF80",
                          borderRadius: "9px",
                          color: "#fff",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Kuiri #{index + 1}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        {new Date(
                          item.createdDate[0],
                          item.createdDate[1] - 1,
                          item.createdDate[2]
                        ).toLocaleDateString("en-GB")}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Pegawai: {item.officerName}
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        p: 3,
                        border: "1px solid #D9D9D9",
                        borderRadius: "14px",
                        width: "100%",
                        mt: 2,
                        minHeight: "150px",
                        position: "relative",
                      }}
                    >
                      <Typography sx={{ mb: 3, color: "#666666" }}>
                        {item.note}
                      </Typography>
                      <span
                        style={{
                          fontFamily: '"Poppins", sans-serif',
                          backgroundColor: item.finished
                            ? "var(--primary-color)80"
                            : "#FF000080",
                          border: `1px solid ${
                            item.finished ? "var(--primary-color)" : "#FF0000"
                          }`,
                          padding: "6px 20px",
                          borderRadius: "18px",
                          color: "#fff",
                          fontSize: "14px",
                          fontWeight: "400",
                          position: "absolute",
                          bottom: "20px",
                          right: "20px",
                        }}
                      >
                        {item.finished ? t("SELESAI") : t("belumselesai")}
                      </span>
                    </Box>
                  </Box>
                </Box>
              ))
            ) : (
              <Typography className="label">{t("noData")}</Typography>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default KeputusanCawanganPendaftaranComp;
