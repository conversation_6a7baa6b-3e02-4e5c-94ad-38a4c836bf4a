import { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";

import { SelectChangeEvent } from "@mui/material/Select";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
// import { OrganizationStepper } from "../organization-stepper";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCustom } from "@refinedev/core";
import Input from "../../../../components/input/Input";
import { useLocation, useNavigate } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useSelector } from "react-redux";
import dayjs from "dayjs";

export const Kaunter = () => {
  const location = useLocation();
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const [societyData, setSocietyData] = useState<any>(null);
  const { amendmentId } = location.state || {};
  const navigate = useNavigate(); 
  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("societyId"); 
  const fasalRedux = useSelector((state: { fasal: any }) => state.fasal.data);
  const createdDate = fasalRedux.createdDate || null;
  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/${encodedId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (data) => {
        setSocietyData(data?.data?.data);
      },
    },
  });

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const handleCetak = async () => { 
    try {
      if (encodedId) {
        // const d = new Date();
        // d.setHours(d.getHours() + 8);
        // let dateStr = d.toISOString();
        // dateStr = dateStr.slice(0, -5);
        // const decodedId = atob(encodedId);
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: "Pindaan Undang-Undang (Pembayaran KAUNTER)",
            paymentMethod: "C", // O = online, C = counter
            societyId: encodedId,
            registerDateTime: dayjs(createdDate).format(
              "YYYY-MM-DDTHH:mm:ss"
            ), 
            amount: 10.0,
            referenceNo: amendmentId,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  // @ts-ignore
  // const societyDataRedux = useSelector((state) => state.societyData.data)
  // console.log(societyDataRedux)
  useEffect(() => {
    const fetchSocietyData = async () => {
      try {
        if (encodedId) {
          /*const response = await fetch(`${API_URL}/society/${atob(encodedId)}`, {
            headers: {
              portal: localStorage.getItem("portal") || "",
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          });
          const data = await response.json();
          setSocietyData(data);*/
        }
      } catch (error) {
        console.error("Error fetching society data:", error);
      }
    };

    fetchSocietyData();
  }, [encodedId]);

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("pengesahan")} {t("payment")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2,
                }}
              >
                {t("pengurusanPerlembangaanPayment")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={1}>
                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyData?.societyName || ""}
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  value={amendmentId}
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value="Pindaan Undang-Undang (Pembayaran KAUNTER)"
                />
                <Input disabled label={t("paymentAmount")} value="RM 10.00" />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2,
                }}
              >
                {t("noteKaunter")}
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 3,
              }}
            >
              <ButtonOutline onClick={() => navigate("/pertubuhan")}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>

      {/* <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper activeStep={activeStep} />

        <InfoQACard />
      </Box> */}
    </Box>
  );
};

export default Kaunter;
