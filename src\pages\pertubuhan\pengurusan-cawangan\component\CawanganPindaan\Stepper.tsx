import React from "react";
import {
  Stepper,
  Step,
  StepLabel,
  useMedia<PERSON><PERSON>y,
  Theme,
  StepConnector,
  Box,
  Typography,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CheckIcon from "@mui/icons-material/Check";
import { useSelector } from "react-redux";

const steps = ["selectBranch", "fillMeeting", "selectBranchAmend", "bayaran"];

interface CawanganPindaanStepperProps {
  activeStep: number;
  handleClick?: (step: number) => void;
}

export const StepperCawanganPindaan: React.FC<CawanganPindaanStepperProps> = ({
  activeStep,
  handleClick,
}) => {
  const { t } = useTranslation();
  const isView = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.isView
  );

  const finalStep = isView
    ? steps.filter((stepKey) => stepKey !== "bayaran")
    : steps;

  const handleStepClick = (step: number) => {
    handleClick?.(step);
  };

  return (
    <Box
      sx={{
        padding: 3,
        backgroundColor: "white",
        borderRadius: "15px",
        maxHeight: "60vh",
        maxWidth: "18vw",
      }}
    >
      <Typography
        sx={{
          mb: 4,
          fontSize: "16px",
          color: "var(--primary-color)",
          fontWeight: "500 !important",
        }}
      >
        {t("stepAmendmentBranchNameAndAddress")}
      </Typography>
      <Stepper
        sx={{ cursor: "pointer" }}
        activeStep={activeStep}
        orientation="vertical"
        connector={null}
      >
        {finalStep.map((stepKey, index) => (
          <Step key={stepKey} onClick={() => handleStepClick(index)}>
            <StepLabel
              icon={
                <Box
                  sx={{
                    width: 20,
                    height: 20,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: "4px",
                    border: `2px solid ${
                      index < activeStep
                        ? "var(--primary-color)"
                        : index === activeStep
                        ? "var(--primary-color)"
                        : "#DADADA"
                    }`,
                    backgroundColor:
                      index < activeStep
                        ? "var(--primary-color)"
                        : "transparent",
                  }}
                />
              }
            >
              <Typography
                sx={{
                  fontWeight: "500 !important",
                  color:
                    index < activeStep || index === activeStep
                      ? "var(--primary-color)"
                      : "#DADADA",
                }}
              >
                {t(stepKey)}
              </Typography>
            </StepLabel>
          </Step>
        ))}
      </Stepper>
    </Box>
  );
};
