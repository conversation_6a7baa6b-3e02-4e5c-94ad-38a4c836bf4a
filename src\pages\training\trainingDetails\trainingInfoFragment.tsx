import React, {useState} from "react";
import {Typography} from "@mui/material";
import Box from "@mui/material/Box";
import {DurationIcon} from "@/components/icons/duration";
import CertificateFragment from "@/pages/training/certificateDetails/certificateFragment";
import {useTranslation} from "react-i18next";
import {ButtonPrimary} from "@/components";
import TrainingQuiz from "@/pages/training/trainingDetails/trainingQuiz";
import FeedBackFragment from "@/pages/training/trainingDetails/feedBackFragment";

interface TrainingInfoFragmentProps {
  course:any;
  item: any[],
  quiz?: boolean,
  handleNext: (currentPage: number) => void,
  isReview?: boolean,
}

const TrainingInfoFragment: React.FC<TrainingInfoFragmentProps> = ({course, item, quiz=false,handleNext, isReview = false}) => {

  const {t, i18n} = useTranslation();

  const [page, setPage] = useState(0);
  const [quizPage, setQuizPage] = useState(0);
  const [quizFinished, setQuizFinished] = useState(false);
  const [score, setScore] = useState(0);
  const [passed, setPassed] = useState(false);
  const [time, setTime] = useState("");
  const [enrollmentId, setEnrollmentId] = useState();

  const goNext = (currentPage: number) => {
    if(currentPage < item.length+3){
      console.log("goNext",currentPage);
      setPage(currentPage + 1);
      handleNext(currentPage + 1);
    }
  }

  const hour = Math.floor(course.duration/60);
  const minute = course.duration % 60;

  const handleFinish = (submitData: any) => {
    setPage(page + 1);
    setScore(submitData.score);
    setPassed(submitData.passed);
    setQuizFinished(true);
  }

  console.log("item",item);
  console.log("page",page);
  return (
    <>
      {quiz && page===item.length && !quizFinished ? <TrainingQuiz handleFinish={handleFinish} courseId={course.trainingCourseId ?? course.id} /> :
      <Box
        sx={{
          //flex: 5,
          width: "100%",
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        {page === item.length+2 ? <FeedBackFragment courseId={course.trainingCourseId} enrollId={course.id}></FeedBackFragment> :
          <Box
            sx={{
              height: "100%",
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              //flex: 5,
              px: 5,
              py: 2,
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: "#666666",
                //pt: 3,
                fontWeight: "500",
                fontSize: 20,
              }}
            >
              {course.title}
            </Typography>
            <Box sx={{
              p: 0,
              m: 0,
              height: 30,
              display: "flex",
              flexDirection: "row",
            }}>
              <Box
                sx={{py: 1, px: 1}}
              >
                <DurationIcon/>
              </Box>
              <Box
                sx={{py: 1, px: 0}}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    lineHeight: "18px",
                    fontWeight: "400",
                    fontSize: 12,
                  }}
                >
                  {`${hour} Jam ${minute} Minit`}
                </Typography>
              </Box>
            </Box>
            {(page === 0 && isReview) ? <>
              <Box sx={{
                p: 0,
                m: 0,
                height: 30,
                display: "flex",
                flexDirection: "row",
              }}>
                <Box
                  sx={{py: 1, px: 1}}
                >
                  <Typography
                    sx={{
                      color: "#666666",
                      lineHeight: "18px",
                      fontWeight: "400",
                      fontSize: 12,
                    }}
                  >
                    {`Status: Berjaya`}
                  </Typography>
                </Box>
                <Box
                  sx={{py: 1, px: 1}}
                >
                  <Typography
                    sx={{
                      color: "#666666",
                      lineHeight: "18px",
                      fontWeight: "400",
                      fontSize: 12,
                    }}
                  >
                    {`${score} %`}
                  </Typography>
                </Box>
                <Box
                  sx={{py: 1, px: 0}}
                >
                  <Typography
                    sx={{
                      color: "#666666",
                      lineHeight: "18px",
                      fontWeight: "400",
                      fontSize: 12,
                    }}
                  >
                    {`${hour} Jam ${minute} Minit`}
                  </Typography>
                </Box>
              </Box>
            </> : <></>
            }
            {page !== item.length ?
              <>
                <Box sx={{}}>
                  <img
                    src={item[page].image ?? '/latihanSample/images5.jpg'}
                    alt="Logo"
                    style={{
                      //height: "80px", // Default height
                      //marginRight: "10px",
                      height: "500px",
                      width: "100%",
                      objectFit: "cover",
                    }}
                    className="responsive-logo"
                  />
                </Box>
                <Box sx={{mt: 5}}>
                  <Typography
                    sx={{
                      color: "#666666",
                      lineHeight: "18px",
                      fontWeight: "700",
                      fontSize: 20,
                    }}
                  >
                    {`Keterangan Artikel`}
                  </Typography>
                  <Typography
                    sx={{
                      mt: 5,
                      color: "#666666",
                      lineHeight: "18px",
                      fontWeight: "400",
                      fontSize: 14,
                    }}
                  >
                    {item[page].description}
                  </Typography>
                </Box>
              </> : <></> }
              {page == item.length+1 ?
              <>
                <Box sx={{height:"85%", display:"flex", flexDirection:"column", justifyContent:"center"}}>
                  <Box>
                    <Typography
                      sx={{
                        color: passed ? "#84D819" : "#FF0000",
                        fontWeight: "500",
                        fontSize: 45,
                        textAlign: "center",
                      }}
                    >
                      {`${score} %`}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography
                      sx={{
                        color: "#0CA6A6",
                        fontWeight: "400",
                        fontSize: 14,
                        textAlign: "center"
                      }}
                    >
                      {course.title}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography
                      sx={{
                        color: "#55556D",
                        fontWeight: "400",
                        fontSize: 14,
                        textAlign: "center"
                      }}
                    >
                      {`Latihan tamat. Sila teruskan untuk menuntut sijil anda. Tahniah anda telah lulus sukatan pelajaran dan peperiksaan ini.`}
                    </Typography>
                  </Box>
                  <Box sx={{display: "flex", mt: 1, flexDirection: "row",justifyContent: "center", gap: 3}}>
                    <Typography
                      sx={{
                        color: "#666666",
                        fontWeight: "500",
                        fontSize: 14,
                        textAlign: "center"
                      }}
                    >
                      {`Masa: 2:17:04`}
                    </Typography>
                    <Typography
                      sx={{
                        color: "#666666",
                        fontWeight: "500",
                        fontSize: 14,
                        textAlign: "center"
                      }}
                    >
                      {`Point diperolehi: + 10 Pts`}
                    </Typography>
                  </Box>
                </Box>
              </> : <></> }
            {page !== item.length+2 ?
              <Box sx={{display: "flex", mt: 1, justifyContent: "flex-end"}}>
                <ButtonPrimary
                  variant="outlined"
                  sx={{
                    borderColor: "#0CA6A6",
                    bgcolor: "#0CA6A6",
                    "&:hover": {bgcolor: "#0CA6A6", borderColor: "#0CA6A6",},
                    color: "#fff",
                    fontWeight: "400",
                  }}
                  onClick={() => goNext(page)}
                >
                  {t("next")}
                </ButtonPrimary>
              </Box> : <></>}
          </Box>}
      </Box>}
    </>
  );
}

export default TrainingInfoFragment;
