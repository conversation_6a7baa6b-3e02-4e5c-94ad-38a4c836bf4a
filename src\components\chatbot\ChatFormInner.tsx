import { useRef, KeyboardEvent, useState, useEffect, forwardRef, useImperativeHandle } from "react";
import { ChatMessageOnlyText } from "./types";
import { Controller, useFormContext } from "react-hook-form";
import { Box, IconButton, TextField, Tooltip, CircularProgress, Fade, keyframes } from "@mui/material";
import { Send, Mic, MicOff } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { useSpeech } from "@/contexts/chatbot/SpeechContext";
import { setupSpeechRecognition } from '@/components/chatbot/SpeechUtils';
import { normalizeRosieName } from '@/components/chatbot/SpeechUtils';
import DeleteIcon from '@mui/icons-material/Delete';

/**
 * Component for the chat input form
 * Clean version with ElevenLabs integration and conditional speech controls
 */
export const ChatFormInner = forwardRef<{ focus: () => void }, { isWidget?: boolean }>(
  ({ isWidget = false }, ref) => {
    const { i18n } = useTranslation();
    const location = useLocation();
    const buttonRef = useRef<HTMLButtonElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const { formState, control, setValue, watch } = useFormContext<ChatMessageOnlyText>();

    // Get speech recognition state and UI control from the speech context
    const { speechRecognition, showSpeechControls } = useSpeech();

    // Check if we're on the chatbot page
    const isOnChatbotPage = location.pathname === "/chatbot";

    const loading = formState.isSubmitting;
    const recognitionRef = useRef<any>(null);
    const allResultsRef = useRef<string[]>([]);
    const [isListening, setIsListening] = useState(false);
    const [liveTranscript, setLiveTranscript] = useState("");
    const [listenStart, setListenStart] = useState<number | null>(null);
    const [timer, setTimer] = useState('00:00');

    const buttonDisabled = loading || (!formState.isValid && !isListening);
    const locale = ["bm", "my"].includes(i18n.language) ? "ms" : i18n.language;
    const placeholder = locale === "ms" ? "Tulis pesan..." : "Type a message...";

    // Check if input is empty (for showing mic vs send button)
    const currentText = watch('text') || '';
    const isInputEmpty = !currentText.trim() && !isListening;

    // Speech recognition tooltips
    const startListeningTooltip = locale === "ms" ? "Mulakan pengecaman suara" : "Start speech recognition";
    const stopListeningTooltip = locale === "ms" ? "Hentikan pengecaman suara" : "Stop speech recognition";

    // WhatsApp-style animations
    const pulseAnimation = keyframes`
      0% {
        box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
      }
    `;

    const rippleAnimation = keyframes`
      0% {
        transform: scale(0.8);
        opacity: 1;
      }
      100% {
        transform: scale(2.4);
        opacity: 0;
      }
    `;

    // Expose focus method to parent component
    useImperativeHandle(ref, () => ({
      focus: () => {
        if (inputRef.current && !isListening && !loading) {
          inputRef.current.focus();
        }
      }
    }), [isListening, loading]);

    // Focus the input field when component mounts
    useEffect(() => {
      const timer = setTimeout(() => {
        if (inputRef.current && !isListening && !loading) {
          inputRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }, []);

    // Focus the input field after form submission (when loading changes from true to false)
    useEffect(() => {
      if (!loading && !isListening) {
        const timer = setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 100);
        return () => clearTimeout(timer);
      }
    }, [loading, isListening]);

    // Timer effect
    useEffect(() => {
      let interval: NodeJS.Timeout;
      if (isListening && listenStart) {
        setTimer('00:00');
        interval = setInterval(() => {
          const elapsed = Math.floor((Date.now() - listenStart) / 1000);
          const min = String(Math.floor(elapsed / 60)).padStart(2, '0');
          const sec = String(elapsed % 60).padStart(2, '0');
          setTimer(`${min}:${sec}`);
        }, 250);
      } else {
        setTimer('00:00');
      }
      return () => clearInterval(interval);
    }, [isListening, listenStart]);

    /**
     * Handles keyboard events for the input field
     */
    const handleInputKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
      if (event.key === "Enter" && !event.shiftKey && !loading) {
        event.preventDefault();
        buttonRef.current?.click();
      }
    };

    /**
     * Handles toggling speech recognition
     */
    const handleToggleSpeechRecognition = () => {
      if (isListening) {
        setIsListening(false);
        setListenStart(null);
        if (recognitionRef.current) recognitionRef.current.stop();
        setTimeout(() => {
          if (allResultsRef.current.length > 0) {
            let fullText = allResultsRef.current.join(' ');
            fullText = normalizeRosieName(fullText);
            setValue("text", fullText);
            setTimeout(() => {
              if (buttonRef.current) {
                buttonRef.current.click();
              }
            }, 100);
          }
          setLiveTranscript("");
          allResultsRef.current = [];
        }, 500);
      } else {
        setIsListening(true);
        setListenStart(Date.now());
        setLiveTranscript("");
        allResultsRef.current = [];
        recognitionRef.current = setupSpeechRecognition({
          locale: locale === "ms" ? "ms-MY" : "en-US",
          onInterim: (interim) => setLiveTranscript(interim),
          onFinal: (final) => {
            allResultsRef.current.push(final);
            setLiveTranscript("");
          },
          onError: () => setIsListening(false),
          onEnd: () => setIsListening(false),
        });
        recognitionRef.current.start();
      }
    };

    useEffect(() => {
      return () => {
        allResultsRef.current = [];
        if (recognitionRef.current) recognitionRef.current.stop();
      };
    }, []);

    // Cancel/Trash button handler
    const handleCancelListening = () => {
      setIsListening(false);
      setListenStart(null);
      setLiveTranscript("");
      allResultsRef.current = [];
      if (recognitionRef.current) recognitionRef.current.stop();
    };

    // Send button handler (when listening)
    const handleSendListening = () => {
      setIsListening(false);
      setListenStart(null);
      if (recognitionRef.current) recognitionRef.current.stop();
      setValue("text", liveTranscript, { shouldValidate: true });
      setTimeout(() => {
        if (buttonRef.current) {
          buttonRef.current.click();
        }
        setLiveTranscript("");
        allResultsRef.current = [];
        setTimeout(() => {
          if (inputRef.current) inputRef.current.focus();
        }, 100);
      }, 100);
    };

    // Simple animated waveform (fake bars)
    const Waveform = () => (
      <Box sx={{ display: 'flex', alignItems: 'center', height: 24, mx: 2 }}>
        {[0,1,2,3,4,5,6,7].map(i => (
          <Box key={i} sx={{
            width: 3,
            height: `${8 + Math.abs((Date.now()/100 + i*2)%16-8)}px`,
            bgcolor: 'primary.main',
            borderRadius: 1,
            mx: 0.3,
            transition: 'height 0.2s',
            animation: `wave 1s infinite linear`,
            animationDelay: `${i*0.1}s`,
            '@keyframes wave': {
              '0%': { height: '8px' },
              '50%': { height: '24px' },
              '100%': { height: '8px' },
            },
          }} />
        ))}
      </Box>
    );

    return (
      <Box sx={{ 
        display: "flex", 
        alignItems: "flex-end", 
        gap: 1,
        p: 2,
        bgcolor: 'background.paper',
        borderTop: 1,
        borderColor: 'divider',
        position: 'relative'
      }}>
        {isListening ? (
          // WhatsApp-style listening bar
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            {/* Trash/Cancel */}
            <IconButton color="error" onClick={handleCancelListening}>
              <DeleteIcon />
            </IconButton>
            {/* Timer */}
            <Box sx={{ minWidth: 40, textAlign: 'center', fontWeight: 500 }}>{timer}</Box>
            {/* Waveform */}
            <Waveform />
            {/* Send button */}
            <IconButton color="primary" onClick={handleSendListening} disabled={!liveTranscript.trim()} sx={{ ml: 'auto', bgcolor: 'primary.main', color: 'white', '&:hover': { bgcolor: 'primary.dark' } }}>
              <Send />
            </IconButton>
          </Box>
        ) : (
          <Controller
            control={control}
            name="text"
            render={({ field }: { field: any }) => (
              <TextField
                type="text"
                {...field}
                inputRef={inputRef}
                fullWidth
                placeholder={placeholder}
                size="small"
                multiline
                maxRows={4}
                disabled={loading || isListening}
                onKeyDown={handleInputKeyDown}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    bgcolor: 'grey.50',
                    '&:hover': {
                      bgcolor: 'grey.100',
                    },
                    '&.Mui-focused': {
                      bgcolor: 'background.paper',
                    },
                    ...(isListening ? {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                        borderWidth: 2,
                      }
                    } : {})
                  }
                }}
                value={field.value}
              />
            )}
          />
        )}

        {/* Action button container */}
        <Box sx={{ position: 'relative' }}>
          {/* Speech recognition button - only show when input is empty and speech controls are enabled */}
          {showSpeechControls && speechRecognition.isElevenLabsAvailable && isInputEmpty && !isListening && (
            <Fade in={true} timeout={200}>
              <Tooltip title={startListeningTooltip}>
                <IconButton
                  color="primary"
                  onClick={handleToggleSpeechRecognition}
                  disabled={loading}
                  sx={{
                    width: 48,
                    height: 48,
                    bgcolor: 'primary.main',
                    color: 'white',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                    '&:disabled': {
                      bgcolor: 'grey.300',
                      color: 'grey.500',
                    }
                  }}
                >
                  <Mic />
                </IconButton>
              </Tooltip>
            </Fade>
          )}

          {/* Send button - show when typing or listening */}
          {(!isInputEmpty || isListening) && (
            <Fade in={true} timeout={200}>
              <Tooltip title={isListening ? stopListeningTooltip : "Send message"}>
                <IconButton
                  ref={buttonRef}
                  color="primary"
                  type={isListening ? "button" : "submit"}
                  onClick={isListening ? handleToggleSpeechRecognition : undefined}
                  disabled={buttonDisabled}
                  sx={{
                    width: 48,
                    height: 48,
                    bgcolor: 'primary.main',
                    color: 'white',
                    position: 'relative',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                    '&:disabled': {
                      bgcolor: 'grey.300',
                      color: 'grey.500',
                    },
                    // ...(isListening && {
                    //   animation: `${pulseAnimation} 1.5s infinite`,
                    //   '&::before': {
                    //     content: '""',
                    //     position: 'absolute',
                    //     top: '50%',
                    //     left: '50%',
                    //     width: 48,
                    //     height: 48,
                    //     borderRadius: '50%',
                    //     bgcolor: 'primary.main',
                    //     transform: 'translate(-50%, -50%)',
                    //     animation: `${rippleAnimation} 1.5s infinite`,
                    //     zIndex: -1,
                    //   }
                    // })
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : isListening ? (
                    <MicOff />
                  ) : (
                    <Send />
                  )}
                </IconButton>
              </Tooltip>
            </Fade>
          )}
        </Box>
      </Box>
    );
  }
);
