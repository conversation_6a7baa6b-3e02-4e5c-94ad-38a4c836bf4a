import { Box, Typography } from "@mui/material";
import React from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";

const CawanganAjkNavigationTabs = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const disabled = location.state?.disabled ?? false;
  const branchId = location.state?.branchId ?? 0;

  const tabs = [
    { label: "Jawatankuasa", path: "jawatankuasa" },
    { label: "Ahli", path: "ahli" },
    { label: "Pemegang Amanah", path: "pemegang-amanah" },
    { label: "Pegawai", path: "pegawai" },
    { label: "Juruaudit", path: "juruaudit" },
    { label: "Aliran Tugas", path: "aliran-tugas" },
  ];

  const handleNavigation = (path: any) => {
    navigate(`/pertubuhan/society/${id}/senarai/cawangan/view/ajk/${path}`,{
      state: {
        disabled: disabled,
        branchId: branchId
      },
    });
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        backgroundColor: "white",
        paddingBottom: "8px",
        justifyContent: "space-between",
        borderRadius: "10px",
        px: 3,
        py: 1,
        mb: 1,
      }}
    >
      {tabs.map((tab, index) => {
        // Tentukan apakah tab saat ini aktif berdasarkan URL
        const isActive = location.pathname.includes(tab.path);

        return (
          <React.Fragment key={index}>
            <Box
              key={index}
              onClick={() => {
                if(!disabled)handleNavigation(tab.path)
              }}
              sx={{
                cursor: "pointer",
                color: isActive ? "#67D1D1" : "#DADADA",
                transition: "color 0.3s, border-bottom 0.3s",
              }}
            >
              <Typography sx={{ fontWeight: "500 !important" }}>
                {tab.label}
              </Typography>
            </Box>
            {
              tabs.length - 1 != index ?
                <Box
                  sx={{
                    color: "#DADADA"
                  }}
                >
                  <Typography sx={{ fontWeight: "100 !important" }}>
                    |
                  </Typography>
                </Box> : null
            }
          </React.Fragment>
        );
      })}
    </Box>
  );
};

export default CawanganAjkNavigationTabs;
