import Box from "@mui/material/Box";
import { useTranslation } from "react-i18next";
import { Switch as CustomSwitch } from "@/components/switch";

import { Grid, Typography, Stack } from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import CreateAset from "./CreateAset";
import CreateLiabiliti from "./CreateLiabiliti";
import { useLocation, useNavigate } from "react-router-dom";
import useQuery from "@/helpers/hooks/useQuery";
import { FieldValues, SubmitHandler, useForm } from "react-hook-form";
import { useState } from "react";
import { useCustomMutation } from "@refinedev/core";
import { filterEmptyValuesOnObject } from "@/helpers/utils";
import { API_URL } from "@/api";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";

export const CawanganCreateAsetDanLiabiliti = () => {
  const { t } = useTranslation();
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  //@ts-ignore
  const isviewStatement = useSelector((state) => state?.statementData?.isViewStatement);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;

  const navigate = useNavigate();

  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const handleNextActions = () => {
    handleNext();
    navigate(`../sumbangan`, {
      state: {
        societyId: societyId,
        statementId: statementId,
        year: year,
      },
    });
  };

  const financialForm = useForm<FieldValues>({
    defaultValues: {
      buildingAsset: 0,
      investmentAsset: 0,
      intangibleAsset: 0,
      landAsset: 0,
      vehicleAsset: 0,
      machineAsset: 0,
      furnitureAsset: 0,
      officeAsset: 0,

      assetOnHand: 0,
      bankAsset: 0,
      accountAsset: 0,
      inventoryAsset: 0,
      investAsset: 0,
      depositAsset: 0,
      taxAsset: 0,

      totalAsset: 0,

      creditorLiability: 0,
      taxLiability: 0,
      loanLiability: 0,

      debtLiability: 0,
      deferredTaxLiability: 0,
      borrowLiability: 0,

      totalLiability: 0,
    },
  });

  const [id, setId] = useState<number | undefined>();

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const { mutate: saveStatementFinancial, isLoading: isLoading } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = filterEmptyValuesOnObject(data);
    console.log(payload); 

    saveStatementFinancial(
      {
        url: id
        ? `${API_URL}/society/statement/statement-financial/${statementId}/edit`
        : `${API_URL}/society/statement/statement-financial/create?societyId=${societyId}&statementId=${statementId}`, 
        method: "put",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
      },
      {
        onSuccess: (data) => {
          const id = data?.data?.data?.id
          setId(id);
        },
      }
    );
  };

  useQuery({
    url: `society/statement/statement-financial/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const financialStatement = data?.data?.data || [];

      Object.entries(financialStatement).forEach(([key, value]) => {
        financialForm.setValue(key, value);
      });

      setId(financialStatement.id);
    },
  });

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <span style={{ fontSize: 12 }}>{t("assetsLiabilitiesInfo")}</span>
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("pengakuan")}
          </Typography>

          <Grid container spacing={10} alignItems="flex-start" sx={{ mb: 1 }}>
            <Grid item sm={10}>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: "400 !important",
                }}
              >
                <span style={{ fontSize: 12 }}>
                  {t("peyataTahunan_pendapatan_pengakuan")}
                </span>
              </Typography>
            </Grid>
            <Grid item sm={2}>
              <CustomSwitch checked={true} disabled={isDisabled} />
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <form onSubmit={financialForm.handleSubmit(onSubmit)}>
          <CreateAset
            control={financialForm.control}
            watch={financialForm.watch}
            setValue={financialForm.setValue}
            getValues={financialForm.getValues}
            t={t}
          />

          <CreateLiabiliti
            control={financialForm.control}
            watch={financialForm.watch}
            setValue={financialForm.setValue}
            getValues={financialForm.getValues}
            t={t}
          />

          <Stack
            direction="row"
            spacing={2}
            sx={{ mt: 4, pl: 1 }}
            justifyContent="flex-end"
          >
            <ButtonOutline onClick={handleBackActions}>
              {t("back")}
            </ButtonOutline>
            <ButtonPrimary type="submit">{t("save")}</ButtonPrimary>
          </Stack>
        </form>
        <Stack
          direction="row"
          spacing={2}
          sx={{ pl: 1, mt: 2 }}
          justifyContent="flex-end"
        >
          <ButtonPrimary onClick={handleNextActions}>{t("next")}</ButtonPrimary>
        </Stack>
      </Box>
    </>
  );
};

export default CawanganCreateAsetDanLiabiliti;
