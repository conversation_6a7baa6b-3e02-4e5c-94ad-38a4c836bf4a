import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setBranchByIdDataRedux, setBranchByIdError, setBranchByIdLoading } from '../branchByIdDataReducer';

export const fetchBranchByIdData = createAsyncThunk(
  'branchById/fetchData',
  async ({ id }: { id: string },
    { dispatch }) => {
    dispatch(setBranchByIdLoading(true));
    try {
      const response = await fetch(`${API_URL}/society/branch/getById/${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
      const data = await response.json();
      // console.log('data from think', data.data)
      dispatch(setBranchByIdDataRedux(data?.data));
    } catch (error: any) {
      dispatch(setBranchByIdError(error.message));
    } finally {
      dispatch(setBranchByIdLoading(false));
    }
  }
);
