import React, { useState } from "react";
import { Box, Typography } from "@mui/material"; // Assuming you're using Material-UI for Box
import CustomMiniTab from "@/components/customMiniTab";
import NotificationList from "./notificicationList";
import { t } from "i18next";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

interface NotificationListDashboardProps {
  isInternal?: boolean;
}

// Define the Notification interface
const NotificationListDashboard: React.FC<NotificationListDashboardProps> = ({
  isInternal,
}) => {
  const [activeTab, setActiveTab] = useState<number>(0);

  const groupedNotifications = useSelector(
    (state: RootState) => state.notifications.groupedNotifications
  );

  const tabs = [
    {
      label: t("total"),
      content: (
        <NotificationList
          //@ts-ignore
          notifications={Object.values(groupedNotifications).flat()}
        />
      ),
    },
  ];

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        overflow: "hidden",
      }}
    >
      <Typography sx={{ mt: 2, ml: 2 }} className="sub-step-login">
        {t("notifikasi")}
      </Typography>

      <Box sx={{ flexGrow: 1, overflow: "hidden" }}>
        <CustomMiniTab
          tabs={tabs}
          variant="standard"
          removePaddingOnContent={true}
          onTabChange={(index) => console.log("Tab changed:", index)}
        />
      </Box>
    </Box>
  );
};

export default NotificationListDashboard;
