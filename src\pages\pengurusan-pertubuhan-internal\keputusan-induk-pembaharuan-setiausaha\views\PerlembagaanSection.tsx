import { Box, Grid, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useCustom } from "@refinedev/core";
import { ApplicationStatus, ConstitutionType } from "../../../../helpers/enums";
import FasalContent from "../../../../components/FasalContent";
import { useKeputusanIndukPembaharuanSetiausahaContext } from "..";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

export const PerlembagaanSection = () => {
  const { t } = useTranslation();
  const [fasal, setFasal] = useState([]);

  const { societyDetailData } = useKeputusanIndukPembaharuanSetiausahaContext();

  const { data: clauseContentData, isLoading: loadingData } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyDetailData?.id,
        status: ApplicationStatus["AKTIF"],
      },
    },
    queryOptions: {
      enabled: !!societyDetailData?.id,
    },
  });

  const clauseContent = clauseContentData?.data?.data?.data || [];
  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const allConstitutions = constitutionData?.data?.data || [];
  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyDetailData?.id}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const constitutionType =
    societyData?.data?.data?.constitutionType || ConstitutionType.IndukNGO[1];

  const generatePerlembagaanAfter = () => {
    if (constitutionType) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContent.find((item: any) =>
              item.clauseNo
                ? Number(item.clauseNo) === Number(clause.id)
                : item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          setFasal(item.clauseContents);
        }
      });
    } else {
      setFasal([]);
    }
  };

  useEffect(() => {
    if (clauseContent) {
      generatePerlembagaanAfter();
    }
  }, [constitutionType, constitutionData, clauseContent]);

  const isLoading = loadingData || isConstitutionLoading;
  return (
    <Box>
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #D9D9D9",
          borderRadius: "14px",
          overflow: "hidden",
        }}
      >
        <Typography sx={subTitleStyle}>{t("checkTheConstitution")}</Typography>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            {!isLoading && fasal.length > 0 && (
              <FasalContent fasalContent={fasal} />
            )}
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default PerlembagaanSection;
