import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { ButtonOutline } from "../../../../components/button";
import { useEffect } from "react";
import { useCustomMutation } from "@refinedev/core";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
} from "../../../../helpers/enums";
import Input from "../../../../components/input/Input";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { getLocalStorage } from "../../../../helpers/utils";
import dayjs from "dayjs";
import { capitalizeWords, useQuery } from "@/helpers";

export const ViewJuruAudit: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleBack = () => {
    navigate(-1);
  };

  const location = useLocation();
  const auditorId = location.state?.auditorId;

  const {
    // addressList,
    // fetchAddressList,
    auditor,
    fetchAuditor,
    members,
    fetchMembers,
  } = usejawatankuasaContext();

  useEffect(() => {
    // fetchAddressList();
    // console.log(addressList);
    fetchMembers();
  }, [fetchMembers]);

  useEffect(() => {
    console.log("Members data updated:", members);
  }, [members]);

  const { data: addressListResponse, isLoading: isLoadingAddressList } =
    useQuery<{ data: any }>({
      url: "society/admin/address/list",
    });
  const addressListData = addressListResponse?.data?.data ?? [];

  const occupationList = getLocalStorage("occupation_list", []);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const defaultFormValues = {
    id: "",
    societyId: "",
    societyNo: "",
    branchId: "",
    branchNo: "",
    statementId: "",
    auditorType: "",
    titleCode: "",
    name: "",
    licenseNo: "",
    companyName: "",
    companyNo: "",
    gender: "",
    nationalityStatus: "",
    identificationType: "",
    identificationNo: "",
    dateOfBirth: "",
    placeOfBirth: "",
    employmentCode: "",
    address: "",
    countryCode: "",
    stateCode: "",
    districtCode: "",
    smallDistrictCode: "",
    city: "",
    postcode: "",
    email: "",
    telephoneNo: "",
    phoneNo: "",
    appointmentDate: "",
    createdBy: "",
    createdDate: "",
    modifiedBy: "",
    modifiedDate: "",
    status: "",
    deleteStatus: "",
    pemCaw: "",
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    reset: resetForm,
  } = useForm<FieldValues>({
    defaultValues: defaultFormValues,
  });

  useEffect(() => {
    if (auditorId) {
      fetchAuditor();
      if (auditor) {
        Object.entries(auditor).forEach(([key, value]) => {
          if (key === 'nationalityStatus') {
            // Convert string "1" or "2" to number
            setValue(key, Number(value));
          } else {
            setValue(key, value);
          }
        });
      }
    }
  }, [fetchAuditor]);

  const { id: societyId } = useParams();

  const { mutate: saveAuditor, isLoading: isLoadingSaveAuditor } =
    useCustomMutation();

  const fillFormFields = (option: any) => {
    setValue("name", option?.name ?? "-");
    let nationality = option?.nationalityStatus;
    // Handle both string numbers and text values
    if (option?.nationalityStatus === "Warganegara" || option?.nationalityStatus === "1") {
      nationality = 1;
    } else if (option?.nationalityStatus === "Bukan Warganegara" || option?.nationalityStatus === "2") {
      nationality = 2;
    }
    if (nationality) {
      setValue("nationalityStatus", Number(nationality));
    }
    setValue("identificationType", option?.identificationType ?? "-");
    setValue("gender", option?.gender ?? "-");
    setValue("committeeName", option?.id);
    setValue("titleCode", option?.titleCode);
    setValue("identificationNo", option?.identificationNo);
    setValue("dateOfBirth", option?.dateOfBirth);
    setValue("placeOfBirth", option?.placeOfBirth);
    setValue("employmentCode", option?.jobCode);
    setValue("address", option?.residentialAddress);
    setValue("postcode", option?.residentialPostcode);
    setValue("stateCode", option?.residentialStateCode);
    setValue("districtCode", option?.residentialDistrictCode);
    setValue("city", option?.residentialCity);
    setValue("email", option?.email);
    setValue("phoneNo", option?.phoneNumber);
    setValue("telephoneNo", option?.telephoneNumber);
  };

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("pilihanJuruaudit")}
        </Typography>

        <Controller
          disabled
          name="auditorType"
          control={control}
          defaultValue={getValues("auditorType")}
          render={({ field }) => (
            <Input
              {...field}
              label={t("auditorType")}
              type="select"
              required
              onChange={(e) => setValue("auditorType", e.target.value)}
              options={[
                { value: "L", label: "Bertauliah" },
                { value: "D", label: "Dalaman" },
              ]}
            />
          )}
        />
        {watch("auditorType") === "D" && (
          <Controller
            disabled
            name="committeeName"
            control={control}
            defaultValue={getValues("committeeName")}
            render={({ field }) => {
              // Simply display the name from the form data
              const displayName = getValues("name") || "-";
              console.log("Display name from form:", displayName);

              return (
                <Input
                  {...field}
                  disabled
                  label={t("namaAhliPertubuhan")}
                  type="text"
                  value={displayName}
                />
              );
            }}
          />
        )}
      </Box>

      {watch("auditorType") === "D" && (
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatJuruauditDalaman")}
          </Typography>

          <Controller
            disabled
            name="appointmentDate"
            control={control}
            render={({ field }) => {
              return (
                <Input
                  required
                  {...field}
                  onChange={(newValue) =>
                    setValue("appointmentDate", newValue.target.value)
                  }
                  value={
                    getValues("appointmentDate")
                      ? dayjs(getValues("appointmentDate")).format("DD-MM-YYYY")
                      : ""
                  }
                  label={t("tarikhLantik")}
                  type="date"
                />
              );
            }}
          />

          <Controller
            disabled
            name="titleCode"
            control={control}
            defaultValue={getValues("titleCode")}
            render={({ field }) => (
              <Input
                {...field}
                required
                disabled
                label={t("title")}
                type="select"
                options={ListGelaran}
              />
            )}
          />

          <Controller
            disabled
            name="name"
            control={control}
            defaultValue={getValues("name")}
            render={({ field }) => (
              <Input
                {...field}
                disabled
                required
                label={t("fullName")}
                type="text"
              />
            )}
          />

          <Controller
            disabled
            name="gender"
            control={control}
            defaultValue={getValues("gender")}
            render={({ field }) => (
              <Input
                {...field}
                required
                disabled
                label={t("gender")}
                type="select"
                value={getValues("gender")}
                options={ListGender.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
              />
            )}
          />

          <Controller
            disabled
            name="nationalityStatus"
            control={control}
            defaultValue={Number(getValues("nationalityStatus"))}
            render={({ field }) => (
              <Input
                {...field}
                disabled
                required
                label={t("citizenship")}
                type="select"
                options={CitizenshipStatus.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
              />
            )}
          />

          <Controller
            disabled
            name="identificationType"
            control={control}
            defaultValue={getValues("identificationType")}
            render={({ field }) => (
              <Input
                {...field}
                required
                disabled
                label={t("idType")}
                type="select"
                options={IdTypes.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
              />
            )}
          />

          <Controller
            disabled
            name="identificationNo"
            control={control}
            defaultValue={getValues("identificationNo")}
            render={({ field }) => (
              <Input
                {...field}
                disabled
                required
                label={t("idNumber")}
                type="text"
              />
            )}
          />

          <Controller
            disabled
            name="dateOfBirth"
            control={control}
            defaultValue={getValues("dateOfBirth")}
            render={({ field }) => {
              return (
                <Input
                  required
                  // disabled
                  {...field}
                  label={t("dateOfBirth")}
                  type="date"
                  onChange={(newValue) =>
                    setValue("dateOfBirth", newValue.target.value)
                  }
                  value={
                    getValues("dateOfBirth")
                      ? dayjs(getValues("dateOfBirth")).format("DD-MM-YYYY")
                      : ""
                  }
                />
              );
            }}
          />

          <Controller
            disabled
            name="placeOfBirth"
            control={control}
            defaultValue={getValues("placeOfBirth")}
            render={({ field }) => (
              <Input
                {...field}
                required
                label={t("placeOfBirth")}
                type="text"
              />
            )}
          />

          <Controller
            disabled
            name="employmentCode"
            control={control}
            defaultValue={getValues("employmentCode")}
            render={({ field }) => (
              <Input
                {...field}
                required
                // disabled
                label={t("occupation")}
                type="select"
                options={occupationList}
              />
            )}
          />

          <Controller
            disabled
            name="address"
            control={control}
            defaultValue={getValues("address")}
            render={({ field }) => (
              <Input
                {...field}
                // disabled
                required
                label={t("residentialAddress")}
                type="text"
                multiline
                rows={3}
              />
            )}
          />

          <Controller
            disabled
            name="stateCode"
            control={control}
            defaultValue={getValues("stateCode")}
            render={({ field }) => (
              <Input
                {...field}
                required
                // disabled
                label={t("state")}
                type="select"
                onChange={(e) => setValue("stateCode", e.target.value)}
                value={parseInt(getValues("stateCode"))}
                options={addressListData
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => ({
                    label: item.name,
                    value: item.id,
                  }))}
              />
            )}
          />

          <Controller
            disabled
            name="districtCode"
            control={control}
            defaultValue={getValues("districtCode")}
            render={({ field }) => (
              <Input
                {...field}
                // disabled
                required
                label={t("district")}
                type="select"
                value={parseInt(getValues("districtCode"))}
                onChange={(e) => setValue("districtCode", e.target.value)}
                options={addressListData
                  .filter(
                    (item: any) => item.pid === parseInt(watch("stateCode"))
                  )
                  .map((item: any) => ({ label: item.name, value: item.id }))}
              />
            )}
          />

          <Controller
            disabled
            name="city"
            control={control}
            defaultValue={getValues("city")}
            render={({ field }) => (
              <Input
                {...field}
                // disabled
                label={t("city")}
                type="text"
              />
            )}
          />

          <Controller
            disabled
            name="postcode"
            control={control}
            defaultValue={getValues("postcode")}
            render={({ field: { onChange, value, ...rest } }) => (
              <Input
                {...rest}
                // disabled
                value={value}
                required
                label={t("poskod")}
                type="text"
                inputMode="numeric"
                onChange={(e) => {
                  const onlyDigits = e.target.value
                    .replace(/\D/g, "")
                    .slice(0, 5);
                  onChange(onlyDigits);
                }}
              />
            )}
          />

          <Controller
            disabled
            name="email"
            control={control}
            defaultValue={getValues("email")}
            render={({ field }) => (
              <Input
                {...field}
                disabled
                required
                label={t("email")}
                type="text"
              />
            )}
          />

          <Controller
            disabled
            name="phoneNo"
            control={control}
            defaultValue={getValues("phoneNo")}
            render={({ field }) => (
              <Input
                {...field}
                required
                disabled
                label={t("phoneNumber")}
                type="text"
              />
            )}
          />

          <Controller
            disabled
            name="telephoneNo"
            control={control}
            defaultValue={getValues("telephoneNo")}
            render={({ field }) => (
              <Input {...field} label={t("homeNumber")} type="text" />
            )}
          />
        </Box>
      )}

      {watch("auditorType") === "L" && (
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatJuruauditBertauliah")}
          </Typography>

          <Controller
            disabled
            name="appointmentDate"
            control={control}
            defaultValue={getValues("appointmentDate")}
            render={({ field }) => {
              const date = getValues("appointmentDate");

              const formattedDate = dayjs(date).format("DD-MM-YYYY");

              return (
                <Input
                  required
                  {...field}
                  label={t("tarikhLantik")}
                  type="date"
                  value={formattedDate}
                />
              );
            }}
          />

          <Controller
            disabled
            name="name"
            control={control}
            defaultValue={getValues("name")}
            render={({ field }) => (
              <Input {...field} required label={t("fullName")} type="text" />
            )}
          />

          <Controller
            disabled
            name="licenseNo"
            control={control}
            defaultValue={getValues("licenseNo")}
            render={({ field }) => (
              <Input {...field} required label={t("nomborLesen")} type="text" />
            )}
          />

          <Controller
            disabled
            name="companyName"
            control={control}
            defaultValue={getValues("companyName")}
            render={({ field }) => (
              <Input
                {...field}
                // required
                label={capitalizeWords(t("companyName"))}
                type="text"
              />
            )}
          />
          <Controller
            disabled
            name="companyNo"
            control={control}
            defaultValue={getValues("companyNo")}
            render={({ field }) => (
              <Input
                {...field}
                // required
                label={t("noSyarikat")}
                type="text"
              />
            )}
          />
          <Controller
            disabled
            name="address"
            control={control}
            defaultValue={getValues("address")}
            render={({ field }) => (
              <Input
                {...field}
                label={t("companyAddress")}
                type="text"
                multiline
                rows={3}
              />
            )}
          />
          <Controller
            disabled
            name="stateCode"
            control={control}
            defaultValue={getValues("stateCode")}
            render={({ field }) => (
              <Input
                {...field}
                // required
                label={t("state")}
                type="select"
                value={parseInt(getValues("stateCode"))}
                onChange={(e) => setValue("stateCode", e.target.value)}
                options={addressListData
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => ({
                    label: item.name,
                    value: item.id,
                  }))}
              />
            )}
          />

          <Controller
            disabled
            name="districtCode"
            control={control}
            defaultValue={getValues("districtCode")}
            render={({ field }) => (
              <Input
                {...field}
                // required
                label={t("district")}
                type="select"
                value={parseInt(getValues("districtCode"))}
                onChange={(e) => setValue("districtCode", e.target.value)}
                options={addressListData
                  .filter(
                    (item: any) => item.pid === parseInt(watch("stateCode"))
                  )
                  .map((item: any) => ({ label: item.name, value: item.id }))}
              />
            )}
          />
          <Controller
            disabled
            name="city"
            control={control}
            defaultValue={getValues("city")}
            render={({ field }) => (
              <Input {...field} label={t("city")} type="text" />
            )}
          />

          <Controller
            disabled
            name="postcode"
            control={control}
            defaultValue={getValues("postcode")}
            render={({ field: { onChange, value, ...rest } }) => (
              <Input
                {...rest}
                value={value}
                // required
                label={t("poskod")}
                type="text"
                inputMode="numeric"
                onChange={(e) => {
                  const onlyDigits = e.target.value
                    .replace(/\D/g, "")
                    .slice(0, 5);
                  onChange(onlyDigits);
                }}
              />
            )}
          />

          <Controller
            disabled
            name="email"
            control={control}
            defaultValue={getValues("email")}
            render={({ field }) => (
              <Input
                {...field}
                required
                label={t("emelSyarikat")}
                type="text"
              />
            )}
          />

          <Controller
            disabled
            name="phoneNo"
            control={control}
            defaultValue={getValues("phoneNo")}
            render={({ field }) => (
              <Input {...field} required label={t("phoneNumber")} type="text" />
            )}
          />
        </Box>
      )}

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>
      </Box>
    </Box>
  );
};

export default ViewJuruAudit;
