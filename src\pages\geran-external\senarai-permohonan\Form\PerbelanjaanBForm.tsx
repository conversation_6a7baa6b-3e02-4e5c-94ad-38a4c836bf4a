import React from "react";
import { useTranslation } from "react-i18next";
import { useFormContext } from "react-hook-form";
import { globalStyles } from "@/helpers";

import { Box, Typography } from "@mui/material";
import { ToggleButtonController, TextFieldController } from "@/components";

const perbelanjaanOptions = [
  {
    value: "1",
    label: "Program pencegahan jenayah",
  },
  {
    value: "2",
    label: "Program keselamatan komuniti",
  },
  {
    value: "3",
    label: "Program komuniti bebas dadah",
  },
  {
    value: "4",
    label: "Program pencegarah pembakaran",
  },
  {
    value: "5",
    label: "Program Bantuan Awal Kecemasan dan Program Pengurusan Bencana",
  },
  {
    value: "other",
    label: "Lain-Lain",
  },
];

const Form = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const { control, watch } = useFormContext();

  return (
    <Box className={classes.sectionBox} mb={2}>
      <Typography className="title" mb={2}>
        Perbelanjaan bahagian B (Skop Program)
      </Typography>

      <ToggleButtonController
        name="perbelanjaanB"
        control={control}
        options={perbelanjaanOptions}
        sx={{
          gap: 4,
        }}
      />

      {watch("perbelanjaanB") === "other" && (
        <TextFieldController
          name="otherReason"
          control={control}
          multiline
          rows={1}
        />
      )}
    </Box>
  );
};

const PerbelanjaanBForm = React.memo(Form);
export default PerbelanjaanBForm;
