import React, { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, Grid, CircularProgress } from "@mui/material";
import Input from "../../../../components/input/Input";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import ConfirmationDialog from "../../../../components/dialog/confirm";
import AlertDialog from "../../../../components/dialog/alert";
import { API_URL } from "../../../../api";
import NewAlertDialog from "../../../../components/dialog/newAlert";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { useCustomMutation } from "@refinedev/core";
import { DialogConfirmation } from "@/components";

const Kemaskini: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);

  const hasKaunterUpdatePermission = AuthHelper.hasAuthority([
    `${PermissionNames.MEJA_BANTUAN1.label}:${pageAccessEnum.Update}`,
    `${PermissionNames.MEJA_BANTUAN2.label}:${pageAccessEnum.Update}`,
    `${PermissionNames.MEJA_BANTUAN3.label}:${pageAccessEnum.Update}`,
  ]);

  const closeSuccess = () => {
    setDialogAlertSaveOpen(false);
    navigate(-1);
  };

  useEffect(() => {
    if (!hasKaunterUpdatePermission) {
      navigate("/internal-user");
    }
  }, [hasKaunterUpdatePermission, navigate]);

  const paymentId = location.state.id;
  const prevState = location.state;

  const [noResit, setNoResit] = useState(
    prevState?.receiptNo ? prevState?.receiptNo : ""
  );
  const [noResitError, setNoResitError] = useState<{
    [key: string]: string;
  }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (!noResit || noResit === "") {
      errors.noResit = t("requiredValidation");
    }

    return errors;
  };

  const handleConfirm = () => {
    UpdatePayment();
  };

  const handleUpdate = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setNoResitError(errors);
      return;
    }

    setDialogSaveOpen(true);
  };

  // const updatePayment = async () => {
  //   try {
  //     const response = await fetch(`${API_URL}/society/payment/updatePayment`, {
  //       method: "put",
  //       headers: {
  //         portal: localStorage.getItem("portal") || "",
  //         authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         paymentId: paymentId,
  //         paymentStatus: "P", // C - cancel , P - paid
  //         receiptNo: noResit,
  //       }),
  //     });

  //     const result = await response.json();

  //     if (result.status === "SUCCESS" && result.data) {
  //     } else {
  //       console.error("API Error:", result);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching society data:", error);
  //   }
  // };

  const { mutate: updatePayment, isLoading } = useCustomMutation();
  const UpdatePayment = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    updatePayment(
      {
        url: `${API_URL}/society/payment/updatePayment`,
        method: "put",
        values: {
          paymentId: paymentId,
          paymentStatus: "P", // C - cancel , P - paid
          receiptNo: noResit,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            setDialogSaveOpen(false);
            setDialogAlertSaveOpen(true);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <Box
        sx={{
          p: 2,
          mb: 1,
          backgroundColor: "#FCFCFC",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            backgroundColor: "var(--primary-color)",
            padding: "27px 41px",
            borderRadius: "14px",
            position: "relative",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{ fontWeight: "bold", color: "white", fontSize: "18px" }}
          >
            <>
              {prevState?.societyName}
              <br />
              {prevState.societyNo}
            </>
          </Typography>

          <Box
            sx={{
              width: "94px",
              position: "absolute",
              right: "37px",
              bottom: "-20px",
            }}
          >
            <img src="/ornament.svg" alt="ornament" width="100%" />
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          p: 2,
          mb: 1,
          backgroundColor: "#FCFCFC",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            px: 2,
            border: "1px solid #D9D9D9",
            backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Grid container spacing={2} pl={4} pt={6} pb={4}>
            <Typography
              variant="subtitle1"
              sx={{ color: "var(--primary-color)", fontWeight: 600 }}
            >
              {t("butiranPembayaranKaunter")}
            </Typography>
          </Grid>

          <Grid container spacing={2} pl={4} pt={2} pb={2}>
            <Input
              disabled
              label={t("jenisPembayaran")}
              value={prevState.paymentType}
            />
            <Input
              disabled
              label={t("keteranganPembayaran")}
              value={prevState.paymentType}
            />
            <Input
              disabled
              label={t("amaunPembayaran")}
              value={`RM ${prevState.amount.toFixed(2)}`}
            />
            <Input disabled label={t("kodHasil")} value={prevState.docCode} />
            <Input
              disabled
              label={t("namaPembayar")}
              value={prevState.userName}
            />
            <Input
              disabled
              label={t("noPengenalanDiriPembayar")}
              value={prevState.icNo}
            />
            <Input
              required
              error={!!noResitError.noResit}
              helperText={noResitError.noResit}
              label={t("noResit")}
              onChange={(e) => {
                setNoResit(e.target.value);
                setNoResitError((prev) => ({
                  ...prev,
                  noResit: "",
                }));
              }}
            />
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          mt: 3,
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>
          {t("semula")}
        </ButtonOutline>
        <ButtonPrimary onClick={handleUpdate}>{t("update")}</ButtonPrimary>
      </Box>

      <ConfirmationDialog
        open={dialogSaveOpen}
        onClose={() => setDialogSaveOpen(false)}
        title={t("pembayaranKaunter")}
        message={t("pembayaranKaunterDialogMsg1")}
        onConfirm={handleConfirm}
        onCancel={() => setDialogSaveOpen(false)}
        isMutation={isLoading}
      />

      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={() => closeSuccess()}
        message={t("applicationSuccessfulySubmited")}
      />
    </>
  );
};

export default Kemaskini;
