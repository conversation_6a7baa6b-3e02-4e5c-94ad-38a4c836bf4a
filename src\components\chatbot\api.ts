import axios from "axios";
import { ChatbotApiResponse, GroupedChatMessage, LangflowMessage } from "./types";
import { convertLangflowMessages, getSessionId } from "./utils";
import { IUser } from "@/types";

// Constants
const CHATBOT_API_URL = "https://chatbot.ros.gov.my/api/v1";
const API_KEY = "sk-BMuaq4o7XA5yhdrx_2sVABZLrUCof4Laf8pOkAnfLJ8";
const FLOW_ID = "8033381a-b216-46c7-8496-c0b4bd3f88b5";

/**
 * Sends a message to the chatbot API
 * @param query The user's message
 * @param user The current user
 * @param locale The current locale
 * @returns The bot's response or an error message
 */
export async function sendChatbotMessage(
  query: string,
  user: IUser | undefined,
  locale: string
): Promise<string | null> {
  try {
    const response = await axios.post<ChatbotApiResponse>(
      `${CHATBOT_API_URL}/run/${FLOW_ID}?stream=false`,
      {
        input_value: query,
        output_type: "chat",
        input_type: "chat",
        session_id: getSessionId(user),
      },
      {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": API_KEY,
        },
      }
    );

    // Extract the bot message from the response
    const botMessage = response.data?.outputs?.[0]?.outputs?.[0]?.results?.message?.text;

    // Try different paths in the response to find the message
    const fallbackMessage =
      botMessage ||
      response.data?.outputs?.[0]?.outputs?.[0]?.results?.text ||
      response.data?.outputs?.[0]?.outputs?.[0]?.artifacts?.message ||
      response.data?.outputs?.[0]?.outputs?.[0]?.messages?.[0]?.message ||
      response.data?.result?.outputs?.[0]?.outputs?.[0]?.outputs?.message?.message;

    if (fallbackMessage) {
      return fallbackMessage;
    } else {
      // console.error("Could not find message in response:", response.data);
      return locale === "ms"
        ? "Maaf, tiada jawapan tersedia. Sila cuba soalan lain."
        : "Sorry, no answer is available. Please try another question.";
    }
  } catch (err) {
    // console.error("API Error:", err);
    return null;
  }
}

/**
 * Fetches message history for a user session
 * @param sessionId The user's session ID
 * @param limit Optional limit for the number of messages to fetch (default: 100 to get all messages)
 * @returns Array of chat messages
 */
export async function fetchMessageHistory(
  sessionId: string,
  limit: number = 100
): Promise<GroupedChatMessage[]> {
  try {
    // console.log(`Fetching message history for session ${sessionId} with limit ${limit}`);

    const response = await axios.get(
      `${CHATBOT_API_URL}/monitor/messages`,
      {
        headers: {
          Accept: "application/json",
          "x-api-key": API_KEY,
        },
        params: {
          session_id: sessionId,
          limit: limit
        },
      }
    );

    // console.log(`Received ${response.data?.length || 0} messages from API`);

    const messages = convertLangflowMessages(response.data as LangflowMessage[]);

    // Add unique message IDs if they don't exist
    const messagesWithIds = messages.map(msg => ({
      ...msg,
      messageId: msg.messageId || `${msg.sender}_${Date.now().toString(36)}_${Math.random().toString(36).substring(2)}`
    }));

    // console.log(`Processed ${messagesWithIds.length} messages with IDs`);

    return messagesWithIds;
  } catch (error) {
    // console.error("Error fetching message history:", error);
    return [];
  }
}

/**
 * Fetches the latest conversation (last user question and bot response)
 * @param user The current user
 * @param locale The current locale
 * @returns The latest conversation or null if none exists
 */
export async function fetchLatestConversation(
  user: IUser | undefined,
  locale: string
): Promise<{ userMessage: GroupedChatMessage | null, botResponse: GroupedChatMessage | null } | null> {
  try {
    const sessionId = getSessionId(user);
    const history = await fetchMessageHistory(sessionId, 10); // Get last 10 messages to find the latest conversation

    if (history.length === 0) {
      return null;
    }

    // Sort by timestamp in descending order to get the most recent messages
    const sortedHistory = [...history].sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // Find the latest bot message
    const latestBotMessage = sortedHistory.find(msg => msg.sender === 'bot');

    // Find the latest user message that came before the bot message
    const latestUserMessage = sortedHistory.find(msg =>
      msg.sender === 'user' &&
      (!latestBotMessage || new Date(msg.timestamp).getTime() <= new Date(latestBotMessage.timestamp).getTime())
    );

    return {
      userMessage: latestUserMessage || null,
      botResponse: latestBotMessage || null
    };
  } catch (error) {
    // console.error("Error fetching latest conversation:", error);
    return null;
  }
}

/**
 * Fetches society list data for the current user
 * @param user The current user
 * @param locale The current locale
 * @param refetchSocietyList Function to refetch society list data
 * @param societyListDataResponse Current society list data
 * @returns A message with the society count or an error message
 */
export async function fetchSocietyList(
  user: IUser | undefined,
  locale: string,
  refetchSocietyList: () => Promise<any>,
  societyListDataResponse: any
): Promise<string | null> {
  if (!user?.identificationNo) {
    return locale === "ms"
      ? "Sila log masuk terlebih dahulu."
      : "Please login first.";
  }

  try {
    await refetchSocietyList();
    const totalList = societyListDataResponse?.data?.data?.total ?? 0;

    return locale === "ms"
      ? `Terdapat ${totalList} daftar pertubuhan.`
      : `There are ${totalList} society registrations.`;
  } catch (err) {
    // console.error("Error fetching society list:", err);
    return locale === "ms"
      ? "Terdapat masalah semasa mendapatkan maklumat daftar pertubuhan."
      : "There was an error fetching the society list.";
  }
}

/**
 * Delete chat history for a specific session
 * @param sessionId The session ID to delete
 */
export const deleteChatHistory = async (sessionId: string): Promise<void> => {
  try {
    const response = await fetch(`${CHATBOT_API_URL}/monitor/messages/session/${sessionId}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete chat history: ${response.statusText}`);
    }
  } catch (error) {
    // console.error('Error deleting chat history:', error);
    throw error;
  }
};
